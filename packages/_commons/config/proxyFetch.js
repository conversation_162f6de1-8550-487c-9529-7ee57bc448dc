/**
 * @file 代理的请求配置
 * <AUTHOR> (ma<PERSON><EMAIL>)
 * <AUTHOR>
 * @date 2017/11/30.
 */

const httpProxy = require('http-proxy');
const http = require('http');
const https = require('https');
const {
    proxyToEnum,
    proxyModeEnum,
    proxyTo,
    proxyMode,
    pathList,
    proxyToTestPort
} = require('./proxyConfig');

const proxyToConfig = {
    /*
        yapi环境
     */
    [proxyToEnum.YAPI.key]: {
        proxyUrl: `http://api.weiyun.baidu.com/mock/33115`,
        proxyHost: 'api.weiyun.baidu.com',
        devHost: 'dev.fctest.baidu.com',
        https: false,
        agent: http.globalAgent
    },
    /*
        测试环境
     */
    [proxyToEnum.TEST.key]: {
        proxyUrl: `http://fctest.baidu.com:${proxyToTestPort}`,
        proxyHost: 'fctest.baidu.com',
        devHost: 'dev.fctest.baidu.com',
        https: false,
        // proxyUrl: `http://fcfeed.baidu.com:${proxyToTestPort}`,
        // proxyHost: 'fcfeed.baidu.com',
        // devHost: 'dev.fcfeed.baidu.com',
        agent: http.globalAgent
    },
    /*
        预上线环境
        127.0.0.1	dev.fengchao.baidu-int.com
     */
    [proxyToEnum.PREONLINE.key]: {
        proxyUrl: 'https://fengchao.baidu-int.com',
        proxyHost: 'fengchao.baidu-int.com',
        devHost: 'dev.fengchao.baidu-int.com',
        https: true,
        agent: https.globalAgent
    },
    /*
        线上环境
     */
    [proxyToEnum.ONLINE.key]: {
        proxyUrl: 'https://fengchao.baidu.com',
        proxyHost: 'fengchao.baidu.com',
        // devHost: 'dev.feedads.baidu.com', // 调试feed使用
        devHost: 'dev.fengchao.baidu.com',
        https: true,
        agent: https.globalAgent
    }
};

const isInPathList = path => pathList.some(rule => {
    return rule instanceof RegExp ? rule.test(path) : rule === path;
});

const HAIRUO_PATH = '/hairuo/request.ajax';

const isProxyRemote = path => (proxyMode === proxyModeEnum.WHOLE)
    || (proxyMode === proxyModeEnum.WHITELIST && isInPathList(path))
    || (proxyMode === proxyModeEnum.BLACKLIST && !isInPathList(path));


const {proxyUrl, devHost, agent, proxyHost} = proxyToConfig[proxyTo.key];
const proxyServer = httpProxy.createProxyServer({
    target: proxyUrl,
    changeOrigin: true,
    agent,
    headers: {
        host: proxyHost,
        origin: proxyUrl
    }
});

proxyServer.on('error', e => {
});

module.exports = {
    isProxyRemote,
    HAIRUO_PATH,
    proxyConfig: proxyToConfig[proxyTo.key],
    proxyServer,
    host: devHost
};
