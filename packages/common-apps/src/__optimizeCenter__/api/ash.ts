/*
 * @file fe-ash-node api
 * <AUTHOR>
 * @date 2025-08-13 16:47:57
 */

// const apiPath = 'https://qingge.baidu.com/ash-api/cardRecommendation/getUserRecommendation';
const apiPath = 'http://localhost:8848/ash-api/cardRecommendation/getUserRecommendation';

export async function getUserRecommendation({userId}: {userId: string}): Promise<any> {
    // @ts-ignore
    // const flag = window.userRecommendationTest;
    // if (flag !== '1') {
    //     return {};
    // }
    try {
        const body = new URLSearchParams({
            params: JSON.stringify({
                source: 100 // 区分凤巢和轻舸
            }),
            path: '/ash-api/cardRecommendation/getUserRecommendation',
            userid: userId
        });

        const resp = await fetch(apiPath, {
            method: 'POST',
            mode: 'cors',
            credentials: 'include',
            body
        });

        const res = await resp.json();
        const data = res.data;
        data.interfereList = [];
        data.interfereIndexs = [];
        return data;
    }
    catch (e) {
        return {};
    }
}
