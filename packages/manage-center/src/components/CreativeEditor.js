import {useCallback, useMemo, useState, useEffect} from 'react';
import {Form, Layout} from '@baidu/one-ui';
import {useBoolean} from '@huse/boolean';
import {FunnelProcessWrapper, useFunnelRecordCallback} from 'commonLibs/funnelWrapper';
import {partial, unionBy} from 'lodash-es';
import SuspenseBoundary, {CacheProvider} from 'commonLibs/suspenseBoundary';
import {fetchEditCreativeInfo} from 'app/containers/fcNew/apis/creative';
import {useResource} from 'commonLibs/magicSuspenseBoundary';
import {FORM_FIELDS, ScrollClassNameConfig} from 'app/containers/fcNew/creative/config';
import CreativeImage from 'app/containers/fcNew/creative/creativeImage';
import CreativeImport from 'app/containers/fcNew/creative/creativeImport';
import {scrollToClassName} from 'commonLibs/utils/scrollToPosition';
import useAreaHasClickMonitor from 'commonLibs/hooks/useAreaHasClickMonitor';
import {CREATIVE_STEPS} from 'app/containers/fcNew/config/webMarketSteps';
import {
    creativeLimitOfAdgroup, getRecommendConfig,
    getRecommendKey, RECOMMEND_KEY_CONFIG
} from 'app/containers/fcNew/creative/recommend/config';
import {useRecordByComponent} from 'commonLibs/hooks/record';
import {RECORD_MATERIAL_KEY} from 'commonLibs/config/record';
import {importFormData, isNewCreativeRecommendUser} from 'app/containers/fcNew/creative/utils';
import {isOcpcCampaign} from 'commonLibs/utils/campaign';
import {PROJECT_MODE_TYPE} from 'commonLibs/config/ocpc';
import {useEfficiencyImpactFactor} from 'commonLibs/hooks/monitor';
import CreativeTestStatus from 'app/containers/fcNew/creative/creativeText/Status';
import CreativeRecommendBar from 'app/containers/fcNew/creative/recommendBar';
import CreativeRecommend from 'app/containers/fcNew/creative/recommend';
import 'app/containers/fcNew/creative/style.less';
import CreativePreview from 'app/containers/fcNew/creative/preview';
import {getImageTypes} from 'app/containers/fcNew/creative/utils/image';
import Footer from 'app/containers/fcNew/creative/editCreative/footer';
import {usePinpaiInfo, useAdgroupImage, useUrlSuffixExpand} from 'app/containers/fcNew/creative/hooks';
import TopAlert from 'app/containers/fcNew/creative/topAlert';
import {getCPQLAlertKey} from 'app/containers/fcNew/creative/topAlert/config';
import FormContainer from 'app/containers/fcNew/creative/editCreative/form';
import creativeTextSvg from 'app/resource/svg/creative_text.svg';
import {
    getIsPcFinalUrlRequired,
    getIsMobileFinalUrlRequired
} from 'app/containers/fcNew/creative/url/validator';
import {useAdgroupBindAppInfo} from 'app/hooks/useAdgroupBindAppInfo';

function EditNewCreative(props) {
    const {form, currentId, getMaterialById, closeEditor, refreshList} = props;
    const {adgroupId, campaignId, creativeId} = getMaterialById(currentId) || {};
    const [data = []] = useResource(fetchEditCreativeInfo, {campaignId, adgroupId, creativeId});
    let [campaignInfo, adgroups, creativeInfo, accountVideoCount, adgroupVideoCount] = data;
    const {onClickCancelRecord, source} = useFunnelRecordCallback({
        getFieldError: form.getFieldError,
        needLogErrorMessage: true
    });
    const {marketingTargetId, adType, campaignBidType, projectModeType} = campaignInfo;
    const monitorProps = {
        level: 'idea',
        process_entence: 'EditCreativeForm',
        marketingTargetId,
        source
    };
    useEffect(() => {
        return onClickCancelRecord;
    }, []);

    const {formValues, imageSegments, pinpaiSegments, videoSegments} = creativeInfo;
    const adgroupIds = useMemo(() => [adgroupId], [adgroupId]);

    const [adgroupInfo, setAdgroupInfo] = useState({});
    useEffect(() => {
        setAdgroupInfo(adgroups[0]);
    }, [adgroups[0]]);
    const onChangeAdgroupInfo = useCallback((field, value) => {
        setAdgroupInfo({
            ...adgroupInfo,
            [field]: value
        });
    }, [adgroupInfo]);

    useRecordByComponent({recordKey: RECORD_MATERIAL_KEY.editCreative});
    const [impactFactor, impactMethods] = useEfficiencyImpactFactor({
        aiShoppingUrlSelect: false // 是否使用了爱采购的url下拉功能。
    });
    const [isShowUrlSettings, changeUrlSettingsVisible] = useState(false);
    const [{
        isShowMobileUrlSuffix,
        isShowPcUrlSuffix
    }, {
        changeMobileUrlSuffixVisible,
        changePcUrlSuffixVisible
    }] = useUrlSuffixExpand();
    const {
        isShowMobileFinalUrl,
        isShowPcFinalUrl
    } = useMemo(() => {
        const isShowMobileFinalUrl = getIsMobileFinalUrlRequired({
            ...adgroupInfo
        });
        const isShowPcFinalUrl = getIsPcFinalUrlRequired({
            ...adgroupInfo
        });
        return {
            isShowMobileFinalUrl,
            isShowPcFinalUrl
        };
    }, [adgroupInfo, campaignInfo]);
    // 品牌信息
    const {displayUrl, portraitPicUrl} = usePinpaiInfo({
        adgroupId,
        segments: pinpaiSegments,
        isMultiAdgroup: false,
        marketingTargetId
    });
    // 导入图片素材
    const [adgroupItem, changeAdgroupImage, validateErrors] = useAdgroupImage({
        imageSegments,
        videoSegments,
        segmentRecommendStatus: adgroupInfo.segmentRecommendStatus,
        knowledgeTextStatus: adgroupInfo.knowledgeTextStatus,
        jimuyuContentStatus: adgroupInfo.jimuyuContentStatus
    });

    // 推荐创意配置&&导入方法
    const recommendConfig = useMemo(() => getRecommendConfig({
        marketingTargetId,
        isConverionCampaign: isOcpcCampaign({adType, campaignBidType})
    }), [marketingTargetId, adType, campaignBidType]);
    const [recommendSource, setRecommendSource] = useState(null);
    const importCreatives = useCallback((recommendCreatives, recommendSegments) => {
        const newSegments = unionBy(adgroupItem.bindInfo.segments, recommendSegments, 'bindId');
        changeAdgroupImage({
            bindInfo: {
                ...adgroupItem.bindInfo,
                segments: newSegments
            }
        });
        const {title, description1, description2, source} = recommendCreatives[0];
        setRecommendSource(source ? getRecommendKey(RECOMMEND_KEY_CONFIG.ZNLX, source) : source);
        importFormData({
            form,
            changeUrlSettingsVisible,
            changeMobileUrlSuffixVisible,
            changePcUrlSuffixVisible,
            formValues: {
                [FORM_FIELDS.title]: title,
                [FORM_FIELDS.description1]: description1,
                [FORM_FIELDS.description2]: description2
            }
        });
        scrollToClassName(ScrollClassNameConfig.CREATIVE_TEXT, 0);
    }, [adgroupItem.bindInfo, changeAdgroupImage, form, changeMobileUrlSuffixVisible, changePcUrlSuffixVisible,
        changeUrlSettingsVisible]);

    // 根据当前单元的流量设置拿到可以选择的图片类型
    const imageTypes = useMemo(() => {
        return getImageTypes(adgroups, campaignInfo);
    }, [adgroups, campaignInfo]);

    const previewProps = {
        imageTypes,
        form,
        activeAdgroupItem: adgroupItem,
        marketingTargetId,
        adgroupIds,
        displayUrl,
        portraitPicUrl
    };
    const disabledStopTextOptimization = projectModeType === PROJECT_MODE_TYPE.ai;
    // 创意推荐展开收起
    const [recommendFlag, {toggle: onVisibleChange}] = useBoolean();
    const {onAreaHasClickMonitor} = useAreaHasClickMonitor(monitorProps);
    const {data: adgroupBindInfo, pending} = useAdgroupBindAppInfo(adgroups);
    // 创意推荐展开收起
    const alertKey = getCPQLAlertKey({
        marketingTargetId,
        structuredContentIds: adgroupInfo.structuredContentIds,
        adgroupInfo: adgroupBindInfo?.[0]
    });
    const baseProps = {
        form,
        changeUrlSettingsVisible,
        changeMobileUrlSuffixVisible,
        changePcUrlSuffixVisible
    };
    return (
        <Layout style={{minWidth: 800}}>
            <div className="creative-adgroup-tip">
                所属计划 “计划{campaignInfo.campaignName}”，单元“单元{adgroupInfo.adgroupName}”
            </div>
            {!pending && <TopAlert alertKey={alertKey} />}
            <div className="mc-new-creative-main-container">
                {
                    !isNewCreativeRecommendUser(marketingTargetId) && recommendConfig && (
                        <CreativeRecommend
                            adgroupIds={adgroupIds}
                            upperLimitNum={creativeLimitOfAdgroup}
                            importCreatives={importCreatives}
                            displayUrl={displayUrl}
                            portraitPicUrl={portraitPicUrl}
                            imageTypes={imageTypes}
                            {...recommendConfig}
                        />
                    )
                }
                <div className="creative-main">
                    <div className="creative-form" onClick={onAreaHasClickMonitor}>
                        <div
                            className={ScrollClassNameConfig.CREATIVE_TEXT}
                            id={CREATIVE_STEPS.CREATIVE_CONTENT}
                        >
                            <div className="creative-form-title">
                                <img src={creativeTextSvg} />
                                创意文案
                            </div>
                            <CreativeTestStatus
                                disabled={disabledStopTextOptimization}
                                adgroupId={adgroups[0]?.adgroupId}
                                value={adgroupInfo.creativeTextOptimizationStatus}
                                onChange={partial(onChangeAdgroupInfo, 'creativeTextOptimizationStatus')}
                            />
                            <CreativeImport
                                marketingTargetId={marketingTargetId}
                                {...baseProps}
                                displayUrl={displayUrl}
                                portraitPicUrl={portraitPicUrl}
                            />
                            <FormContainer
                                {...baseProps}
                                marketingTargetId={marketingTargetId}
                                campaignInfo={campaignInfo}
                                adgroupInfo={adgroupInfo}
                                initFormValues={formValues}
                                isShowUrlSettings={isShowUrlSettings}
                                isShowMobileUrlSuffix={isShowMobileUrlSuffix}
                                isShowPcUrlSuffix={isShowPcUrlSuffix}
                                isShowMobileFinalUrl={isShowMobileFinalUrl}
                                isShowPcFinalUrl={isShowPcFinalUrl}
                                impactMethods={impactMethods}
                            />
                        </div>
                        <FunnelProcessWrapper name="EditCreativeForm" extraParams={{marketingTargetId}}>
                            <CreativeImage
                                form={form}
                                isNoShowPicEdit={false}
                                disabledStopOptimization={disabledStopTextOptimization}
                                changeAdgroupImage={changeAdgroupImage}
                                imageTypes={imageTypes}
                                adgroups={adgroups}
                                data={adgroupItem}
                                recommend={{
                                    title: form.getFieldValue(FORM_FIELDS.title) || ''
                                }}
                                accountVideoSegmentCount={accountVideoCount - adgroupVideoCount}
                            />
                        </FunnelProcessWrapper>
                        <Footer
                            {...baseProps}
                            campaignId={campaignId}
                            adgroupId={adgroupId}
                            creativeId={creativeId}
                            campaignInfo={campaignInfo}
                            adgroupItem={adgroupItem}
                            initialSegments={imageSegments}
                            initialVideos={videoSegments}
                            recommendSource={recommendSource}
                            validateErrors={validateErrors}
                            impactFactor={impactFactor}
                            onImmersiveCancel={closeEditor}
                            refreshList={refreshList}
                        />
                    </div>
                    {
                        isNewCreativeRecommendUser(marketingTargetId) ? (
                            <div className="creative-preview-recommend">
                                {
                                    recommendConfig && (
                                        <div style={{display: recommendFlag ? 'none' : 'block'}}>
                                            <CreativeRecommendBar
                                                recommendFlag={recommendFlag}
                                                onClick={onVisibleChange}
                                            />
                                        </div>
                                    )
                                }
                                <div
                                    className="creative-preview"
                                    style={{display: recommendFlag ? 'none' : 'block'}}
                                >
                                    <CreativePreview {...previewProps} />
                                </div>
                                <div
                                    className="creative-recommend"
                                    style={{display: recommendFlag ? 'block' : 'none'}}
                                >
                                    {
                                        recommendConfig && (
                                            <CreativeRecommend
                                                adgroupIds={adgroupIds}
                                                upperLimitNum={creativeLimitOfAdgroup}
                                                importCreatives={importCreatives}
                                                displayUrl={displayUrl}
                                                portraitPicUrl={portraitPicUrl}
                                                imageTypes={imageTypes}
                                                recommendFlag={recommendFlag}
                                                onVisibleChange={onVisibleChange}
                                                marketingTargetId={marketingTargetId}
                                                {...recommendConfig}
                                            />
                                        )
                                    }
                                </div>
                            </div>
                        ) : (
                            <div className="creative-oldpreview">
                                <CreativePreview {...previewProps} />
                            </div>
                        )
                    }
                </div>
            </div>
        </Layout>
    );
};

const EditCreativeWithForm = Form.create({
    name: 'editCreative'
})(EditNewCreative);

export default function EditCreative(props) {
    return (
        <SuspenseBoundary loading={{tip: '正在初始化创意'}}>
            <CacheProvider>
                <FunnelProcessWrapper name="EditCreativeForm">
                    <EditCreativeWithForm {...props} />
                </FunnelProcessWrapper>
            </CacheProvider>
        </SuspenseBoundary>
    );
}
