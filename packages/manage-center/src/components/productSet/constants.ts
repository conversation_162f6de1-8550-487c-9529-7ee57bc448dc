/**
 * @file 凤巢商品目录_商品组 constants
 * <AUTHOR>
 * @date 2023-4-6
 */
import {ProductSetTypeEnum} from './core-type';

export const PRODUCTSET_SELECT_TYPE_CONFIG = [
    {
        label: '全部商品',
        value: ProductSetTypeEnum.ALL_PRODUCT
    },
    {
        label: '自定义选择',
        value: ProductSetTypeEnum.CUSTOMIZED_PRODUCT
    }
];

export const ALL_PRODUCTSET_ID = -1; // 全部商品组向后端传-1
export const NON_PRODUCTSET_ID = 0; // 自定义商品组无选中时id设为0

export const MAX_PREVIEW_DEMO_NUM = 16; // 商品组最多预览16个
