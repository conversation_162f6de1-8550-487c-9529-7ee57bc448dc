
export const enum ProductSetTypeEnum {
    ALL_PRODUCT = 'ALL_PRODUCT',
    CLASSIFIED_PRODUCT = 'CLASSIFY_PRODUCT',
    CUSTOMIZED_PRODUCT = 'CUSTOMIZE_PRODUCT'
}

export interface CustomizedCondition {
    field?: string;
    operation?: string;
    value?: string;
}

export interface ConditionError {
    key: number | string;
    field?: string;
    operation?: string;
    value?: string;
    all?: string;
}

export interface CategoryFieldInfo {
    label: string;
    suggest: boolean;
    value: string;
    conditions: {label: string; value: string}[];
}

export interface ProductSetInfo {
    catalogId: number;
    catalogName?: string;
    conditions: CustomizedCondition[];
    id?: number;
    level?: string;
    productNum: number;
    productSetId: number;
    productSetName?: string;
    type: ProductSetTypeEnum;
    userId?: number;
}

export interface ProductSetDetailDemoInfo {
    productId: number;
    refuseReason: string;
    name: string;
    image: string;
    auditStatus: string;
}

export interface ProductSetDetail {
    total: number;
    demoProducts: ProductSetDetailDemoInfo[];
}
