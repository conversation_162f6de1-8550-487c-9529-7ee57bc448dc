/**
 * @file 商品组设置
 * <AUTHOR>
 * @date 2023-4-6
 */
import {useCallback, useEffect, useState, useMemo} from 'react';
import {Radio, Button} from '@baidu/one-ui';
import {useMap} from '@huse/collection';
import {useOriginalDeepCopy} from '@huse/previous-value';
import {useRequest} from '@huse/request';
import {IconPlus} from 'dls-icons-react';
import {isEmpty} from 'lodash-es';
import {useControl} from 'commonLibs/hooks/externalControl';
import {getProductSet, getDemoProductsByRulesApi} from 'app/api/commodity';
import {ALL_PRODUCTSET_ID, NON_PRODUCTSET_ID, PRODUCTSET_SELECT_TYPE_CONFIG} from './constants';
import {ProductSetTypeEnum, ProductSetInfo} from './core-type';
import DownloadProductSet from './components/DownloadProductSet';
import CustomProductSetEditor from './components/CustomProductSetEditor';
import './style.less';

const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;

interface ProductSetEditorProps {
    value: number | null;
    onChange: (val: number) => void;
    campaignInfo: {
        catalogId: number;
    };
    // 如果是编辑商品组，编辑前的商品组id
    initialProductSetId?: number;
}

const ProductSetEditor = (props: ProductSetEditorProps) => {
    const {
        value: productSetId,
        onChange,
        campaignInfo: {catalogId},
        initialProductSetId
    } = props;

    const [isInitialSelectAllProduct, setIsInitialSelectAllProduct] = useState(false);
    const isSelectAllProductSet = useMemo(() => {
        return isInitialSelectAllProduct ? productSetId === initialProductSetId : productSetId === ALL_PRODUCTSET_ID;
    }, [productSetId, initialProductSetId, isInitialSelectAllProduct]);

    const [
        productSetInfoMap, {set: setProductInfo, setAll: setProductSetInfoList, clear: clearProductSetInfoMap}
    ] = useMap<number, ProductSetInfo>();

    const productTotalCount = productSetInfoMap.get(productSetId)?.productNum;
    const currentConditions = useOriginalDeepCopy(productSetInfoMap.get(productSetId)?.conditions);
    const requestTotalCount = useCallback(({id, conditions}) => {
        // -1说明总数计算中，用getDemoProductsByRulesApi获取商品总数
        if (productTotalCount === -1 && id && !isEmpty(conditions)) {
            return getDemoProductsByRulesApi({catalogId: id, conditions, limit: 0})
                .then((res = []) => res && res[0].total);
        }
        return Promise.resolve(0);
    }, [productTotalCount]);
    const {pending, data: total} = useRequest(requestTotalCount, {id: catalogId, conditions: currentConditions});

    const changeSelectType = useCallback((evt: {target: {value: ProductSetTypeEnum}}) => {
        const newSelectType = evt.target.value;
        const isSelectAll = newSelectType === ProductSetTypeEnum.ALL_PRODUCT;
        let newValue = NON_PRODUCTSET_ID;
        if (isSelectAll) {
            newValue = isInitialSelectAllProduct ? initialProductSetId as number : ALL_PRODUCTSET_ID;
        }
        onChange(newValue);
    }, [initialProductSetId, isInitialSelectAllProduct]);

    const [ProductSetEditor, {openEditor, closeEditor}] = useControl(CustomProductSetEditor);
    const customProductSetEditorProps = {
        productSetId,
        productSetInfoMap,
        setProductInfo,
        setProductSetInfoList,
        catalogId,
        onSelectAllProduct: useCallback(() => {
            closeEditor();
            changeSelectType({target: {value: ProductSetTypeEnum.ALL_PRODUCT}});
        }, [changeSelectType, closeEditor]),
        onChange
    };

    const updateAllProductInfo = useCallback(id => {
        if (id) {
            return getDemoProductsByRulesApi({catalogId: id, conditions: [], limit: 0})
                .then((res = []) => {
                    if (res && res[0]) {
                        setProductInfo(ALL_PRODUCTSET_ID, {
                            productSetId: ALL_PRODUCTSET_ID,
                            id: ALL_PRODUCTSET_ID,
                            catalogId: id,
                            productNum: res[0].total,
                            type: ProductSetTypeEnum.ALL_PRODUCT,
                            catalogName: '',
                            conditions: [],
                            level: '',
                            productSetName: ''
                        });
                    }
                });
        }
        return Promise.resolve([]);
    }, [setProductInfo]);
    useRequest(updateAllProductInfo, catalogId);

    // 记录初始选中的商品组的信息，判断是否是全部商品
    const requestProductInfo = useCallback(id => {
        if (id) {
            return getProductSet({productSetIds: [id]}).then(info => {
                setProductInfo(id, info);
                setIsInitialSelectAllProduct(info.type === ProductSetTypeEnum.ALL_PRODUCT);
            });
        }
        return Promise.resolve(null);
    }, [setProductInfo, setIsInitialSelectAllProduct]);
    useRequest(requestProductInfo, initialProductSetId);

    useEffect(() => {
        return clearProductSetInfoMap;
    }, []);

    return (
        <div className="product-set-editor-container">
            <RadioGroup
                value={isSelectAllProductSet ? ProductSetTypeEnum.ALL_PRODUCT : ProductSetTypeEnum.CUSTOMIZED_PRODUCT}
                onChange={changeSelectType}
            >
                {PRODUCTSET_SELECT_TYPE_CONFIG.map(({label, value}) => (
                    <RadioButton key={value} value={value}>{label}</RadioButton>
                ))}
            </RadioGroup>
            <div>
                {productSetId ? (
                    <span className={`product-set-content ${isSelectAllProductSet ? '' : 'has-change-button'}`}>
                        <span className="count">
                            已选择商品：{pending ? '' : (productTotalCount === -1 ? total : productTotalCount)}个
                        </span>
                        {isSelectAllProductSet ? null : (
                            <Button type="text-strong" onClick={openEditor}>更换</Button>
                        )}
                    </span>
                ) : (
                    <span className="product-set-content add-button" onClick={openEditor}>
                        <IconPlus />点击选择商品
                    </span>
                )}

                <DownloadProductSet className="product-set-download-button" productSetId={productSetId} />
            </div>
            {catalogId && <ProductSetEditor {...customProductSetEditorProps} />}
        </div>
    );
};

export default ProductSetEditor;