/*
 * @file: shawdowCard
 * @author: lian<PERSON>of<PERSON>@baidu.com
 * @Date: 2021-08-25 21:52:53
 */
import {useToggle} from '@huse/boolean';
import classNames from 'classnames';
import './style.less';

const Card = props => {
    const {shadowPrefer, shadowNode, node} = props;
    return (
        <div>
            {shadowPrefer ? shadowNode : node}
            <div className="edit-switch-btn">{shadowPrefer ? '编辑后' : '编辑前'}</div>
        </div>
    );
};

const ShadowCard = props => {
    const {
        node,
        shadowNode,
        className
    } = props;

    // 编辑后的影子物料内容优先展示位置，默认是在上。
    const [shadowPrefer, toggle] = useToggle(true);

    const shawdowCardClassName = classNames(className, {
        'common-shadow-card': true
    });
    const baseProps = {
        node,
        shadowNode
    };
    return (
        <div className={shawdowCardClassName}>
            <div className='edit-card card-position-down' onClick={toggle}>
                <Card {...baseProps} shadowPrefer={!shadowPrefer} />
            </div>
            <div className='edit-card card-position-up'>
                <Card {...baseProps} shadowPrefer={shadowPrefer} />
            </div>
        </div>
    );
};

export default ShadowCard;
