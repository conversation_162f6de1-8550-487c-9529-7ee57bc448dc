.common-shadow-card {
    position: relative;
    height: 80px;
    min-width: 230px;
    .edit-card {
        position: absolute;
        background: #fff;
        cursor: pointer;
        padding: @dls-padding-unit @dls-padding-unit @dls-padding-unit*5;
        border: 1px solid @dls-color-gray-2;
        border-radius: @dls-border-radius-1;
    }
    .card-position {
        &-down {
            left: @dls-padding-unit*4;
            top: @dls-padding-unit*5;
        }
    }
    .edit-switch-btn {
        position: absolute;
        right: @dls-padding-unit;
        cursor: pointer;
    }
    .url-text {
        width: 200px;
        min-height: 30px;
    }
}
.shadow-card-creative-ontent {
    height: @dls-padding-unit * 45;
    min-width: @dls-padding-unit * 69;
    margin-right: @dls-padding-unit * 3;
}