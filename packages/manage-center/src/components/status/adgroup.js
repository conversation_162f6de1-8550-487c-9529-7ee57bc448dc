import Status from 'app/components/status';
import {
    statusTextMap, statusKeyMap, statusClassNames as statusMap
} from 'app/containers/adgroups/config/status';
import {actionTypes as ACTIONS} from 'app/reducers/adgroups';
import getAdgroup from 'commonLibs/apis/getAdgroup';
import idType from 'commonLibs/config/idType';
import {request, success} from 'app/containers/adgroups/utils/request';

export default ({record}) => {
    const {adgroupId} = record;
    const statusProps = {
        record, statusTextMap, statusMap, pauseStatus: statusKeyMap.PAUSE, request, success: success(),
        showReasonParams: getAdgroup({
            adgroupFields: ['offlineReasons'], ids: [adgroupId], idType: idType.UNIT_LEVEL
        }),
        ACTIONS, levelName: '单元'
    };
    return <Status {...statusProps} />;
};
