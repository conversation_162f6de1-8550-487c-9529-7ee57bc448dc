import Status from 'app/components/status';
import {
    statusTextMap, statusKeyMap, statusClassNames as statusMap
} from 'app/containers/adgroups/config/status';
import {actionTypes as ACTIONS} from 'app/reducers/creatives';
import {getCreative} from 'commonLibs/apis/getCreative';
import {IDEA_LEVEL} from 'commonLibs/config/idType';
import {request, success} from 'app/containers/creatives/utils/request';

export default props => {
    const {record} = props;
    const {creativeId} = record;
    const statusProps = {
        record, statusTextMap, statusMap, pauseStatus: statusKeyMap.PAUSE, request, success: success(),
        showReasonParams: getCreative({
            creativeFields: ['offlineReasons'], ids: [creativeId], idType: IDEA_LEVEL
        }),
        ACTIONS, levelName: '创意'
    };

    return <Status {...statusProps} />;
};
