import {useSelector} from 'react-redux';
import {IconPlayCircle, IconPauseCircle} from 'dls-icons-react';
import {StatusReason} from 'commonLibs/tableList/columns/status';
import {useParams} from 'commonLibs/route';
import {filterLastOfflineTime} from 'app/utils/getOfflineReason';

export default props => {
    const {
        record, statusTextMap, statusMap, pauseStatus, request, success,
        showReasonParams, ACTIONS, levelName, requestCallback, afterSave
    } = props;
    const {userId} = useParams();
    const {campaignId, pause: isPause, status} = record;
    const reOnlineReasons = useSelector(state => state?.__entities__?.campaignMap?.[campaignId]?.reOnlineReasons) || [];
    const accOfflineTime = useSelector(state => state?.accountSettings?.account?.lastOfflineTime);
    const statusProps = {
        // 为了处理后端接口isPause有时候为true、false，有时候为0、1
        label: statusTextMap[status],
        color: statusMap[status],
        Icon: status === pauseStatus ? IconPlayCircle : IconPauseCircle,
        request: (...args) => {
            if (requestCallback) {
                requestCallback(...args);
            }
            return request(record, {pause: !(isPause)})(...args);
        },
        success,
        afterSave,
        showReasonParams,
        commonReasonParams: {
            levelName, userId, campaignOfflineTime: filterLastOfflineTime(reOnlineReasons), accOfflineTime
        },
        ACTIONS
    };
    return <StatusReason {...statusProps} />;
};
