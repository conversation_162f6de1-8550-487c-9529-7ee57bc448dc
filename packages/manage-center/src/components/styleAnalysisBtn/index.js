import Tip from 'commonLibs/Tips';
import {Button} from '@baidu/one-ui';
import {useRouterRedirect} from 'commonLibs/route';
import toolsCenter from 'toolsCenter/routes';
import './style.less';

export const StyleAnalysisBtn = props => {
    const linkToAnalysis = useRouterRedirect({name: 'styleAnalyse', module: toolsCenter});
    return (
        <>
            <Button type="translucent" size="small" className="style-recognize" onClick={linkToAnalysis}>
                广告识别 <Tip keyName="styleAnalyse" className="style-recognize-tip" />
            </Button>
        </>
    );
};

export default StyleAnalysisBtn;
