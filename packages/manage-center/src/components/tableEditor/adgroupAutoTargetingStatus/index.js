/*
 * @file: adgroupAutoTargetingStatusEditor
 * @author: l<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2021-11-12 17:59:53
 */
import {useMemo} from 'react';
import {Form, Switch} from '@baidu/one-ui';
import FormItem from 'commonLibs/materialList/FormItem';
import SaveFooter from 'commonLibs/SaveFooter';
import Tip from 'commonLibs/Tips';
import {batchSaveAdgroupAutoTargetingStatus} from 'app/api/adgroup/batchUpdate';
import {useAdgroupBatchModParams} from 'app/containers/adgroups/hooks/useBatchProps';
import {useSuccessCallback} from 'app/containers/adgroups/hooks/useSuccessCallback';
import {PATH_ARRAY} from 'app/containers/adgroups/config/reducer';
import
BatchSwitchRadio,
{
    SelectStatusTextCom,
    getMatrialInfoAllValid,
    genNumFunc
} 
    from 'app/components/tableEditor/batchSwitchRadio';
import {createError, getErrorDisplayForForm} from 'commonLibs/utils/materialList/error';
import {useLog} from 'commonLibs/logger';
import './style.less';

const AdgroupAutoTargetingStatusEditor = props => {
    const {
        form,
        onCancel,
        className,
        selectedIds,
        dataMap,
        isCheckAll
    } = props;
    const [showMaterialStatusTipFlag, openLength] = useMemo(() => {
        const showMaterialStatusTipFlag = getMatrialInfoAllValid({
            selectedIds,
            isCheckAll,
            getInfoById: id => dataMap[id]
        });
        const openMaterialLength = genNumFunc({
            condition: ({adgroupAutoTargetingStatus}) => !!adgroupAutoTargetingStatus,
            getInfoById: id => dataMap[id],
            selectedIds
        });
        return [showMaterialStatusTipFlag, openMaterialLength];
    }, [selectedIds, dataMap, isCheckAll]);

    const makeBatchModParams = useAdgroupBatchModParams(PATH_ARRAY, 'adgroupMap');
    const [{onSuccessCallback}] = useSuccessCallback();
    const displayError = useMemo(() => getErrorDisplayForForm(form), [form]);
    const log = useLog();

    const onSave = async () => {
        log('stage', {target: '^save_batch_autoTargetingStatus'});
        log('stage', {target: '~save_batch_autoTargetingStatus'});
        const values = await form.validateFields();
        try {
            const params = makeBatchModParams({items: {adgroupAutoTargetingStatus: values.adgroupAutoTargetingStatus}});
            const data = await batchSaveAdgroupAutoTargetingStatus(params);
            onSuccessCallback(data);
        }
        catch (err) {
            const formatError = createError(err);
            formatError.materialName = '单元';
            formatError.optName = '修改自动定向';
            displayError(formatError);
        }
        finally {
            log('stage', {target: '$save_batch_autoTargetingStatus'});
        }
    };
    const selectStatusTextComProps = {
        levelName: '单元',
        optionName: '自动定向',
        openLength,
        selectedLength: selectedIds.length,
        showMaterialStatusTipFlag
    };
    const saveFooterProps = {
        onSave,
        onCancel,
        className: 'adgroup-editor-footer'
    };
    return (
        <div className="todo-edit-panel adgroup-autotarget-status-editor">
            <Form className="new-form" labelCol={{className: 'label-container'}}>
                <SelectStatusTextCom {...selectStatusTextComProps} />
                <FormItem
                    form={form}
                    name={
                        <span>自动定向<Tip keyName="adgroupAutoTargetingStatus" /></span>
                    }
                    field='adgroupAutoTargetingStatus'
                    initialValue={true}
                    colon={false}
                >
                    <BatchSwitchRadio />
                </FormItem>
            </Form>
            <SaveFooter {...saveFooterProps} />
        </div>
    );
};

function SwitchOneUI(props) {
    const {value, onChange} = props;
    const switchProps = {
        onChange,
        checked: value
    };
    return (
        <Switch {...switchProps} />
    );
}

export default AdgroupAutoTargetingStatusEditor;
