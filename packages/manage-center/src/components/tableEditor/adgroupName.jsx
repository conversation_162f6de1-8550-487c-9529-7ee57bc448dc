// @ts-check
import {memo} from 'react';
import {useParams} from 'commonLibs/route';
import {isFinite} from 'lodash-es';
import {Name} from 'commonLibs/tableList/columns';
import appendSearch from 'commonLibs/utils/appendSearch';
import AuditLink from 'app/components/auditLink';
import {viewTypeEnum} from 'commonLibs/config/viewType';
import {isNumbers} from 'app/utils/validateNumber';
import useViewType from 'app/hooks/useViewType';

/**
 * @typedef {object} AdgroupNameProps
 * @property {string} adgroupName 计划名称
 * @property {string} adgroupId 后端数据的层级信息
 * @property {string} campaignId
 * @property {string} marketingTargetId
 * @property {string} businessPointId
 * @property {string} linkUrl 跳转的url
 * @property {boolean} [isInternal] 是否外链
 * @property {boolean} [hasAuditLink] 是否有审核信息
 */

/** @type {React.VFC<AdgroupNameProps>} */
function AdgroupName({
    adgroupName,
    adgroupId,
    campaignId,
    marketingTargetId,
    businessPointId,
    linkUrl,
    isInternal = true,
    hasAuditLink // 是否需要审核单元功能
}) {
    // @ts-ignore
    const {adgroupId: adgroupLevelId} = useParams(); // TODO 路由参数在哪里加上需要再看一看
    const viewType = useViewType();
    const isBiz = viewType === viewTypeEnum.BIZ;
    const bizOrMtId = isBiz ? businessPointId : marketingTargetId;
    const noLink = isFinite(+adgroupLevelId) || !isNumbers([bizOrMtId, campaignId, adgroupId]);
    const nameProps = {
        name: adgroupName,
        isInternal,
        toUrl: noLink ? null : appendSearch(linkUrl),
        canEdit: false
    };
    const linkProps = {
        adgroupId,
        campaignId
    };
    return (<>
        <Name {...nameProps} />
        {hasAuditLink && <AuditLink {...linkProps} />}
    </>);
}

export default memo(AdgroupName);
