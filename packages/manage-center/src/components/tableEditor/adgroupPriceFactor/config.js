/*
 * @file: config
 * @author: <EMAIL>
 * @Date: 2021-10-20 20:01:04
 */
import {getValidator, toOneUIFormRule} from 'commonLibs/validators';
import {BID_PREFER} from 'commonLibs/config/bidPrefer';

export const PRICE_RATIO_TYPE = {
    PriceRatioTypeCampaign: -1, // 使用计划出价系数,
    PriceRatioTypeAdgroup: 0 // 使用单元出价系数
};

export const MIN_VALUE = 0;
export const MAX_VALUE = 10;


const adgroupDeviceValidator = getValidator([
    function (value, form) {
        if (value.priceType === PRICE_RATIO_TYPE.PriceRatioTypeAdgroup) {
            if (!value.customPrice) {
                return '出价系数不可为空';
            }
            if (+value.customPrice < MIN_VALUE || +value.customPrice > MAX_VALUE) {
                return `范围：${MIN_VALUE} ～ ${MAX_VALUE}`;
            };
        }
    }
]);
export const adgroupDeviceRules = toOneUIFormRule(adgroupDeviceValidator);

export function getCampaignFactor({bidPrefer, campaignPriceRatio, campaignPcPriceRatio}) {
    return bidPrefer === BID_PREFER.PC_PREFER ? campaignPriceRatio : campaignPcPriceRatio;
};

export function getRatioText({bidPrefer}) {
    return bidPrefer === BID_PREFER.PC_PREFER ? '移动' : '计算机';
};

export function getPriceType({ratio}) {
    return ratio === PRICE_RATIO_TYPE.PriceRatioTypeCampaign
        ? PRICE_RATIO_TYPE.PriceRatioTypeCampaign
        : PRICE_RATIO_TYPE.PriceRatioTypeAdgroup;
};

export function getDevicePriceType({bidPrefer, priceRatio, pcPriceRatio}) {
    return bidPrefer === BID_PREFER.PC_PREFER
        ? getPriceType({ratio: priceRatio})
        : getPriceType({ratio: pcPriceRatio});
};

export function getAdgroupPriceRatioText({
    bidPrefer,
    priceRatio,
    pcPriceRatio,
    campaignPriceRatio,
    campaignPcPriceRatio
}) {
    const mobileRatio = priceRatio !== PRICE_RATIO_TYPE.PriceRatioTypeCampaign
        ? `${(priceRatio || 0).toFixed(2)}`
        : `${(campaignPriceRatio || 0).toFixed(2)}`;
    const pcRatio = pcPriceRatio !== PRICE_RATIO_TYPE.PriceRatioTypeCampaign
        ? `${(pcPriceRatio || 0).toFixed(2)}`
        : `${(campaignPcPriceRatio || 0).toFixed(2)}`;
    return bidPrefer === BID_PREFER.PC_PREFER ? mobileRatio : pcRatio;
};
