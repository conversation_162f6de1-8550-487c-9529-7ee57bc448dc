/*
 * @file: adgroupPrice
 * @author: l<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2021-10-20 14:40:05
 */
import {values} from 'lodash-es';
import {Radio, NumberInput} from '@baidu/one-ui';
import {PRICE_RATIO_TYPE, MIN_VALUE, MAX_VALUE} from './config';
import './style.less';

const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;

export const PriceFactor = props => {
    const {onChange, value, factorTypeLabel, campaignFactor} = props;
    const {priceType, customPrice} = value;
    const adgroupBidFactorText = {
        [PRICE_RATIO_TYPE.PriceRatioTypeCampaign]: `使用计划${factorTypeLabel}出价系数：${campaignFactor}`,
        [PRICE_RATIO_TYPE.PriceRatioTypeAdgroup]: `使用并设置单元${factorTypeLabel}出价系数`
    };
    const onPriceTypeChange = e => {
        onChange({
            ...value,
            priceType: e.target.value
        });
    };
    const onBidFactorChange = e => {
        onChange({
            ...value,
            customPrice: e.target.value
        });
    };
    const customPriceProps = {
        value: customPrice,
        type: 'float',
        fixed: 2,
        step: 0.01,
        onChange: onBidFactorChange,
        min: MIN_VALUE,
        max: MAX_VALUE
    };
    return (
        <div className="manage-new-ws-adgroup">
            <RadioGroup value={priceType} onChange={onPriceTypeChange}>
                {values(PRICE_RATIO_TYPE).map(type => (
                    <RadioButton value={type} key={type}>
                        {adgroupBidFactorText[type]}
                    </RadioButton>
                ))}
            </RadioGroup>
            {
                priceType === PRICE_RATIO_TYPE.PriceRatioTypeAdgroup && (
                    <div className="manage-new-ws-adgroup-number-input">
                        <NumberInput {...customPriceProps} />
                        <div className="example-tip">
                            若某关键词出价为1.00元，在没有为该关键词设其它系数的情况下，该关键词在{factorTypeLabel}上的出价为{customPrice}元
                        </div>
                    </div>
                )
            }
        </div>
    );
};



