import {Radio} from '@baidu/one-ui';
import './style.less';

const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;

export function getMatrialInfoAllValid({selectedIds, getInfoById, isCheckAll}) {
    if (isCheckAll) {
        return false;
    }
    return selectedIds.every(id => !!getInfoById(id));
}

export function genNumFunc({condition, getInfoById, selectedIds}) {
    return selectedIds.filter(id => {
        return condition(getInfoById(id));
    }).length;
}

export const SelectStatusConfig = {
    NON: 0, // 跨页批量选择，就不展示状态话术
    ALL_OPEN: 1, // 展示【全部开启】
    PART_OPEN: 2, // 展示【部分开启】
    ALL_CLOSED: 3 // 展示【全部关闭】
};

const SelectStatusTextConfig = {
    [SelectStatusConfig.ALL_OPEN]: '全部开启',
    [SelectStatusConfig.PART_OPEN]: '部分开启',
    [SelectStatusConfig.ALL_CLOSED]: '全部关闭'
};
// showMaterialStatusTipFlag 表示是否需要展示已选物料的信息提示
export function getSelectedStatusText({levelName, optionName, openLength, selectedLength, showMaterialStatusTipFlag}) {
    if (!showMaterialStatusTipFlag) {
        return '';
    }
    let status;
    switch (openLength) {
        case selectedLength:
            status = SelectStatusConfig.ALL_OPEN;
            break;
        case 0:
            status = SelectStatusConfig.ALL_CLOSED;
            break;
        default:
            status = SelectStatusConfig.PART_OPEN;
            break;
    }
    return `已选${levelName}的${optionName}为${SelectStatusTextConfig[status]}状态`;
}

const radioButtons = [
    <RadioButton value key={1}>统一开启</RadioButton>,
    <RadioButton value={false} key={2}>统一关闭</RadioButton>
];

const BatchSwitchRadio = ({value, onChange}) => {
    return (
        <RadioGroup defaultValue value={value} onChange={onChange}>
            {radioButtons}
        </RadioGroup>
    );
};

export const SelectStatusTextCom = props => {
    const text = getSelectedStatusText(props);
    return (
        <>
            {text ? <span className="batch-selected-status-text">{text}</span> : null}
            {props.extraText ? <span className="batch-selected-status-text">{props.extraText}</span> : null}
        </>
    );
};
export default BatchSwitchRadio;
