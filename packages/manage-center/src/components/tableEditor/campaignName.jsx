// @ts-check
import {memo} from 'react';
import {useParams} from 'commonLibs/route';
import {isFinite} from 'lodash-es';
import {Name} from 'commonLibs/tableList/columns';
import appendSearch from 'commonLibs/utils/appendSearch';
import {viewTypeEnum} from 'commonLibs/config/viewType';
import {isNumbers} from 'app/utils/validateNumber';
import useViewType from 'app/hooks/useViewType';

/**
 * @typedef {object} CampaignNameProps
 * @property {string} campaignName 计划名称
 * @property {string} campaignId 后端数据的层级信息
 * @property {string} marketingTargetId
 * @property {string} businessPointId
 * @property {string} linkUrl 跳转的url
 * @property {boolean} [isInternal] 是否外链
 */

/** @type {React.VFC<CampaignNameProps>} */
function CampaignName({
    campaignName,
    campaignId,
    marketingTargetId,
    businessPointId,
    linkUrl,
    isInternal = true
}) {
    // @ts-ignore
    const {campaignId: campaignLevelId, adgroupId: adgroupLevelId} = useParams();
    const viewType = useViewType();
    const isBiz = viewType === viewTypeEnum.BIZ;
    const bizOrMtId = isBiz ? businessPointId : marketingTargetId;
    const noLink = (isFinite(+campaignLevelId) && !isFinite(+adgroupLevelId)) || !isNumbers([bizOrMtId, campaignId]);
    const nameProps = {
        name: campaignName,
        isInternal,
        toUrl: noLink ? null : appendSearch(linkUrl),
        canEdit: false
    };
    return <Name {...nameProps} />;
}

export default memo(CampaignName);
