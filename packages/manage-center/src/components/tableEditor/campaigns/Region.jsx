import {useSelector} from 'react-redux';
import {Popover} from '@baidu/one-ui';
import {getRegionShowText, getProvinceShowText} from 'commonLibs/utils/handleRegion';
import {getIsSupportThirdRegion} from 'commonLibs/region';

const Region = props => {
    const {record} = props;
    const {regionRestrictStatus, regionTarget, regionType, marketingTargetId} = record;
    const allowRegion = useSelector(state => state?.basicInfo?.accountInfo?.allowRegion);
    const isSupportThirdRegion = getIsSupportThirdRegion({marketingTargetId});
    const content = getRegionShowText({regionTarget, regionType, isSupportThirdRegion});
    const commonTipProps = {
        overlayClassName: 'limit-region-tip',
        placement: 'bottomLeft',
        content: `根据规范本地化行业推广行为要求, 您账户的允许推广地域为${getProvinceShowText(allowRegion)}，本计划地域设置违规，已处于离线中，请修改。`,
        trigger: 'click'
    };
    return regionRestrictStatus
        ? (
            <Popover {...commonTipProps}>
                {content}
            </Popover>
        )
        : content;
};

export default Region;