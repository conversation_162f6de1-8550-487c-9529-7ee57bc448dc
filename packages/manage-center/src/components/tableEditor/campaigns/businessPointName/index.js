/**
 @file 推广业务行内展示公共组件
 <AUTHOR>
 @date 2021-12-10 15:42:57
*/
// @ts-check
import {Popover} from '@baidu/one-ui';
import {brandBizPoint} from 'commonLibs/businessPoint';
import {IconGradeCircle, IconEdit} from 'dls-icons-react';
import './index.less';

/** @type {React.VFC<{record: import('app/types/materialData').CampaignInfo, onClick: (...args) => void}>} */
const BusinessPointName = ({record, onClick, isEditable = true}) => {
    const {businessPointName, businessPointId} = record;
    const tipProps = {
        placement: 'bottomLeft',
        content: (
            <div>
                <p>1.推广业务补充完成后可通过报告查看不同业务的整体数据、大盘数据和竞争数据，方便您分析投放效果，定位流量波动原因。</p>
                <p>2.设置推广业务的方式：计划列表-推广业务列-点击“去补充”。</p>
            </div>
        ),
        trigger: 'hover'
    };
    return (
        <div className="manage-center-business-point-container">
            <div>
                {+businessPointId === brandBizPoint && <IconGradeCircle className="brand-icon" />}
                {businessPointName ? businessPointName : <Popover {...tipProps}>去补充</Popover>}
            </div>
            {isEditable ? <IconEdit className="inline-operation-icon" onClick={onClick} /> : null}
        </div>
    );
};

export default BusinessPointName;
