/**
 @file 标签批量更新
 <AUTHOR>
 @date 2021-08-27 11:27:51
*/

import {forwardRef, useImperativeHandle, useCallback} from 'react';
import {Dialog} from '@baidu/one-ui';
import {displayErrorButStillThrow} from 'commonLibs/utils/materialList/error';
import useAllMarkers from 'app/components/tableEditor/newMarker/useAllMarkers';
import useSelectItem from 'app/components/tableEditor/newMarker/useSelectItem';
import EditTag from 'app/components/tableEditor/newMarker/EditTag/Index';
import {transfromMarkerToTab} from 'app/components/tableEditor/newMarker/util';

function BatchMaterielTag(props) {
    const {batchSaveMaterialTag, getSelectedInfo, wrappedComponentRef, type = 'keywords', refreshList,
        initialValue} = props;
    const {selectedItems, changeSelectedItem, selectItem} = useSelectItem(initialValue);
    const {markers,
        isLoading,
        refetchMarkers,
        updateMarkerMutate,
        deleteMarkersMutate
    } = useAllMarkers(type);
    const onSave = useCallback(async (items, markers) => {
        let data;
        try {
            data = await batchSaveMaterialTag({items, markers});
        }
        catch (error) {
            error.optName = '修改标签';
            displayErrorButStillThrow(error);
        }
        return data;
    }, [batchSaveMaterialTag]);
    useImperativeHandle(wrappedComponentRef, () => {
        return {
            async onSave() {
                const tabs = transfromMarkerToTab(selectedItems, markers);
                const items = {
                    tabs
                };
                let data;
                if (tabs.length > 0) {
                    data = await onSave(items, markers);
                }
                else {
                    const {totalCount, isCheckAll, selectedCount} = getSelectedInfo();
                    const realCount = isCheckAll ? totalCount : selectedCount;
                    data = await new Promise((resolve, reject) => {
                        Dialog.confirm({
                            title: '确认清除',
                            content: `是否确定清除所选的${realCount}个${type === 'keywords' ? '关键词' : '创意'}的标签`,
                            onOk: async () => {
                                try {
                                    const data = await onSave(items, markers);
                                    resolve(data);
                                }
                                catch (error) {
                                    reject(error);
                                }
                            },
                            onCancel: () => reject()
                        });
                    });
                }
                return data;
            }
        };
    });
    const editTagProps = {
        visible: true, // 与行内属性同步
        hasScrollBar: false,
        selectedItems, changeSelectedItem, selectItem,
        markers,
        isLoading,
        refetchMarkers,
        updateMarkerMutate,
        deleteMarkersMutate,
        refreshList
    };
    return (
        <div>
            <EditTag {...editTagProps} />
        </div>
    );
}

export default forwardRef(BatchMaterielTag);
