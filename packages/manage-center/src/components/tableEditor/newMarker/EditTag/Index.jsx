/**
 @file 通用的标签编辑组件
 <AUTHOR>
 @date 2021-08-04 14:09:44
*/
import {useState, useCallback, useEffect, memo} from 'react';
import classNames from 'classnames';
import {partial, findIndex} from 'lodash-es';
import {Loading, Button, Popover} from '@baidu/one-ui';
import {removeAt} from 'commonLibs/utils/array';
import {handleRequestErrorByToast} from 'app/containers/keywords/utils';
import TagList from './TagList';
import TagEditer from './TagEditer';
import {getMarkerLimitNumber, getNewTag} from '../util';
import './index.less';

/**
 * 关键词和创意列表都会使用的标签编辑
 * @param {Array} newMarker
 * @param {boolean} visible
 * @param {string} type
 */
function EditTag(props) {
    const {
        visible = false,
        hasScrollBar = true, // Dialog默认有scrollBar而Popover没有，根据容器选择属性
        selectedItems, changeSelectedItem, selectItem,
        markers,
        isLoading,
        refetchMarkers,
        updateMarkerMutate,
        deleteMarkersMutate,
        refreshList
    } = props;
    const [isCreating, setIsCreating] = useState(false);
    const [editItem, setEditItem] = useState(null);
    const markersLength = markers.length;
    const hasMarkers = !isLoading && markersLength > 0; // 是否有标签
    const limit = getMarkerLimitNumber(markers); // 计算最大标签数
    const canAddMarker = markersLength < limit;
    // 初始化页面父组件拿不到ref的钩子，内部来初始化
    useEffect(() => {
        // Popover很奇怪第一次visible为false是不挂载组件，为true一次就挂载组件了
        if (visible) {
            refetchMarkers();
        }
        else {
            setEditItem(null);
            setIsCreating(false);
        }
    }, [refetchMarkers, visible]);
    const cancelCreate = useCallback(() => {
        setEditItem(null);
        setIsCreating(false);
    }, []);
    const updateMarker = useCallback(async mergeMarkers => {
        try {
            await updateMarkerMutate(mergeMarkers);
            if (isCreating) { // 新建成功还要选中新建项
                const newMarkerBit = mergeMarkers[mergeMarkers.length - 1].bit;
                selectItem(newMarkerBit);
            }
            cancelCreate();
        }
        catch (error) {
            handleRequestErrorByToast(error);
        }

    }, [isCreating, selectItem, updateMarkerMutate, cancelCreate]);
    const deleteMarker = useCallback(async marker => {
        const bit = marker.bit;
        const index = findIndex(markers, item => item.bit === bit);
        const newMarker = removeAt(markers, index);
        try {
            await deleteMarkersMutate(newMarker, bit);
            refreshList();
        }
        catch (error) {
            handleRequestErrorByToast(error);
        }

    }, [markers, deleteMarkersMutate, refreshList]);
    // follow目前线上逻辑编辑中不能切换编辑
    function editOrAddItem(marker) {
        if (isCreating) {
            return;
        }
        if (editItem) {
            return;
        }
        if (marker) { // 编辑情况
            setEditItem(marker);
        }
        else { // 新建情况
            const newTag = getNewTag(markers);
            setEditItem(newTag);
            setIsCreating(true);
        }
    }
    return (
        <div className="edit-tag-container">
            <div className={classNames({'edit-tag-list-container': hasScrollBar})}>
                {
                    isLoading ? <Loading />
                        : (
                            <>
                                {/* 标签列表 */}
                                {
                                    hasMarkers ? <TagList
                                        markers={markers}
                                        editItem={editItem}
                                        setEditItem={setEditItem}
                                        isCreating={isCreating}
                                        editOrAddItem={editOrAddItem}
                                        selectedItems={selectedItems}
                                        changeSelectedItem={changeSelectedItem}
                                        updateMarker={updateMarker}
                                        deleteMarker={deleteMarker}
                                    />
                                        : (
                                            <div className="tag-item-none">
                                                <p>您可通过“标签”整理关键词或创意，点击下方“新建标签”。</p>
                                            </div>
                                        )
                                }
                            </>
                        )
                }
            </div>
            <div className="edit-tag-create-container">
                {/* 新建标签区域 */}
                {
                    isCreating ? (
                        <div className="materiel-create-tag-item">
                            <TagEditer
                                editItem={editItem}
                                setEditItem={setEditItem}
                                markers={markers}
                                isCreating={isCreating}
                                updateMarker={updateMarker}
                                cancelCreate={cancelCreate}
                            />
                        </div>
                    ) : null
                }
                {/* 新建标签按钮 */}
                {
                    !isCreating ? canAddMarker
                        ? (
                            <Button
                                type="text-strong"
                                size="small"
                                disabled={isLoading}
                                onClick={partial(editOrAddItem, null)}
                            >
                                新建标签
                            </Button>
                        )
                        : (
                            <Popover content="标签已添加上限，可删除其他标签后，再添加。">
                                <Button type="text-aux" size="small" disabled>
                                    新建标签
                                </Button>
                            </Popover>
                        ) : null
                }
            </div>
        </div>
    );
}

export default memo(EditTag);
