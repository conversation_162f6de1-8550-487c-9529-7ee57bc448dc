import {useState, useCallback} from 'react';
import {Input, Button} from '@baidu/one-ui';
import {validateTagName, hasRepeatName} from '../util';

function TagEditer(props) {
    const {editItem, markers, setEditItem, isCreating, updateMarker, cancelCreate} = props;
    const [nameErrorText, setNameErrorText] = useState('');
    const beforeChange = useCallback(() => {
        setNameErrorText('');
    }, [],);
    const inputChange = useCallback(({value}) => {
        const errorText = validateTagName(value);
        setNameErrorText(errorText);
        // follow线上逻辑，这里并没有对错误拦截
        setEditItem({
            ...editItem,
            name: value
        });
    }, [editItem, setEditItem]);
    const validateTagAndPrepareUpdate = useCallback(() => {
        // 先对数据做校验
        const errorText = validateTagName(editItem.name);
        if (errorText) {
            setNameErrorText(errorText);
            return;
        }
        if (hasRepeatName(markers, editItem, !isCreating)) {
            setNameErrorText('标签名称重复');
            return;
        }
        // 处理数据逻辑
        let mergeMarkers;
        if (isCreating) {
            mergeMarkers = [...markers, editItem];
        }
        else {
            mergeMarkers = markers.map(marker => {
                if (marker.bit === editItem.bit) {
                    return editItem;
                }
                return marker;
            });
        }
        return mergeMarkers;
    }, [editItem, markers, isCreating]);
    const validateTagAndUpdate = useCallback(() => {
        const mergeMarkers = validateTagAndPrepareUpdate();
        if (!mergeMarkers) { // 校验未通过
            return;
        };
        updateMarker(mergeMarkers);
    }, [validateTagAndPrepareUpdate, updateMarker]);
    const handleBlur = useCallback(() => {
        if (isCreating) {
            return;
        }
        validateTagAndUpdate();
    }, [isCreating, validateTagAndUpdate]);
    const inputProps = {
        size: 'small',
        placeholder: '请输入标签名称',
        value: editItem?.name || '',
        errorLocation: 'bottom',
        maxLen: 40,
        errorMessage: nameErrorText,
        onBlur: handleBlur,
        onPressEnter: validateTagAndUpdate,
        onFocus: beforeChange,
        onChange: inputChange,
        width: 173
    };
    return (
        <>
            <Input {...inputProps} />
            {/* 新建时需带上底部按钮 */}
            {isCreating && <div className="tag-editer-footer">
                <Button
                    type="text-strong"
                    size="small"
                    className="tag-editer-footer-confirm-btn"
                    onClick={validateTagAndUpdate}
                >
                    保存并选中
                </Button>
                <Button
                    type="text-aux"
                    size="small"
                    onClick={cancelCreate}
                >
                    取消
                </Button>
            </div>}
        </>);
}

export default TagEditer;
