import {Popover, Tooltip} from '@baidu/one-ui';
import {partial} from 'lodash-es';
import Tip from 'commonLibs/Tips';
import {IconEdit, IconTrash} from 'dls-icons-react';
import Tag from '../Tag';
import TagEditer from './TagEditer';
import {specialTagBit} from '../util';

const getParentNode = node => (node && node.parentNode);

function TagList(props) {
    const {
        markers,
        editItem,
        setEditItem,
        isCreating,
        updateMarker,
        deleteMarker,
        editOrAddItem,
        selectedItems,
        changeSelectedItem
    } = props;
    return (
        <div className="materiel-edit-tag">
            {
                markers.map(marker => {
                    return (
                        <div key={marker.name} className="materiel-edit-tag-item">
                            <Tag
                                color={marker.color}
                                className="materiel-edit-tag-icon"
                                selected={selectedItems[marker.bit]}
                                onClick={partial(changeSelectedItem, marker)}
                            />
                            {
                                editItem && editItem.bit === marker.bit ? <TagEditer
                                    editItem={editItem}
                                    setEditItem={setEditItem}
                                    markers={markers}
                                    isCreating={isCreating}
                                    updateMarker={updateMarker}
                                />
                                    : marker.bit === specialTagBit ? (
                                        <span
                                            className="tag-name-text"
                                            onClick={partial(changeSelectedItem, marker)}
                                        >
                                            {marker.name}
                                            <Tip keyName="keyPointKeyword" />
                                        </span>)
                                        : (
                                            <>
                                                <Tooltip
                                                    title={marker.name}
                                                    getPopupContainer={getParentNode}
                                                    overlayClassName="tag-name-tooltip"
                                                    type="dark"
                                                >
                                                    <span
                                                        className="tag-name-text"
                                                        onClick={partial(changeSelectedItem, marker)}
                                                    >
                                                        {marker.name}
                                                    </span>
                                                </Tooltip>
                                                <IconEdit
                                                    className="tag-icon tag-icon-edit"
                                                    onClick={partial(editOrAddItem, marker)}
                                                />
                                                <Popover
                                                    content={'您确认要删除标签？删除标签也会清除该标签与相关物料的绑定关系'}
                                                    placement="left"
                                                    trigger="hover"
                                                    overlayStyle={{width: 200}}
                                                    getPopupContainer={getParentNode}
                                                >
                                                    <IconTrash
                                                        className="tag-icon tag-icon-delete"
                                                        onClick={partial(deleteMarker, marker)}
                                                    />
                                                </Popover>
                                            </>
                                        )
                            }
                        </div>
                    );
                })
            }
        </div>
    );
}

export default TagList;
