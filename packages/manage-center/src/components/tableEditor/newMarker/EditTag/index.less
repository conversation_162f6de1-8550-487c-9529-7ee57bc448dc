.edit-tag-container {
    font-size: 12px;
    .tag-icon {
        display: none;
        cursor: pointer;
        font-size: 14px;
    }
    .materiel-edit-tag-item {
        display: flex;
        align-items: center;
        height: 32px;
        width: 100%;
        &:hover {
            background-color: #f7f7f7;
            .tag-icon {
                display: block;
            }
        }
    }
    .materiel-edit-tag-icon {
        margin-right: 10px;
        cursor: pointer;
        flex: 0 1 auto;
    }
    .tag-item-none {
        position: relative;
        margin: 0 auto;
        color: #333;
    }
    .tag-name-tooltip {
        max-width: 300px;
        word-wrap: break-word;
    }
    .tag-name-text {
        display: inline-block;
        // max-width: 150px;
        // width: 150px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
        padding-top: 2px;
        cursor: pointer;
        flex: 1;
    }
    .def-tag-color {
        color: #333;
    }
    .tag-icon-edit {
        margin-right: 4px;
    }
    .tag-icon-delete {
        margin-right: 12px;
        box-sizing: content-box;
        padding: 5px;
    }
}
.edit-tag-list-container {
    max-height: 240px;
    overflow: auto;
    // &::-webkit-scrollbar{
    //     width: 0;
    // }
}
.edit-tag-create-container {
    border-top: 1px solid #f3f4f8;
    padding-top: 8px;
    .tag-editer-footer {
        margin-top: 12px;
    }
    .tag-editer-footer-confirm-btn {
        margin-right: 12px;
    }
}
