/**
 @file 标签行内编辑
 <AUTHOR>
 @date 2021-08-27 11:28:11
*/

import {memo} from 'react';
import {Button} from '@baidu/one-ui';
import EditTag from 'app/components/tableEditor/newMarker/EditTag/Index';

interface Props {
    // 是否展示编辑浮层的提交和取消按钮
    hasOperationFooter?: boolean;
    onSubmit: () => Promise<any>;
    onCancel: () => void;
}

function MaterielTag(props: Props) {
    const {
        hasOperationFooter,
        onSubmit,
        onCancel,
        ...contentProps
    } = props;

    const saveButtonProps = {
        type: 'text-strong',
        size: 'small',
        onClick: onSubmit
    };

    const cancelButtonProps = {
        type: 'text',
        size: 'small',
        style: {marginLeft: '16px'},
        onClick: onCancel
    };

    return (
        <div className="materiel-list-row-tag">
            <EditTag {...contentProps} />
            {hasOperationFooter ? (
                <div style={{marginTop: '12px'}}>
                    <Button {...saveButtonProps}>确定</Button>
                    <Button {...cancelButtonProps}>取消</Button>
                </div>
            ) : null}
        </div>
    );
}

export default memo(MaterielTag);
