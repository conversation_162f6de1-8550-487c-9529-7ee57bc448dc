/**
 @file 标签行内编辑
 <AUTHOR>
 @date 2021-08-27 11:28:11
*/

import {useMemo, memo, useCallback} from 'react';
import {Popover} from '@baidu/one-ui';
import {useDerivedState} from 'huse';
import {useLog} from 'commonLibs/logger';
import useSelectItem from 'app/components/tableEditor/newMarker/useSelectItem';
import useAllMarkers from 'app/components/tableEditor/newMarker/useAllMarkers';
import EditTag from 'app/components/tableEditor/newMarker/InlineMaterielTag/Editor';
import TagsPopoverContent from 'app/components/tableEditor/newMarker/TagsPopoverContent';
import Tag from 'app/components/tableEditor/newMarker/Tag';
import {transfromMarkerToTab, defaultMarkers} from 'app/components/tableEditor/newMarker/util';
import {commonInlineRequestHandleErrorByToast} from 'app/containers/keywords/utils';
import './index.less';

interface Props {
    // 是否展示编辑浮层的提交和取消按钮
    hasOperationFooter?: boolean;
    // 已选的标签
    newMarker: unknown[];
    primaryId: unknown;
    onInlineMaterialSave: (data: unknown) => Promise<unknown>;
    type: 'keywords' | 'creatives';
    refreshList: () => void;
    visible?: boolean;
}

const tagClassNames = ['tag-front', 'tag-back'];
function MaterielTag(props: Props) {
    const {
        hasOperationFooter,
        newMarker = defaultMarkers,
        primaryId,
        onInlineMaterialSave: updateSelectedMarkersMutate,
        type = 'keywords',
        refreshList,
        visible: visible_
    } = props;
    const log = useLog();
    const {selectedItems, changeSelectedItem, selectItem, reset} = useSelectItem(newMarker);
    const {markers,
        isLoading,
        refetchMarkers,
        updateMarkerMutate,
        deleteMarkersMutate
    } = useAllMarkers(type);
    const [visible, setVisible] = useDerivedState(
        visible_,
        (propValue, stateValue) => {
            if (propValue != null) {
                return propValue;
            }
            return stateValue == null ? false : stateValue;
        }
    );
    const onTagsVisibleChange = useCallback(function (visible) {
        if (!visible) {
            const tabs = transfromMarkerToTab(selectedItems, markers);
            commonInlineRequestHandleErrorByToast(updateSelectedMarkersMutate, primaryId, {tabs, markers});
        }
        log('click', {target: 'tag_editor_visible_change', params: {visible}});
        setVisible(visible);
    }, [selectedItems, markers, updateSelectedMarkersMutate, primaryId, setVisible, log]);
    const onTagSubmit = useCallback(() => {
        log('click', {target: 'tag_editor_click_submit'});
        onTagsVisibleChange(false);
    }, [onTagsVisibleChange, log]);
    const onTagCancel = useCallback(() => {
        log('click', {target: 'tag_editor_click_cancel'});
        log('click', {target: 'tag_editor_visible_change', params: {visible: false}});
        reset();
        setVisible(false);
    }, [reset, setVisible, log]);
    const rowTagElement = useMemo(() => {
        const newMarkersLength = newMarker.length;
        if (newMarkersLength === 0) {
            return <Tag />;
        }
        return (
            <Popover
                content={<TagsPopoverContent newMarker={newMarker} />}
                placement="left"
                trigger="hover"
                overlayClassName="materiel-tag-inline-popover-content"
            >
                {
                    newMarkersLength === 1
                        ? <Tag color={newMarker[0]?.color} selected />
                        : (
                            <div>
                                {
                                    newMarker.slice(0, 2).reverse().map(({color}, index) =>
                                        <Tag key={index} color={color} className={tagClassNames[index]} selected />)
                                }
                                <span className="tag-num">{newMarker.length}</span>
                            </div>
                        )
                }
            </Popover>
        );
    }, [newMarker]);
    const contentProps = {
        visible,
        selectedItems, changeSelectedItem, selectItem,
        markers,
        isLoading,
        refetchMarkers,
        updateMarkerMutate,
        deleteMarkersMutate,
        refreshList,
        hasOperationFooter,
        onSubmit: onTagSubmit,
        onCancel: onTagCancel
    };
    return (
        <div className="materiel-list-row-tag">
            <Popover
                content={<EditTag {...contentProps} />}
                placement="right"
                trigger="click"
                onVisibleChange={onTagsVisibleChange}
                visible={visible}
                overlayClassName="materiel-tag-popover-content"
            >
                {rowTagElement}
            </Popover>
        </div>
    );
}

export default memo(MaterielTag);
