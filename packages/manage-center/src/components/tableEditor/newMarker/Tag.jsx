/**
 @file 包装一层标签图标给固定的大小,并进行数据容错
 <AUTHOR>
 @date 2021-08-02 16:10:45
*/

import {memo} from 'react';
import {IconTag, IconTagSolid} from 'dls-icons-react';

function Tag({color, fontSize = 18, selected = false, ...otherProps}) {
    return (<>
        {
            selected ? <IconTagSolid style={{color: color, fontSize: fontSize}} {...otherProps} />
                : <IconTag style={{color: color, fontSize: fontSize}} {...otherProps} />
        }
    </>);
}

export default memo(Tag);
