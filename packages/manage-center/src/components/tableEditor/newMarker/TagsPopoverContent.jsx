/**
 @file TableRow中的Tag在有数据条件下的浮层
 <AUTHOR>
 @date 2021-08-02 18:41:37
*/

import {memo} from 'react';
import Tag from './Tag';

function TagsPopoverContent({newMarker}) {
    return (
        <div>
            {
                newMarker.map(mark => (<p key={mark.bit} className="materiel-list-tag-hover-item">
                    <Tag color={mark.color} selected className="tag-icon" />
                    <span>{mark.name}</span>
                </p>))
            }
        </div>
    );
}

export default memo(TagsPopoverContent);
