/**
 @file 处理标签的query-hooks
 <AUTHOR>
 @date 2021-08-23 14:38:54
*/
import {useCallback, useMemo, useEffect} from 'react';
import {useRequestCallback} from '@huse/request';
import {useMethodsNative} from '@huse/methods';
import idType from 'commonLibs/config/idType';
import {getNewMarkersApi, updateMarkersApi, deleteMarkerApi} from 'app/containers/keywords/api/newMarker';

const mapReducer = {
    set(state, values) {
        return values;
    }
};

const defaultData = {}; // 防止在没拿到数据用户关闭窗口导致死循环

export default function useAllMarkers(type) {
    const [refetchMarkers, {data = defaultData, pending: isLoading}] = useRequestCallback(getNewMarkersApi);
    const [allMarkers, {set: setMarkers}] = useMethodsNative(mapReducer, data);
    const {keywordMarkers = [], creativeMarkers = []} = allMarkers;
    const isKeyWordTag = type === 'keywords';
    const markers = isKeyWordTag ? keywordMarkers : creativeMarkers; // 根据场景选择字段
    useEffect(() => {
        setMarkers(data);
    }, [data, setMarkers]);
    const getComplexTagParmas = useCallback(markers => {
        return isKeyWordTag ? {
            complexMarkers: {
                keywordMarkers: markers,
                creativeMarkers
            },
            materielLevel: idType.WORD_LEVEL
        } : {
            complexMarkers: {
                keywordMarkers,
                creativeMarkers: markers
            },
            materielLevel: idType.IDEA_LEVEL
        };
    }, [isKeyWordTag, keywordMarkers, creativeMarkers]);
    const commonHandleRequest = useCallback(mutateFuntion => async (...args) => {
        const data = await mutateFuntion(...args);
        setMarkers(data);
    }, [setMarkers]);
    const updateMarkerMutate = useMemo(() => commonHandleRequest(async mergeMarkers => {
        const {complexMarkers} = getComplexTagParmas(mergeMarkers);
        // 关键词和创意公用请求
        const data = await updateMarkersApi(complexMarkers);
        return data;
    }), [commonHandleRequest, getComplexTagParmas]);
    const deleteMarkersMutate = useMemo(() => commonHandleRequest(async (newMarker, bit) => {
        const {complexMarkers, materielLevel} = getComplexTagParmas(newMarker);
        // 关键词和创意公用请求
        const data = await deleteMarkerApi(complexMarkers, bit, materielLevel);
        return data;
    }), [commonHandleRequest, getComplexTagParmas]);
    return {
        markers,
        isLoading,
        refetchMarkers,
        updateMarkerMutate,
        deleteMarkersMutate
    };
}