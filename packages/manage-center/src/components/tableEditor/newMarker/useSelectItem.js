import {useCallback, useEffect} from 'react';
import {useBoolKeyValueMap} from 'commonLibs/hooks/collection/map';
import {defaultMarkers} from './util';

function getInitSelectMarkers(selectedMarkers) {
    return selectedMarkers.reduce((memo, marker) => {
        const id = marker.bit;
        memo[id] = true;
        return memo;
    }, {});
}

export default function useSelectItem(selectedMarkers = defaultMarkers) {
    const [selectedItems, {toggle, on, reset}] = useBoolKeyValueMap(() => {
        return getInitSelectMarkers(selectedMarkers);
    });
    const changeSelectedItem = useCallback(marker => {
        const {bit} = marker;
        toggle(bit);
    }, [toggle]);
    useEffect(() => {
        reset(getInitSelectMarkers(selectedMarkers));
    }, [selectedMarkers, reset]);
    const resetSelectedItems = useCallback(() => {
        reset(getInitSelectMarkers(selectedMarkers));
    }, [reset, selectedMarkers]);
    return {
        selectedItems,
        selectItem: on,
        changeSelectedItem,
        reset: resetSelectedItems
    };
}