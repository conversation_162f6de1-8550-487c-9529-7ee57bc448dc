import {getLengthInBytes} from 'commonLibs/utils/string';

export const specialTagBit = 1073741824;
export const specialTag = {
    color: '#9E51C9',
    name: '重点关键词',
    bit: specialTagBit
};

const colorPool = [
    '#2973FF', '#4CBDF2', '#008B8B', '#5AC581', '#A8B91E', '#FF9126', '#D54763', '#FF7FF0'
];
const ALL_BITS = 0b111111111111111111111111111111; // 30位的2进制数
const nameReg = new RegExp('^[A-Za-z0-9\u4e00-\u9fa5]+$');
export const defaultMarkers = [];

/**
 * 读取最低位的1
 * @param {number} bit
 */
function getLowBit(bit) {
    return bit & -bit;
}

/**
 * 动态计算标签的上限值
 * @param {Array} markers
 */
export function getMarkerLimitNumber(markers) {
    const hasSpecialTag = markers.some(item => item.bit === specialTagBit);
    if (hasSpecialTag) {
        return 31;
    }
    return 30;
}

/**
 * 随机生成一种标签颜色
 */
export function getRadomColor() {
    return colorPool[Math.floor(Math.random() * colorPool.length)];
};

/**
 * 根据当前已经使用的bit位计算出一个可使用的位
 * @param {Array} markers
 */
export function getLogicBit(markers) {
    const bits = markers.reduce((prev, cur) => {
        return prev | (cur.bit || 0);
    }, 0);
    const remainBits = ALL_BITS & ~bits; // 获得可用的bit位
    return getLowBit(remainBits);
}

/**
 * 根据当前的标签生成可以使用的标签
 * @param {Array} markers
 */
export function getNewTag(markers) {
    return {
        color: getRadomColor(),
        name: '',
        bit: getLogicBit(markers)
    };
}

/**
 * 获取当前2进制数的最高位
 * @param {number} number
 */
function getBitIndexByValue(number) {
    return parseInt(Math.log(number) / Math.log(2), 10) + 1;
}

/**
 * 根据2进制数位数获取值
 * @param {number} number
 */
function getBitValueByIndex(number) {
    return 2 ** (+number - 1);
}

/**
 * 对标签名称进行校验,onChange时校验
 * @param {string} name
 */
export function validateTagName(name) {
    let errorText = '';
    if (!name) {
        errorText = '标签名称不能为空';
    }
    else if (getLengthInBytes(name) > 40) {
        errorText = '标签名称不能超过40个字符';
    }
    else if (!nameReg.test(name)) {
        errorText = '标签名称不合法(只支持数字、中英文)';
    }
    return errorText;
}

/**
 * 对名称是否重复进行校验, save时校验
 * 编辑时可以和本身重复所以可以相同
 * @param {Array} markers
 * @param {Object} marker
 * @param {boolean} isEdit
 */
export function hasRepeatName(markers, marker, isEdit) {
    const tagNames = markers.map(marker => marker.name);
    const index = tagNames.indexOf(marker.name);
    const hasRepeat = index > -1;
    if (hasRepeat && isEdit) {
        if (markers[index].bit === marker.bit) {
            return false;
        }
        return true;
    }
    return false;
}

/**
 * 标签现有逻辑，保存时需要将二进制数转化位二进制位数
 * @param {Object<string, boolean>} selectedItems
 * @param {Array} markers
 */
export function transfromMarkerToTab(selectedItems, markers) {
    return markers.filter(marker => selectedItems[marker.bit])
        .map(marker => getBitIndexByValue(marker.bit));
}

/**
 * 将后端返回的tab转化成可以使用的newMarker数据
 * @param {Array<number>} tabs tab值数组，bit索引
 * @param {Array} markers 当前的所有marker
 */
export function transfromTabsToMarkers(tabs, markers) {
    // 首先将tab值映射成bit值
    const bitValues = tabs.map(index => getBitValueByIndex(index));
    // 生成当前marker的map
    const markersMap = markers.reduce((memo, marker) => {
        memo[marker.bit] = marker;
        return memo;
    }, {});
    // 将tabVal也就是bit映射成完整的marker数据
    const newMarker = bitValues.map(bit => markersMap[bit]).filter(marker => !!marker);
    return newMarker;
}
