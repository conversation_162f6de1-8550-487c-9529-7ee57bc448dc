/**
 @file 行内状态组件不使用redux
 <AUTHOR>
 @date 2021-08-23 16:48:46
*/

import {useCallback, useEffect, useMemo} from 'react';
import {useParams} from 'commonLibs/route';
import {Popover, Toast} from '@baidu/one-ui';
import {IconEllipsisCircle, IconEdit, IconPlayCircle, IconPauseCircle} from 'dls-icons-react';
import {useRequestCallback} from '@huse/request';
import {formatOfflineReason} from 'commonLibs/tableList/columns/status/Panel/util';
import {StatusPure, StatusPanel as Panel} from 'commonLibs/tableList/columns/status';
import {getErrorTextByCode} from 'commonLibs/utils/getErrorTextByCode';
import {useBoolean} from '@huse/boolean';
import {getIsOfflineMtid} from 'commonLibs/utils/materialList/status';
import {fetchCorejectReasons} from 'app/api/creatives/status';
import {useLog} from 'commonLibs/logger';
import './index.less';

function Status(props) {
    const {
        primaryId, // 主键
        record, // 物料数据
        onInlineMaterialSave: updateStatusMutate, // 外部注入启停方法
        isShowEditIcon = true, // 有特殊显示逻辑单独注入
        isShowAuditIcon = false, // 不同层级判断标准不同
        getOfflineReasonApi, // 获取原因所有物料接口都不相同单独注入
        localReasons = [], // 特殊的列表会注入特殊的原因，写死在前端配置
        offlineReasonParams = {}, // 不同的状态需要不同的数据来获取拒绝原因，外部构造
        statusConfig = {}, // 不同层级有不同的状态配置
        levelConfig, // 层级配置
        tipText = '',
        otherParams = {},
        mode
    } = props;
    const {
        status, segmentType
    } = record;
    const {statusTextMap, statusClassNames, statusKeyMap, statusTypes} = statusConfig;
    const {userId} = useParams();
    const [visible, {on, off}] = useBoolean(); // 不是真实可见，表明用户有想显示的意图
    const text = statusTextMap[status];
    const isOfflineMtid = getIsOfflineMtid(segmentType); // 下线物料的提示
    const log = useLog();
    const [getOfflineReasons,
        {
            data: offlineReasons = [],
            error: offlineReasonsError,
            pending: offlineReasonsLoading
        }
    ] = useRequestCallback(getOfflineReasonApi, primaryId);
    const [
        getCorejectReasons,
        {
            data: corejectReasons
        }
    ] = useRequestCallback(fetchCorejectReasons, levelConfig);
    const {
        commonReasons,
        ...statusPanelConmmonProps
    } = formatOfflineReason({
        offlineReasons,
        ...offlineReasonParams
    });
    const updateStatus = useCallback(async e => {
        e.stopPropagation();
        log('stage', {target: '^save_inline_status'});
        await updateStatusMutate(primaryId, {pause: status !== statusKeyMap.PAUSE, ...otherParams});
        log('stage', {target: '$save_inline_status'});
    }, [primaryId, status, updateStatusMutate, statusKeyMap, otherParams, log]);
    const Icon = status === statusKeyMap.PAUSE ? IconPlayCircle : IconPauseCircle;
    function injectOfflineReason(e) {
        // 确保点击其他状态能关闭当前行抽屉
        setTimeout(() => {
            on(); // 表明有想打开意图
            getOfflineReasons();
        }, 0);
    }
    // 处理错误
    useEffect(() => {
        if (offlineReasonsError) {
            off(); // 失败不能展示，比如第一次失败，需要修改显示的意图，第二次可以正常点击
            const code  = offlineReasonsError.errors?.[0]?.code;
            Toast.error({
                content: `${getErrorTextByCode(code)}`,
                duration: 3
            });
        }
    }, [offlineReasonsError, off]);
    const statusPureProps = {
        color: statusClassNames[status],
        statusType: statusTypes?.[status],
        label: text,
        onClick: injectOfflineReason,
        hasUnderscore: true,
        tipText,
        mode
    };
    const realCommonReason = useMemo(() => {
        return localReasons && localReasons.length > 0 ? [...commonReasons, ...localReasons] : commonReasons;
    }, [commonReasons, localReasons]);
    // 目前huse request不能表达用户意图，是否显示不光需要数据还需要用户是否有要打开的意图
    const realVisible = useMemo(() =>
        visible
        && !offlineReasonsLoading // 加载中不能展示
        && (offlineReasons.length > 0 || localReasons.length > 0 // 最终的原因存在才展示
        ),
    [visible, offlineReasons, localReasons, offlineReasonsLoading]);
    const panelProps = {
        visible: realVisible,
        userId,
        ...(levelConfig ? {
            ...levelConfig
        } : {}),
        onClose: () => {
            off();
        },
        commonReasons: realCommonReason,
        ...statusPanelConmmonProps,
        fetchCorejectReasons: getCorejectReasons,
        corejectReasons
    };
    return (
        <>
            <StatusPure {...statusPureProps}>
                <div className="keywords-status">
                    {isShowAuditIcon && <IconEllipsisCircle className="inline-operation-icon" />}
                    {
                        isShowEditIcon && (
                            isOfflineMtid
                                ? (
                                    <Popover
                                        placement="bottom"
                                        trigger="click"
                                        content="多视频类组件已下线，物料不可编辑且未来不会产生消费，建议删除处理"
                                    >
                                        <IconEdit className="inline-operation-icon" />
                                    </Popover>
                                ) : <Icon className="inline-operation-icon" onClick={updateStatus} />
                        )
                    }
                </div>
            </StatusPure>
            {/* 状态抽屉 */}
            <Panel {...panelProps} />
        </>
    );
}

export default Status;
