/*
 * @file: ulink(ios9.0系统往上使用的体验更好的应用调起方式)
 * @author: <EMAIL>
 * @Date: 2022-05-06
 */
import {Input} from '@baidu/one-ui';

const UlinkEditor = props => {
    const {value, onChange, inputWidth = 300} = props;
    const inputProps = {
        value,
        style: {
            width: inputWidth
        },
        placeholder: '请查看问号提示，按照规范输入',
        onChange: e => onChange(e.value)
    };
    return (
        <Input {...inputProps} />
    );
};

export default UlinkEditor;