/*
 * @file: inlineUlinkEditor
 * @author: <EMAIL>
 * @Date: 2022-05-06
 */
import FormItem from 'commonLibs/materialList/FormItem';
import Tip from 'commonLibs/Tips';
import ULinkEditor from './index';
import {validateUlinkUrlOptional} from './config';

const InlineUlinkEditor = (props, ref) => {
    const {form, field, record} = props;
    const {ulink} = record;

    const rules = validateUlinkUrlOptional.toOneUIFormRules();

    return (
        <FormItem
            form={form}
            field={field}
            name={(<span>Ulink<Tip keyName="ulink-description" /></span>)}
            initialValue={ulink || ''}
            rules={rules}
            colon={false}
        >
            <ULinkEditor />
        </FormItem>
    );
};

export default InlineUlinkEditor;
