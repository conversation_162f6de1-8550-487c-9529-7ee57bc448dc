/*
 * @file: BatchFinalUrlDetailEditor
 * @author: <EMAIL>
 * @Date: 2021/12/15
 */
import {forwardRef, useImperativeHandle, useMemo, useCallback} from 'react';
import {Alert} from '@baidu/light-ai-react';
import {Form, Checkbox} from '@baidu/one-ui';
import {get, isFunction} from 'lodash-es';
import {isLiveSubMarket} from 'commonLibs/config/marketTarget';
import {getErrorDisplayForForm} from 'commonLibs/utils/materialList/error';
import {
    urlTextMap,
    validateFinalUrlReuqired,
    validateFinalUrlOptional,
    validateTrackParam,
    validateTrackTemplate,
    urlFieldsName
} from 'app/components/tableEditor/url/config';
import {FinalUrlDetailEditor} from './finalUrlDetailEditor';
import {useShowFinalUrlDetail, urlDetailItemConfig} from './useFinalUrlDetail';
import './style.less';

export function URlItemShowCheckBox({urlShowDetailList, changeUrlFieldShow, children}) {
    const onChangeCheckBox = useCallback(checkList => {
        if (checkList.length > urlShowDetailList.length) {
            const addFields = checkList.filter(field => !urlShowDetailList.includes(field));
            changeUrlFieldShow([{
                field: addFields[0],
                value: true
            }]);
        }
        else {
            const deletedFields = urlShowDetailList.filter(field => !checkList.includes(field));
            changeUrlFieldShow([{
                field: deletedFields[0],
                value: false
            }]);
        }
    }, [changeUrlFieldShow, urlShowDetailList]);
    const checkboxGroupProps = {
        className: 'final-url-detail-check-box',
        value: urlShowDetailList,
        onChange: onChangeCheckBox
    };
    return (
        <div>
            <Checkbox.Group {...checkboxGroupProps}>
                {
                    Object.values(urlDetailItemConfig).map(({text, value}) => {
                        return <Checkbox.Button value={value} key={value}>{text}</Checkbox.Button>;
                    })
                }
            </Checkbox.Group>
            {children}
        </div>
    );
}

const BatchFinalUrlDetailEditor = (props, ref) => {
    const {form, field, batchSaveFinalUrl, finalUrlRequired = false,
        getSelectedInfo, getMaterialById
    } = props;
    const {validateFields} = form;
    const displayError = useMemo(() => getErrorDisplayForForm(form), [form]);

    const onSave = async () => {
        let data;
        const values = await validateFields();
        delete values.urlPerview;
        try {
            data = await batchSaveFinalUrl({
                items: values
            });
        }
        catch (err) {
            err.optName = `修改${urlTextMap[field]}`;
            displayError(err);
            throw err;
        }
        return data;
    };

    useImperativeHandle(ref, () => ({onSave}));

    const validators = useMemo(() => getValidators(field, finalUrlRequired), [field, finalUrlRequired]);

    const [
        {
            urlShowDetailList,
            isShowUrlPreview
        },
        {
            changeUrlFieldShow
        }
    ] = useShowFinalUrlDetail({
        field,
        form
    });

    const checkBoxProps = {
        urlShowDetailList,
        changeUrlFieldShow
    };

    const editorProps = {
        validators,
        materiaData: props.initialValue,
        finalUrlDetailShowFlag: {
            isShowFinalUrl: urlShowDetailList.includes(urlDetailItemConfig.finalUrl.value),
            isShowTrackParam: urlShowDetailList.includes(urlDetailItemConfig.trackParam.value),
            isShowTrackTemplate: urlShowDetailList.includes(urlDetailItemConfig.trackTemplate.value),
            isShowUrlPreview,
            changeUrlFieldShow
        },
        ...props
    };

    const tip = useMemo(
        () => {
            let hasLiveMaterial = false;
            if (urlFieldsName.includes(field) && isFunction(getSelectedInfo) && isFunction(getMaterialById)) {
                const selectedInfo = getSelectedInfo();
                hasLiveMaterial = (selectedInfo?.selectedIds ?? []).some(
                    id => isLiveSubMarket(get(getMaterialById(id), ['subMarketingTargetId']))
                );
            }
            return hasLiveMaterial ? '直播场景不支持修改计算机最终访问网址、移动最终访问网址' : '';
        },
        [field, getSelectedInfo, getMaterialById]
    );

    return (
        <>
            <URlItemShowCheckBox {...checkBoxProps}>
                <FinalUrlDetailEditor {...editorProps} />
            </URlItemShowCheckBox>
            {tip ? <Alert style={{marginTop: 12}}>{tip}</Alert> : null}
        </>
    );
};

// 拆分跟组件state无关的静态实现，让组件实现本身更专注于 UI 逻辑
// 不需要复用的直接写在这里就行了，不需要特地拆分文件

const fieldToValidatorMap = {
    pcFinalUrl: urlReuqired => (urlReuqired ? validateFinalUrlReuqired : validateFinalUrlOptional),
    pcTrackParam: () => validateTrackParam,
    pcTrackTemplate: () => validateTrackTemplate,
    mobileFinalUrl: urlReuqired => (urlReuqired ? validateFinalUrlReuqired : validateFinalUrlOptional),
    mobileTrackParam: () => validateTrackParam,
    mobileTrackTemplate: () => validateTrackTemplate
};
export function getValidators(field, urlReuqired) {
    const fields = getAllFields(field);
    return fields.reduce((validators, field) => {
        validators[field] = fieldToValidatorMap[field](urlReuqired);
        return validators;
    }, {});
}
export function getAllFields(field) {
    return {
        pcFinalUrl: [field, 'pcTrackParam', 'pcTrackTemplate'],
        mobileFinalUrl: [field, 'mobileTrackParam', 'mobileTrackTemplate']
    }[field];
}

export default Form.create({name: 'BatchFinalUrlDetailEditor'})(forwardRef(BatchFinalUrlDetailEditor));
