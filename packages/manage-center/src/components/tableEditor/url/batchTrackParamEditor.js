/*
 * @file: batchTrackParamEditor
 * @author: <EMAIL>
 * @Date: 2021/12/16
 */
import {forwardRef, useImperativeHandle, useMemo} from 'react';
import {Form} from '@baidu/one-ui';
import {getErrorDisplayForForm} from 'commonLibs/utils/materialList/error';
import Tip from 'commonLibs/Tips';
import {
    urlTextMap,
    initialValue,
    validateTrackParam
} from 'app/components/tableEditor/url/config';
import FormItem from 'commonLibs/materialList/FormItem';
import TrackParamEditor from './trackParam';
import './style.less';

const BatchTrackParamEditor = (props, ref) => {
    const {form, field, batchSaveTrackParam} = props;
    const {validateFields} = form;

    const displayError = useMemo(() => getErrorDisplayForForm(form), [form]);

    const onSave = async () => {
        let data;
        const values = await validateFields();
        try {
            data = await batchSaveTrackParam({
                items: values
            });
        }
        catch (err) {
            err.optName = `修改${urlTextMap[field]}`;
            displayError(err);
            throw err;
        }
        return data;
    };

    useImperativeHandle(ref, () => ({onSave}));

    return (
        <Form labelAlign="right">
            <FormItem
                form={form}
                field={field}
                name={
                    <span>监控后缀<Tip keyName="trackParam" /></span>
                }
                initialValue={initialValue}
                rules={validateTrackParam.toOneUIFormRules()}
                required={validateTrackParam.isRequired}
            >
                <TrackParamEditor form={form} field={field} />
            </FormItem>
        </Form>
    );
};

export default Form.create({name: 'BatchTrackParamEditor'})(forwardRef(BatchTrackParamEditor));