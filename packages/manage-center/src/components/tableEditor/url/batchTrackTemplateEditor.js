/*
 * @file: batchTrackTemplateEditor
 * @author: <EMAIL>
 * @Date: 2021/12/16
 */
import {forwardRef, useImperativeHandle, useMemo} from 'react';
import {Form} from '@baidu/one-ui';
import {getErrorDisplayForForm} from 'commonLibs/utils/materialList/error';
import Tip from 'commonLibs/Tips';
import {
    urlTextMap,
    initialValue,
    validateTrackTemplate
} from 'app/components/tableEditor/url/config';
import FormItem from 'commonLibs/materialList/FormItem';
import TrackParamEditor from './trackParam';
import './style.less';

const BatchTrackTemplateEditor = (props, ref) => {
    const {form, field, batchSaveTrackTemplate} = props;
    const {validateFields} = form;

    const displayError = useMemo(() => getErrorDisplayForForm(form), [form]);

    const onSave = async () => {
        let data;
        const values = await validateFields();
        try {
            data = await batchSaveTrackTemplate({
                items: values
            });
        }
        catch (err) {
            err.optName = `修改${urlTextMap[field]}`;
            displayError(err);
            throw err;
        }
        return data;
    };

    useImperativeHandle(ref, () => ({onSave}));

    return (
        <Form labelAlign="right">
            <FormItem
                form={form}
                field={field}
                name={
                    <span>追踪模板<Tip keyName="trackTemplate" /></span>
                }
                initialValue={initialValue}
                rules={validateTrackTemplate.toOneUIFormRules()}
                required={validateTrackTemplate.isRequired}
            >
                <TrackParamEditor form={form} field={field} />
            </FormItem>
        </Form>
    );
};

export default Form.create({name: 'BatchTrackTemplateEditor'})(forwardRef(BatchTrackTemplateEditor));