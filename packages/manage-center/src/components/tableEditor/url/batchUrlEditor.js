/*
 * @file: BatchUrlEditor
 * @author: lian<PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2021-08-09 19:39:49
 */
import {forwardRef, useImperativeHandle, useMemo} from 'react';
import {Form} from '@baidu/one-ui';
import {get, isFunction} from 'lodash-es';
import {Alert} from '@baidu/light-ai-react';
import {isLiveSubMarket} from 'commonLibs/config/marketTarget';
import {getErrorDisplayForForm} from 'commonLibs/utils/materialList/error';
import FormItem from 'commonLibs/materialList/FormItem';
import UrlEditor from 'app/components/tableEditor/url';
import {initialValue, urlTextMap, validateDestinationUrl, urlFieldsName} from 'app/components/tableEditor/url/config';
import './style.less';

const BatchUrlEditor = (props, ref) => {
    const {form, field, batchSaveDestinationUrl, getSelectedInfo, getMaterialById} = props;
    const {validateFields} = form;
    const displayError = useMemo(() => getErrorDisplayForForm(form), [form]);

    const onSave = async () => {
        let data;
        const values = await validateFields();
        try {
            data = await batchSaveDestinationUrl({
                items: {
                    [field]: values[field]
                }
            });
        }
        catch (err) {
            err.optName = `修改${urlTextMap[field]}`;
            displayError(err);
            throw err;
        }
        return data;
    };

    useImperativeHandle(ref, () => ({onSave}));

    const tip = useMemo(
        () => {
            let hasLiveMaterial = false;
            if (urlFieldsName.includes(field) && isFunction(getSelectedInfo) && isFunction(getMaterialById)) {
                const selectedInfo = getSelectedInfo();
                hasLiveMaterial = (selectedInfo?.selectedIds ?? []).some(
                    id => isLiveSubMarket(get(getMaterialById(id), ['subMarketingTargetId']))
                );
            }
            return hasLiveMaterial ? '直播场景不支持修改计算机最终访问网址、移动最终访问网址' : '';
        },
        [field, getSelectedInfo, getMaterialById]
    );

    return (
        <>
            <Form labelAlign="right">
                <FormItem
                    form={form}
                    field={field}
                    name={urlTextMap[field]}
                    initialValue={initialValue}
                    rules={validateDestinationUrl.toOneUIFormRules()}
                    required={validateDestinationUrl.isRequired}
                >
                    <UrlEditor form={form} field={field} hasSelect={false} />
                </FormItem>
            </Form>
            {tip ? <Alert style={{marginTop: 12}}>{tip}</Alert> : null}
        </>
    );
};

export default Form.create({name: 'batchUrlEditor'})(forwardRef(BatchUrlEditor));
