/*
 * @file: config
 * @author: l<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2021-08-09 19:46:22
 */
import {includes} from 'lodash-es';
import {getValidator} from 'commonLibs/validators';
import {isUrlTemplateUser, isCannotUseAgentUrlRegUser} from 'commonLibs/utils/getFlag';
import {
    getLengthInBytes,
    matchValueFromContent,
    convertWordRangesToElements
} from 'commonLibs/utils/string';
import {shopTypeConfig} from 'commonLibs/config/shop';
import {getPcPageUser} from 'commonLibs/components/jimuyuSelect/util';
import MARKET_TARGET from 'commonLibs/config/marketTarget';

export const TEMPLATE_URL_MAP = {
    common: '{lpurl}',
    encode: '{lpurl_encode}'
};
export const AGENT_URL_REG = new RegExp('https://ada.baidu.com/site/wjz[a-zA-Z0-9]*/agent\\?.*$');

export const SECURE_PROTOCOL_URL_REG = /^\bhttps:\/\//;
export const UNSECURE_PROTOCOL_URL_REG = /^\bhttp:\/\//;

export const TEMPLATE_REPLACE_URL_REG = new RegExp(`(${TEMPLATE_URL_MAP.common}){1}|(${TEMPLATE_URL_MAP.encode}){1}`);
export const TEMPLATE_REPLACE_ALL_URL_REG = new RegExp(`
    (${TEMPLATE_URL_MAP.common})+.*(${TEMPLATE_URL_MAP.encode})+
    |(${TEMPLATE_URL_MAP.encode}).*(${TEMPLATE_URL_MAP.common})+
`);

export const urlTextMap = {
    mobileDestinationUrl: '移动访问网址',
    pcDestinationUrl: '计算机访问网址 ',
    pcFinalUrl: '计算机最终访问网址',
    mobileFinalUrl: '移动最终访问网址',
    pcTrackParam: '计算机最终访问网址监控后缀',
    pcTrackTemplate: '计算机最终访问网址追踪模板',
    mobileTrackParam: '移动最终访问网址监控后缀',
    mobileTrackTemplate: '移动最终访问网址追踪模板',
    newMobileFinalUrl: '移动访问网址',
    newPcFinalUrl: '计算机访问网址'
};


export const urlTypeTextMap = {
    adgroupUrlType: '广告链接类型',
    keyWordUrlType: '广告链接类型',
    creativeUrlType: '广告链接类型'
};

export const urlFieldsName = [
    ...Object.keys({...urlTextMap, ...urlTypeTextMap}), 'miniProgramUrl', 'deeplink', 'ulink'
];

export const displayUrlTextMap = {
    mobileDisplayUrl: '移动显示网址',
    pcDisplayUrl: '计算机显示网址'
};

export const destinatioToDiplayField = {
    mobileDestinationUrl: 'mobileDisplayUrl',
    pcDestinationUrl: 'pcDisplayUrl'
};

export const urlDataToShadowField = {
    mobileDestinationUrl: 'shadowMobileDestinationUrl',
    pcDestinationUrl: 'shadowPcDestinationUrl',
    pcFinalUrl: 'shadowPcFinalUrl',
    mobileFinalUrl: 'shadowMobileFinalUrl',
    pcTrackParam: 'shadowPcTrackParam',
    mobileTrackParam: 'shadowMobileTrackParam',
    pcTrackTemplate: 'shadowPcTrackTemplate',
    mobileTrackTemplate: 'shadowMobileTrackTemplate',
    newMobileFinalUrl: 'newShadowMobileFinalUrl',
    newPcFinalUrl: 'newShadowPcFinalUrl'
};



export const DESTINATION_URL_MAX_LENGTH_IN_BYTES = 1024;
export const DISPLAY_URL_MAX_LENGTH_IN_BYTES = 36;
export const MINIPROGRAM_URL_MAX_LENGTH_IN_BYTES = 1024;
export const AGENT_PARAM_LIMIT = 1024;

export const defaultUrlInputPlaceholder = `0-${DESTINATION_URL_MAX_LENGTH_IN_BYTES}个字符`;
export const initialValue = '';

const getAgentUrlRegValidator = () => {
    if (isCannotUseAgentUrlRegUser()) {
        return [
            ['not:match', AGENT_URL_REG, '落地页链接不支持填写商家智能体，您可通过将当前单元的「广告链接类型」修改为商家智能体进行智能体投放']
        ];
    }
    return [];
};

// 批量编辑监控后缀、追踪模板校验
export const validateFinalUrlReuqired = getValidator([
    ['required', '请输入最终访问网址'],
    ['isUrl'],
    ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]]
]);

export const validateFinalUrlOptional = getValidator([
    ['optional'],
    ['isUrl'],
    ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]]
]);

export const validateTrackParam = getValidator([
    ['optional'],
    ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]]
]);

export const validateTrackTemplate = getValidator([
    ['optional'],
    ['isUrl'],
    ['match', TEMPLATE_REPLACE_URL_REG, `必须包含一个${TEMPLATE_URL_MAP.encode}`],
    ['not:match', TEMPLATE_REPLACE_ALL_URL_REG, `追踪模板中必须有且仅有一个${TEMPLATE_URL_MAP.encode}通配符`],
    ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]]
]);

export const validateDestinationUrl = getValidator([
    ['legacyIsURL'],
    ...getAgentUrlRegValidator(),
    ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]]
]);
export const validateRequiredDestinationUrl = getValidator([
    ['required', '请输入推广网址'],
    ['legacyIsURL'],
    ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]]
]);
export const validateRequiredDisplayUrl = getValidator([
    ['required', '请输入显示网址'],
    ['legacyIsURL'],
    ['textRange', [0, DISPLAY_URL_MAX_LENGTH_IN_BYTES]]
]);

export function getUnitPreviewUrl({finalUrl, trackParam, trackTemplate}) {
    let previewUrl = '';
    const finalUrlAndTrackParam = trackParam ? finalUrl + trackParam : finalUrl;
    if (trackTemplate) {
        const commonRanges = matchValueFromContent(trackTemplate, TEMPLATE_URL_MAP.common);
        const commonElements = convertWordRangesToElements(trackTemplate, commonRanges);
        previewUrl = commonElements.map(([text, matched]) => {
            return matched ? finalUrlAndTrackParam : text;
        }).join('');
        const encodeRanges = matchValueFromContent(previewUrl, TEMPLATE_URL_MAP.encode);
        const encodeElements = convertWordRangesToElements(previewUrl, encodeRanges);
        previewUrl = encodeElements.map(([text, matched]) => {
            return matched ? encodeURIComponent(finalUrlAndTrackParam) : text;
        }).join('');
    }
    else {
        previewUrl = finalUrlAndTrackParam;
    }
    return previewUrl;
}
function validatorFinalUrlDetailFromRecord(field, record) {
    return function (value, form) {
        const {getFieldError, setFields, getFieldValue, validateFields, getFieldsValue} = form;
        const [finalUrlField, trackParamField, trackTemplateField] = field.includes('pc')
            ? ['pcFinalUrl', 'pcTrackParam', 'pcTrackTemplate']
            : ['mobileFinalUrl', 'mobileTrackParam', 'mobileTrackTemplate'];
        let errorMessage = '';
        const {
            [finalUrlField]: recordFinalUrl,
            [trackParamField]: recordTrackParam,
            [trackTemplateField]: recordTrackTemplate
        } = record || {};
        const {
            [finalUrlField]: formFinalUrl,
            [trackParamField]: formTrackParam,
            [trackTemplateField]: formTrackTempalte
        } = getFieldsValue([finalUrlField, trackParamField, trackTemplateField]);
        let finalUrlDetailObj;
        // 没有最终访问网址说明是监控后缀或者追踪模板单独编辑
        if (formFinalUrl === undefined) {
            // 除了正在编辑的编辑项，其他使用创意数据
            finalUrlDetailObj = {
                finalUrl: recordFinalUrl,
                trackParam: field === trackParamField ? formTrackParam : recordTrackParam,
                trackTemplate: field === trackTemplateField ? formTrackTempalte : recordTrackTemplate
            };
        }
        else {
            finalUrlDetailObj = {
                finalUrl: formFinalUrl,
                trackParam: formTrackParam === undefined ? recordTrackParam : formTrackParam,
                trackTemplate: formTrackTempalte === undefined ? recordTrackTemplate : formTrackTempalte
            };
        }
        const unitUrl = getUnitPreviewUrl(finalUrlDetailObj);
        if (unitUrl && getLengthInBytes(unitUrl) > DESTINATION_URL_MAX_LENGTH_IN_BYTES) {
            errorMessage = `最终访问网址、监控后缀${isUrlTemplateUser()
                ? '和追踪模板' : ''}总和最长不超过${MINIPROGRAM_URL_MAX_LENGTH_IN_BYTES}`;
        }
        [finalUrlField, trackParamField, trackTemplateField].forEach(item => {
            const fieldError = getFieldError(item) || [];
            const fieldValue = getFieldValue(item);
            // 防止表单项之间的重复触发校验
            // 针对三个表单项加起来长度不超过1024，改变其中任何一个表单项，都会触发校验
            if (!fieldError.length && fieldValue && errorMessage) {
                setFields({
                    [item]: {
                        value: fieldValue,
                        errors: [{
                            message: errorMessage}]
                    }
                });
            }
            else if (fieldError.length && fieldValue && !errorMessage) {
                validateFields([item]);
            }
        });
        return errorMessage;
    };
}

function validatorFinalUrlDetail(field, record, onFinalUrlFiledChange) {
    return function (value) {
        if (typeof onFinalUrlFiledChange === 'function') {
            onFinalUrlFiledChange({
                field,
                value
            });
        }
        return '';
    };
}

// 最终访问网址校验
export const getInlineFinalUrlValidator = (form, field, record, onFinalUrlFiledChange) => getValidator([
    validatorFinalUrlDetail(field, record, onFinalUrlFiledChange),
    ['optional'],
    ['isUrl'],
    ...getAgentUrlRegValidator(),
    ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]],
    validatorFinalUrlDetailFromRecord(field, record)
], form);

export const getInlineRequiredFinalUrlValidator = (form, field, record, onFinalUrlFiledChange) => getValidator([
    validatorFinalUrlDetail(field, record, onFinalUrlFiledChange),
    ['required', '请输入最终访问网址'],
    ['isUrl'],
    ...getAgentUrlRegValidator(),
    ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]],
    validatorFinalUrlDetailFromRecord(field, record)
], form);

export const getInlineTrackParamsValidator = (form, field, record) => getValidator([
    ['optional'],
    ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]],
    validatorFinalUrlDetailFromRecord(field, record),
    value => {
        if (value && value.trim() === '') {
            return '请设置监控后缀后再提交';
        }
        return '';
    }
], form);

export const getInlineTrackTemplateValidator = (form, field, record) => getValidator([
    ['optional'],
    ['isUrl'],
    ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]],
    ['match', TEMPLATE_REPLACE_URL_REG, `追踪模板中必须有且仅有一个${TEMPLATE_URL_MAP.encode}通配符`],
    ['not:match', TEMPLATE_REPLACE_ALL_URL_REG, `追踪模板中必须有且仅有一个${TEMPLATE_URL_MAP.encode}通配符`],
    validatorFinalUrlDetailFromRecord(field, record)
], form);

export function getUrlIsOnlyShowMobile({marketingTargetId, shopType}) {
    return marketingTargetId === MARKET_TARGET.APP
        || marketingTargetId === MARKET_TARGET.STORE
        || (marketingTargetId === MARKET_TARGET.SHOP && shopType === shopTypeConfig.DU_XIAO_DIAN);
}

export function getValidatorJimuyuSelectFlag({marketingTargetId, field}) {
    // 选择落地页功能小流量,移动的全流量了，pc的有小流量
    // 选择落地页功能,移动的仅支持营销目标为网站链接和app下载和SHOP，pc的仅支持网站链接
    const mobilehasSelectTarget = [MARKET_TARGET.WEB, MARKET_TARGET.APP, MARKET_TARGET.SHOP];
    const mobilehasSelect = includes(mobilehasSelectTarget, marketingTargetId);
    const pchasSelect = marketingTargetId === MARKET_TARGET.WEB && getPcPageUser();
    return field.includes('mobile') ? mobilehasSelect : pchasSelect;
}
