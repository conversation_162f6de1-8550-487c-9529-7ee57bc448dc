/*
 * @file: displayUrl
 * @author: l<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2021-09-13 11:53:34
 */
import {Input} from '@baidu/one-ui';
import {DISPLAY_URL_MAX_LENGTH_IN_BYTES} from './config';

const DisplayUrlEditor = props => {
    const {
        placeholder = `0-${DISPLAY_URL_MAX_LENGTH_IN_BYTES}个字符`,
        value,
        onChange,
        inputWidth = 420
    } = props;

    const inputProps = {
        value,
        onChange: e => onChange(e.value),
        errorLocation: 'bottom',
        placeholder,
        style: {
            width: inputWidth
        }
    };

    return (
        <div className="keywords-url-editor">
            <Input {...inputProps} />
        </div>
    );
};

export default DisplayUrlEditor;
