/*
 * @file: finalurl最终访问网址
 * @author: <EMAIL>
 * @Date: 2021/12/13
 */
import {useCallback, useMemo} from 'react';
import {Input, Toast} from '@baidu/one-ui';
import DropdownUrlSelect from 'commonLibs/components/dropdownUrlSelect';
import {useUserInfo} from 'commonLibs/context/sharedInfo';
import {isUrlSplitUser} from 'commonLibs/utils/getFlag';
import {shopTypeConfig} from 'commonLibs/config/shop';
import {pageTypeConfig} from 'commonLibs/components/dropdownUrlSelect/config';
import {useUrlVerification} from 'commonLibs/hooks/url';
import {getSingleErrorMessage} from 'commonLibs/utils/getErrorTextByCode';
import {noop} from 'lodash-es';
import {useElementSize} from '@huse/element-size';
import {useAiShopping} from 'commonLibs/components/dropdownUrlSelect/utils';
import SplitUrl from './splitUrl';
import {defaultUrlInputPlaceholder, DESTINATION_URL_MAX_LENGTH_IN_BYTES} from './config';
import './style.less';

export const BASE_INPUT_WIDTH = 420;
export const JIMUYU_SELECT_WIDTH = 118;

const FinalUrlEditor = props => {
    const {
        form,
        field: finaUrlField,
        hasSelect = false,
        marketingTargetId,
        adgroupIds = [],
        showType,
        shopType,
        placeholder = defaultUrlInputPlaceholder,
        maxLen = DESTINATION_URL_MAX_LENGTH_IN_BYTES,
        value,
        onChange,
        inputWidth = BASE_INPUT_WIDTH,
        onSplitUrl,
        trackParamsField,
        trackTemplateField,
        needAuditingData = false,
        disabled,
        idType,
        isNeedBlur = false,
        className = '',
        urlRecommendOptions,
        impactMethods,
        splitUrlStyle
    } = props;
    const isShowSplitUrl = useMemo(() => {
        // 拆分URL仅限移动访问网址使用
        return isUrlSplitUser() && finaUrlField.includes('mobile');
    }, [finaUrlField]);

    const {getFieldError, setFields} = form;
    const [validateUrl] = useUrlVerification({
        field: finaUrlField,
        idType,
        marketingTargetId
    });

    const onUrlValidate = useCallback(e => {
        const fieldError = getFieldError(finaUrlField);
        // 有错误时不去请求后端错误
        if (!e.value || (fieldError && fieldError.length)) {
            return;
        }
        validateUrl({
            url: e.value
        }).catch(error => {
            const errorText = getSingleErrorMessage(error.errors?.[0]);
            setFields({
                [finaUrlField]: {
                    value: e.value,
                    errors: [{
                        message: errorText
                    }]
                }
            });
        });
    }, [setFields, getFieldError, validateUrl, finaUrlField]);
    const [inputRef, size = {}] = useElementSize();

    const {userId, optId} = useUserInfo();

    const selectProps = {
        marketingTargetId,
        adgroupIds,
        onChange: e => onChange(e.url),
        showType,
        shopType,
        title: '选择已有',
        needAuditingData,
        // 健康商城是基于URL归一化的基础上做的所以只在finurl上面加pageTypeList就行
        pageTypeList: shopType === shopTypeConfig.HEALTH
            ? [pageTypeConfig.commodity, pageTypeConfig.content]
            : [],
        userId,
        optId,
        overlayWidth: size.width + JIMUYU_SELECT_WIDTH
    };
    const {isAiShop} = useAiShopping();
    const inputProps = {
        inputRef,
        value,
        maxLen,
        onChange: e => {
            onChange(e.value);
            if (impactMethods?.changeImpactFactor) {
                impactMethods.changeImpactFactor({
                    aiShoppingUrlSelect: isAiShop
                });
            }
        },
        onBlur: isNeedBlur ? onUrlValidate : noop,
        options: urlRecommendOptions,
        errorLocation: 'bottom',
        placeholder,
        style: {
            marginRight: '4px',
            width: inputWidth
        },
        disabled,
        className,
        showErrorMessage: false
    };

    const splitUrlSetFields = useCallback((urlList = []) => {
        const {finalUrl, trackParam, trackTemplate} = urlList[0] || {};
        let infoText = '';
        if (!trackParam && !trackTemplate) {
            infoText = '系统暂无拆分建议';
        }
        else {
            infoText = '拆分成功';
            setFields({
                [finaUrlField]: {
                    value: finalUrl || ''
                },
                [trackParamsField]: {
                    value: trackParam || ''
                },
                [trackTemplateField]: {
                    value: trackTemplate || ''
                }
            });
        }
        Toast.success({
            content: infoText,
            duration: 3
        });
    }, [setFields]);

    const spiltUrlProps = {
        getError: () => getFieldError(finaUrlField),
        onSplitUrl,
        urls: [value],
        setFields: splitUrlSetFields,
        style: splitUrlStyle
    };

    return (
        <div className="final-url-editor">
            <Input {...inputProps} />
            {hasSelect && <DropdownUrlSelect {...selectProps} />}
            {isShowSplitUrl && <SplitUrl {...spiltUrlProps} />}
        </div>
    );
};

export default FinalUrlEditor;
