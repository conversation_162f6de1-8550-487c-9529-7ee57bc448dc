/*
 * @file: FinalUrlDetailEditor
 * @author: <EMAIL>
 * @Date: 2021/12/15
 */
import {Form} from '@baidu/one-ui';
import FormItem from 'commonLibs/materialList/FormItem';
import AdType from 'commonLibs/config/adType';
import Tip from 'commonLibs/Tips';
import {
    initialValue,
    urlDataToShadowField
} from 'app/components/tableEditor/url/config';
import {isOptimizedPageUser, isCannotUseAgentUrlRegUser} from 'commonLibs/utils/getFlag';
import {CacheProvider} from 'commonLibs/suspenseBoundary';
import MARKET_TARGET from 'commonLibs/config/marketTarget';
import {useSlot} from 'commonLibs/hooks/slot';
import FinalUrlEditor from 'app/components/tableEditor/url/finalUrl';
import UrlTrackParams from 'app/components/tableEditor/url/trackParam';
import UnitUrlPreview from 'app/components/tableEditor/url/preview';
import {useFinalUrlDetail, useShowFinalUrlDetail, urlDetailItemConfig} from './useFinalUrlDetail';
import {BASE_INPUT_WIDTH, JIMUYU_SELECT_WIDTH} from './finalUrl';
import WithDeleteElement from './withDelteElement';
import UrlPreview from 'app/components/urlEditor/urlPreview';
import JmyUrlDrawerEditor from './jmyUrlEditor';
import './style.less';

export const FinalUrlDetailEditor = ({
    form,
    field,
    materiaData,
    hasSelect = false,
    marketingTargetId,
    adgroupIds,
    showType,
    shopType,
    needAuditingData = false, // 基木鱼落地页选择器是否可以选择审核中的页面
    validators,
    disabled = false,
    finalUrlDetailShowFlag: {
        isShowFinalUrl,
        isShowTrackParam,
        isShowTrackTemplate,
        isShowUrlPreview,
        changeUrlFieldShow
    },
    slotProps = {slots: {}},
    isShowShopCreativeEditUrlTip = false,
    adType
}) => {
    const [
        {
            trackParamsField,
            trackTemplateField,
            finalUrlField
        },
        {
            onSplitUrl
        }
    ] = useFinalUrlDetail({
        changeUrlFieldShow,
        field
    });

    const Slot = useSlot(slotProps);

    function getFormItemProps(field) {
        const item =  {
            field: field,
            initialValue: materiaData?.[urlDataToShadowField[field]]
                || materiaData?.[field]
                || initialValue,
            required: validators[field].isRequired,
            rules: validators[field].toOneUIFormRules()
        };
        return item;
    }
    const mobileFinalUrl = form.getFieldsValue()?.mobileFinalUrl; // 只针对移动最终访问网址
    const urlPreviewProps = {
        url: mobileFinalUrl,
        marketingTargetId
    };

    const isUseUrlDrawerEditor = (
        (marketingTargetId === MARKET_TARGET.APP && adType === AdType.NORMAL)
        || (marketingTargetId === MARKET_TARGET.CPQL && field === 'mobileFinalUrl' && isOptimizedPageUser())
    );
    return (
        <Form className="batch-final-url-editor-form">
            {
                isShowFinalUrl && (
                    <>
                        <FormItem
                            form={form}
                            name={
                                <span>最终访问网址<Tip keyName="finalUrl" /></span>
                            }
                            {...getFormItemProps(finalUrlField)}
                        >
                            {
                                isUseUrlDrawerEditor ? (
                                    <JmyUrlDrawerEditor
                                        form={form}
                                        adgroupIds={adgroupIds}
                                        isHideAgentUrl={isCannotUseAgentUrlRegUser()}
                                        {...urlPreviewProps}
                                    />
                                ) : (
                                    <FinalUrlEditor
                                        form={form}
                                        field={finalUrlField}
                                        // eslint-disable-next-line max-len
                                        inputWidth={hasSelect ? BASE_INPUT_WIDTH - JIMUYU_SELECT_WIDTH : BASE_INPUT_WIDTH}
                                        hasSelect={hasSelect}
                                        onSplitUrl={onSplitUrl}
                                        trackParamsField={trackParamsField}
                                        trackTemplateField={trackTemplateField}
                                        marketingTargetId={marketingTargetId}
                                        adgroupIds={adgroupIds}
                                        showType={showType}
                                        shopType={shopType}
                                        needAuditingData={needAuditingData}
                                        disabled={disabled}
                                    />
                                )
                            }
                        </FormItem>
                        <Slot name="finalUrlGuide" />
                    </>
                )
            }
            {
                (
                    <>
                        {
                            isShowTrackParam && (
                                <FormItem
                                    form={form}
                                    name={
                                        <span>监控后缀<Tip keyName="trackParam" /></span>
                                    }
                                    {...getFormItemProps(trackParamsField)}
                                >
                                    <WithDeleteElement
                                        onDelete={() => changeUrlFieldShow([{
                                            field: urlDetailItemConfig.trackParam.value,
                                            value: false
                                        }])}
                                        isDeleted={!isShowTrackParam}
                                    >
                                        <UrlTrackParams form={form} field={trackParamsField} />
                                    </WithDeleteElement>
                                </FormItem>
                            )
                        }
                        {
                            isShowTrackTemplate && (
                                <FormItem
                                    form={form}
                                    name={
                                        <span>追踪模板<Tip keyName="trackTemplate" /></span>
                                    }
                                    {...getFormItemProps(trackTemplateField)}
                                >
                                    <WithDeleteElement
                                        onDelete={() => changeUrlFieldShow([{
                                            field: urlDetailItemConfig.trackTemplate.value,
                                            value: false
                                        }])}
                                        isDeleted={!isShowTrackTemplate}
                                    >
                                        <UrlTrackParams form={form} field={trackTemplateField} />
                                    </WithDeleteElement>
                                </FormItem>
                            )
                        }
                        {
                            isShowUrlPreview && (
                                <FormItem
                                    form={form}
                                    field="urlPerview"
                                    name="落地网址预览"
                                    initialValue={materiaData?.urlPerview || initialValue}
                                >
                                    <UnitUrlPreview
                                        form={form}
                                        urlField={field}
                                        materiaData={materiaData}
                                        trackParamsField={trackParamsField}
                                        trackTemplateField={trackTemplateField}
                                    />
                                </FormItem>
                            )
                        }
                        {
                            isUseUrlDrawerEditor ? (
                                <UrlPreview {...urlPreviewProps} />
                            ) : null
                        }
                        {
                            isShowShopCreativeEditUrlTip
                            && <div className="mod-url-description">说明：电商店铺创意设置的计算机访问网址不生效</div>
                        }
                    </>
                )
            }
        </Form>
    );
};

const FinalUrlDetailEditorWithDefaultShowFlag = ({
    isNeedUrlPreview = true,
    field,
    form,
    adType,
    ...otherProps
}) => {
    const [
        {
            urlShowDetailList,
            isShowUrlPreview
        },
        {
            changeUrlFieldShow
        }
    ] = useShowFinalUrlDetail({
        isNeedUrlPreview,
        field,
        form
    });
    const finalUrlDetailEditorProps =  {
        field,
        form,
        adType,
        ...otherProps,
        finalUrlDetailShowFlag: {
            isShowFinalUrl: urlShowDetailList.includes(urlDetailItemConfig.finalUrl.value),
            isShowTrackParam: urlShowDetailList.includes(urlDetailItemConfig.trackParam.value),
            isShowTrackTemplate: urlShowDetailList.includes(urlDetailItemConfig.trackTemplate.value),
            isShowUrlPreview,
            changeUrlFieldShow
        }
    };
    return (
        <CacheProvider>
            <FinalUrlDetailEditor {...finalUrlDetailEditorProps} />
        </CacheProvider>
    );
};

export default FinalUrlDetailEditorWithDefaultShowFlag;
