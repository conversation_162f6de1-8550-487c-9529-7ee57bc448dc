/*
 * @file: url
 * @author: <EMAIL>
 * @Date: 2021-08-09 19:38:52
 */

import {Input} from '@baidu/one-ui';
import DropdownUrlSelect from 'commonLibs/components/dropdownUrlSelect';
import {useUserInfo} from 'commonLibs/context/sharedInfo';
import {defaultUrlInputPlaceholder} from './config';

const UrlEditor = props => {
    const {
        hasSelect = false,
        marketingTargetId,
        adgroupIds = [],
        showType,
        shopType,
        placeholder = defaultUrlInputPlaceholder,
        value,
        onChange,
        inputWidth = 420
    } = props;

    const {userId, optId} = useUserInfo();

    const selectProps = {
        marketingTargetId,
        adgroupIds,
        onChange: e => onChange(e.url),
        showType,
        shopType,
        title: '选择已有',
        overlayWidth: inputWidth + 118,
        userId,
        optId
    };
    const inputProps = {
        value,
        onChange: e => onChange(e.value),
        errorLocation: 'bottom',
        placeholder,
        style: {
            marginRight: '4px',
            width: inputWidth
        }
    };

    return (
        <div className="keywords-url-editor">
            <Input {...inputProps} />
            {hasSelect && <DropdownUrlSelect {...selectProps} />}
        </div>
    );
};

export default UrlEditor;
