/*
 * @file: 行内编辑最终访问网址监控后缀
 * @author: <EMAIL>
 * @Date: 2021/12/16
 */
import {useMemo} from 'react';
import TrackParamsEditor from 'app/components/tableEditor/url/trackParam';
import FormItem from 'commonLibs/materialList/FormItem';
import {toOneUIFormRule} from 'commonLibs/validators';
import {
    getInlineTrackParamsValidator
} from 'app/components/tableEditor/url/config';
import {getUrlDataAndShadowData} from 'app/utils/getUrl';

function InlineTrackParamEditor({form, field, record, rules}) {
    const [trackParams, shadowTrackParams] = getUrlDataAndShadowData(record, field);
    const initialValue = shadowTrackParams || trackParams;

    const trackParamEditorProps = {
        form,
        field,
        inputWidth: 220
    };
    const trackParamRules = useMemo(() => {
        return toOneUIFormRule(getInlineTrackParamsValidator(form, field, record));
    }, [form, field, record]);

    return (
        <FormItem
            form={form}
            field={field}
            initialValue={initialValue}
            rules={rules || trackParamRules}
            colon={false}
        >
            <TrackParamsEditor {...trackParamEditorProps} />
        </FormItem>
    );
};

export default InlineTrackParamEditor;
