/*
 * @file: 行内编辑最终访问网址追踪模板
 * @author: <EMAIL>
 * @Date: 2021/12/16
 */
import {useMemo} from 'react';
import TrackParamsEditor from 'app/components/tableEditor/url/trackParam';
import FormItem from 'commonLibs/materialList/FormItem';
import {getUrlDataAndShadowData} from 'app/utils/getUrl';
import {toOneUIFormRule} from 'commonLibs/validators';
import {
    getInlineTrackTemplateValidator
} from 'app/components/tableEditor/url/config';

function InlineTrackTemplateEditor({form, field, record, rules}) {
    const [trackTemplate, shadowTrackTemplate] = getUrlDataAndShadowData(record, field);
    const initialValue = shadowTrackTemplate || trackTemplate;

    const trackParamEditorProps = {
        form,
        field,
        inputWidth: 220
    };

    const trackTemplateRules = useMemo(() => {
        return toOneUIFormRule(getInlineTrackTemplateValidator(form, field, record));
    }, [form, field, record]);

    return (
        <FormItem
            form={form}
            field={field}
            initialValue={initialValue}
            rules={rules || trackTemplateRules}
            colon={false}
        >
            <TrackParamsEditor {...trackParamEditorProps} />
        </FormItem>
    );
};

export default InlineTrackTemplateEditor;
