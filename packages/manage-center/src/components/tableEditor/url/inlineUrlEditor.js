/*
 * @file: 行内编辑url-移动访问网址/计算机访问网址
 * @author: l<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2021-08-20 10:50:21
 */
import {includes} from 'lodash-es';
import {shopTypeConfig} from 'commonLibs/config/shop';
import MARKET_TARGET from 'commonLibs/config/marketTarget';
import UrlEditor from 'app/components/tableEditor/url';
import {getPcPageUser} from 'commonLibs/components/jimuyuSelect/util';
import {showTypeKeyMap} from 'commonLibs/components/jimuyuSelect/config';
import FormItem from 'commonLibs/materialList/FormItem';
import {getUrlDataAndShadowData} from 'app/utils/getUrl';
import {validateDestinationUrl} from './config';

function InlineUrlEditor({form, field, record, rules}) {
    const {marketingTargetId, adgroupId, shopType} = record;
    const [url, shadowUrl] = getUrlDataAndShadowData(record, field);
    const initialValue = shadowUrl || url;
    // 选择落地页功能小流量,移动的全流量了，pc的有小流量
    // 选择落地页功能,移动的仅支持营销目标为网站链接和app下载和SHOP，pc的仅支持网站链接
    const mobilehasSelectTarget = [MARKET_TARGET.WEB, MARKET_TARGET.APP, MARKET_TARGET.SHOP];
    const mobilehasSelect = includes(mobilehasSelectTarget, marketingTargetId);
    const pchasSelect = marketingTargetId === MARKET_TARGET.WEB && getPcPageUser();
    const hasSelect = field.toLowerCase().includes('mobile') ? mobilehasSelect : pchasSelect;

    const urlEditorProps = {
        form,
        field,
        inputWidth: 220,
        marketingTargetId,
        adgroupIds: [adgroupId],
        showType: field.toLowerCase().includes('mobile') ? showTypeKeyMap.mobile : showTypeKeyMap.pc,
        shopType: shopType || shopTypeConfig.THIRD_PARTY[0],
        hasSelect
    };
    return (
        <FormItem
            form={form}
            field={field}
            initialValue={initialValue}
            rules={rules || validateDestinationUrl.toOneUIFormRules()}
            colon={false}
        >
            <UrlEditor {...urlEditorProps} />
        </FormItem>
    );
};

export default InlineUrlEditor;
