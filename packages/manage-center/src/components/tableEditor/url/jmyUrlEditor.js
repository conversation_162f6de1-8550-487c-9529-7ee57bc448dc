import {Input, Button} from '@baidu/one-ui';
import {useControl} from 'commonLibs/hooks/externalControl';
import SuspenseBoundary from 'commonLibs/suspenseBoundary';
import UrlSelectDrawer from 'app/components/urlEditor/drawer';

const JmyUrlEditorOfApp = props => {
    const {isHideAgentUrl, value, onChange, adgroupIds = [], marketingTargetId, url} = props;
    const [UrlSelector, {open}] = useControl(UrlSelectDrawer);
    const inputJmyProps = {
        width: 480,
        value,
        onChange,
        className: 'app-jmy-url-input',
        placeholder: '请输入落地页链接或选择已有落地页',
        suffix: <Button type="text-strong" onClick={open}>更换</Button>
    };
    const adgroupInfo = {
        url: value,
        adgroupId: adgroupIds[0],
        marketingTargetId
    };
    const urlSelectorProps = {
        onChange,
        adgroupInfo,
        marketingTargetId,
        isHideAgentUrl,
        url
    };
    return (
        <>
            <Input {...inputJmyProps} />
            <SuspenseBoundary hideLoading>
                <UrlSelector {...urlSelectorProps} />
            </SuspenseBoundary>
        </>

    );
};

export default JmyUrlEditorOfApp;
