/* eslint-disable react/prop-types */
/*
 * @file: previewUrl
 * @author: <EMAIL>
 * @Date: 2021-12-06 15:52:34
 */
import {useEffect, useMemo} from 'react';
import {Link, Popover} from '@baidu/one-ui';
import {isUrl} from 'commonLibs/validators';
import {
    urlDataToShadowField
} from 'app/components/tableEditor/url/config';
import {getUnitPreviewUrl} from './config';
import './style.less';

const Preview = props => {
    const {form, urlField, trackParamsField, trackTemplateField, onChange, materiaData, materiaError} = props;
    const {
        value: {
            [urlField]: finalUrl,
            [trackParamsField]: trackParamsFormValue,
            [trackTemplateField]: trackTemplateFormValue
        } = {},
        error: {
            [urlField]: finalUrlError = [],
            [trackParamsField]: trackParamsError = [],
            [trackTemplateField]: trackTemplateError = []
        } = {}
    } = useMemo(
        () => {
            if (form?.getFieldsValue) {
                const {getFieldsValue, getFieldsError} = form;
                return {
                    value: getFieldsValue([urlField, trackParamsField, trackTemplateField]),
                    error: getFieldsError([urlField, trackParamsField, trackTemplateField])
                };
            }
            return {
                value: materiaData, error: materiaError
            };
        },
        [form, urlField, trackParamsField, trackTemplateField, materiaError, materiaData]
    );

    const [trackParam, trackTemplate] = useMemo(() => {
        return [
            trackParamsFormValue !== undefined
                ? trackParamsFormValue
                : (
                    materiaData?.[urlDataToShadowField[trackParamsField]] || materiaData?.[trackParamsField]
                ),
            trackTemplateFormValue !== undefined
                ? trackTemplateFormValue
                : (
                    materiaData?.[urlDataToShadowField[trackTemplateField]] || materiaData?.[trackTemplateField]
                )
        ];
    }, [materiaData, trackParamsFormValue, trackTemplateFormValue, trackParamsField, trackTemplateField]);

    const unitUrl = useMemo(() => {
        if (!finalUrlError.length && !trackParamsError.length && !trackTemplateError.length) {
            const unitUrl = getUnitPreviewUrl({finalUrl, trackParam, trackTemplate});
            if (unitUrl) {
                return unitUrl;
            }
        }
        return '';
    }, [
        finalUrl,
        trackParam,
        trackTemplate,
        finalUrlError,
        trackParamsError,
        trackTemplateError
    ]);


    useEffect(() => onChange(unitUrl), [unitUrl]);

    const toUrl = useMemo(() => {
        if (!/^(http:|https:)/i.test(unitUrl)) {
            return `https://${unitUrl}`;
        }
        return unitUrl;
    }, [unitUrl]);

    const urlNode = (
        <Link
            type="strong"
            target="_blank"
            toUrl={toUrl}
        >
            <span className="batch-final-url-editor-form-preview-url-span">{unitUrl}</span>
        </Link>
    );
    return (
        <div className="batch-final-url-editor-form-preview-url">
            {
                unitUrl && isUrl(unitUrl)
                    ? (
                        <Popover
                            content={unitUrl}
                        >
                            {urlNode}
                        </Popover>
                    )
                    : '请输入有效落地页信息'
            }
        </div>
    );
};

export default Preview;
