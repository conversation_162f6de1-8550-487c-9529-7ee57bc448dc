import {useCallback} from 'react';
import classNames from 'classnames';
import {Toast, Button} from '@baidu/one-ui';
import Tip from 'commonLibs/Tips';
import {useUrlSplitSuggestion} from 'app/hooks/url';

const SplitUrl = ({
    getError,
    onSplitUrl,
    urls,
    setFields,
    style
}) => {
    const {loading, splitUrl} = useUrlSplitSuggestion();

    const onClick = useCallback(() => {
        const fieldError = getError();
        // 有错误时不去请求后端错误
        if (fieldError && fieldError.length) {
            return;
        }
        onSplitUrl && onSplitUrl();
        splitUrl(urls).then(urlList => {
            setFields(urlList);
        }).catch(() => {
            Toast.error({
                content: '系统暂无拆分建议',
                duration: 3
            });
        });
    }, [
        getError,
        onSplitUrl,
        urls,
        setFields,
        splitUrl
    ]);
    return (
        <div className="final-url-editor-split-btn-box" style={style}>
            <Button
                type={classNames({
                    'text': loading,
                    'text-strong': !loading
                })}
                onClick={onClick}
                loading={loading}
                className="final-url-editor-split-btn"
            >
                {loading ? '拆分中...' : '自动拆分访问网址'}
            </Button>
            <Tip keyName="urlSplit" className="split-tip-key" />
        </div>
    );
};

export default SplitUrl;
