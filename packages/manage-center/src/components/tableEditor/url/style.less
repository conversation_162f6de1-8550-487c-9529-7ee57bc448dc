.batch-final-url-editor-form {
    .one-form-item-label {
        width: @dls-padding-unit * 31;
    }
    .url-preview-single-container, .url-preview-group-container {
        margin-left: @dls-padding-unit * 31;
    }
    &-preview-url {
        display: inline-block;
        width: 420px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        word-break: break-all;
        font-size: @dls-font-size-1;
        color: @dls-color-gray-7;
    }
    .mod-url-description {
        margin-top: -@dls-padding-unit * 3;
        color: @dls-color-gray-7;
    }
}
.final-url-track-params-editor {
    &-tarsh-icon {
        margin-left: @dls-padding-unit*5;
        color: @dls-color-gray-7;
        font-size: @dls-font-size-2;
        cursor: pointer;
    }
}
.ele-witch-del {
    &-container {
        display: flex;
        align-items: center;
    }
    &-btn {
        cursor: pointer;
        font-size: @dls-font-size-3;
        color: @dls-color-gray-6;
        margin-left: @dls-padding-unit;
    }
}
.final-url-editor {
    display: flex;
    align-items: center;
    .jimuyu-select-landpage-common-new {
        margin-left: @dls-padding-unit*2;
    }
}
.final-url-editor-split-btn {
    margin-left: @dls-padding-unit*2;
}
.split-tip-key {
    flex-shrink: 0;
}
.final-url-detail-check-box {
    margin-bottom: 20px;
}
.app-jmy-url-input {
    input {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 98%;
    }
}
.batch-url-tips {
    color: #999;
    margin-top: 4px;
}
