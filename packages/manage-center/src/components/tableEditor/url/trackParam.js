/*
 * @file: trackParams
 * @author: <EMAIL>
 * @Date: 2021/12/14
 */

import {Input} from '@baidu/one-ui';
import {DESTINATION_URL_MAX_LENGTH_IN_BYTES} from './config';
import './style.less';

const UrlTrackParams = props => {
    const {
        value,
        onChange,
        inputWidth = 420,
        className,
        field,
        placeholder,
        maxLen = DESTINATION_URL_MAX_LENGTH_IN_BYTES
    } = props;
    const defaultUrlInputPlaceholder = ['pcTrackParam', 'mobileTrackParam', 'adgroupAgentParam'].includes(field)
        ? '示例：?keyword={keywordid}'
        : '示例：https://www.trackingtemplate.com?url={lpurl_encode}';
    const inputProps = {
        placeholder: placeholder || defaultUrlInputPlaceholder,
        value,
        onChange,
        width: inputWidth,
        className,
        maxLen
    };

    return (
        <div className="final-url-track-params-editor">
            <Input {...inputProps} />
        </div>
    );
};

export default UrlTrackParams;
