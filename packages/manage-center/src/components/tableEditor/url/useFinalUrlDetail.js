import {useCallback, useMemo, useState} from 'react';
import {uniq} from 'lodash-es';

export const urlDetailItemConfig = {
    finalUrl: {
        text: '最终访问网址',
        value: 'finalUrl'
    },
    trackParam: {
        text: '监控后缀',
        value: 'trackParam'
    },
    trackTemplate: {
        text: '追踪模板',
        value: 'trackTemplate'
    }
};

export function useFinalUrlDetail({
    changeUrlFieldShow, // 如果需要在拆分时展示隐藏的监控后缀和追踪模板再传这个函数，不然就不用传
    field
}) {

    const onSplitUrl = useCallback(() => {
        changeUrlFieldShow && changeUrlFieldShow([
            {
                field: urlDetailItemConfig.trackParam.value,
                value: true
            },
            {
                field: urlDetailItemConfig.trackTemplate.value,
                value: true
            }
        ]);
    }, [
        changeUrlFieldShow
    ]);

    const [trackParamsField, trackTemplateField, finalUrlField] = useMemo(() => {
        if (field === 'pcFinalUrl' || field === 'pcTrackParam' || field === 'pcTrackTemplate') {
            return ['pcTrackParam', 'pcTrackTemplate', 'pcFinalUrl'];
        }
        return ['mobileTrackParam', 'mobileTrackTemplate', 'mobileFinalUrl'];
    }, [field]);

    return [
        {
            trackParamsField,
            trackTemplateField,
            finalUrlField
        },
        {
            onSplitUrl
        }
    ];
}

export function useShowFinalUrlDetail({
    isNeedUrlPreview,
    field,
    form,
    initialValue = urlDetailItemConfig.finalUrl.value
}) {
    const {setFieldsValue} = form;
    const [urlShowDetailList, setUrlShowDetailList] = useState([initialValue]);
    const isShowUrlPreview = useMemo(() => {
        if (isNeedUrlPreview) {
            return true;
        }
        return urlShowDetailList?.length === Object.keys(urlDetailItemConfig).length;
    }, [isNeedUrlPreview, urlShowDetailList]);
    const [trackParamsField, trackTemplateField, finalUrlField] = useMemo(() => {
        if (field === 'pcFinalUrl' || field === 'pcTrackParam' || field === 'pcTrackTemplate') {
            return ['pcTrackParam', 'pcTrackTemplate', 'pcFinalUrl'];
        }
        return ['mobileTrackParam', 'mobileTrackTemplate', 'mobileFinalUrl'];
    }, [field]);

    const changeUrlFieldShow = useCallback(changeList => {
        let chengedUrlShowDetailList = [...urlShowDetailList];
        changeList.map(({field, value}) => {
            if (!value) {
                switch (field) {
                    case urlDetailItemConfig.finalUrl:
                        setFieldsValue({[finalUrlField]: ''});
                        break;
                    case urlDetailItemConfig.trackParam:
                        setFieldsValue({[trackParamsField]: ''});
                        break;
                    case urlDetailItemConfig.trackTemplate:
                        setFieldsValue({[trackTemplateField]: ''});
                        break;
                    default:
                        break;
                }
            }
            chengedUrlShowDetailList = value
                ? uniq([...chengedUrlShowDetailList, field])
                : chengedUrlShowDetailList.filter(item => item !== field);
        });
        setUrlShowDetailList(chengedUrlShowDetailList);
    }, [urlShowDetailList]);
    return [
        {
            urlShowDetailList,
            isShowUrlPreview: isShowUrlPreview
        },
        {
            changeUrlFieldShow
        }
    ];
}