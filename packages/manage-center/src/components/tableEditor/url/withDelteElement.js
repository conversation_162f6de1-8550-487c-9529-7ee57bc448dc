import React, {Fragment, useCallback} from 'react';
import {useSwitch} from '@huse/boolean';
import {IconTrash} from 'dls-icons-react';
import './style.less';

const WithDeleteElement = ({children, onDelete, ...props}) => {
    const [isDeleted, deleteEele, showEle] = useSwitch(false);
    const onClick = useCallback(() => {
        if (onDelete && typeof onDelete === 'function') {
            onDelete();
        } else {
            deleteEele();
        }
    }, [onDelete]);
    return (
        <Fragment>
            {
                isDeleted
                    ? null
                    : (
                        <div className="ele-witch-del-container">
                            {
                                React.Children.map(children, child => {
                                    return React.cloneElement(child, {...props});
                                })
                            }
                            <IconTrash className="ele-witch-del-btn" onClick={onClick} />
                        </div>
                    )
            }
        </Fragment>
    );
};

export default WithDeleteElement;
