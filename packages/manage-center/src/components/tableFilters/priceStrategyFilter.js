/**
 * @file 出价策略筛选
 * <AUTHOR>
 * @date 2021/08/20
 */
import {useCallback} from 'react';
import {CascaderPane} from '@baidu/one-ui';
import {flatten} from 'lodash-es';
import TagRender from '@baidu/one-ui-pro/es/components/filterDropdown/tagRender';

export default function PriceStrategyFilter({value, onChange, dataSource = []}) {

    const cascaderPaneProps = {
        options: dataSource,
        checkedKeys: value,
        showCheckbox: true,
        showSearch: true,
        size: 'small',
        onCheckboxChange: useCallback(checkedKeys => {
            onChange({value: checkedKeys});
        }, [onChange]),
        style: {
            '--dls-dropdown-max-display-items': 6
        }
    };

    return <CascaderPane {...cascaderPaneProps} />;
}

export function PriceStrategyFilterItem({config, ...tagProps}) {
    const {dataSource} = config;
    const strategyList = flatten(dataSource.map(item => {
        if (item.children && item.children.length) { // 取叶子节点
            return item.children;
        }
        return item;
    }));
    const value = strategyList.filter(option => tagProps.value.includes(option.value)).map(option => option.label);
    return <TagRender {...tagProps} value={value} />;
}
