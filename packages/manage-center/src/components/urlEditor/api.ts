import {request} from '@baidu/winds-ajax';
import AppType from 'commonLibs/config/osType';
import {APP, CPQL} from 'commonLibs/config/marketTarget';
import {productAppId, showType} from 'commonLibs/config/miniProgram';
import platFormKey from 'commonLibs/config/platform';
import {SELECT_TYPE, SORT_TYPE_ENUM, getPageUrlType, URL_TYPE_ENUM, SUPPORT_GROUP_CONFIG_ENUM} from './config';

enum IdTypeConfigEnum {
    ADGROUP = 5,
    CHANNEL = 56,
    APP_STORE = 57
}

function getPageListApiParamsForCPQL_(props) {
    const {
        adgroupInfo,
        sort,
        pageNo,
        pageSize,
        isSupportPageGroup = SUPPORT_GROUP_CONFIG_ENUM.NO_LIMIT,
        filter
    } = props;
    const {url} = adgroupInfo || {};
    const {value: filterValue, type: filterType} = filter;
    const params = {
        showType: showType.h5,
        desc: sort === SORT_TYPE_ENUM.EDIT_TIME_LATEST,
        searchFields: {
            productAppIdNotIn: [productAppId.duStore],
            selectUrl: url
        },
        limit: 500,
        pageLimit: [(pageNo - 1) * pageSize, pageSize],
        platformIds: [platFormKey.JIMUYU, platFormKey.MEDICAL],
        extraUrlTypes: [-1],
        queryThumbnail: true
    };
    if (filterValue) {
        const field = filterType === SELECT_TYPE.NAME ? 'pageName' : 'id';
        params.searchFields[field] = filterValue;
    }
    if (isSupportPageGroup !== SUPPORT_GROUP_CONFIG_ENUM.NO_LIMIT) {
        params.searchFields.supportGroupPage = isSupportPageGroup === SUPPORT_GROUP_CONFIG_ENUM.SUPPORTED;
    }
    return {
        path: 'lightning/GET/UrlPromotionSiteService/getJmyPromotionPage',
        params
    };
}

function getPageGroupListApiParamsForCPQL_(props) {
    const {
        adgroupInfo,
        sort,
        pageNo,
        pageSize,
        filter
    } = props;
    const {url} = adgroupInfo || {};
    const {value: filterValue, type: filterType} = filter;
    const params = {
        showType: showType.h5,
        desc: sort === SORT_TYPE_ENUM.EDIT_TIME_LATEST,
        urlTypes: [
            +URL_TYPE_ENUM.JMY_ADGROUP_Abtest,
            +URL_TYPE_ENUM.JMY_ADGROUP_CLUE
        ],
        needAuditingData: false,
        orderBy: 'updateTime',
        limit: [(pageNo - 1) * pageSize, pageSize],
        selectUrl: url
    };
    if (filterValue) {
        params.fieldFilter = {
            field: filterType === SELECT_TYPE.NAME ? 'pageName' : 'pageId',
            op: 'like',
            values: [filterValue]
        };
    }
    return {
        path: 'lightning/GET/UrlPromotionSiteService/getJmyGroupPage',
        params
    };
};

function getPageListApiParamsForAPP_(props) {
    const {
        adgroupInfo,
        sort,
        urlType,
        pageNo,
        pageSize,
        filter
    } = props;
    const {
        adgroupId, channelId, osType, url
    } = adgroupInfo || {};
    const {value: filterValue, type: filterType} = filter;

    const newIdType = osType === AppType.ANDROID.value ? IdTypeConfigEnum.CHANNEL : IdTypeConfigEnum.APP_STORE;
    const params = {
        id: adgroupId ? adgroupId : channelId, // 新建传应用id，编辑传单元id
        idType: adgroupId ? IdTypeConfigEnum.ADGROUP : newIdType,
        urlTypes: [+urlType],
        orderBy: 'modTime',
        desc: sort === SORT_TYPE_ENUM.EDIT_TIME_LATEST,
        limit: [(pageNo - 1) * pageSize, pageSize]
    };
    if (url) {
        params.selectUrl = url;
    }
    if (filterValue) {
        params.fieldFilter = {
            field: filterType === SELECT_TYPE.NAME ? 'pageName' : 'pageId',
            op: 'like',
            values: [filterValue]
        };
    }

    return {
        path: 'puppet/GET/NovelPromotionSiteFunction/getAppNewNovelPromotionInfo',
        params
    };
}

function getAgentListApiParams({filter}) {
    const {value: filterValue} = filter;
    const params = {
        limit: [1, 10],
        sortBy: {field: 'updateTime', isAsc: false}
    };
    if (filterValue) {
        params.predicates = {
            field: 'pageName',
            op: 'like',
            values: [filterValue]
        };
    }
    return {
        path: 'lightning/GET/AgentService/getAgentList',
        params
    };
}

// 以营销目标来梳理下参数：获取单元、创意流程中落地页或者落地页组url
function getPageListApiParams(props) {
    const {
        adgroupInfo: {marketingTargetId} = {},
        urlType
    } = props;

    // 营销目标是销售线索，落地页和落地页组使用不同接口
    if (marketingTargetId === CPQL) {
        const selectedUrlType = getPageUrlType(urlType);
        if (selectedUrlType === URL_TYPE_ENUM.JMY_SINGLE) {
            return getPageListApiParamsForCPQL_(props);
        }
        else if (selectedUrlType === URL_TYPE_ENUM.JMY_ADGROUP_BOT) {
            return getAgentListApiParams(props);
        }
        return getPageGroupListApiParamsForCPQL_(props);
    }

    // 其他营销目标（目前只有应用推广）
    return getPageListApiParamsForAPP_(props);
}

export async function fetchPageList(props) {
    const result = await request(getPageListApiParams(props));
    return result;
}


export async function fetchPageOrPageGroupInfoByUrl({url, marketingTargetId = APP} = {}) {
    if (url && [APP, CPQL].includes(marketingTargetId)) {
        const result = await request({
            path: 'lightning/GET/UrlPromotionSiteService/getAppJmyPageByUrl',
            params: {
                onlineUrl: url
            }
        });
        return result;
    }
    return {};
}


// 以营销目标来梳理下参数：获取单元、创意流程中将落地页打包生成落地页组
function getAddPageGroupApiParams(params, materialInfo = {}) {
    if (materialInfo?.marketingTargetId === CPQL) {
        return {
            path: 'lightning/GET/UrlPromotionSiteService/getAndAddGroupJmyPage',
            params
        };
    }

    return {
        path: 'lightning/GET/UrlPromotionSiteService/getAppGroupJmyPage',
        params
    };
};

export async function onAddJMYPageOrPageGroup(params, materialInfo) {
    const result = await request(getAddPageGroupApiParams(params, materialInfo));
    return result;
}