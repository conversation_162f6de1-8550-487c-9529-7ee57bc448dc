import {CPQL} from 'commonLibs/config/marketTarget';

export enum URL_TYPE_ENUM {
    /**
     * 基木鱼落地页单页
     */
    JMY_SINGLE = '0',
    /**
     * 基木鱼落地页组 - 下载类
     */
    JMY_ADGROUP_APP = '4',
    /**
     * 基木鱼落地页组 - abtest
     */
    JMY_ADGROUP_Abtest = '1',
    /**
     * 基木鱼落地页组 - 销售线索
     */
    JMY_ADGROUP_CLUE = '16',
    /**
     * 基木鱼落地页 - 商家智能体
     */
    JMY_ADGROUP_BOT = '18'
};

export function isSinglePageUrlType(urlType: string | number) {
    return `${urlType}` === URL_TYPE_ENUM.JMY_SINGLE;
}

export function isAgentPageUrlType(urlType: string | number) {
    return `${urlType}` === URL_TYPE_ENUM.JMY_ADGROUP_BOT;
}

export function isSingleOrAgentPageUrlType(urlType: string | number) {
    return isSinglePageUrlType(urlType) || isAgentPageUrlType(urlType);
}

export function isPageGroupUrlType(urlType: string | number) {
    return [
        URL_TYPE_ENUM.JMY_ADGROUP_APP,
        URL_TYPE_ENUM.JMY_ADGROUP_Abtest,
        URL_TYPE_ENUM.JMY_ADGROUP_CLUE
    ].includes(urlType);
}

export function getPageUrlType(urlType: string | number) {
    switch (urlType) {
        case URL_TYPE_ENUM.JMY_SINGLE:
            return URL_TYPE_ENUM.JMY_SINGLE;
        case URL_TYPE_ENUM.JMY_ADGROUP_BOT:
            return URL_TYPE_ENUM.JMY_ADGROUP_BOT;
        default:
            return URL_TYPE_ENUM.JMY_ADGROUP_APP;
    }
}

export enum SORT_TYPE_ENUM {
    /**
     * 修改时间从近到远
     */
    EDIT_TIME_LATEST = 'modTime_latest',
    /**
     * 修改时间从远到近
     */
    EDIT_TIME_OLDER = 'modTime_older',
    /**
     * 创建时间
     */
    CREATIVE_TIME = 'addTime'
};

export const SORT_CONFIG = [
    {
        key: SORT_TYPE_ENUM.EDIT_TIME_LATEST,
        label: '修改时间从近到远'
    },
    {
        key: SORT_TYPE_ENUM.EDIT_TIME_OLDER,
        label: '修改时间从远到近'
    }
];

// 是否支持程序化优选 - 多页打包
export enum SUPPORT_GROUP_CONFIG_ENUM {
    SUPPORTED = 1,
    UNSUPPORTED = 2,
    NO_LIMIT = 3
}

export const SUPPORT_GROUP_CONFIG = [
    {
        key: SUPPORT_GROUP_CONFIG_ENUM.SUPPORTED,
        label: '支持程序化优选'
    },
    {
        key: SUPPORT_GROUP_CONFIG_ENUM.UNSUPPORTED,
        label: '不支持程序化优选'
    }
];

export const SELECT_TYPE = {
    ID: 'id',
    NAME: 'name'
};

export const maxImageDisplayNumOfGroup = 4;

export const getMaxImgOfSinglePage = (marketingTargetId, isMustSelectSingleUrl = false, isAgentPage) => {
    // 商家智能体只能选择一个
    if (isAgentPage) {
        return 1;
    }
    if (marketingTargetId === CPQL) {
        return isMustSelectSingleUrl ? 1 : 40;
    }
    return 30;
};

export const maxImgOfGroupPage = 1;

export const maxDisplayNum = 7;