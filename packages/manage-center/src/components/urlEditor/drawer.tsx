import {Drawer, Toast} from '@baidu/one-ui';
import {isEmpty} from 'lodash-es';
import {useResource} from 'commonLibs/suspenseBoundary';
import {displayErrorAsToast, createError} from 'commonLibs/utils/materialList/error';
import {useImperativeHandle, forwardRef, useCallback, useState, useEffect} from 'react';
import {useBoolean} from '@huse/boolean';
import {useActionPending} from '@huse/action-pending';
import UrlSelect from './urlSelect';
import {URL_TYPE_ENUM, isSinglePageUrlType} from './config';
import {adgroupInfoProps} from './type';
import {getSaveParams} from './util';
import {onAddJMYPageOrPageGroup, fetchPageOrPageGroupInfoByUrl, fetchPageList} from './api';
import {usePageSelectOptions} from './hook';

interface UrlSelectDrawerProps {
    isHideAgentUrl?: boolean;
    adgroupInfo: adgroupInfoProps;
    onChange: (value: string) => void;
    marketingTargetId: string | number;
    url: string;
};

function getPageInfo() {
    return {
        type: URL_TYPE_ENUM.JMY_SINGLE,
        ids: [],
        groupPageList: []
    };
}

function UrlSelectDrawer_(props: UrlSelectDrawerProps, ref) {
    const {
        adgroupInfo = {},
        onChange,
        marketingTargetId,
        isHideAgentUrl,
        url
    } = props;
    const [initPageInfo] = useResource(fetchPageOrPageGroupInfoByUrl, {url, marketingTargetId});
    const [visible, {on: open, off: close}] = useBoolean();
    useImperativeHandle(ref, () => ({open, close}));

    const [pageInfo, setPageInfo] = useState(() => getPageInfo());
    const [
        {
            isOnlySelectSingleUrl,
            isSupportPageGroup,
            showIsSupportPageGroup,
            maxSelectedNum
        },
        {setIsSupportPageGroup}
    ] = usePageSelectOptions({
        marketingTargetId,
        urlType: pageInfo?.type
    });

    // 编辑时，初始化落地页信息，如果是单个落地页，需要用查询接口的返回值初始化
    const setSinglePageInfoBySearch = useCallback(async initInfo => {
        const {urlType, pageId} = initInfo;
        const allpages = await fetchPageList({
            adgroupInfo,
            sort: '',
            urlType,
            filter: {},
            pageNo: 1,
            pageSize: 20
        });
        const {pageList = []} = allpages || {};
        const initPage = pageList.find(page => page.pageId === pageId) || initInfo;

        setPageInfo({
            ...initPage,
            type: `${urlType}`,
            ids: [pageId],
            allPageList: [initPage],
            onlineUrl: {[pageId]: initPage.onlineUrl}
        });
    }, [adgroupInfo, setPageInfo]);

    useEffect(() => {
        if (!isEmpty(initPageInfo)) {
            const {urlType, pageId, groupPageList} = initPageInfo;
            if (isSinglePageUrlType(urlType)) {
                setSinglePageInfoBySearch(initPageInfo);
            } else {
                setPageInfo({
                    ...pageInfo,
                    type: `${urlType}`,
                    ids: [pageId],
                    allPageList: [initPageInfo],
                    groupPageList
                });
            }
        } else {
            setPageInfo({
                ...pageInfo,
                type: URL_TYPE_ENUM.JMY_SINGLE,
                ids: []
            });
        }
    }, [initPageInfo]);

    const onOk_ = useCallback(async () => {
        if (isEmpty(pageInfo.ids)) {
            Toast.error({
                content: '请选择落地页或者落地页组'
            });
            return;
        }
        if (pageInfo.ids?.length > maxSelectedNum) {
            Toast.error({
                content: '落地页数量超过限制'
            });
            return;
        }

        const {channelId, osTypeFromResponse} = pageInfo;
        const params = getSaveParams({pageInfo, adgroupInfo: {...adgroupInfo, channelId, osTypeFromResponse}});
        try {
            const {type, onlineUrl, ids} = pageInfo;
            // 选择落地页组直接回填不需要请求
            const data = type === URL_TYPE_ENUM.JMY_SINGLE && ids.length > 1
                ? await onAddJMYPageOrPageGroup(params, adgroupInfo)
                : {onlineUrl: Object.values(onlineUrl)[0]};
            const onlineUrl_ = data.onlineUrl;
            close();
            onChange(onlineUrl_);
            return data;
        }
        catch (err) {
            const error = createError(err);
            error.optName = '新建';
            displayErrorAsToast(error);
        }
    }, [maxSelectedNum, close, onChange, pageInfo, adgroupInfo]);

    const [onOk, saveLoading] = useActionPending(onOk_);

    const onCancel = useCallback(() => {
        close();
    }, [close]);

    const urlSelectEditorProps = {
        pageInfo,
        adgroupInfo,
        saveLoading,
        isHideAgentUrl,
        isOnlySelectSingleUrl,
        isSupportPageGroup,
        showIsSupportPageGroup,
        maxSelectedNum,
        setIsSupportPageGroup,
        setPageInfo,
        onOk,
        onCancel
    };

    return (
        <Drawer
            title={null}
            placement="right"
            visible={visible}
            onClose={close}
            destroyOnClose
            width={864}
            footer={null}
            type="basic"
        >
            <UrlSelect {...urlSelectEditorProps} />
        </Drawer>
    );
}

export default forwardRef(UrlSelectDrawer_);
