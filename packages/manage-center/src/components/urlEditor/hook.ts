import {useState, useMemo} from 'react';
import {useAccountInfo} from 'commonLibs/context/sharedInfo';
import {CPQL} from 'commonLibs/config/marketTarget';
import {SUPPORT_GROUP_CONFIG_ENUM, isSinglePageUrlType, isAgentPageUrlType, getMaxImgOfSinglePage} from './config';

/**
 * 判断是否必须选择单个落地页
 */
export function usePageSelectOptions(props) {
    const {
        marketingTargetId,
        urlType
    } = props;
    const isCpqlMt = marketingTargetId === CPQL;

    const {trusteeshipTradeId1} = useAccountInfo();
    // 营销目标=销售线索时，医疗医美行业用户只能选择单个落地页
    const isOnlySelectSingleUrl = isCpqlMt && ['TG_1', 'TG_2'].includes(trusteeshipTradeId1);

    // 销售线索、非医疗医美行业用户、位于落地页单页tab下时，展示“是否支持程序化落地页”切换按钮
    const showIsSupportPageGroup = useMemo(() => {
        return isCpqlMt && !isOnlySelectSingleUrl && isSinglePageUrlType(urlType);
    }, [isCpqlMt, isOnlySelectSingleUrl, urlType]);
    const [isSupportPageGroup, setIsSupportPageGroup] = useState(SUPPORT_GROUP_CONFIG_ENUM.SUPPORTED);
    const isRealSupportPageGroup = useMemo(() => {
        return showIsSupportPageGroup ? isSupportPageGroup : SUPPORT_GROUP_CONFIG_ENUM.NO_LIMIT;
    }, [showIsSupportPageGroup, isSupportPageGroup]);

    const maxSelectedNum = useMemo(() => {
        const isMustSelectSingleUrl = (
            !isSinglePageUrlType(urlType)
            || isOnlySelectSingleUrl
            || isRealSupportPageGroup === SUPPORT_GROUP_CONFIG_ENUM.UNSUPPORTED
        );
        const isAgentPage = isAgentPageUrlType(urlType);
        return getMaxImgOfSinglePage(marketingTargetId, isMustSelectSingleUrl, isAgentPage);
    }, [marketingTargetId, isOnlySelectSingleUrl, urlType, isRealSupportPageGroup]);

    return [
        {
            isOnlySelectSingleUrl,
            isSupportPageGroup: isRealSupportPageGroup,
            showIsSupportPageGroup,
            maxSelectedNum
        },
        {
            setIsSupportPageGroup
        }
    ];
}
