import {APP} from 'commonLibs/config/marketTarget';
import FinalUrl from 'app/containers/fcNew/creative/url/finalUrl';
import UrlDrawerEditor from './main';

function urlEditor(props) {
    const {marketingTargetId} = props;
    if (marketingTargetId === APP) {
        return <UrlDrawerEditor {...props} />;
    }
    return <FinalUrl {...props} />;
}

export default urlEditor;
export {UrlDrawerEditor};