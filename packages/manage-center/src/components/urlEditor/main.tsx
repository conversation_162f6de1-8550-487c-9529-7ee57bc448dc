import {Input, Button} from '@baidu/one-ui';
import SuspenseBoundary from 'commonLibs/suspenseBoundary';
import {APP} from 'commonLibs/config/marketTarget';
import {useControl} from 'commonLibs/hooks/externalControl';
import UrlSelectDrawer from './drawer';
import {adgroupInfoProps} from './type';
import './style.less';

interface Props {
    value: string;
    onChange: (value: string | undefined) => void;
    adgroupInfo: adgroupInfoProps;
    isHideAgentUrl?: boolean;
}

function UrlDrawerEditor(props: Props) {
    const {isHideAgentUrl, value, onChange, adgroupInfo = {}, marketingTargetId} = props;
    const [UrlSelector, {open}] = useControl(UrlSelectDrawer);
    const {channelId} = adgroupInfo;
    const disabledSelect = !channelId && marketingTargetId === APP;
    const inputProps = {
        width: 480,
        value,
        onChange,
        className: 'app-jmy-url-input',
        placeholder: '请输入落地页链接或选择已有落地页',
        suffix: <Button type="text-strong" onClick={open} disabled={disabledSelect}>选择</Button>
    };
    const urlSelectorProps = {
        onChange,
        isHideAgentUrl,
        adgroupInfo,
        marketingTargetId,
        url: value
    };
    return (
        <>
            <Input {...inputProps} />
            <SuspenseBoundary hideLoading>
                <UrlSelector {...urlSelectorProps} />
            </SuspenseBoundary>
        </>
    );
}

export default UrlDrawerEditor;