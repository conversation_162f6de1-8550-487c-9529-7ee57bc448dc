export interface UrlInfoProps {
    type?: string;
    ids: number[] | string[];
    onlineUrl?: object;
    channelId?: number;
    osTypeFromResponse?: string;
    groupPageList?: pageProps[];
    allPageList?: pageProps[];
};
export interface adgroupInfoProps {
    adgroupId?: number; // 编辑用
    channelId?: number; // 新建&编辑用，包括Android和iOS
    url?: string; // 编辑用
    osType?: string; // 新建&编辑用
    osTypeFromResponse?: string;
    marketingTargetId?: number;
};

export interface SinglePageProps {
    pageId: number | string;
    thumbnail: string;
    pageName: string;
    onlineUrl: string;
    editorUrl: string;
    platformId?: number;
    storeId?: unknown;
}
export interface pageProps extends SinglePageProps {
    // 落地页组才有的字段
    groupPageList?: SinglePageProps[];
}

export interface initPageInfoProps {
    urlType: string;
    pageId: number;
    groupPageList: SinglePageProps[];
};