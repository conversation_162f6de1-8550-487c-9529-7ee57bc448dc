import {Carousel} from '@baidu/one-ui';
import {chunk, isEmpty} from 'lodash-es';
import SuspenseBoundary, {useResource} from 'commonLibs/suspenseBoundary';
import {fetchPageOrPageGroupInfoByUrl} from '../api';
import {isSingleOrAgentPageUrlType, maxDisplayNum} from '../config';
import './style.less';

interface Props {
    url: string;
    marketingTargetId: number;
}

const UrlPreview = (props: Props) => {
    const {url, marketingTargetId} =  props;
    const [initPageInfo] = useResource(fetchPageOrPageGroupInfoByUrl, {url, marketingTargetId});
    if (!url || isEmpty(initPageInfo)) {
        return <div />;
    }
    const {urlType, pageId, pageName, thumbnail, groupPageList = []} = initPageInfo;
    const groupPageList_ = chunk(groupPageList, maxDisplayNum);
    if (isSingleOrAgentPageUrlType(urlType)) {
        return (
            <div className="url-preview-single-container">
                <div className="url-image">
                    <img src={thumbnail} className="url-image-preview" />
                </div>
                <div className="url-info">
                    <div className="url-title">{pageName}</div>
                    <div className="url-desc">ID：{pageId}</div>
                </div>
            </div>
        );
    }
    return (
        <div className="url-preview-group-container">
            <div className="url-title">{pageName}</div>
            <div className="url-desc">ID：{pageId}
                <span className="count">数量：{groupPageList.length}个</span>
            </div>
            <Carousel
                mode="single"
                className="image-carousel"
                sliderMode="dot"
                showButton
            >
                {
                    groupPageList_.map((groupPages, index1) => {
                        return (
                            <div key={index1} className="url-image-carousel">
                                {
                                    groupPages.map(item => {
                                        return (
                                            <div key={item.thumbnail} className="url-image">
                                                <img src={item.thumbnail} className="url-image-preview" />
                                            </div>
                                        );
                                    })
                                }
                            </div>
                        );
                    })
                }
            </Carousel>
        </div>
    );
};

export default props => (
    <SuspenseBoundary loading={{tip: '', size: 'small'}}>
        <UrlPreview {...props} />
    </SuspenseBoundary>
);
