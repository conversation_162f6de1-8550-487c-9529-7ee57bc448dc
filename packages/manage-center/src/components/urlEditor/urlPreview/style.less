.url-desc() {
    margin-top: @dls-padding-unit * 2;
    font-weight: @dls-font-weight-1;
    font-size: @dls-font-size-0;
    color: @dls-color-gray-7;
}

.url-title() {
    font-weight: @dls-font-weight-2;
    font-size: @dls-font-size-1;
    color: @dls-color-gray-9;
}

.url-image-preview() {
    width: 100%;
    height: @dls-padding-unit * 16;
    border-radius: @dls-border-radius-1;
    object-fit: contain;
}

.url-preview-group-container {
    border-radius: @dls-border-radius-1;
    background-color: @dls-color-gray-1;
    width: @dls-padding-unit * 120;
    padding: @dls-padding-unit * 3;
    margin-top: -@dls-padding-unit * 4;
    box-sizing: border-box;
    .url-title {
        .url-title();
    };
    .url-desc {
        .url-desc();
        margin-bottom: @dls-padding-unit * 3;
        .count {
            padding-left: @dls-padding-unit * 4;
        }
    }
    .url-image-carousel {
        display: flex !important;
        .url-image {
            border-radius: @dls-border-radius-1;
            background-color: @dls-color-gray-3;
            height: @dls-padding-unit * 16;
            width: @dls-padding-unit * 16;
            margin-right: @dls-padding-unit * 2;
            min-width: @dls-padding-unit * 16;
            .url-image-preview {
                .url-image-preview()
            };
        }
    }
}

.url-preview-single-container {
    border-radius: @dls-border-radius-1;
    background-color: @dls-color-gray-1;
    height: @dls-padding-unit * 17;
    width: @dls-padding-unit * 120;
    display: flex;
    .url-image {
        border-radius: @dls-border-radius-1;
        background-color: @dls-color-gray-3;
        height: @dls-padding-unit * 16;
        width: @dls-padding-unit * 16;
        .url-image-preview {
            .url-image-preview();
        }
    }
    .url-info {
        align-self: center;
        margin-left: @dls-padding-unit * 3;
        .url-title {
            .url-title()
        };
        .url-desc {
            .url-desc();
        };
    }
}