import classnames from 'classnames';
import {IconEye, IconEdit} from 'dls-icons-react';
import {Button, Checkbox} from '@baidu/one-ui';
import defaultPreviewImgSrc from 'app/resource/img/bot_preview.png';
import {SinglePageProps} from '../../type';

interface Props {
    page: SinglePageProps;
    isSelected: boolean;
    onClick: () => void;
    onPreview: () => void;
    disabled: boolean;
}

export function AgentPageItem({
    page, onClick, isSelected, onPreview, disabled
}: Props) {
    const {pageId, pageName, editorUrl = 'https://aiagent.baidu.com/mbot/index.html'} = page || {};
    const onEdit = () => {
        window.open(editorUrl, '_blank');
    };
    return (
        <div
            className={classnames({
                'single-page-item': true,
                'active': isSelected,
                'disabled': disabled
            })}
            onClick={onClick}
        >
            <div
                className="single-page-item-img-box"
                style={{
                    backgroundImage: `url(${defaultPreviewImgSrc})`
                }}
            >
                <Checkbox
                    value={pageId}
                    checked={isSelected}
                    className="single-page-item-checkbox"
                    disabled={disabled}
                />
            </div>
            <div className="single-page-item-info">
                <div className="single-page-item-name">
                    {pageName}
                </div>
                <div className="single-page-item-sub-info">
                    <div className="b2b-pages-id">
                        ID: {pageId}
                    </div>
                    <div className="b2b-pages-links">
                        <Button
                            className="b2b-pages-link"
                            type="text-strong"
                            onClick={onPreview}
                            size="small"
                        >
                            <IconEye />
                            <span className="b2b-pages-link-text">预览</span>
                        </Button>
                        <Button
                            className="b2b-pages-link-edit"
                            type="text-strong"
                            onClick={onEdit}
                            size="small"
                        >
                            <IconEdit />
                            <span className="b2b-pages-link-text">编辑</span>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
}
