import {Input, Select, Button} from '@baidu/one-ui';
import {IconSearch} from 'dls-icons-react';
import Tip from 'commonLibs/Tips';
import {SORT_TYPE_ENUM, SORT_CONFIG, SELECT_TYPE,
    SUPPORT_GROUP_CONFIG_ENUM, SUPPORT_GROUP_CONFIG} from '../../config';

const {Option} = Select;

const mapConfigToOption = ({key, label}) => {
    return <Select.Option key={key} value={key}>{label}</Select.Option>;
};

interface SelectFilterProps {
    selectType: string;
    setSelectType: (type: string) => void;
    searchInputValue: string;
    setSearchInputValue: (value: string) => void;
    sort: SORT_TYPE_ENUM;
    setSort: (value: SORT_TYPE_ENUM) => void;
    onSearch: (value: string) => void;
    isSupportPageGroup: SUPPORT_GROUP_CONFIG_ENUM;
    setIsSupportPageGroup: (value: SUPPORT_GROUP_CONFIG_ENUM) => void;
    showIsSupportPageGroup: boolean;
};

function Filter(props: SelectFilterProps) {
    const {
        selectType,
        setSelectType,
        searchInputValue,
        setSearchInputValue,
        onSearch,
        sort,
        setSort,
        isSupportPageGroup,
        showIsSupportPageGroup,
        setIsSupportPageGroup
    } = props;

    const sortSelectProps = {
        width: 166,
        value: sort,
        className: 'url-select-sort',
        onChange: setSort
    };

    const selectBeforeProps = {
        value: selectType,
        onChange: value => setSelectType(value)
    };

    const supportSelectProps = {
        width: 166,
        value: isSupportPageGroup,
        onChange: setIsSupportPageGroup
    };

    const selectBefore = (
        <Select {...selectBeforeProps} style={{width: 80, height: 32, cursor: 'pointer'}}>
            <Option value={SELECT_TYPE.NAME}>名称</Option>
            <Option value={SELECT_TYPE.ID}>ID</Option>
        </Select>
    );
    const onInputSearch = () => onSearch({
        value: searchInputValue,
        type: selectType
    });
    const inputProps = {
        placeholder: '请输入',
        width: 160,
        className: 'url-select-searchbox',
        value: searchInputValue,
        onChange: e => setSearchInputValue(e.value),
        onPressEnter: onInputSearch
    };

    return (
        <div className="url-select-filter">
            <Input.Group>
                <Input addonBefore={selectBefore} {...inputProps} />
                <Button type="primary" onClick={onInputSearch} icon={IconSearch} />
            </Input.Group>
            <Select {...sortSelectProps}>
                {
                    SORT_CONFIG.map(mapConfigToOption)
                }
            </Select>
            {showIsSupportPageGroup ? (
                <>
                    <Select {...supportSelectProps}>
                        {
                            SUPPORT_GROUP_CONFIG.map(mapConfigToOption)
                        }
                    </Select>
                    <span style={{marginRight: '4px'}}>
                        <Tip keyName="cpqlGroupPageUrlTypeInfo" />
                    </span>
                </>
            ) : null}
        </div>
    );
}

export default Filter;