import {<PERSON><PERSON>, <PERSON>} from '@baidu/one-ui';
import {IconExclamationCircleSolid} from 'dls-icons-react';
import {useURLFormatter} from 'commonLibs/hooks/url';
import {useUserInfo} from 'commonLibs/context/sharedInfo';

function getLinkUrl(isSinglePage: boolean, isAgentPage: boolean) {
    if (isSinglePage) {
        return {
            label: '去基木鱼新建落地页',
            url: 'https://wutong.baidu.com/platform/user/${optId}/mySite?ucUserId=${userId}'
        };
    }
    if (isAgentPage) {
        return {
            label: '去基木鱼新建商家智能体',
            url: 'https://aiagent.baidu.com/mbot/index.html'
        };
    }
    return {
        label: '去基木鱼新建落地页组',
        url: 'https://wutong.baidu.com/platform/user/${optId}/LpUpdate/smartPage/edit?ucUserId=${userId}'
    };
}

function Footer(props) {
    const {
        onOk,
        onCancel,
        maxSelectedCount,
        selectNum,
        isSinglePage,
        isAgentPage,
        saveLoading
    } = props;
    const useInfo = useUserInfo();
    const {label, url} = getLinkUrl(isSinglePage, isAgentPage);
    const linkUrl = useURLFormatter(url)(useInfo);
    return (
        <div className="url-select-footer">
            <span>
                <Button className="footer-item" type="primary" size="medium" onClick={onOk} loading={saveLoading}>
                    确定
                </Button>
                <Button className="footer-item" size="medium" onClick={onCancel}>取消</Button>
                <span className="footer-num">已选：{selectNum}/{maxSelectedCount}</span>
            </span>

            <span className="footer-new-link">
                <IconExclamationCircleSolid className="icon" />
                <span>未找到合适的？</span>
                <Link type="strong" className="link" target="_blank" toUrl={linkUrl}>{label}</Link>
            </span>
        </div>
    );
}

export default Footer;