import classnames from 'classnames';
import {IconEye, IconEdit} from 'dls-icons-react';
import {Button, Carousel} from '@baidu/one-ui';
import {chunk} from 'lodash-es';
import {pageProps} from '../../type';
import {maxImageDisplayNumOfGroup} from '../../config';

interface Props {
    page: pageProps;
    isSelected: boolean;
    onClick: () => void;
    onPreview: () => void;
}

export function GroupPageItem({
    page, onClick, isSelected, onPreview
}: Props) {
    const {pageId, pageName, editorUrl, groupPageList = []} = page || {};
    const onEdit = () => {
        window.open(editorUrl, '_blank');
    };
    const groupPageList_ = chunk(groupPageList, maxImageDisplayNumOfGroup);
    return (
        <div
            className={classnames({
                'group-page-item': true,
                'active': isSelected
            })}
            onClick={onClick}
        >
            <div
                className="group-page-item-img-box"
            >
                <div className='group-content'>
                    <div className="title">{pageName}</div>
                    <div className="group-page-item-info">
                        <div className="group-page-item-sub-info">
                            <div className="b2b-pages-id">
                                <div>ID: {pageId}</div>
                                <div className="count">数量: {groupPageList.length}</div>
                            </div>
                            <div className="b2b-pages-links">
                                <Button
                                    className="b2b-pages-link"
                                    type="text-strong"
                                    onClick={onPreview}
                                    size="small"
                                >
                                    <IconEye />
                                    <span className="b2b-pages-link-text">预览</span>
                                </Button>
                                <Button
                                    className="b2b-pages-link-edit"
                                    type="text-strong"
                                    onClick={onEdit}
                                    size="small"
                                >
                                    <IconEdit />
                                    <span className="b2b-pages-link-text">编辑</span>
                                </Button>
                            </div>
                        </div>
                    </div>
                    <Carousel
                        mode="single"
                        sliderMode="dot"
                        showButton
                    >
                        {
                            groupPageList_.map((groupPages, index1) => {
                                return (
                                    <div key={index1} className="content-image">
                                        {
                                            groupPages.map(item => {
                                                return (
                                                    <div
                                                        style={{
                                                            backgroundImage: `url(${item.thumbnail})`
                                                        }}
                                                        key={item.thumbnail}
                                                        className="image-item"
                                                    />
                                                );
                                            })
                                        }
                                    </div>
                                );
                            })
                        }
                    </Carousel>
                </div>
            </div>
        </div>
    );
}