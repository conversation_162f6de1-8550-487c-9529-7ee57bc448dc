import {Nav} from '@baidu/one-ui';
import {isJmyAdgroupBot} from 'commonLibs/utils/getFlag';
import MARKET_TARGET from 'commonLibs/config/marketTarget';
import {URL_TYPE_ENUM, getPageUrlType} from '../../config';
import {adgroupInfoProps} from '../../type';

interface HeaderProps {
    isHideAgentUrl?: boolean;
    adgroupInfo: adgroupInfoProps;
    isOnlySelectSingleUrl: boolean;
    value: string;
    onChange: (value: URL_TYPE_ENUM) => void;
};

const DATA_SOURCE = [
    {
        label: '落地页',
        key: URL_TYPE_ENUM.JMY_SINGLE
    },
    {
        label: '落地页组',
        key: URL_TYPE_ENUM.JMY_ADGROUP_APP
    }
];

const DATA_SOURCE_BOT = [
    {
        label: '商家智能体',
        key: URL_TYPE_ENUM.JMY_ADGROUP_BOT
    }
];

const DATA_SOURCE_SINGLE = [
    {
        label: '落地页',
        key: URL_TYPE_ENUM.JMY_SINGLE
    }
];

export function getHeaderData({
    isOnlySelectSingleUrl, adgroupInfo, isHideAgentUrl
}: {
    isOnlySelectSingleUrl: boolean;
    adgroupInfo: adgroupInfoProps;
}) {
    if (isOnlySelectSingleUrl) {
        return DATA_SOURCE_SINGLE;
    }
    if (adgroupInfo?.marketingTargetId === MARKET_TARGET.CPQL && !isHideAgentUrl) {
        return [...DATA_SOURCE, ...DATA_SOURCE_BOT];
    }
    return DATA_SOURCE;
}

function Header(props: HeaderProps) {
    const {
        adgroupInfo,
        isHideAgentUrl,
        value,
        isOnlySelectSingleUrl,
        onChange
    } = props;
    const navProps = {
        className: 'url-select-nav',
        value: getPageUrlType(value),
        onChange: e => {
            onChange(e.target.value);
        },
        dataSource: getHeaderData({isHideAgentUrl, isOnlySelectSingleUrl, adgroupInfo})
    };
    return <Nav {...navProps} />;
}

export default Header;