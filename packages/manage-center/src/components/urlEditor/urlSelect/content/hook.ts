import {useState} from 'react';
import {useTablePagination} from 'commonLibs/hooks/pagination';
import {SORT_TYPE_ENUM, SELECT_TYPE} from '../../config';

export function useUrlSelect() {
    const [searchInputValue, setSearchInputValue] = useState('');
    const [filter, setFilter] = useState({
        value: '',
        type: SELECT_TYPE.NAME
    });
    const [selectType, setSelectType] = useState(SELECT_TYPE.NAME);
    const [sort, setSort] = useState(SORT_TYPE_ENUM.EDIT_TIME_LATEST);
    const [pagination, paginationMethods] = useTablePagination();
    return [
        {
            filter,
            selectType,
            searchInputValue,
            sort,
            pagination
        },
        {
            setFilter,
            setSelectType,
            setSearchInputValue,
            setSort,
            paginationMethods
        }
    ];
}

