import {useState, useEffect, useCallback} from 'react';
import {useBoolean} from '@huse/boolean';
import {useRequest} from '@huse/request';
import {without, concat, uniqBy} from 'lodash-es';
import {Pagination, Empty, Loading, Button, Link} from '@baidu/one-ui';
import PagePreviewModal from '@baidu/m-vibranium-react/lib/components/pagePreview/pagePreviewModal';
import {toOneUIPaginationProps} from 'commonLibs/hooks/pagination';
import {useUrlSelect} from './hook';
import {fetchPageList} from '../../api';
import TipInfo from './tip';
import Footer from './footer';
import './style.less';
import {
    isSinglePageUrlType,
    isPageGroupUrlType,
    isAgentPageUrlType,
    isSingleOrAgentPageUrlType,
    SUPPORT_GROUP_CONFIG_ENUM,
    getMaxImgOfSinglePage
} from '../../config';
import {SinglePageItem, SinglePageCardItem} from './singlePageItem';
import {AgentPageItem} from './agentPageItem';
import {GroupPageItem} from './groupPageItem';
import Filter from './filter';
import {adgroupInfoProps, UrlInfoProps, pageProps} from '../../type';

interface Props {
    pageInfo: UrlInfoProps;
    adgroupInfo: adgroupInfoProps;
    onOk: () => void;
    onCancel: () => void;
    setPageInfo: (value: UrlInfoProps) => void;
    saveLoading: boolean;
    isSupportPageGroup: SUPPORT_GROUP_CONFIG_ENUM;
    setIsSupportPageGroup: (value: SUPPORT_GROUP_CONFIG_ENUM) => void;
    showIsSupportPageGroup: boolean;
    maxSelectedNum: number;
}
const EMPTY_PAGE_LIST: pageProps[] = [];
const EMPTY_PAGE_ID_LIST: number[] = [];
const getListId = page => page.pageId;

function EmptyForBot() {
    const linkUrl = 'https://aiagent.baidu.com/mbot/index.html';
    return (
        <div className="empty-for-bot">
            <Empty
                title={
                    <p>
                        还未创建属于你的商家智能体，
                        <Link type="strong" className="link" target="_blank" toUrl={linkUrl}>前往基木鱼新建</Link>
                    </p>
                }
                description=""
            />
        </div>
    );
}
const agentSearchUrl = 'isInPreviewIframe=1&preview=2&conversationFlowId=123&previewRobotType=agent';
function List(props: Props) {
    const {
        pageInfo,
        adgroupInfo,
        onOk,
        onCancel,
        showIsSupportPageGroup,
        isSupportPageGroup,
        maxSelectedNum,
        saveLoading,
        setPageInfo,
        setIsSupportPageGroup
    } = props;
    const {
        type: urlType, ids: pageIds = EMPTY_PAGE_ID_LIST, onlineUrl, allPageList: initialPageList = EMPTY_PAGE_LIST
    } = pageInfo;
    const isSinglePage = isSinglePageUrlType(urlType);
    const isAgentPage = isAgentPageUrlType(urlType);
    const isPageGroup = isPageGroupUrlType(urlType);
    const isSingleOrAgent = isSingleOrAgentPageUrlType(urlType);
    const isCanOnlySelectOnePage = maxSelectedNum === 1;
    const [
        {
            filter,
            searchInputValue,
            sort,
            pagination,
            selectType
        },
        {
            setFilter,
            setSearchInputValue,
            setSort,
            paginationMethods,
            setSelectType
        }
    ] = useUrlSelect();
    useEffect(() => {
        paginationMethods?.resetPageNo();
    }, [urlType]);
    const [previewVisible, {on: openPreview, off: closePreview}] = useBoolean(false);
    const [previewUrlList, setPreviewUrlList] = useState([]);
    const [onlineUrls, setOnlineUrls] = useState<Record<string, string>>({});
    const {data: allpages = {}, pending, error} = useRequest(fetchPageList, {
        adgroupInfo,
        sort,
        urlType,
        filter,
        isSupportPageGroup,
        ...pagination
    });
    const {
        totalNum: totalCount = 0, pageList, bindId, bindIdType,
        canSelectTotalNum, groupPageTotalNum
    } = allpages;
    useEffect(() => {
        // 落地页list接口中的返回值作为保存接口中参数
        setPageInfo({
            ...pageInfo,
            channelId: bindId,
            osTypeFromResponse: bindIdType
        });
    }, [urlType, pageIds, bindId, bindIdType, setPageInfo, onlineUrl]);
    const tipInfoProps = {
        showIsSupportPageGroup,
        canSelectTotalNum,
        groupPageTotalNum,
        maxSelectedNum,
        maxMultipleNum: getMaxImgOfSinglePage(adgroupInfo.marketingTargetId),
        totalCount,
        urlType
    };
    const paginationProps = toOneUIPaginationProps(
        {...pagination, ...paginationMethods}, totalCount, {showSizeChange: false, showPageJumper: false, showTotal: true}
    );

    const previewProps = {
        visible: previewVisible,
        onModalClose: closePreview,
        isShowPcLink: false, // 是否展示Pc
        isShowCopyLink: true, // 是否展示复制链接
        isShowPageSelect: true, // 是否展示多页选择
        isShowPageId: true, // 是否展示pageId
        previewText: '预览页面链接，不可用于投放，且仅做样式展示',
        pageList: previewUrlList,
        modalWidth: 640
    };
    const footerProps = {
        onOk,
        onCancel,
        isSinglePage,
        isAgentPage,
        maxSelectedCount: maxSelectedNum,
        selectNum: pageIds.length,
        saveLoading
    };

    // 清空所有已选页面
    const onDeleteAllSelectedList = useCallback(() => {
        // 被选过取消选中
        const emptyRes = {};
        setOnlineUrls(emptyRes);
        setPageInfo({
            ...pageInfo,
            type: urlType,
            ids: EMPTY_PAGE_ID_LIST,
            onlineUrl: emptyRes,
            allPageList: EMPTY_PAGE_LIST
        });
    }, [setOnlineUrls, urlType]);

    const onDeletePage = useCallback(pageId => {
        delete onlineUrls[pageId];
        setPageInfo({
            ...pageInfo,
            type: urlType,
            ids: without(pageIds, pageId),
            onlineUrl: onlineUrls
        });
    }, [pageIds, setPageInfo, urlType, onlineUrls]);

    const filterProps = {
        selectType,
        setSelectType,
        searchInputValue,
        setSearchInputValue,
        showIsSupportPageGroup,
        isSupportPageGroup,
        setIsSupportPageGroup: value => {
            setIsSupportPageGroup(value);
            onDeleteAllSelectedList();
        },
        onSearch: value => {
            setFilter(value);
            paginationMethods?.resetPageNo();
        },
        sort,
        setSort: value => {
            setSort(value);
            paginationMethods?.resetPageNo();
        }
    };

    return (
        <>
            <div className="url-select-content">
                <TipInfo {...tipInfoProps} />
                {!isAgentPage && <Filter {...filterProps} />}
                <div className="url-select-list-content">
                    {
                        pending ? (
                            <Loading size="medium" type="strong" className="content-empty-tip url-select-list-content-left" />
                        ) : (
                            !totalCount ? (
                                <div className="content-empty-tip url-select-list-content-left">
                                    {isAgentPage ? <EmptyForBot /> : <Empty />}
                                </div>
                            ) : (
                                <div className="list-container url-select-list-content-left">
                                    {
                                        pageList.map(page => {
                                            // 这边需要对urlType进行判断是展示单页的还是组的
                                            const {pageId, onlineUrl, groupPageList = [], pageName} = page || {};
                                            const isSelected = pageIds.includes(pageId);
                                            // eslint-disable-next-line max-len
                                            const disabled = !isCanOnlySelectOnePage && pageIds.length >= maxSelectedNum && !isSelected;
                                            const onClick = () => {
                                                if (disabled) {
                                                    return;
                                                }
                                                if (isSelected) { // 被选过取消选中
                                                    onDeletePage(pageId);
                                                } else { // 选中
                                                    onlineUrls[pageId] = onlineUrl;
                                                    setOnlineUrls(onlineUrls);
                                                    setPageInfo({
                                                        ...pageInfo,
                                                        type: urlType,
                                                        ids: isCanOnlySelectOnePage ? [pageId] : [...pageIds, pageId],
                                                        onlineUrl: onlineUrls,
                                                        // eslint-disable-next-line max-len
                                                        allPageList: isPageGroup ? initialPageList : uniqBy(concat(initialPageList, page), getListId)
                                                    });
                                                }
                                            };
                                            const onPreview = e => {
                                                const previewUrls = isSinglePage
                                                    ? [{id: pageId, previewUrl: onlineUrl, name: pageName}]
                                                    : isAgentPage
                                                        ? [{
                                                            id: pageId,
                                                            // eslint-disable-next-line max-len
                                                            previewUrl: `${onlineUrl}${onlineUrl.includes('?') ? '&' : '?'}${agentSearchUrl}`,
                                                            name: pageName
                                                        }]
                                                        : groupPageList.map(groupPage => ({
                                                            id: groupPage.pageId,
                                                            previewUrl: groupPage.onlineUrl,
                                                            name: groupPage.pageName
                                                        }));
                                                setPreviewUrlList(previewUrls);
                                                openPreview();
                                                e.stopPropagation();
                                            };
                                            if (isSinglePage) {
                                                return (
                                                    <SinglePageItem
                                                        page={page}
                                                        isSelected={isSelected}
                                                        onClick={onClick}
                                                        onPreview={onPreview}
                                                        key={pageId}
                                                        disabled={disabled}
                                                    />
                                                );
                                            }
                                            if (isAgentPage) {
                                                return (
                                                    <AgentPageItem
                                                        page={page}
                                                        isSelected={isSelected}
                                                        onClick={onClick}
                                                        onPreview={onPreview}
                                                        key={pageId}
                                                        disabled={disabled}
                                                    />
                                                );
                                            }
                                            return (
                                                <GroupPageItem
                                                    page={page}
                                                    isSelected={isSelected}
                                                    onClick={onClick}
                                                    onPreview={onPreview}
                                                    key={pageId}
                                                />
                                            );
                                        })
                                    }
                                </div>
                            )
                        )
                    }
                    {!isCanOnlySelectOnePage && <div className="url-select-list-content-right">
                        <div className="url-select-list-content-right-header">
                            <span>已选({pageIds.length}/{maxSelectedNum})</span>
                            <Button type="text-strong" onClick={onDeleteAllSelectedList}>清空</Button>
                        </div>
                        <div className="url-select-list-content-right-list">
                            {initialPageList.filter(v => pageIds.includes(v.pageId)).map(
                                page => <SinglePageCardItem key={page.pageId} page={page} onDelete={onDeletePage} />
                            )}
                        </div>
                    </div>}
                </div>
                {totalCount ? <Pagination className="url-select-content-pagination" {...paginationProps} /> : null}
            </div>
            <Footer {...footerProps} />
            <PagePreviewModal {...previewProps} />
        </>

    );
};
export default List;