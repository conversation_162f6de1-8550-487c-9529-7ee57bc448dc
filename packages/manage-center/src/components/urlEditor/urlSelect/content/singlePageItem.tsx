import classnames from 'classnames';
import {useCallback} from 'react';
import {IconEye, IconEdit, IconTimes} from 'dls-icons-react';
import {Button, Checkbox} from '@baidu/one-ui';
import {giveMeShortcuts} from 'commonLibs/utils/handleOptions';
import defaultPreviewImgSrc from 'app/resource/img/store_preview.png';
import {pageProps} from '../../type';

interface Props {
    page: pageProps;
    isSelected: boolean;
    onClick: () => void;
    onPreview: () => void;
    disabled: boolean;
}

const [_list, {getLabelByKey}] = giveMeShortcuts([
    {key: 1, label: '营销页'},
    {key: 3, label: '店铺页'}
]);

export function SinglePageItem({
    page, onClick, isSelected, onPreview, disabled
}: Props) {
    const {pageId, storeId, thumbnail, pageName, editorUrl} = page || {};
    const pageTypeLabel = getLabelBy<PERSON><PERSON>(page.platformId) || '';
    const onEdit = () => {
        window.open(editorUrl, '_blank');
    };
    return (
        <div
            className={classnames({
                'single-page-item': true,
                'active': isSelected,
                'disabled': disabled
            })}
            onClick={onClick}
        >
            <div
                className="single-page-item-img-box"
                style={{
                    backgroundImage: `url(${thumbnail ?? defaultPreviewImgSrc})`
                }}
            >
                <Checkbox
                    value={pageId}
                    checked={isSelected}
                    className="single-page-item-checkbox"
                    disabled={disabled}
                />
            </div>
            <div className="single-page-item-info">
                <div className="single-page-item-name">
                    {pageName}{pageTypeLabel ? `(${pageTypeLabel})` : ''}
                </div>
                <div className="single-page-item-sub-info">
                    <div className="b2b-pages-id">
                        ID: {storeId ?? pageId}
                    </div>
                    <div className="b2b-pages-links">
                        <Button
                            className="b2b-pages-link"
                            type="text-strong"
                            onClick={onPreview}
                            size="small"
                        >
                            <IconEye />
                            <span className="b2b-pages-link-text">预览</span>
                        </Button>
                        <Button
                            className="b2b-pages-link-edit"
                            type="text-strong"
                            onClick={onEdit}
                            size="small"
                        >
                            <IconEdit />
                            <span className="b2b-pages-link-text">编辑</span>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
}


export function SinglePageCardItem({
    page, onDelete
}) {
    const {pageId, thumbnail, pageName, platformId} = page || {};
    const onClickDelete = useCallback(() => {
        onDelete(pageId);
    }, [onDelete, pageId]);
    return (
        <div className="single-page-card-item">
            <img
                className="single-page-item-img"
                src={thumbnail ?? defaultPreviewImgSrc}
            />
            <div className="single-page-item-info">
                <div className="single-page-item-info-name">{pageName}</div>
                <div className="single-page-item-info-type">
                    {getLabelByKey(platformId) || ''}
                </div>
            </div>
            <div className="single-page-item-button">
                <IconTimes onClick={onClickDelete} />
            </div>
        </div>
    );
}
