.single-page-item {
    height: 296px;
    display: flex;
    flex-direction: column;
    width: 140px;
    background-color: @dls-color-gray-0;
    border: 1px solid @dls-color-gray-2;
    border-radius: @dls-border-radius-1;
    overflow: hidden;
    cursor: pointer;
    margin-right: @dls-padding-unit * 4;
    margin-bottom: @dls-padding-unit * 4;
    transition: all 0.2s ease;
    .single-page-item-img-box {
        position: relative;
        user-select: none;
        height: 230px;
        background-size: cover;
        background-position: center;
    }
    .single-page-item-checkbox {
        position: absolute;
        top: @dls-padding-unit * 2;
        left: @dls-padding-unit * 2;
        line-height: 1; // 用于取消默认高度，不然间距不对
    }
    .single-page-item-info {
        // height: 66px;
        padding: @dls-padding-unit * 3;
        box-sizing: border-box;
        line-height: @dls-line-height-1;
        font-size: @dls-font-size-0;
    }
    .single-page-item-name {
        color: @dls-color-gray-11;
        font-weight: @dls-font-weight-2;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .single-page-item-sub-info {
        margin-top: @dls-padding-unit * 2;
        color: @dls-color-gray-7;
    }

    &.disabled {
        .single-page-item-img-box,
        .single-page-item-name,
        .single-page-item-sub-info {
            cursor: not-allowed;
            opacity: 0.5;
        }
    }
    &.active,
    &:hover {
        border: 1px solid @dls-color-gray-4;
        &.disabled {
            .single-page-item-sub-info {
                cursor: auto;
                opacity: 1;
            }
        }
    }
    &.active {
        border: 1px solid @dls-color-brand-7;
    }

    .b2b-pages-links {
        display: none;
        align-items: center;
        padding: @dls-padding-unit 0;
        .b2b-pages-link-edit {
            margin-left: @dls-padding-unit * 3;
        }
        .b2b-pages-link-text {
            margin-left: @dls-padding-unit;
        }
    }

    .b2b-pages-id {
        display: flex;
        align-items: center;
        padding: @dls-padding-unit 0;
    }

    &:hover {
        .b2b-pages-links {
            display: flex;
        }
        .b2b-pages-id {
            display: none;
        }
    }
}

.single-page-card-item {
    height: 50px;
    display: flex;
    flex-direction: row;
    width: 152px;
    align-items: center;
    justify-content: space-between;
    .single-page-item-img {
        flex-shrink: 0;
        height: 50px;
        width: 50px;
        background-size: cover;
        background-position: center;
    }
    .single-page-item-info {
        margin: 0 @dls-padding-unit * 2;
        overflow: hidden;
        flex-grow: 1;
        &-name {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
        &-type {
            color: @dls-color-gray-7;
        }
    }
    .single-page-item-button {
        svg {
            color: @dls-color-gray-7;
            cursor: pointer;
        }
    }
}

.group-page-item {
    height: @dls-padding-unit * 54;
    display: flex;
    flex-direction: column;
    width: @dls-padding-unit * 63;
    background-color: @dls-color-gray-0;
    border: 1px solid @dls-color-gray-2;
    border-radius: @dls-border-radius-1;
    overflow: hidden;
    cursor: pointer;
    margin-right: @dls-padding-unit * 4;
    margin-bottom: @dls-padding-unit * 4;
    transition: all 0.2s ease;
    .group-page-item-img-box {
        position: relative;
        user-select: none;
        height: @dls-padding-unit * 57.5;
        background-size: cover;
        background-position: center;
        padding: @dls-padding-unit * 3;
        .group-content {
            .title {
                color: @dls-color-gray-9;
                font-weight: @dls-font-weight-2;
                font-size: @dls-font-size-1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .content-image {
                display: flex !important;
                margin-top: @dls-padding-unit * 2;
                .image-item {
                    border: 1px solid @dls-color-gray-2;
                    position: relative;
                    user-select: none;
                    height: @dls-padding-unit * 30;
                    width: @dls-padding-unit * 16;
                    min-width: @dls-padding-unit * 16;
                    background-size: cover;
                    background-position: center;
                    margin-right: @dls-padding-unit * 2;
                }
            }
        }
    }
    .group-page-item-info {
        font-size: @dls-font-size-0;
        box-sizing: border-box;
        line-height: @dls-line-height-1;
    }
    .group-page-item-sub-info {
        font-size: @dls-font-size-0;
        margin-top: @dls-padding-unit * 2;
        color: @dls-color-gray-7;
    }

    &.disabled {
        .group-page-item-img-box,
        .group-page-item-sub-info {
            cursor: not-allowed;
            opacity: 0.5;
        }
    }
    &.active,
    &:hover {
        border: 1px solid @dls-color-gray-4;
        &.disabled {
            .group-page-item-sub-info {
                cursor: auto;
                opacity: 1;
            }
        }
    }
    &.active {
        border: 1px solid @dls-color-brand-7;
    }

    .b2b-pages-links {
        display: none;
        align-items: center;
        padding: @dls-padding-unit 0;
        .b2b-pages-link-edit {
            margin-left: @dls-padding-unit * 3;
        }
        .b2b-pages-link-text {
            margin-left: @dls-padding-unit;
        }
    }

    .b2b-pages-id {
        display: flex;
        align-items: center;
        padding: @dls-padding-unit 0;
        .count {
            margin-left: @dls-padding-unit * 3;
        }
    }

    &:hover {
        .b2b-pages-links {
            display: flex;
        }
        .b2b-pages-id {
            display: none;
        }
    }
}
.empty-for-bot {
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(100vh);
    width: 100%;
}