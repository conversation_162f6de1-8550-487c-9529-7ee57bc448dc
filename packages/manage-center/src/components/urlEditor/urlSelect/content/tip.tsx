import {useMemo} from 'react';
import {Alert} from '@baidu/one-ui';
import {URL_TYPE_ENUM, isSinglePageUrlType} from '../../config';

interface TipInfoProps {
    totalCount: number;
    maxSelectedNum: number; // 最大选择数量
    maxMultipleNum: number;
    // 可参与投放的落地页数量
    canSelectTotalNum?: number;
    // 支持打包成组投放的落地页数量
    groupPageTotalNum?: number;
    urlType: URL_TYPE_ENUM;
    showIsSupportPageGroup: boolean;
};


function TipInfo(props: TipInfoProps) {
    const {
        showIsSupportPageGroup,
        canSelectTotalNum,
        groupPageTotalNum,
        totalCount,
        urlType,
        maxSelectedNum,
        maxMultipleNum
    } = props;
    const isSinglePage = isSinglePageUrlType(urlType);

    const tipTextArr = useMemo(() => {
        const res: string[] = [];
        const label = `落地页${isSinglePage ? '' : '组'}`;
        if (canSelectTotalNum ?? totalCount) {
            res.push(`共${canSelectTotalNum ?? totalCount}个可参与投放的${label}`);
        }
        if (isSinglePage) {
            if (maxMultipleNum === 1) {
                res.push('仅可单选投放');
            } else if (showIsSupportPageGroup) {
                res.push(...[
                    `${groupPageTotalNum}个支持程序化优选`,
                    `最多可在其中选择${maxMultipleNum}个落地页打包成组投放；${canSelectTotalNum - groupPageTotalNum}个不支持程序化优选`,
                    '仅可单选投放'
                ]);
            } else {
                res.push(`支持最多选择${maxSelectedNum}个落地页打包成组投放`);
            }
        }
        return res;
    }, [totalCount, isSinglePage, maxSelectedNum, maxMultipleNum, canSelectTotalNum,
        groupPageTotalNum, showIsSupportPageGroup]);

    return totalCount ? (
        <Alert
            type="info"
            showIcon
            className="tip-container"
            content={tipTextArr.join('，')}
        />
    ) : null;
}

export default TipInfo;