import {useCallback} from 'react';
import Header from './content/header';
import List from './content/list';
import {UrlInfoProps, adgroupInfoProps} from '../type';
import {SUPPORT_GROUP_CONFIG_ENUM} from '../config';
import './style.less';

interface UrlSelectProps {
    isHideAgentUrl?: boolean;
    pageInfo: UrlInfoProps;
    setPageInfo: (value: UrlInfoProps) => void;
    adgroupInfo: adgroupInfoProps;
    onOk: () => void;
    onCancel: () => void;
    saveLoading: boolean;
    isSupportPageGroup: SUPPORT_GROUP_CONFIG_ENUM;
    setIsSupportPageGroup: (value: SUPPORT_GROUP_CONFIG_ENUM) => void;
    showIsSupportPageGroup: boolean;
    isOnlySelectSingleUrl: boolean;
    maxSelectedNum: number;
};

function UrlSelect(props: UrlSelectProps) {
    const {
        isHideAgentUrl,
        pageInfo,
        setPageInfo,
        adgroupInfo,
        onOk,
        onCancel,
        saveLoading,
        isOnlySelectSingleUrl,
        isSupportPageGroup,
        setIsSupportPageGroup,
        showIsSupportPageGroup,
        maxSelectedNum
    } = props;
    const {type: urlType} = pageInfo;

    const listProps = {
        showIsSupportPageGroup,
        isSupportPageGroup,
        maxSelectedNum,
        pageInfo,
        adgroupInfo,
        saveLoading,
        onOk,
        onCancel,
        setIsSupportPageGroup,
        setPageInfo
    };
    const setUrlType = useCallback(type => {
        setPageInfo({
            type,
            ids: [],
            allPageList: [],
            groupPageList: [],
            onlineUrl: {}
        });
    }, [setPageInfo]);

    const headerProps = {
        adgroupInfo,
        isHideAgentUrl,
        isOnlySelectSingleUrl,
        value: urlType,
        onChange: setUrlType
    };
    return (
        <div className="url-select-container">
            <Header {...headerProps} />
            <List {...listProps} />
        </div>
    );
}

export default UrlSelect;