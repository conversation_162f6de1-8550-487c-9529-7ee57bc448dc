.url-select-container {
    height: 100%;
    .url-select-nav {
        font-size: @dls-font-size-3;
    }
    .content-empty-tip {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .url-select-footer {
        padding: @dls-padding-unit * 6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0px 16px 24px 2px rgba(0, 0, 0, 0.06), 0px 6px 30px 5px rgba(0, 0, 0, 0.05), 0px 8px 10px -5px rgba(0, 0, 0, 0.04);
        .footer-item {
            margin-right: @dls-padding-unit * 3;
        }
        .icon {
            margin-right: @dls-padding-unit;
        }
        .footer-new-link, .footer-num {
            color: @dls-color-gray-7;
            font-size: @dls-font-size-1;
        }
        .footer-num {
            margin-top: @dls-padding-unit * 0.5;
        }
    }
}

.url-select-container .url-select-content {
    height: calc(100% - 176px);
    background-color: @dls-color-gray-1;
    padding: @dls-height-unit * 4;
    background-color: @dls-color-gray-0;
    border-radius: @dls-border-radius-2;
    display: flex;
    flex-direction: column;
    .url-select-filter {
        display: flex;
        align-items: center;
        margin-bottom: @dls-padding-unit * 2;
        .url-select-sort {
            margin: 0 @dls-padding-unit * 2;
        }
    }
    .tip-container {
        margin-bottom: @dls-padding-unit * 2;
    }
    .url-select-list-content {
        height: calc(100% - 148px);
        display: flex;
        width: 100%;
        padding: @dls-padding-unit 0;
        &-left, &-right {
            height: 100%;
        }   
        &-left {
            flex-grow: 1;
        }
        &-right {
            flex-basis: 160px;
            flex-shrink: 0;
            font-size: @dls-font-size-1;
            border: 1px solid @dls-color-gray-2;
            border-radius: @dls-border-radius-1;
            padding: @dls-padding-unit * 2;
            &-header {
                display: flex;
                justify-content: space-between;
                margin-bottom: @dls-padding-unit * 2;
            }
            &-list {
                overflow-y: auto;
                height: calc(100% - 42px);
                .single-page-card-item {
                    margin-bottom: @dls-padding-unit * 4;
                }
            }
        }
        .list-container {
            overflow-y: auto;
            display: inline-flex;
            flex-wrap: wrap;
        }
    }
    &-pagination {
        margin: @dls-padding-unit * 4 0;
    }
}