import {CPQL} from 'commonLibs/config/marketTarget';
import platFormKey from 'commonLibs/config/platform';
import {UrlInfoProps, adgroupInfoProps} from './type';

export interface props {
    pageInfo: UrlInfoProps;
    adgroupInfo: adgroupInfoProps;
};

function normalizePageInfo({allPageList = [], ids = []}: UrlInfoProps) {
    return allPageList
        .filter(page => ids.includes(page.pageId))
        .map(page => {
            if (page.platformId === platFormKey.MEDICAL) {
                return {...page, shopId: page.storeId};
            }
            return page;
        });
}

export const getSaveParams = ({pageInfo, adgroupInfo}: props) => {
    const {ids} = pageInfo;
    const {channelId, osTypeFromResponse, marketingTargetId} = adgroupInfo;
    if (marketingTargetId === CPQL) {
        return {
            pageInfoList: normalizePageInfo(pageInfo)
        };
    }
    return {
        pageList: ids,
        bindId: channelId,
        bindIdType: osTypeFromResponse
    };
};
