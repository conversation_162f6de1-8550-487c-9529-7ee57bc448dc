import {AdLaunchProcessData} from 'app/api/helper';
import {formatToPercentageWithOutSingle} from 'app/containers/project/fcNewHelper/utils';

export const closeTooltip = '您将关闭卡片，后续可在项目列表体验全链智能挖潜能力';

export enum tipTagTypeEnum {
    high = 1,
    middle = 2,
    lower = 3
}

const aboutMap = {
    [tipTagTypeEnum.high]: '存在优化空间', // 黑
    [tipTagTypeEnum.middle]: '有扩量潜力，建议优化', // 黄
    [tipTagTypeEnum.lower]: '高扩量潜力，急需优化'// 红
};
function getRank(industryRank: number = 0) {
    return (
        <>
            {industryRank === 0 ? '数据不足' : (
                <>
                    同行业：{industryRank < 0 ? '后' : '前'}
                    {formatToPercentageWithOutSingle(Math.abs(industryRank))}
                </>
            )}
        </>
    );
}
function getAbout(level: number) {
    return aboutMap[level] || '暂无优化建议';
}

export const getGuideSteps = (overviewData: AdLaunchProcessData = {} as AdLaunchProcessData) => {
    return [
        {
            title: '定向覆盖',
            description: '基于业务点呈现用户热搜词、高消费高转化榜单词，同时也可进行海量词包下载',
            rank: getRank(overviewData.adCoverage?.industryRank),
            about: getAbout(overviewData.adCoverage?.level),
            level: overviewData.adCoverage?.level
        },
        {
            title: '定向筛选',
            description: '基于撞线频次和预估流量损失，呈现待优化预算的关键计划，针对性调优',
            rank: getRank(overviewData.targetingPassRate?.industryRank),
            about: getAbout(overviewData.targetingPassRate?.level),
            level: overviewData.targetingPassRate?.level
        },
        {
            title: '预算余额',
            description: '洞察预算缺口,针对高频撞线计划进行预算优化,提升预算利用率',
            rank: getRank(overviewData.budgetPassRate?.industryRank),
            about: getAbout(overviewData.budgetPassRate?.level),
            level: overviewData.budgetPassRate?.level
        },
        {
            title: '质量准入',
            description: '基于漏斗数据，快速定位待优化相关性的关键词与落地页，调优以提升广告吸引力',
            rank: getRank(overviewData.qualityAdmissionPassRate?.industryRank),
            about: getAbout(overviewData.qualityAdmissionPassRate?.level),
            level: overviewData.qualityAdmissionPassRate?.level
        },
        {
            title: '竞争胜出',
            description: '提升出价、点击、转化各环节的竞争力，获取更多转化',
            rank: getRank(overviewData.competitionPassRate?.industryRank),
            about: getAbout(overviewData.competitionPassRate?.level),
            level: overviewData.competitionPassRate?.level
        }
    ];
};
