import {useState, useCallback, useImperativeHandle, forwardRef, Ref} from 'react';
import classnames from 'classnames';
import {Dialog, Tooltip, Popover} from '@baidu/one-ui';
import {Button} from '@baidu/light-ai-react';
import {IconCompassSolid, IconTimes} from 'dls-icons-react';
import {UserReturnInfoDataProps, DialogHandler} from './type';
import {closeTooltip, getGuideSteps} from './config';
import './style.less';
import stepIcon from './step.svg';
import BaseTag from 'app/containers/project/fcNewHelper/components/BaseTag';
import {levelEnum} from 'app/api/helper';
import {useUserInfo} from 'commonLibs/context/sharedInfo';
import {sendFinalDialogMonitor} from 'commonLibs/utils/copilotMonitor';


const defaultDialogProps = {
    className: 'user-return-info-dialog-wrapper',
    contentClassName: 'user-return-info-dialog-content',
    width: 882,
    height: 337,
    footer: [],
    destroyOnClose: true,
    needCloseIcon: false,
    title: (
        <div className="title-with-icon-content">
            <IconCompassSolid />
            智能助手为您制定复投方案
        </div>
    )
};

export default forwardRef(function UserReturnInfoDialog(props, ref: Ref<DialogHandler>) {
    const {overviewData} = props || {};
    const [data, setData] = useState(null);
    const open = useCallback(newData => setData(newData), []);
    const close = useCallback(() => setData(null), []);
    useImperativeHandle(ref, () => ({open, close}), [open, close]);

    const {userId} = useUserInfo();

    const {
        recordCallback, guideCallback
    } = (data || {}) as UserReturnInfoDataProps;

    const onCancel = useCallback(() => {
        sendFinalDialogMonitor({item: '季末弹窗关闭'});
        close();
        guideCallback?.();
    }, [close, guideCallback]);

    const goToCopilot = useCallback(() => {
        sendFinalDialogMonitor({item: '前往开启季末扩量'});
        location.href = `/fc/managecenter/dashboard/projects/user/${userId}#from=finalDialog`;
        close();
        recordCallback?.();
        guideCallback?.();
    }, [close, recordCallback, guideCallback]);

    const dialogProps = {
        ...defaultDialogProps,
        visible: !!data,
        onCancel
    };

    function PopoverContent({title, desc}) {
        return (
            <div className="return-dialog-popover-content">
                <div className="title">{title}</div>
                <div className="content">{desc}</div>
            </div>
        );
    }
    return (
        <Dialog {...dialogProps}>
            <Tooltip type="dark" title={closeTooltip}>
                <IconTimes className="dialog-close" onClick={onCancel} />
            </Tooltip>
            <div className="summary-title-info">
                <div className="title-introduction">
                    <div>
                        智能助手推出了全链智能挖潜能力，助您定位投放薄弱环节，结合针对性的优化建议提升投放竞争力，获取增量转化
                    </div>
                </div>
            </div>
            <div className="main">
                <div className="steps-content">
                    <div className="steps-info">
                        <div className="horizontal-steps-content">
                            {getGuideSteps(overviewData).map((item, index) => {
                                const {title, description, level, about, rank} = item;
                                return (
                                    <Popover
                                        key={index}
                                        content={<PopoverContent title={title} desc={description} />}
                                        placement='top'
                                    >
                                        <div className="content-wrapper" key={index}>
                                            <div className="label">
                                                <div className="title">{title}</div>
                                                <BaseTag tipTagType={level} className='label-base-tag' />
                                            </div>
                                            <div className="step-icon">
                                                <img src={stepIcon} />
                                            </div>
                                            {about && (
                                                <div className={classnames('system-about', {
                                                    'user-final-color-red': level === levelEnum.LOW,
                                                    'user-final-color-yellow': level === levelEnum.MIDDLE,
                                                    'user-final-color-grey': level === levelEnum.HIGH
                                                })}
                                                >
                                                    {about}
                                                </div>
                                            )}
                                            <div className="rank">
                                                {rank}
                                            </div>
                                        </div>
                                    </Popover>
                                );
                            })}
                        </div>
                    </div>
                </div>
            </div>
            <div className="footer">
                <Button variant="primary" onClick={goToCopilot} className='footer-btn-position'>
                    前往开启季末扩量
                </Button>
            </div>
        </Dialog>
    );
});
