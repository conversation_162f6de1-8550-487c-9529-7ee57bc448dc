.user-return-info-dialog-wrapper {
    .user-return-info-dialog-content {
        position: relative;
        padding: @dls-padding-unit * 6 @dls-padding-unit * 8 @dls-padding-unit * 8 @dls-padding-unit * 8;
        border-radius: @dls-padding-unit * 4;
        background-image: url(app/images/userReturnInfoDialog/bg.png);
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        .one-dialog-header {
            margin-bottom: @dls-padding-unit * 4;
        }
        .one-dialog-body {
            overflow: hidden;
        }
        .title-with-icon-content {
            color: @dls-color-brand-7;
            font-size: @dls-font-size-3;
            font-weight: @dls-font-weight-2;
            display: flex;
            align-items: center;
            gap: 8px;
            svg {
                width: @dls-padding-unit * 4.5;
                height: @dls-padding-unit * 4.5;
            }
        }
        .title-introduction {
            display: flex;
            list-style: disc;
        }
        .dialog-close {
            position: absolute;
            top: @dls-padding-unit * 3;
            right: @dls-padding-unit * 3;
            font-size: @dls-font-size-0;
            width: @dls-padding-unit * 4;
            height: @dls-padding-unit * 4;
            color: @dls-color-gray-7;
            cursor: pointer;
        }
        .summary-title-info {
            font-size: @dls-font-size-1;
            line-height: @dls-padding-unit * 6.5;
            color: @dls-color-gray-9;
            margin-top: @dls-padding-unit * 8;
        }
        .main {
            margin-top: @dls-padding-unit * 4;
            display: flex;
            gap: @dls-padding-unit * 2;
            .vertical-steps {
                display: flex;
                width: @dls-padding-unit * 7;
                height: @dls-padding-unit * 25.5;
                flex-direction: column;
                align-items: center;
                gap: @dls-padding-unit * 2;
                flex-shrink: 0;
            }
            .steps-content {
                color: #191b1e;
                font-size: @dls-font-size-1;
                .steps-title {
                    font-weight: @dls-font-weight-2;
                    line-height: @dls-padding-unit * 5;
                    margin-top: @dls-padding-unit;
                }
                .steps-info {
                    line-height: @dls-padding-unit * 6;
                    margin-top: @dls-padding-unit * 0.5;
                    .bold {
                        font-weight: @dls-font-weight-2;
                    }
                }
            }
            .horizontal-steps {
                display: flex;
                align-items: center;
                gap: @dls-padding-unit * 2;
                margin-left: @dls-padding-unit * 16;
                margin-right: @dls-padding-unit * 16;
                margin-top: @dls-padding-unit * 3;
                margin-bottom: @dls-padding-unit * 2;
            }
            .horizontal-steps-content {
                color: #191b1e;
                font-size: @dls-font-size-0;
                display: flex;
                gap: 12px;
                .content-wrapper {
                    cursor: pointer;
                    box-sizing: border-box;
                    width: 154px;
                    height: 95px;
                    background: linear-gradient(180deg, rgba(235, 242, 255, 0.6) 0%, rgba(235, 242, 255, 0.3) 100%);
                    padding: 8px;
                    border-radius: 6px;
                    position: relative;
                    .label {
                        display: flex;
                        color: #191b1e;
                        font-size: 14px;
                        font-weight: 500;
                        line-height: 20px;
                        .label-base-tag {
                            margin-left: 4px;
                        }
                    }
                    .step-icon {
                        height: 4px;
                        transform: translateY(-12px);
                    }
                    .system-about {
                        line-height: 16px;
                        transform: translateY(12px);
                    }
                    .rank {
                        position: absolute;
                        bottom: 8px;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 16px;
                        color: #545b66;
                    }
                    .description {
                        margin-top: @dls-padding-unit * 2;
                        color: @dls-color-gray-7;
                        line-height: @dls-padding-unit * 5.5;
                        text-align: center;
                    }
                    &:nth-child(2),
                    &:nth-child(3) {
                        width: @dls-padding-unit * 39;
                    }
                }
            }
        }
        .footer {
            margin-top: @dls-padding-unit * 6;
            display: flex;
            justify-content: center;
        }
    }
}

.return-dialog-popover-content {
    width: 240px;
    .title {
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        margin-bottom: 8px;
    }
    .content {
        font-size: 12px;
        font-weight: 400;
        line-height: 22px;
    }
}

.user-final-color-red {
    font-weight: 500;
    color: #f53f3f;
}
.user-final-color-yellow {
    font-weight: 500;
    color: #f27318;
}
.user-final-color-grey {
    font-weight: 500;
    color: #282c33;
}
