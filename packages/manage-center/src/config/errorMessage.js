// from asser-center errorMessage
import getErrorMessage, {unLocationField, getCommonTips} from 'commonLibs/utils/getErrorMessage';
import {getErrorTextByCodeCustom} from 'commonLibs/utils/getErrorTextByCode';

export const handleNumber = number => (number == null ? '' : `：${number}个`);

const CAMPAIGN_NEGATIVE_MAX_NUMBER_FUN = number =>
    `当前计划的短语否定关键词数量达到上限${handleNumber(number)}，无法添加否定关键词，请删除部分否定关键词后添加`;
const CAMPAIGN_EXACT_NEGATIVE_MAX_NUMBER_FUN = number =>
    `当前计划的精确否定关键词数量达到上限${handleNumber(number)}，无法添加否定关键词，请删除部分否定关键词后添加`;

const specialWordsError = '添加的否定关键词包含特殊字符，无法保存，请及时调整';
const exactWordsError = '添加的精确否定关键词包含特殊字符，无法保存，请及时调整';

export const CAMPAIGN_NAME_EXISTED = '当前计划名称已存在';
export const CONSULT_URL_FORMAT_ERROR = '网址格式有误，请输入以http://或https://开头的网址';
export const XST_BIND_RELATION_RELATION = '计划绑定有误，请重新选择。';
export const NAVIGATE_ERROR = '另一匹配模式下已经有相同的否定关键词';
// 主体不一致的文案
export const DOMAIN_TEXT = '推广网址所属的账户公司名称，同您账户的公司名称不一致，您可以选择已有网址或联系您的推广顾问';
export const urlTip = '此账户为托管账户，请填写正确的托管页网址进行投放。如对账户托管要求有疑问，请联系推广顾问反馈。';

const getCampaignNumOverFlow = (num = '') => `当前账户的计划数量达到上限：${num}个，无法添加计划，请删除部分计划后添加`;

const ALL_LEVEL_NEGATIVE_WORDS_ERROR = '添加的短语否定关键词字面与已有的智能、短语、精确匹配关键词重复，无法保存，请及时调整';
const ALL_LEVEL_EXACT_NEGATIVE_WORDS_ERROR = '添加的精确否定关键词字面与已有的智能、短语、精确匹配关键词重复，无法保存，请及时调整';

const ADGROUP_NEGATIVE_MAX_NUMBER_FUN = number =>
    `当前单元的短语否定关键词数量达到上限${handleNumber(number)}，无法添加否定关键词，请删除部分否定关键词后添加`;
const ADGROUP_EXACT_NEGATIVE_MAX_NUMBER_FUN = number =>
    `当前单元的精确否定关键词数量达到上限${handleNumber(number)}，无法添加否定关键词，请删除部分否定关键词后添加`;

export const NEGATIVE_CAMPAIGN_LEVEL_ERROR = {
    251: CAMPAIGN_NEGATIVE_MAX_NUMBER_FUN,
    252: CAMPAIGN_EXACT_NEGATIVE_MAX_NUMBER_FUN,
    257: ALL_LEVEL_NEGATIVE_WORDS_ERROR, // 计划
    258: ALL_LEVEL_EXACT_NEGATIVE_WORDS_ERROR,
    270: CAMPAIGN_NEGATIVE_MAX_NUMBER_FUN,
    276: CAMPAIGN_NEGATIVE_MAX_NUMBER_FUN,
    277: CAMPAIGN_NEGATIVE_MAX_NUMBER_FUN,
    278: CAMPAIGN_NEGATIVE_MAX_NUMBER_FUN,
    279: CAMPAIGN_NEGATIVE_MAX_NUMBER_FUN,
    280: CAMPAIGN_EXACT_NEGATIVE_MAX_NUMBER_FUN,
    282: CAMPAIGN_EXACT_NEGATIVE_MAX_NUMBER_FUN,
    283: CAMPAIGN_EXACT_NEGATIVE_MAX_NUMBER_FUN,
    284: CAMPAIGN_EXACT_NEGATIVE_MAX_NUMBER_FUN,
    285: CAMPAIGN_EXACT_NEGATIVE_MAX_NUMBER_FUN
};

export const ADD_SINGLE_NEGATIVE_ERROR = {
    441: ADGROUP_NEGATIVE_MAX_NUMBER_FUN,
    442: ADGROUP_EXACT_NEGATIVE_MAX_NUMBER_FUN,
    447: ALL_LEVEL_NEGATIVE_WORDS_ERROR, // 单元
    448: ALL_LEVEL_EXACT_NEGATIVE_WORDS_ERROR,
    449: NAVIGATE_ERROR,
    460: ADGROUP_NEGATIVE_MAX_NUMBER_FUN,
    462: ADGROUP_NEGATIVE_MAX_NUMBER_FUN,
    463: ADGROUP_NEGATIVE_MAX_NUMBER_FUN,
    464: ADGROUP_NEGATIVE_MAX_NUMBER_FUN,
    465: ADGROUP_NEGATIVE_MAX_NUMBER_FUN,
    466: ADGROUP_EXACT_NEGATIVE_MAX_NUMBER_FUN,
    467: ADGROUP_EXACT_NEGATIVE_MAX_NUMBER_FUN,
    468: ADGROUP_EXACT_NEGATIVE_MAX_NUMBER_FUN,
    469: ADGROUP_EXACT_NEGATIVE_MAX_NUMBER_FUN,
    470: ADGROUP_EXACT_NEGATIVE_MAX_NUMBER_FUN
};

export const NEGATIVE_COMMON_ERROR = {
    80001142: '否定关键词包的短语否定关键词与所绑定计划已有的智能，短语，精确匹配关键词重复，无法保存，请及时调整', // 否定关键词包
    80001143: '否定关键词包的精确否定关键词与所绑定计划已有的智能，短语，精确匹配关键词重复，无法保存，请及时调整',
    80001144: '否定关键词包的短语否定关键词与所绑定计划的精确否定关键词重复，无法保存，请及时调整',
    80001145: '否定关键词包的精确否定关键词与所绑定计划的短语否定关键词重复，无法保存，请及时调整',
    80001527: '系统正在处理，请不要频繁操作，稍后请刷新重试',
    ...NEGATIVE_CAMPAIGN_LEVEL_ERROR,
    ...ADD_SINGLE_NEGATIVE_ERROR
};


export const allErrorsMap = {
    shareRepertoryName: {
        80001125: '当前否定关键词包名称已存在'
    },
    negativeWords: {
        251: CAMPAIGN_NEGATIVE_MAX_NUMBER_FUN,
        257: ALL_LEVEL_NEGATIVE_WORDS_ERROR,
        262: specialWordsError,
        270: CAMPAIGN_NEGATIVE_MAX_NUMBER_FUN,
        276: CAMPAIGN_NEGATIVE_MAX_NUMBER_FUN,
        277: CAMPAIGN_NEGATIVE_MAX_NUMBER_FUN,
        278: CAMPAIGN_NEGATIVE_MAX_NUMBER_FUN,
        279: CAMPAIGN_NEGATIVE_MAX_NUMBER_FUN,
        447: ALL_LEVEL_NEGATIVE_WORDS_ERROR,
        80001127: '否定关键词包中短语否定关键词数量超过上限',
        80001130: specialWordsError,
        80001139: CAMPAIGN_NEGATIVE_MAX_NUMBER_FUN,
        80001142: '否定关键词包的短语否定关键词与所绑定计划已有的智能，短语，精确匹配关键词重复，无法保存，请及时调整',
        80001144: '否定关键词包的短语否定关键词与所绑定计划的精确否定关键词重复，无法保存，请及时调整'
    },
    exactNegativeWords: {
        280: CAMPAIGN_EXACT_NEGATIVE_MAX_NUMBER_FUN,
        282: CAMPAIGN_EXACT_NEGATIVE_MAX_NUMBER_FUN,
        283: CAMPAIGN_EXACT_NEGATIVE_MAX_NUMBER_FUN,
        284: CAMPAIGN_EXACT_NEGATIVE_MAX_NUMBER_FUN,
        285: CAMPAIGN_EXACT_NEGATIVE_MAX_NUMBER_FUN,
        252: CAMPAIGN_EXACT_NEGATIVE_MAX_NUMBER_FUN,
        258: ALL_LEVEL_EXACT_NEGATIVE_WORDS_ERROR,
        265: exactWordsError,
        448: ALL_LEVEL_EXACT_NEGATIVE_WORDS_ERROR,
        80001128: '否定关键词包中精确否定关键词数量超过上限',
        80001131: exactWordsError,
        80001140: CAMPAIGN_EXACT_NEGATIVE_MAX_NUMBER_FUN,
        80001143: '否定关键词包的精确否定关键词与所绑定计划已有的智能，短语，精确匹配关键词重复，无法保存，请及时调整',
        80001145: '否定关键词包的精确否定关键词与所绑定计划的短语否定关键词重复，无法保存，请及时调整'
    },
    negatvieCampaginLevel: NEGATIVE_CAMPAIGN_LEVEL_ERROR,
    addSingleNegativeError: ADD_SINGLE_NEGATIVE_ERROR,
    negativeCommon: NEGATIVE_COMMON_ERROR,
    [unLocationField]: {
        80001126: '否定关键词包中短语否定关键词数量超过上限',
        80001527: '系统正在处理，请不要频繁操作，稍后请刷新重试'
    }
};

export default getErrorMessage(allErrorsMap);

export const errorsMap = Object.keys(allErrorsMap).reduce((item, key) => {
    return {
        ...item,
        ...allErrorsMap[key]
    };
}, {});

export const getBatchConfirmErrorSource = errorCodeConfig => res => {
    const errorArray = res.errors || [];
    const errorSource = errorArray.map(({code, id, position}) => {
        return {
            id,
            materialName: position,
            errorMessage: getErrorTextByCodeCustom(errorCodeConfig)(code)
                || getCommonTips(code, 'adgroups', res.reqid, res.traceid)
        };
    });
    return errorSource;
};
