import {giveMeShortcuts} from 'commonLibs/utils/handleOptions';

export const MonitorUrlTypeEnum = {
    ACCOUNT: 0,
    CAMPAIGN: 1,
    TRANSASSET: 2
};

export const [MonitorUrlTypeOptions, {getLabelByKey: getMonitorUrlTypeLabelByType}] = giveMeShortcuts([
    {value: MonitorUrlTypeEnum.ACCOUNT, label: '账户监测'},
    {value: MonitorUrlTypeEnum.CAMPAIGN, label: '自定义计划监测'},
    {value: MonitorUrlTypeEnum.TRANSASSET, label: '转化资产监测'}
]);