import {MATERIAL_CONFIG} from 'app/config/material';

// 位于各个物料层级获取设备出价系数字段
export const MATERIAL_PRICE_FACTOR_CONFIG = {
    // 单元层级
    [MATERIAL_CONFIG.ADGROUP]: {
        // 获取计划设备出价系数(第一个是pc，第二个是wise)
        campaign: ['campaignPcPriceRatio', 'campaignPriceRatio'],
        adgroup: ['pcPriceRatio', 'priceRatio']
    },
    // 关键词层级
    [MATERIAL_CONFIG.KEYWORD]: {
        campaign: ['campaignPcPriceFactor', 'campaignMobilePriceFactor'],
        adgroup: ['adgroupPcPriceFactor', 'adgroupMobilePriceFactor']
    },
    // 创意层级
    [MATERIAL_CONFIG.CREATIVE]: {
        campaign: ['campaignPcPriceRatio', 'campaignPriceRatio'],
        adgroup: ['pcPriceRatio', 'priceRatio']
    }
};

export function getCampaignAndAdgroupPriceFactor(materialData = {}, materialLevel) {
    const priceFactorFiedConfig = MATERIAL_PRICE_FACTOR_CONFIG[materialLevel];
    if (priceFactorFiedConfig) {
        return {
            campaign: [
                materialData[priceFactorFiedConfig.campaign[0]],
                materialData[priceFactorFiedConfig.campaign[1]]
            ],
            adgroup: [
                materialData[priceFactorFiedConfig.adgroup[0]],
                materialData[priceFactorFiedConfig.adgroup[1]]
            ]
        };
    }
}