import EmptyInfo from 'commonLibs/tableList/EmptyText';
import {isInQinggeIframe} from 'commonLibs/utils/isInIframe';

export const tablePropsWithoutScroll = {
    size: 'small',
    autoHideOperation: 'filter',
    locale: {
        emptyText: <EmptyInfo />
    },
    pagination: false, // 如果采用表格的分页器，数据要为前端分页，如果是后端排序，现在仅支持自己写pager
    updateWidthChange: true // 注意此属性，如果为false，浏览器缩放时不会重新计算表格宽度
};

export const baseTableProps = {
    ...tablePropsWithoutScroll,
    headerFixTop: isInQinggeIframe ? 80 : 140,
    bottomScroll: {
        bottom: 40
    }
};
