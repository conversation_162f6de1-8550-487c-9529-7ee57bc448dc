/**
 * @file 线索通通用配置信息
 * <AUTHOR>
 * @date 2021/11/5
 */
import Enum from 'enum';

// 开关状态
export const SWITCH_STATUS = new Enum({
    0: '关闭',
    1: '开启'
});

export const SWITCH_FLAG = new Enum({
    close: 0,
    open: 1
});

export const SWITCH_TYPE = new Enum({
    returnedCustomer: 1400,
    dynamicText: 1700,
    consultLink: 1500,
    consultImlp: 1900,
    customform: 2000
});

export const SWITCH_TYPE_DEFAULT = {
    [SWITCH_TYPE.returnedCustomer.value]: 0,
    [SWITCH_TYPE.dynamicText.value]: 1,
    [SWITCH_TYPE.consultLink.value]: 0,
    [SWITCH_TYPE.consultImlp.value]: 0
};

export const ACCOUNT_SETTING_ARRAY = [
// TODO 目前账户设置没用到，就先不添加这两个参数了，用到了再加
//  SWITCH_TYPE.returnedCustomer.value,
//  SWITCH_TYPE.dynamicText.value,
    SWITCH_TYPE.consultLink.value,
    SWITCH_TYPE.consultImlp.value
];
