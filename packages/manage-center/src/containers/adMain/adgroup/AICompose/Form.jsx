import {useActionPending} from 'huse';
import {Button} from '@baidu/one-ui';
import {createError, displayErrorAsToast} from 'commonLibs/utils/materialList/error';
import {useAdgroupForm} from '../hooks';
import {useProcessAdgroupResource, useCampaignInfo} from '../context';
import {getFormConfig} from '../config';
import {getInitialData, generateAdgroupParams} from '../config/AI';
import {useFormulatorLogMethods} from 'commonLibs/hooks/funnelLog';
import './form.less';

export default function AdgroupForm({onAIProcessSave, onAIProcessCancel, adgroupInfo}) {
    const campaignInfo = useCampaignInfo();
    const [{fields}] = useProcessAdgroupResource(getFormConfig, campaignInfo);
    const [initialData] = useProcessAdgroupResource(getInitialData, {adgroupInfo, campaignInfo});

    const [{Form, FormInstance, formData}, {validateFields, getFieldError}]
        = useAdgroupForm({fields}, initialData);

    const {
        logOnSubmitClick,
        logOnSubmitSuccess,
        logOnCancelClick,
        logOnAreaClick
    } = useFormulatorLogMethods({
        shouldSendChangeLog: true,
        formData,
        source: 'aiCompositionProcessAdgroupForm',
        config: {
            commonTrackParams: {
                marketingtargetid: campaignInfo.marketingTargetId
            }
        },
        getFieldError
    });

    async function submit() {
        logOnSubmitClick();
        const values = await validateFields();
        try {
            const params = generateAdgroupParams(campaignInfo, values);
            await onAIProcessSave(params);
            logOnSubmitSuccess();
        } catch (error) {
            const err = createError(error);
            err.optName = '编辑';
            displayErrorAsToast(err);
        }
    };
    function onCancel() {
        logOnCancelClick();
        onAIProcessCancel();
    };

    const [, pendingCount] = useActionPending(submit);
    const isSaving = !!pendingCount;
    return (
        <div className="ad-main-new-adgroup-page-form ai-adgroup-form">
            <FormInstance>
                <Form className="use-rf-preset-form-ui adgroup-form-area" onClick={logOnAreaClick}>
                    <div className="ad-main-new-adgroup-page-form-footer">
                        <Button type="primary" onClick={submit} disabled={isSaving}>
                            确定
                        </Button>
                        <Button disabled={isSaving} onClick={onCancel}>
                            取消
                        </Button>
                    </div>
                </Form>
            </FormInstance>
        </div>
    );
}
