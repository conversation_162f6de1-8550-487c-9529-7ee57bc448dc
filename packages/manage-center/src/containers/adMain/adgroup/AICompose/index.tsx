import {RecoilRoot} from 'recoil';
import SuspenseBoundary from 'commonLibs/suspenseBoundary';
import {AdgroupFormValue} from 'app/containers/smartCompose/interface';
import {ProcessAdgroupBoundary, CampaignInfoProvider} from '../context';
import AdgroupForm from './Form';
import {renderError} from '../config/uiConfig';
import '../style.less';

interface AdgroupProps {
    // ai 预生成的单元对象（注：非完整 adgroup对象，仅包含用户可输入的字段）
    campaignInfo: Record<string, any>;
    adgroupInfo: Record<string, any>;
    onAIProcessSave: (formValue: AdgroupFormValue) => void;
    onAIProcessCancel: () => void;
}

function AdgroupPage({campaignInfo, adgroupInfo, ...rest}: AdgroupProps) {
    return (
        <div className="ad-main-new-adgroup-page" id="ad-main-new-adgroup-page">
            <div className="ad-main-new-adgroup-page-container" id="ad-main-new-adgroup-page">
                <CampaignInfoProvider value={campaignInfo}>
                    <SuspenseBoundary loading={{tip: '正在初始化配置'}}>
                        <AdgroupForm
                            adgroupInfo={adgroupInfo}
                            {...rest}
                        />
                    </SuspenseBoundary>
                </CampaignInfoProvider>
            </div>
        </div>
    );
};

export default function (props: AdgroupProps) {
    return (
        <ProcessAdgroupBoundary renderError={renderError}>
            <RecoilRoot>
                <AdgroupPage {...props} />
            </RecoilRoot>
        </ProcessAdgroupBoundary>
    );
};
