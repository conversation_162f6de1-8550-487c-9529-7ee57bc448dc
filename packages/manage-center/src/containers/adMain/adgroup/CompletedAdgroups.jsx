import {useCallback, useMemo} from 'react';
import {partial, noop} from 'lodash-es';
import classNames from 'classnames';
import {Carousel, Tooltip, Popover, Toast} from '@baidu/one-ui';
import {IconTrash} from 'dls-icons-react';
import {useElementSize} from 'huse';
import {useSlot} from 'commonLibs/hooks/slot';
import {AppPlatform} from 'commonLibs/config/osType';
import {promisifyConfirm} from 'commonLibs/hooks/dialog';
import {isOcpcCampaign} from 'commonLibs/utils/campaign';
import {priceUseCampaignBid} from 'app/containers/fcNew/adgroup/form/util';
import {onDeleteAdgroup} from 'app/containers/fcNew/actions/adgroup/api';
import {useCampaignInfo} from './context';
import 'app/containers/fcNew/adgroup/adgroupBars/style.less';
import ALL_ADTYPE from 'commonLibs/config/adType';

const carouselBtnProps = {
    prevButtonProps: {style: {left: -40}},
    nextButtonProps: {style: {right: -40}}
};

const slots = {
    adgroupName: renderAdgroupName,
    product: renderProductName,
    appBind: renderAppBind,
    autoTargetingStatus: renderAutoTargetingStatus,
    keywordsDesc: renderKeywordsDesc,
    adgroupPrice: renderAdgroupPrice,
    url: renderUrl,
    delBtn: DelBtn
};

const emptyCardSlots = {
    adgroupName: renderAdgroupName,
    description: function () {
        return (
            <div className="description">请填写单元信息</div>
        );
    },
    delBtn: function ({closeForm, canDelete}) {
        if (canDelete) {
            return (
                <div className="delete">
                    <IconTrash onClick={closeForm} />
                </div>
            );
        }
        return null;
    }
};

export function CompletedAdgroups({completedAdgroups, deleteAdgroupById, getKey, isFormOpen, closeForm}) {
    const campaignInfo = useCampaignInfo();
    const cardCount = completedAdgroups.length + isFormOpen;
    const [ref, {width, slidesToShow}] = useCarouselSize({count: cardCount, itemWidth: 300});
    return (
        <div className="fcNew-adgroup-bar-container" ref={ref}>
            <Carousel
                className="completed-adgroups-carousel"
                slidesToScroll={Math.floor(slidesToShow) || 1}
                slidesToShow={slidesToShow}
                showButton
                width={width}
                sliderMode="hide"
                {...carouselBtnProps}
            >
                {
                    completedAdgroups.map(adgroup => {
                        const {adgroupType: {adgroupId}} = adgroup;
                        return (
                            <AdgroupCard
                                key={adgroupId}
                                deleteAdgroup={deleteAdgroupById}
                                campaignInfo={campaignInfo}
                                slots={slots}
                                getKey={getKey}
                                {...adgroup}
                            />
                        );
                    })
                }
                {
                    isFormOpen && (
                        <AdgroupCard
                            className="empty"
                            canDelete={!!completedAdgroups.length}
                            closeForm={closeForm}
                            slots={emptyCardSlots}
                        />
                    )
                }
            </Carousel>
        </div>
    );
}

function AdgroupCard({className, ...props}) {
    const Slot = useSlot(props);

    return (
        <div className={classNames('fc-manage-new-adgroup-bar', className)}>
            <div className="info">
                <Slot name="adgroupName" />
                <Slot name="product" />
                <Slot name="appBind" />
                <Slot name="autoTargetingStatus" />
                <Slot name="keywordsDesc" />
                <Slot name="adgroupPrice" />
                <Slot name="url" />
                <Slot name="description" />
                <Slot name="delBtn" />
            </div>
        </div>
    );
}

function DelBtn({deleteAdgroup, getKey = noop, ...props}) {
    const deleteAdgroupById = useCallback(
        async function (adgroupId) {
            const canDeleteIt = await promisifyConfirm({
                title: '提示',
                content: '当前单元已保存并提交，确定要删除吗？删除操作不可恢复。'
            });
            if (canDeleteIt) {
                try {
                    await onDeleteAdgroup(adgroupId);
                    deleteAdgroup(adgroupId);
                    Toast.success({content: '删除单元成功', showCloseIcon: false});
                }
                catch (e) {
                    Toast.error({content: '删除单元失败', showCloseIcon: false});
                }
            }
        },
        [deleteAdgroup]
    );

    return (
        <div className="delete">
            <IconTrash onClick={partial(deleteAdgroupById, getKey(props))} />
        </div>
    );
}

function renderAdgroupName({adgroupType = {}}) {
    const {adgroupName = '新建单元'} = adgroupType;
    return (
        <div className="adgroup-name">
            <Tooltip title={adgroupName} className="names">
                <span className="info-title">单元名称：</span>
                <span>{adgroupName}</span>
            </Tooltip>
        </div>
    );
}

function renderProductName({formValue}) {
    const {catalogId = []} = formValue;
    if (catalogId.length) {
        return (
            <div className="adgroup-detail-item url-info">
                <span className="info-title">产品：</span>
                {catalogId[0].productName}
            </div>
        );
    }
    return null;
}

function renderAppBind({appSelect = {}}) {
    const {appName, versionCode, platform} = appSelect || {};
    if (appName) {
        return (
            <>
                <div className="adgroup-detail-item">
                    <span className="info-title">推广应用：</span>
                    <span>{appName}</span>
                </div>
                <div className="adgroup-detail-item">
                    <span className="info-title">应用类型/版本：</span>
                    {AppPlatform[platform]}/{versionCode}
                </div>
            </>
        );
    }
    return null;
}

function renderAutoTargetingStatus({adgroupType}) {
    const {adgroupAutoTargetingStatus} = adgroupType;
    return (
        <div className="adgroup-detail-item">
            <span className="info-title">自动定向：</span>
            {adgroupAutoTargetingStatus ? '启用' : '不启用'}
        </div>
    );
}

function renderKeywordsDesc({keywordTypes = []}) {
    const keywordsCount = keywordTypes.length;
    const firstWord = keywordTypes[0]?.keyword || '';
    const firstWordFormat = firstWord.length > 6 ? `${firstWord.substring(0, 6)}...` : firstWord;
    return (
        <div className="adgroup-detail-item">
            <span className="info-title">关键词：</span>
            <span>{keywordsCount ? `${firstWordFormat}等${keywordsCount}个` : `${keywordsCount}个`}</span>
        </div>
    );
}

function renderAdgroupPrice({adgroupType, campaignInfo}) {
    const {maxPrice} = adgroupType;
    if (!isOcpcCampaign(campaignInfo)) {
        return (
            <div className="adgroup-detail-item">
                <span className="info-title">单元点击出价：</span>
                {maxPrice === priceUseCampaignBid ? '未设置' : `${maxPrice}元`}
            </div>
        );
    }
    return null;
}

function renderUrl({adgroupType, campaignInfo}) {
    const {pcFinalUrl, mobileFinalUrl, storeName, storePageList, promotionTypes = []} = adgroupType;
    const {adType} = campaignInfo;
    const {checkAllPages} = promotionTypes[0] || {};
    const content = `${storePageList}等${promotionTypes.length}个落地页`;
    if (adType !== ALL_ADTYPE.NEW_STORE) {
        return (
            <>
                <Popover content={pcFinalUrl}>
                    <div className="adgroup-detail-item url-info">
                        <span>计算机最终访问网址：</span>
                        <span>{pcFinalUrl || '未设置'}</span>
                    </div>
                </Popover>
                <Popover content={mobileFinalUrl}>
                    <div className="adgroup-detail-item url-info">
                        <span>移动最终访问网址：</span>
                        <span>{mobileFinalUrl || '未设置'}</span>
                    </div>
                </Popover>
            </>
        );
    }
    return (
        <>
            <Popover content={storeName}>
                <div className="adgroup-detail-item url-info">
                    <span>店铺名称：</span>
                    <span>{storeName || '未设置'}</span>
                </div>
            </Popover>
            {
                !checkAllPages && (
                    <Popover content={content}>
                        <div className="adgroup-detail-item url-info">
                            <span>落地页：</span>
                            <span>{content}</span>
                        </div>
                    </Popover>
                )
            }
        </>
    );
}

function useCarouselSize({count, itemWidth}) {
    const [ref, size] = useElementSize();
    const carouselSize = useMemo(
        () => {
            if (size) {
                const totalWidth = itemWidth * count;
                const areaWith = size.width - 128;
                return {
                    width: totalWidth < areaWith ? totalWidth : areaWith,
                    slidesToShow: totalWidth < areaWith ? count : (areaWith / itemWidth)
                };
            }
            return {width: 0, slidesToShow: 1};
        },
        [count, itemWidth, size]
    );
    return [ref, carouselSize];
}
