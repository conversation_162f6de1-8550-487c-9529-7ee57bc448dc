import {createReactiveData, useFieldWatch} from '@baidu/react-formulator';
import {useCallback, forwardRef, useImperativeHandle, useMemo, useEffect} from 'react';
import {useSwitch, useActionPending, useCounter} from 'huse';
import {concat, get} from 'lodash-es';
import {Button, Dialog} from '@baidu/one-ui';
import {useRouterRedirect} from 'commonLibs/route';
import {sendScreenRecord} from 'commonLibs/hooks/record';
import {useControl} from 'commonLibs/hooks/externalControl';
import AddButton from 'app/containers/fcNew/common/addButton';
import {promisifyConfirm} from 'commonLibs/hooks/dialog';
import {useEfficiencyPPMonitor} from 'commonLibs/hooks/monitor';
import {AutoProductDialog as AutoProductDialogComp} from 'commonLibs/components/commonEntry';
import {useAdgroupForm, useRecommendStructContentByUrl} from './hooks';
import {useProcessAdgroupResource, useCampaignInfo, StructContentInstance} from './context';
import {saveAdgroup} from './api';
import {initialAdgroupForm, getInitialData} from './config';
import {formatSaveAdgroupErrors} from './utils';
import {getEfficiencyBidTypeByCampaignInfo} from '../utils';
import {useFormulatorLogMethods} from 'commonLibs/hooks/funnelLog';
import {useDraft} from 'commonLibs/hooks/draft';
import {isDraftUser} from 'commonLibs/utils/getFlag';
import {useBackToAdgroupList} from 'app/containers/fcNew/common/backUrl';

export const InternalForm = forwardRef(InternalForm_);
function InternalForm_({
    initialVerificationData, children, autoKey, onWatchedFieldsChange, watchedFields = [],
    onFieldsChange, defaultInitialData, formInitProps = {}
}, ref) {
    const campaignInfo = useCampaignInfo();

    const [
        {
            initialData
        }
    ] = useProcessAdgroupResource(getInitialData, {autoKey, defaultInitialData, ...campaignInfo});

    const [
        {
            config,
            formFieldMapForBackend,
            adgroupFields
        }
    ] = useProcessAdgroupResource(initialAdgroupForm, {...campaignInfo, ...formInitProps, initialData});

    const defaultVerificationData = useMemo(() => createReactiveData({}), []);
    const reactiveVerificationData = initialVerificationData ?? defaultVerificationData;

    const [
        {Form, FormInstance, formData},
        {validateFields, setFieldsError, getFieldError, scrollToField, getConfigByField, setFieldsValue}
    ] = useAdgroupForm(config, initialData, reactiveVerificationData);

    const {
        logOnSubmitClick,
        logOnSubmitSuccess,
        logOnSubmitFail,
        logOnCancelClick,
        logOnAreaClick
    } = useFormulatorLogMethods({
        shouldSendChangeLog: true,
        formData,
        source: 'AdgroupForm',
        config: {
            commonTrackParams: {
                marketingtargetid: campaignInfo.marketingTargetId
            }
        },
        getFieldError
    });
    async function submit() {
        logOnSubmitClick();
        const values = await validateFields();
        let adgroup;
        try {
            [adgroup] = await saveAdgroup(campaignInfo, values);
            logOnSubmitSuccess();
        }
        catch (error) {
            const {
                errorFieldsMap,
                alertError
            } = formatSaveAdgroupErrors(error.errors?.[0], values, formFieldMapForBackend, adgroupFields.allFields);
            setFieldsError(errorFieldsMap);
            sendScreenRecord('adgroup_save_error');
            logOnSubmitFail({
                ...error,
                errors: concat(
                    [],
                    get(error, 'errors[0].adgroupError') ? [get(error, 'errors[0].adgroupError')] : [],
                    get(error, 'errors[0].keywordErrors', []),
                )
            });
            if (alertError.length) {
                Dialog.alert({
                    title: '温馨提示',
                    content: alertError.map(e => e.message).join('；')
                });
            }
            throw error;
        }
        return [adgroup, values];
    };
    const [submitAdgroup, pendingCount] = useActionPending(submit);
    const isSaving = !!pendingCount;

    function applyDraftTipTpl({conditions}) {
        const {campaignName} = conditions;
        return `系统检测到您在当前计划(${campaignName})下有未完成的新建单元草稿，您要继续编辑草稿吗?`;
    }
    function saveDraftTipTpl({conditions}) {
        const {campaignName} = conditions;
        return `您当前在计划"${campaignName}"下新建的单元还未完成，保存草稿将方便您下次进入时继续编辑，确认保存吗？`;
    }

    const getFieldsValue_ = () => {
        return formData.$toRaw();
    };

    const {markChanged, markCompleted, blockAndSaveDraft, getFormStatusChanged} = useDraft({
        form: {setFieldsValue, getFieldsValue: getFieldsValue_},
        level: 'adgroup',
        conditions: {campaignId: campaignInfo.campaignId, campaignName: campaignInfo.campaignName},
        tpl: {applyDraftTipTpl, saveDraftTipTpl}
    });

    const [AutoProductDialog, {onProductDialog, offProductDialog}] = useControl(AutoProductDialogComp);

    const [
        {
            pending,
            error,
            cancelable,
            recommendMessage
        },
        {
            onAdgroupFieldsChange,
            setAdgroupChangedFields,
            startGenerateStructContent,
            stopGenerateStructContent
        }
    ] = useRecommendStructContentByUrl({
        campaignInfo,
        setFieldsValue,
        triggerManualRecommendDialog: onProductDialog,
        cancelManualRecommendDialog: offProductDialog
    });

    useImperativeHandle(
        ref,
        () => ({
            validateFields, setFieldsError, formData, formFieldMapForBackend,
            scrollToField, getConfigByField, markCompleted, setFieldsValue,
            pending, blockAndSaveDraft, getFormStatusChanged
        })
    );

    useFieldWatch(formData, '*', changedFields => {
        // 单元字段变化时，触发推荐内容生成
        onAdgroupFieldsChange(getFieldsValue_());
        setAdgroupChangedFields(changedFields);
        // 品牌信息初始化时会onChange，此时不计算为form的变化
        if (changedFields.some(field => !field.startsWith('brandName'))) {
            markChanged();
            if (onFieldsChange) {
                onFieldsChange(changedFields);
            }
        }
        if (changedFields.some(field => watchedFields.includes(field))) {
            onWatchedFieldsChange(getFieldsValue_(), changedFields);
        }
    });

    useEffect(
        () => {
            return () => {
                offProductDialog();
            };
        },
        []
    );

    return (
        <FormInstance>
            <StructContentInstance
                value={{
                    stopGenerateStructContent,
                    pending,
                    cancelable,
                    error,
                    recommendMessage
                }}
            >
                <AutoProductDialog onStart={startGenerateStructContent} />
                <Form className="use-rf-preset-form-ui adgroup-form-area" onClick={logOnAreaClick}>
                    {children && children({
                        submit: submitAdgroup, isSaving, logOnCancelClick,
                        markCompleted, getFormStatusChanged, blockAndSaveDraft
                    })}
                </Form>
            </StructContentInstance>
        </FormInstance>
    );
}

export function AdgroupForm({
    completedAdgroups,
    addAdgroupToCompleted,
    isFormOpen,
    autoKey,
    createAndOpenForm,
    refreshForm,
    campaignInfo
}) {

    const adgroupCount = completedAdgroups.length;
    const isOverLimit = adgroupCount >= 15;
    const linkToNewCreative = useRouterRedirect('@processNewCreative', {inheritParams: true, inheritQuery: true});
    const goBack = useBackToAdgroupList();
    const reportMonitor = useEfficiencyPPMonitor('new_adgroup', 'ad_main_new');

    const onCancelProcess = async logCancel => {
        if (
            !isDraftUser()
            && !adgroupCount
            && !await promisifyConfirm({content: '当前尚未保存任何单元，确定退出吗？'})
        ) {
            reportMonitor({
                count: 0,
                completed: 0,
                marketingTargetId: campaignInfo.marketingTargetId,
                bid_type: getEfficiencyBidTypeByCampaignInfo(campaignInfo)
            });
            return;
        }
        logCancel && logCancel();
        reportMonitor({
            count: adgroupCount,
            marketingTargetId: campaignInfo.marketingTargetId,
            bid_type: getEfficiencyBidTypeByCampaignInfo(campaignInfo)
        });
        goBack();
    };

    const nextToCreatveStep = useCallback(
        () => {
            const adgroupIds = completedAdgroups
                .map(({adgroupType: {adgroupId}}) => adgroupId)
                .join(',');
            reportMonitor({
                count: adgroupCount,
                marketingTargetId: campaignInfo.marketingTargetId,
                bid_type: getEfficiencyBidTypeByCampaignInfo(campaignInfo)
            });
            linkToNewCreative({adgroupIds});
        },
        [completedAdgroups, campaignInfo, adgroupCount, reportMonitor]
    );

    return (
        <div className="ad-main-new-adgroup-page-form">
            {
                isFormOpen
                    ? (
                        <InternalForm key={autoKey} autoKey={autoKey}>
                            {
                                ({submit, isSaving, logOnCancelClick, markCompleted}) => {
                                    const onSaveOneAdgroup = async () => {
                                        const [adgroup, formValue] = await submit();
                                        addAdgroupToCompleted({...adgroup, formValue});
                                        const adgroupIdList = completedAdgroups
                                            .map(({adgroupType: {adgroupId}}) => adgroupId)
                                            .concat(adgroup.adgroupType.adgroupId);
                                        markCompleted();
                                        reportMonitor({
                                            count: adgroupIdList.length,
                                            marketingTargetId: campaignInfo.marketingTargetId,
                                            bid_type: getEfficiencyBidTypeByCampaignInfo(campaignInfo)
                                        });
                                        linkToNewCreative({adgroupIds: adgroupIdList.join(',')});
                                    };
                                    const saveAndAddNextAdgroup = async () => {
                                        const [adgroup, formValue] = await submit();
                                        addAdgroupToCompleted({...adgroup, formValue});
                                        refreshForm();
                                    };
                                    return (
                                        <div className="ad-main-new-adgroup-page-form-footer">
                                            <Button type="primary" onClick={onSaveOneAdgroup} disabled={isSaving}>
                                                保存当前单元并新建创意
                                            </Button>
                                            <Button
                                                type="primary"
                                                disabled={isOverLimit || isSaving}
                                                onClick={saveAndAddNextAdgroup}
                                            >
                                                保存并新增一个单元
                                            </Button>
                                            <Button
                                                onClick={
                                                    () => onCancelProcess(() => logOnCancelClick())
                                                }
                                            >
                                                取消
                                            </Button>
                                        </div>
                                    );
                                }
                            }
                        </InternalForm>
                    )
                    : (
                        <div>
                            {
                                !isOverLimit && (
                                    <div className="add-adgroup-button">
                                        <AddButton onClick={createAndOpenForm} text="新建单元" />
                                    </div>
                                )
                            }
                            <div className="ad-main-new-adgroup-page-form-footer">
                                <Button type="primary" onClick={nextToCreatveStep}>
                                    下一步新建创意
                                </Button>
                                <Button onClick={() => onCancelProcess()}>取消</Button>
                            </div>
                        </div>
                    )
            }
        </div>
    );
}

export function useSwitchForm(initial) {
    const [isFormOpen, openForm, closeForm] = useSwitch(initial);
    const [autoKey, {increment: refreshForm}] = useCounter(0);
    const createAndOpenForm = () => {
        refreshForm();
        openForm();
    };
    return [{isFormOpen, autoKey}, {createAndOpenForm, closeForm, refreshForm}];
}

