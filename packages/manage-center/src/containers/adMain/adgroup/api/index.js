import {request} from '@baidu/winds-ajax';
import {isEmpty} from 'lodash-es';
import {getSingleErrorMessage} from 'commonLibs/utils/getErrorTextByCode';
import {
    adgroupParamsPipeline,
    adgroupTypeParams,
    adgroupBrandParams,
    keywordsTypeParams,
    adgroupUrlParams
} from '../utils/generateParams';
import {remoteConfigLoaders} from '../config';
import ALL_ADTYPE from 'commonLibs/config/adType';

// 1. 要么是营销目标维度，每个营销目标去处理自己的字段
// 2. 要么是字段维度，在每个字段列表再判断营销目标
// 我并不确定哪种是更优，先尝试用这种方式进行整理吧，总比所有逻辑堆一块好些
const defaultGenerateAdgroupParams = adgroupParamsPipeline(
    [adgroupTypeParams, adgroupBrandParams, keywordsTypeParams, adgroupUrlParams],
    {adgroupType: {}, segmentBindTypes: []}
);

export async function saveAdgroup(campaignInfo, values) {
    const {marketingTargetId, adType} = campaignInfo;
    const {generateAdgroupParams = defaultGenerateAdgroupParams} = await remoteConfigLoaders[marketingTargetId]();
    const params = generateAdgroupParams(campaignInfo, values);

    const data = await request({
        path: 'puppet/ADD/AdgroupKeywordFunction/addAdgroupKeywordAndBusiness',
        params: {
            items: [params]
        }
    });
    if (adType === ALL_ADTYPE.NEW_STORE) {
        // 新建单元卡片需要店铺名，落地页名称等信息，接口并未返回，这里前端处理一下
        data[0].adgroupType.storeName = values.storeInfo[0].storeName;
        data[0].adgroupType.storePageList = values.storeMobileUrlList[0]?.pageName || '';
    }
    return data;
}

export async function preValidate(keywords, {campaignId}) {
    const keywordTypes = [];
    for (let i = 0; i < keywords.length; i++) {
        const {keyValue: keyword, price, matchType: mixMatchType} = keywords[i];
        if (keyword) {
            const [matchType, phraseType] = mixMatchType.split('-');
            keywordTypes.push({keyword, price, matchType, phraseType, campaignId});
        }
    }
    if (keywordTypes.length) {
        try {
            return await request({
                path: 'thunder/GET/KeywordService/getKeywordPreCheck',
                params: {
                    keywordTypes
                }
            });
        }
        catch (error) {
            const errors = error.errors || [];
            const preValidateResult = keywords.map((item, index) => {
                const error = errors.find(err => err.id === index);
                return {
                    ...item,
                    error: error && getSingleErrorMessage(error),
                    errorCode: error && error.code,
                    levelMessage: error && error.message
                };
            });
            throw preValidateResult;
        }
    }
}


// 页面主动推荐接口path
const suggestPath = 'puppet/GET/KeywordSuggestFunction/getKeywordRecommend';
const suggestPassivePath = 'lightning/GET/KeywordSuggestService/getKeywordRecommendPassive';

export async function getKeywordRecommendTotalCount({campaignId, displayUrl}) {
    const isPassiveSuggest = displayUrl && displayUrl.trim();

    const data = await request({
        path: isPassiveSuggest ? suggestPassivePath : suggestPath,
        params: {
            keyWordRecommendFilter: {
                device: 0,
                positiveWords: [],
                negativeWords: [],
                fieldFilters: [],
                keywordRecommendReasons: [],
                searchRegions: '9999999',
                regionExtend: false,
                removeDuplicate: false,
                removeCampaignDuplicate: false
            },
            ...(isPassiveSuggest ? {urlQuerys: [displayUrl.trim()]} : {}),
            source: 'create-web',
            queryBy: 0,
            pageNo: 1,
            pageSize: 3000,
            campaignId
        }
    });
    return data?.totalCount;
}

export async function saveAdgroupsAndCreatives(params) {
    try {
        const data = await request({
            path: 'puppet/ADD/AdgroupKeywordFunction/addAdgroupKeywordCreativeAndSegments',
            params
        });
        return data;
    }
    catch (err) {
        // todo 根据错误的field来确定是单元错误还是创意错误
        throw err;
    }
}

// 查询存量已有的计划或者落地页结构化解析结果
export function getPreSavedUrlStructResult({urls = []}) {
    if (isEmpty(urls)) {
        return Promise.resolve([]);
    }
    return request({
        path: 'lightning/GET/UrlStructService/getUrlStructResult',
        params: {urls}
    });
}

// 实时解析落地页结构化解析结果
export function getRealtimeUrlStructResult({urls = []}) {
    if (isEmpty(urls)) {
        return Promise.resolve([]);
    }
    return request({
        path: 'lightning/GET/UrlStructService/crawlOrGetResult',
        params: {urls}
    });
}
