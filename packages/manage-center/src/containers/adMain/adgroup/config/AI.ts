import {isUndefined, pick} from 'lodash-es';
import {fetchB2bPromotionPagesByUrls, fetchB2bPromotionPageList} from 'commonLibs/apis/b2b';
import {createError, displayErrorAsToast} from 'commonLibs/utils/materialList/error';
import {parseJsonWithFallback} from 'commonLibs/utils/json';
import {autoOrientationConfig} from 'app/containers/fcNew/adgroup/adgroupAutoTargetingStatus/config';
import {
    adgroupParamsPipeline,
    adgroupTypeParams,
    adgroupBrandParams,
    keywordsTypeParams
} from '../utils/generateParams';
import {defaultBrandName, initialData} from './B2B';
import {KeywordProcessor} from '../utils/keyword';

interface Params {
    adgroupInfo: Record<string, any>;
    campaignInfo: Record<string, any>;
}

function handleError(error: any) {
    const err = createError(error);
    err.optName = '初始化移动落地页链接';
    displayErrorAsToast(err);
}

const getMobileFinalUrlInfo = async (url: string) => {
    if (!url) {
        return null;
    }

    let page = null;
    try {
        const item = await fetchB2bPromotionPagesByUrls([url]);
        page = item?.pageList?.[0];
        if (!page) {
            return page;
        }
    }
    catch (err) {
        handleError(err);
        return null;
    }

    // @ts-ignore
    return fetchB2bPromotionPageList(pick(page, ['pageId', 'pageType']))
        .then(data => {
            const {pageList = []} = data;
            return pageList[0];
        })
        .catch(error => {
            handleError(error);
            return null;
        });
};

export const getInitialData = async ({adgroupInfo, campaignInfo}: Params) => {
    const {adgroupType, segmentBindTypes, keywordTypes} = adgroupInfo;
    const mobilePageInfo = await getMobileFinalUrlInfo(adgroupType.mobileFinalUrl);

    const adgroupAutoTargetingStatus = adgroupType?.adgroupAutoTargetingStatus;
    return {
        ...initialData,
        ...pick(adgroupType, ['adgroupName', 'pcFinalUrl']),
        adgroupAutoTargetingStatus: isUndefined(adgroupAutoTargetingStatus) || adgroupAutoTargetingStatus
            ? autoOrientationConfig.on
            : autoOrientationConfig.off,
        equipmentType: campaignInfo.equipmentType,
        mobilePageInfo,
        keywords: parseJsonWithFallback(keywordTypes, [])
            .map((item: Record<string, any>) => (new KeywordProcessor(item).fromParams())),
        brandName: parseJsonWithFallback(segmentBindTypes, [])[0] || defaultBrandName
    };
};


export function b2bUrlParams(
    _: any,
    {pcFinalUrl, mobilePageInfo}: {pcFinalUrl: string, mobilePageInfo: Record<string, any>},
    result: Record<string, any>
) {
    result.adgroupType.pcFinalUrl = pcFinalUrl || '';
    const {onlineUrl, pageName} = mobilePageInfo || {};
    result.adgroupType.mobileFinalUrl = onlineUrl || '';
    result.adgroupType.pageName = pageName || '';
    return result;
}

export const generateAdgroupParams = adgroupParamsPipeline(
    [adgroupTypeParams, adgroupBrandParams, keywordsTypeParams, b2bUrlParams],
    {adgroupType: {}, segmentBindTypes: []}
);
