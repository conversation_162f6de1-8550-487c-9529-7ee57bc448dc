import AppType from 'commonLibs/config/osType';
import {autoOrientationConfig} from 'app/containers/fcNew/adgroup/adgroupAutoTargetingStatus/config';
import {
    adgroupParamsPipeline,
    adgroupTypeParams,
    adgroupBrandParams,
    keywordsTypeParams,
    adgroupUrlParams
} from '../utils/generateParams';
import {getDefaultAdgroupName} from '../utils';
import {getFlattenAppList} from 'app/containers/fcNew/actions/adgroup/appSelector';

export const initialData = {
    adgroupAutoTargetingStatus: autoOrientationConfig.on,
    keywords: [],
    osType: AppType.ANDROID.value,
    appSelect: {},
    appShopDirectStatus: false,
    pcFinalUrl: '',
    pcTrackParam: '',
    pcTrackTemplate: '',
    mobileFinalUrl: '',
    mobileFinalUrlPreview: '',
    mobileTrackParam: '',
    mobileTrackTemplate: '',
    brandName: {
        segmentId: '',
        segmentSign: ''
    },
    adgroupPrice: ''
};

export const getInitialData = async (campaignInfo, defaultInitialData = {}) => {
    const {equipmentType, projectApplicationSid} = campaignInfo;
    const res = {
        ...initialData,
        adgroupName: getDefaultAdgroupName(),
        ...defaultInitialData,
        equipmentType
    };
    if (projectApplicationSid) {
        const appList = await getFlattenAppList([AppType.ANDROID.value, AppType.IOS.value]);
        const appSelect = appList?.appInfoList?.find(item => item.sid === projectApplicationSid);
        if (appSelect?.sid) {
            res.appSelect = appSelect;
            res.osType = appSelect?.platform;
        }
    }
    return res;
};

export const generateAdgroupParams = adgroupParamsPipeline(
    [adgroupTypeParams, adgroupBrandParams, keywordsTypeParams, adgroupUrlParams],
    {adgroupType: {}, segmentBindTypes: []}
);
