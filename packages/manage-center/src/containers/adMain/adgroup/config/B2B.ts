import {autoOrientationConfig} from 'app/containers/fcNew/adgroup/adgroupAutoTargetingStatus/config';
import {FormFieldConfig} from '@baidu/react-formulator';
import queryString from 'query-string';
import {isB2BnewProcessUser} from 'commonLibs/utils/getFlag';
import {fetchB2bPromotionPageList} from 'commonLibs/apis/b2b';
import {FLOW_SCOPE} from 'commonLibs/config/ocpc';
import {B2BMobileUrl} from '../fields/B2BMobileUrl';
import {
    adgroupParamsPipeline,
    adgroupTypeParams,
    adgroupBrandParams,
    keywordsTypeParams
} from '../utils/generateParams';
import {getDefaultAdgroupName} from '../utils';

const getInitialMobileFinalUrl = async (b2bPageId: number, pageType: number) => {
    if (!b2bPageId || !isB2BnewProcessUser()) {
        return null;
    }
    return fetchB2bPromotionPageList({pageId: b2bPageId, pageType}).then(item => {
        const {pageList} = item;
        return pageList[0];
    });
};

export const defaultBrandName = {
    segmentId: '',
    segmentSign: ''
};

export const initialData = {
    adgroupAutoTargetingStatus: autoOrientationConfig.on,
    keywords: [],
    pcFinalUrl: '',
    mobilePageInfo: null,
    brandName: defaultBrandName
};

export const getInitialData = async initProps => {
    const {equipmentType} = initProps;
    const {b2bPageId, pageType} = queryString.parse(window.location.search);
    const mobilePageInfo = await getInitialMobileFinalUrl(+b2bPageId, +pageType);
    return {
        ...initialData,
        mobilePageInfo,
        equipmentType,
        adgroupName: getDefaultAdgroupName()
    };
};

type FormData = typeof initialData;

export const mobileFinalUrl: FormFieldConfig<FormData> = {
    field: 'mobilePageInfo',
    label: '移动落地页链接',
    showRequiredMark: true,
    rules: [['required', '请填写移动落地页链接']],
    use: [B2BMobileUrl],
    visible: formData => {
        return formData.equipmentType !== FLOW_SCOPE.PC;
    }
};

export const fields = {
    mobileFinalUrl
};

export const generateAdgroupParams = adgroupParamsPipeline(
    [adgroupTypeParams, adgroupBrandParams, keywordsTypeParams, b2bUrlParams],
    {adgroupType: {}, segmentBindTypes: []}
);

export function b2bUrlParams(_, {pcFinalUrl, mobilePageInfo}, result) {
    result.adgroupType.pcFinalUrl = pcFinalUrl || '';
    result.adgroupType.mobileFinalUrl = mobilePageInfo?.onlineUrl || '';
    return result;
}


export const formFieldMapForBackend = {
    'mobileFinalUrl': 'mobilePageInfo'
};
