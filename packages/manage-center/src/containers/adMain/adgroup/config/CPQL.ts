import {get} from 'lodash-es';
import {AdgroupUrlTypeEnum} from 'commonLibs/components/commonEntry';
import {autoOrientationConfig} from 'app/containers/fcNew/adgroup/adgroupAutoTargetingStatus/config';
import {getDefaultProductCategoryType} from 'commonLibs/components/selectCard/product/config';
import {StructuredCategoryStatusEnum} from 'commonLibs/components/productSelect/config';
import {fetchSelectedProductInfo} from 'commonLibs/components/productSelect/api';
import {productCategoryTypeEnum} from 'commonLibs/config/enumEntry';
import {
    adgroupParamsPipeline,
    adgroupTypeParams,
    adgroupBrandParams,
    keywordsTypeParams,
    adgroupUrlParams,
    adgroupAnchorParams
} from '../utils/generateParams';
import Product from '../fields/product';
import AdgroupStructuredCategoryType from '../fields/product/adgroupStructuredCategoryType';
import {getDefaultAdgroupName, isAdgroupProductRequired} from '../utils';

export const initialData = {
    catalogId: [],
    autoProductContent: {},
    adgroupAutoTargetingStatus: autoOrientationConfig.on,
    keywords: [],
    pcFinalUrl: '',
    pcTrackParam: '',
    pcTrackTemplate: '',
    mobileFinalUrl: '',
    mobileFinalUrlPreview: '',
    mobileTrackParam: '',
    mobileTrackTemplate: '',
    brandName: {
        segmentId: '',
        segmentSign: ''
    },
    adgroupAgentParam: '',
    adgroupAgentUrl: '',
    adgroupUrlType: AdgroupUrlTypeEnum.normal,
    projectAgentUrl: '',
    adgroupPrice: '',
    newAdgroupStructuredCategoryType: StructuredCategoryStatusEnum.BY_ADGROUP
};

export const getInitialData = async (campaignInfo: any) => {
    const {
        equipmentType,
        projectAgentUrl,
        productCategoryType = getDefaultProductCategoryType(),
        projectStructuredContentIds = [],
        projectStructuredContentIdStrs
    } = campaignInfo;
    const projectProduct = await fetchSelectedProductInfo({adgroupInfo: {
        structuredContentIds: projectStructuredContentIds,
        productCategoryType: productCategoryType
    }});
    return {
        ...initialData,
        equipmentType,
        adgroupName: getDefaultAdgroupName(),
        productCategoryType,
        newAdgroupStructuredCategoryType: (
            projectStructuredContentIds.length > 0 || projectStructuredContentIdStrs?.length
        ) ? StructuredCategoryStatusEnum.BY_PROJECT : StructuredCategoryStatusEnum.BY_ADGROUP,
        // 单元层级默认代入项目层级的产品
        catalogId: projectStructuredContentIdStrs?.length ? projectStructuredContentIdStrs : projectProduct,
        projectStructuredContentIds,
        projectStructuredContentIdStrs,
        projectProduct,
        // 商家智能体，可能需要带入项目的
        ...(
            projectAgentUrl ? {
                projectAgentUrl,
                adgroupUrlType: AdgroupUrlTypeEnum.agent,
                adgroupAgentUrl: projectAgentUrl
            } : {}
        )
    };
};

const product = {
    field: 'catalogId',
    label: '选择产品',
    use: [Product],
    tip: 'adgroupProduct',
    showRequiredMark: isAdgroupProductRequired,
    validators: [
        {
            validator: function (value: any[], formData) {
                if (isAdgroupProductRequired(formData)) {
                    if (!value.length) {
                        return '请选择关联产品';
                    }
                    if (value.length) {
                        const catalogId = get(value, '[0].catalogId');
                        const structuredProductId = get(value, '[0].structuredProductId');
                        if (
                            !catalogId
                            || (
                                formData.newAdgroupStructuredCategoryType !== productCategoryTypeEnum.LAW
                                && !structuredProductId
                            )
                        ) {
                            return '产品缺少必填字段信息，编辑补充完整后即可关联产品';
                        }
                    }
                }
            }
        }
    ],
    componentProps: [
        'newAdgroupStructuredCategoryType',
        'projectProduct',
        'productCategoryType',
        'autoProductContent'
    ]
};


const autoProductContent = {
    field: 'autoProductContent',
    label: '结构化产品信息',
    use: ['Input'],
    visible: () => false
};

const newAdgroupStructuredCategoryType = {
    field: 'newAdgroupStructuredCategoryType',
    label: '',
    use: [AdgroupStructuredCategoryType],
    visible: ({projectStructuredContentIds, projectStructuredContentIdStrs}) => {
        return !!projectStructuredContentIds.length || !!projectStructuredContentIdStrs?.length;
    }
};

export const fields = {
    product,
    autoProductContent,
    newAdgroupStructuredCategoryType
};

export const watch = {
    [newAdgroupStructuredCategoryType.field]: (value, formData) => {
        if (formData.newAdgroupStructuredCategoryType === StructuredCategoryStatusEnum.BY_PROJECT) {
            formData.catalogId = formData.projectStructuredContentIdStrs?.length
                ? formData.projectStructuredContentIdStrs
                : formData.projectProduct;
            formData.autoProductContent = {};
        }
    }
};

function adgroupProductParams(
    _,
    {catalogId, newAdgroupStructuredCategoryType, productCategoryType, projectProduct, projectStructuredContentIdStrs},
    result
) {
    // 产品信息
    if (
        catalogId.length
        && (
            // 非法律行业的，catalogId是[{catalogId}]格式，法律行业，catalogId是[catalogId]格式
            get(catalogId, '[0].catalogId')
            || productCategoryType === productCategoryTypeEnum.LAW
        )
    ) {
        result.adgroupType.productCategoryType = productCategoryType;

        if (newAdgroupStructuredCategoryType === StructuredCategoryStatusEnum.BY_ADGROUP) {
            if (productCategoryType === productCategoryTypeEnum.LAW) {
                result.adgroupType.structuredProductIdStrs = catalogId;
            }
            else {
                // 选择的肯定都是同一个目录下的产品，取第一个提交即可
                result.adgroupType.catalogId = catalogId[0].catalogId;
                result.adgroupType.structuredProductIds = catalogId.map(item => item.structuredProductId);
            }
        }
        else if (newAdgroupStructuredCategoryType === StructuredCategoryStatusEnum.BY_PROJECT) {
            if (productCategoryType === productCategoryTypeEnum.LAW) {
                result.adgroupType.structuredProductIdStrs = projectStructuredContentIdStrs;
            }
            else {
                result.adgroupType.catalogId = projectProduct[0].catalogId;
                result.adgroupType.structuredProductIds = projectProduct.map(item => item.structuredProductId);
            }
        }
    }
    return result;
}

export const generateAdgroupParams = adgroupParamsPipeline(
    [
        adgroupTypeParams,
        adgroupBrandParams,
        keywordsTypeParams,
        adgroupUrlParams,
        adgroupProductParams,
        adgroupAnchorParams
    ],
    {adgroupType: {}, segmentBindTypes: []}
);
