import {AdgroupUrlTypeEnum} from 'commonLibs/components/commonEntry';
import {autoOrientationConfig} from 'app/containers/fcNew/adgroup/adgroupAutoTargetingStatus/config';
import {
    adgroupParamsPipeline,
    adgroupTypeParams,
    adgroupUrlParams,
    adgroupBjhParams,
    keywordsTypeParams
} from '../utils/generateParams';
import {getDefaultAdgroupName} from '../utils';
import {BJHContent} from '../fields/bjhContent';

export const initialData = {
    adgroupAutoTargetingStatus: autoOrientationConfig.on,
    keywords: [],
    pcFinalUrl: '',
    pcTrackParam: '',
    pcTrackTemplate: '',
    mobileFinalUrl: '',
    mobileFinalUrlPreview: '',
    pcUnitUrlPreview: '',
    mobileUnitUrlPreview: '',
    mobileTrackParam: '',
    mobileTrackTemplate: '',
    brandName: {
        segmentId: '',
        segmentSign: ''
    },
    adgroupAgentParam: '',
    adgroupAgentUrl: '',
    adgroupUrlType: AdgroupUrlTypeEnum.bjh,
    adgroupPrice: ''
};

export const getInitialData = (campaignInfo, defaultInitialData = {}) => {
    const {equipmentType} = campaignInfo;
    return {
        ...initialData,
        adgroupName: getDefaultAdgroupName(),
        ...defaultInitialData,
        equipmentType,
        bjhContent: []
    };
};


export const generateAdgroupParams = adgroupParamsPipeline(
    [adgroupTypeParams, adgroupUrlParams, adgroupBjhParams, keywordsTypeParams],
    {adgroupType: {}, segmentBindTypes: []}
);

export const bjhContent = {
    field: 'bjhContent',
    label: '推广内容',
    rules: [['required']],
    use: [BJHContent],
    validators: [
        {
            validator: function (value = []) {
                if (!value.length) {
                    return '请选择推广内容';
                }
            }
        }
    ]
};

export const fields = {
    bjhContent
};
