import {autoOrientationConfig} from 'app/containers/fcNew/adgroup/adgroupAutoTargetingStatus/config';
import StoreInfo from '../fields/storeInfo/index';
import StoreMobileUrlList from '../fields/storeMobileUrl';
import {
    adgroupParamsPipeline,
    adgroupTypeParams,
    adgroupBrandParams,
    keywordsTypeParams
} from '../utils/generateParams';
import queryString from 'query-string';
import {getDefaultAdgroupName} from '../utils';
import {fetchStoreList} from 'commonLibs/apis/store';
import {isStoreNewProcessUser} from 'commonLibs/utils/getFlag';

const radioButtonStyle = {
    width: '88px'
};

const getInitialStoreMobileFinalUrl = async (storePageId: number, pageType: number, storeName: string,
    storeLogo: string = '') => {
    if (!storePageId || !isStoreNewProcessUser()) {
        return [];
    }
    if (storePageId && storeName) {
        return {
            storeId: storePageId,
            storeLogo,
            storeName
        };
    }
    return fetchStoreList({storeIds: [storePageId]}).then(item => {
        const {rows} = item;
        return rows[0];
    });
};

export const storeMobileUrlRadioMap = {
    ALL: 1,
    CUSTOM: 0
};

export const storeMobileUrlOptions = [
    {label: '全部', value: storeMobileUrlRadioMap.ALL, style: radioButtonStyle},
    {label: '自定义', value: storeMobileUrlRadioMap.CUSTOM, style: radioButtonStyle}
];

export const initialData = {
    adgroupAutoTargetingStatus: autoOrientationConfig.on,
    keywords: [],
    brandName: {
        segmentId: '',
        segmentSign: ''
    },
    storeInfo: [],
    storeMobileUrl: storeMobileUrlRadioMap.ALL,
    storeMobileUrlList: []
};

export const getInitialData = async () => {
    const {storePageId, pageType, storeName, storeLogo} = queryString.parse(window.location.search);
    const storeInfo = await getInitialStoreMobileFinalUrl(+storePageId, +pageType, storeName, storeLogo);
    return {
        ...initialData,
        storeInfo: storePageId ? [storeInfo] : [],
        adgroupName: getDefaultAdgroupName()
    };
};

export const watch = {
    'storeInfo': (value, formData) => {
        formData.storeMobileUrl = storeMobileUrlRadioMap.ALL;
        formData.storeMobileUrlList = [];
    },
    'storeMobileUrlList': (value, formData) => {
        if (value.length) {
            formData.storeMobileUrl = storeMobileUrlRadioMap.CUSTOM;
        }
    }
};



export const storeInfo = {
    field: 'storeInfo',
    label: '推广店铺',
    rules: [['required']],
    use: [StoreInfo],
    validators: [
        {
            validator: function (value = []) {
                if (!value.length) {
                    return '请选择推广店铺';
                }
            }
        }
    ]
};


export const storeMobileUrlList = {
    field: 'storeMobileUrlList',
    use: [StoreMobileUrlList],
    visible: formData => {
        return formData.storeMobileUrl === storeMobileUrlRadioMap.CUSTOM;
    },
    validators: function (value = []) {
        if (!value.length) {
            return '请填写落地页';
        }
    },
    componentProps: ['storeInfo']
};

export function storeMobileUrlParams(_, {storeMobileUrl, storeInfo, storeMobileUrlList}, result) {
    if (storeMobileUrl === storeMobileUrlRadioMap.ALL) {
        result.adgroupType.promotionTypes = storeInfo.map(item => (
            {storeId: item.storeId, checkAllPages: true}
        ));
    }
    else {
        result.adgroupType.promotionTypes = storeMobileUrlList.map(item => {
            return {
                storeId: item.storeId,
                checkAllPages: false,
                pageId: item.pageId,
                url: item.pageUrl
            };
        });
    }
    return result;
}

export const generateAdgroupParams = adgroupParamsPipeline(
    [adgroupTypeParams, adgroupBrandParams, keywordsTypeParams, storeMobileUrlParams],
    {adgroupType: {}, segmentBindTypes: []}
);



export const storeMobileUrl = {
    field: 'storeMobileUrl',
    label: '落地页范围',
    showRequiredMark: true,
    use: ['RadioGroup', {
        options: storeMobileUrlOptions
    }]
};

export const fields = {
    storeInfo,
    storeMobileUrl,
    storeMobileUrlList
};

export const formFieldMapForBackend = {
    // 接口返回的门店相关错误码fields都是promotionTypes，前端需要根据错误码区别 映射到不同的组件上
    // 80006139: 「storeid无效」 对应storeInfo层级
    // 80006138: 「pageid无效」 对应storeMobileUrlList层级
    // 80006140: 「url无效」 对应storeMobileUrlList层级
    // 80006141: 「仅支持绑定1000个落地页」 对应storeMobileUrlList层级
    'promotionTypes': (errorCode: number) => {
        if ([80006139].includes(errorCode)) {
            return 'storeInfo';
        }
        if ([80006138, 80006140, 80006141].includes(errorCode)) {
            return 'storeMobileUrlList';
        }
    }
};
