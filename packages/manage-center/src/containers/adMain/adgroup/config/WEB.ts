import {AdgroupUrlTypeEnum} from 'commonLibs/components/commonEntry';
import {autoOrientationConfig} from 'app/containers/fcNew/adgroup/adgroupAutoTargetingStatus/config';
import {
    adgroupParamsPipeline,
    adgroupTypeParams,
    adgroupBrandParams,
    keywordsTypeParams,
    adgroupUrlParams
} from '../utils/generateParams';
import {getDefaultAdgroupName} from '../utils';

export const initialData = {
    adgroupAutoTargetingStatus: autoOrientationConfig.on,
    keywords: [],
    pcFinalUrl: '',
    pcTrackParam: '',
    pcTrackTemplate: '',
    mobileFinalUrl: '',
    mobileFinalUrlPreview: '',
    pcUnitUrlPreview: '',
    mobileUnitUrlPreview: '',
    mobileTrackParam: '',
    mobileTrackTemplate: '',
    brandName: {
        segmentId: '',
        segmentSign: ''
    },
    adgroupAgentParam: '',
    adgroupAgentUrl: '',
    adgroupUrlType: AdgroupUrlTypeEnum.normal,
    adgroupPrice: ''
};

export const getInitialData = (campaignInfo, defaultInitialData = {}) => {
    const {equipmentType} = campaignInfo;
    return {
        ...initialData,
        adgroupName: getDefaultAdgroupName(),
        ...defaultInitialData,
        equipmentType
    };
};

export const generateAdgroupParams = adgroupParamsPipeline(
    [adgroupTypeParams, adgroupBrandParams, keywordsTypeParams, adgroupUrlParams],
    {adgroupType: {}, segmentBindTypes: []}
);
