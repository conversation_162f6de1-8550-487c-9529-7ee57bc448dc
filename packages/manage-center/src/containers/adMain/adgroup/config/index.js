import {Switch, Popover, Link} from '@baidu/one-ui';
import {isEmpty, pick} from 'lodash-es';
import MARKET_TARGET from 'commonLibs/config/marketTarget';
import {isIosAppStructuredUser, isOptimizedPageUser} from 'commonLibs/utils/getFlag';
import osTypeEnum from 'commonLibs/config/osType';
import {campaignBidTypeConfig} from 'commonLibs/config/ocpc';
import {AdgroupUrlTypeEnum, AgentUrlSelect} from 'commonLibs/components/commonEntry';
import BrandName from 'app/containers/fcNew/adgroup/brandName';
import AutoOrientationStatus from '../fields/AdgroupAutoTargetingStatus';
import LiveAnchorSelect from 'app/containers/fcNew/adgroup/anchor';
import {
    getAdMainProcessSteps,
    getCPQLAdgroupUrlFields,
    getAdgroupFields
} from 'app/containers/adMain/config/formFields';
import {
    DESTINATION_URL_MAX_LENGTH_IN_BYTES,
    TEMPLATE_REPLACE_URL_REG,
    TEMPLATE_REPLACE_ALL_URL_REG,
    AGENT_URL_REG,
    TEMPLATE_URL_MAP
} from 'app/components/tableEditor/url/config';
import {getAdgroupUrlIsShow, getAdgroupUrlIsRequired} from 'app/containers/fcNew/adgroup/form/util';
import {isOcpcCampaign} from 'commonLibs/utils/campaign';
import {AdgroupFinalUrl} from '../fields/FinalUrl';
import UrlPreview from 'app/components/urlEditor/urlPreview';
import UnionUrlPreview from 'app/components/tableEditor/url/preview';
import {Keywords} from '../fields/Keywords';
import {convertFieldToConfig, filterWatchByFields} from '../../utils';
import {priceValidate} from 'app/containers/fcNew/adgroup/form/config';
import AdgroupPrice from 'app/containers/fcNew/adgroup/adgroupPrice';
import {storeMobileUrlRadioMap} from './STORE';
import AppSelector from 'app/containers/fcNew/adgroup/appSelect';
import {emptyErrorMap} from 'app/containers/fcNew/adgroup/form/appSetting';
import {appSystemConfig, validateAppUrl} from 'app/containers/fcNew/adgroup/appSelect/config';

const adgroupAutoTargetingStatus = ({campaignAutoTargetingStatus}) => ({
    field: 'adgroupAutoTargetingStatus',
    label: '自动定向',
    tip: 'adgroup_adgroupAutoTargetingStatus',
    showRequiredMark: true,
    use: [
        AutoOrientationStatus,
        {campaignAutoTargetingStatus}
    ],
    componentProps: ({catalogId, newAdgroupStructuredCategoryType, productCategoryType}) => ({
        catalogId,
        productCategoryType,
        newAdgroupStructuredCategoryType
    })
});
const relativeAnchor = {
    field: 'relativeAnchor',
    label: '关联主播',
    rules: [
        ['required', '请选择主播']
    ],
    use: [LiveAnchorSelect]
};

const adgroupUrlType = ({
    isShowAdgroupUrlType,
    isNormalUrlTypeDisabled,
    isAgentUrlTypeDisabled,
    normalUrlTypeTip = '',
    agentCreateUrl = ''
}) => ({
    field: 'adgroupUrlType',
    label: '广告链接类型',
    tip: 'adgroupUrlTypeTipKey',
    use: ['RadioGroup', {options: [
        {
            label: normalUrlTypeTip ? <Popover content={normalUrlTypeTip}><span>落地页链接</span></Popover> : '落地页链接',
            value: AdgroupUrlTypeEnum.normal,
            disabled: isNormalUrlTypeDisabled
        },
        {
            label: isAgentUrlTypeDisabled ? (
                <Popover
                    content={(
                        <span>
                            当前账户暂未查询到可使用的商家智能体，商家智能体作为科学经营的全新阵地，将为您深度链接网民需求与经营服务，助力提升营销获客效率。您可前往
                            <Link type="strong" toUrl={agentCreateUrl} target="_blank">巧舱平台</Link>
                            创建。
                        </span>
                    )}
                >
                    <span>商家智能体</span>
                </Popover>
            ) : '商家智能体',
            value: AdgroupUrlTypeEnum.agent,
            disabled: isAgentUrlTypeDisabled
        }
    ]}],
    visible: () => isShowAdgroupUrlType
});

const useAgentUrl = ({isShowUseAgentUrlField}) => ({
    field: 'useAgentUrl',
    use: ['Checkbox', {
        label: '广告链接类型设置为商家智能体'
    }],
    visible: () => isShowUseAgentUrlField
});

const projectAgentUrl = {
    field: 'projectAgentUrl',
    label: '',
    use: ['Input'],
    visible: () => false
};
const adgroupAgentUrl = ({pending, pageList}) => ({
    field: 'adgroupAgentUrl',
    label: '商家智能体',
    use: [AgentUrlSelect, {isRequired: true, pending, pageList}],
    showRequiredMark: true,
    rules: [
        ['required', '请选择商家智能体'],
        ['legacyIsURL'],
        ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]]
    ],
    visible: formData => {
        return formData.adgroupUrlType === AdgroupUrlTypeEnum.agent;
    }
});
const adgroupAgentParam = () => ({
    field: 'adgroupAgentParam',
    label: '监控后缀',
    tip: 'agentTrackParam',
    rules: [
        ['optional'],
        ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]]
    ],
    use: [
        'Input',
        {
            field: 'adgroupAgentParam',
            placeholder: '示例：?keyword={keywordid}',
            maxLen: DESTINATION_URL_MAX_LENGTH_IN_BYTES,
            width: 400
        }
    ],
    visible: formData => {
        return formData.adgroupUrlType === AdgroupUrlTypeEnum.agent;
    }
});

const adgroupPrice = props => ({
    field: 'adgroupPrice',
    label: '点击出价',
    tip: 'unitBid',
    validators: [
        {
            validator: function (value) {
                const {campaignBid, campaignBidType} = props;
                // 计划自动出价范围内，计划无出价 单元出价必填
                const requiredPrice = campaignBidType === campaignBidTypeConfig.cpc && !campaignBid;
                if (!value && !requiredPrice) {
                    return;
                }
                return priceValidate(value);
            }
        }
    ],
    transform: {parse: e => e?.target?.value},
    use: [AdgroupPrice, {campaignInfo: props}],
    visible: () => {
        return !isOcpcCampaign(props);
    }
});

const keywords = props => ({
    field: 'keywords',
    label: '关键词',
    use: [Keywords, {subMarketingTargetId: props.subMarketingTargetId, campaignId: props.campaignId}],
    componentProps: ({mobileFinalUrl}) => {
        return {
            displayUrl: mobileFinalUrl
        };
    }
});

function getAdgroupUrlFieldsRules(field, props, requiredErrorMessage = '请输入最终访问网址') {
    const isRequired = getAdgroupUrlIsRequired({type: field, ...props});
    return isRequired ? [['required', requiredErrorMessage]] : [];
}

const pcFinalUrl = props => ({
    field: 'pcFinalUrl',
    label: '计算机端落地页链接',
    tip: 'finalUrl',
    componentProps: ['appSelect'],
    showRequiredMark: !isEmpty(getAdgroupUrlFieldsRules('pcFinalUrl', props)),
    rules: [
        ...getAdgroupUrlFieldsRules('pcFinalUrl', props),
        ...(props.isDisabledAgentReg ? [
            ['not:match', AGENT_URL_REG, '落地页链接不支持填写商家智能体，您可通过将当前单元的「广告链接类型」修改为商家智能体进行智能体投放']
        ] : []),
        ['legacyIsURL'],
        ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]]
    ],
    use: [
        AdgroupFinalUrl,
        {fieldName: 'pcFinalUrl', isHideAgentUrl: props.isDisabledAgentReg, isNeedBlur: props.isNeedBlur}
    ],
    visible: formData => getAdgroupUrlIsShow('pcFinalUrl', {...props, ...pick(formData, ['adgroupUrlType'])})
});

const pcTrackParam = props => ({
    field: 'pcTrackParam',
    label: '监控后缀',
    tip: 'trackParam',
    rules: [
        ['optional'],
        ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]]
    ],
    use: [
        'Input',
        {
            field: 'pcTrackParam',
            placeholder: '示例：?keyword={keywordid}',
            maxLen: DESTINATION_URL_MAX_LENGTH_IN_BYTES,
            width: 420
        }
    ],
    visible: formData => getAdgroupUrlIsShow('pcFinalUrl', {...props, ...pick(formData, ['adgroupUrlType'])})
});

const pcTrackTemplate = props => ({
    field: 'pcTrackTemplate',
    label: '追踪模板',
    rules: [
        ['optional'],
        ['legacyIsURL'],
        ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]],
        ['match', TEMPLATE_REPLACE_URL_REG, `追踪模板中必须有且仅有一个${TEMPLATE_URL_MAP.encode}通配符`],
        ['not:match', TEMPLATE_REPLACE_ALL_URL_REG, `追踪模板中必须有且仅有一个${TEMPLATE_URL_MAP.encode}通配符`]
    ],
    use: [
        'Input',
        {
            field: 'pcTrackTemplate',
            placeholder: '示例：https://www.trackingtemplate.com?url={lpurl_encode}',
            maxLen: DESTINATION_URL_MAX_LENGTH_IN_BYTES,
            width: 400
        }
    ],
    visible: formData => getAdgroupUrlIsShow('pcFinalUrl', {...props, ...pick(formData, ['adgroupUrlType'])})
});

const mobileFinalUrl = props => ({
    field: 'mobileFinalUrl',
    label: '移动端落地页链接',
    tip: 'finalUrl',
    showRequiredMark: !isEmpty(getAdgroupUrlFieldsRules('mobileFinalUrl', props)),
    rules: [
        ...(props.marketingTargetId !== MARKET_TARGET.APP ? [['required', '请输入最终访问网址']] : []),
        ...(props.isDisabledAgentReg ? [
            ['not:match', AGENT_URL_REG, '落地页链接不支持填写商家智能体，您可通过将当前单元的「广告链接类型」修改为商家智能体进行智能体投放']
        ] : []),
        ['legacyIsURL'],
        ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]]
    ],
    componentProps: ['appSelect', 'catalogId', 'autoProductContent'],
    use: [
        AdgroupFinalUrl,
        {fieldName: 'mobileFinalUrl', isHideAgentUrl: props.isDisabledAgentReg, isNeedBlur: props.isNeedBlur}
    ],
    visible: formData => getAdgroupUrlIsShow('mobileFinalUrl', {...props, ...pick(formData, ['adgroupUrlType'])})
});

const mobileFinalUrlPreview = props => ({
    field: 'mobileFinalUrlPreview',
    label: '',
    use: [UrlPreview],
    componentProps: ({mobileFinalUrl, marketingTargetId}) => {
        return {
            url: mobileFinalUrl,
            marketingTargetId
        };
    },
    visible: formData => {
        return (
            isOptimizedPageUser()
            && !!formData.mobileFinalUrl
            && getAdgroupUrlIsShow('mobileFinalUrl', {...props, ...pick(formData, ['adgroupUrlType'])})
        );
    }
});

const pcUnitUrlPreview = props => ({
    field: 'pcUnitUrlPreview',
    label: '计算机网址预览',
    use: [
        UnionUrlPreview,
        {
            urlField: 'pcFinalUrl',
            trackParamsField: 'pcTrackParam',
            trackTemplateField: 'pcTrackTemplate'
        }
    ],
    componentProps: formData => ({
        materiaData: {
            pcFinalUrl: formData.pcFinalUrl,
            pcTrackParam: formData.pcTrackParam,
            pcTrackTemplate: formData.pcTrackTemplate
        }
    }),
    visible: () => getAdgroupUrlIsShow('pcFinalUrl', props)
});

const mobileUnitUrlPreview = props => ({
    field: 'mobileUnitUrlPreview',
    label: '移动网址预览',
    use: [
        UnionUrlPreview,
        {
            urlField: 'mobileFinalUrl',
            trackParamsField: 'mobileTrackParam',
            trackTemplateField: 'mobileTrackTemplate'
        }
    ],
    componentProps: formData => ({
        materiaData: {
            mobileFinalUrl: formData.mobileFinalUrl,
            mobileTrackParam: formData.mobileTrackParam,
            mobileTrackTemplate: formData.mobileTrackTemplate
        }
    }),
    visible: () => getAdgroupUrlIsShow('mobileFinalUrl', props)
});

const mobileTrackParam = props => ({
    field: 'mobileTrackParam',
    label: '监控后缀',
    tip: 'trackParam',
    rules: [
        ['optional'],
        ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]]
    ],
    use: [
        'Input',
        {
            field: 'mobileTrackParam',
            placeholder: '示例：?keyword={keywordid}',
            maxLen: DESTINATION_URL_MAX_LENGTH_IN_BYTES,
            width: 400
        }
    ],
    visible: formData => getAdgroupUrlIsShow('mobileFinalUrl', {...props, ...pick(formData, ['adgroupUrlType'])})
});

const mobileTrackTemplate = props => ({
    field: 'mobileTrackTemplate',
    label: '追踪模板',
    rules: [
        ['optional'],
        ['legacyIsURL'],
        ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]],
        ['match', TEMPLATE_REPLACE_URL_REG, `追踪模板中必须有且仅有一个${TEMPLATE_URL_MAP.encode}通配符`],
        ['not:match', TEMPLATE_REPLACE_ALL_URL_REG, `追踪模板中必须有且仅有一个${TEMPLATE_URL_MAP.encode}通配符`]
    ],
    use: [
        'Input',
        {
            field: 'mobileTrackTemplate',
            placeholder: '示例：https://www.trackingtemplate.com?url={lpurl_encode}',
            maxLen: DESTINATION_URL_MAX_LENGTH_IN_BYTES,
            width: 400
        }
    ],
    visible: formData => getAdgroupUrlIsShow('mobileFinalUrl', {...props, ...pick(formData, ['adgroupUrlType'])})
});

const brandName = () => ({
    field: 'brandName',
    label: '品牌信息',
    tip: 'brandNameProcess',
    showRequiredMark: true,
    use: [BrandName, {selectWidth: 400, disabled: true}],
    validators: [
        {
            validator: function (value) {
                const {segmentId} = value;
                if (!segmentId && segmentId !== 0) {
                    return '请选择品牌信息';
                }
            }
        }
    ]
});

const adgroupName = {
    field: 'adgroupName',
    label: '单元名称',
    showRequiredMark: true,
    rules: [
        ['required', '请填写单元名称'],
        ['textRange', [1, 30]]
    ],
    use: [
        'Input',
        {maxLen: 30, width: 400}
    ]
};

const osType = props => ({
    field: 'osType',
    label: '选择应用',
    rules: [
        ['required', '请选择操作系统']
    ],
    use: [
        'RadioGroup',
        {
            options: appSystemConfig,
            disabled: !!props.projectApplicationSid
        }
    ]
});

const appSelect = props => ({
    field: 'appSelect',
    label: ' ',
    validators: [
        (value, formData) => {
            const osType = formData.osType;
            if (isEmpty(value) || (osType === osTypeEnum.IOS.value && !value.appUrl)) {
                return emptyErrorMap[osType];
            }
            const errors = [
                validateAppUrl(value.appUrl || ''),
                value.errorMessage,
                // 防止在收到ajax请求回复前,没有拿到url的解析信息就提交(同时loading中不显示报错信息),这里用空格隐藏
                value?.appId ? '' : ' '
            ].filter(v => v);
            if (errors.length > 0) {
                return errors[0];
            }
            return;
        }
    ],
    componentProps: ['mobileFinalUrl', 'osType'],
    use: [
        AppSelector,
        {
            projectApplicationSid: props.projectApplicationSid,
            iosInputReadOnly: isIosAppStructuredUser()
        }
    ]
});

const appShopDirectStatus = {
    field: 'appShopDirectStatus',
    label: '应用商店直投',
    tip: 'appStoreDirectStatus',
    use: [Switch],
    componentProps: formData => ({
        checked: formData.appShopDirectStatus
    }),
    visible: formData => formData.osType === osTypeEnum.ANDROID.value,
    helpInlineText: '开启后，点击创意的下载按钮将优先跳转至手机系统应用商店的安装详情页，跳转失败则正常下载渠道包'
};

const baseFields = {
    adgroupAutoTargetingStatus,
    relativeAnchor,
    keywords,
    adgroupPrice,
    osType,
    appSelect,
    appShopDirectStatus,
    adgroupUrlType,
    useAgentUrl,
    projectAgentUrl,
    adgroupAgentParam,
    adgroupAgentUrl,
    pcFinalUrl,
    mobileFinalUrl,
    mobileFinalUrlPreview,
    pcUnitUrlPreview,
    mobileUnitUrlPreview,
    brandName,
    adgroupName,
    pcTrackParam,
    pcTrackTemplate,
    mobileTrackParam,
    mobileTrackTemplate
};

const pcUrlGroup = props => ({
    group: 'pcUrlGroup',
    use: ['ExpandableGroup', {title: ['展开监控后缀设置', '收起监控后缀设置']}],
    componentProps: formData => {
        return {defaultExpand: !!formData.pcTrackParam || !!formData.pcTrackTemplate};
    },
    visible: formData => getAdgroupUrlIsShow('pcFinalUrl', {...props, ...pick(formData, ['adgroupUrlType'])})
});


const mobileUrlGroup = props => ({
    group: 'mobileUrlGroup',
    use: ['ExpandableGroup', {title: ['展开监控后缀设置', '收起监控后缀设置']}],
    componentProps: formData => {
        return {defaultExpand: !!formData.mobileTrackParam || !!formData.mobileTrackTemplate};
    },
    visible: formData => getAdgroupUrlIsShow('mobileFinalUrl', {...props, ...pick(formData, ['adgroupUrlType'])})
});


const settingsGroup = {
    group: 'settingsGroup',
    use: ['ExpandableGroup', {title: ['展开更多设置', '收起更多设置']}],
    visible: formData => {
        return formData.storeInfo.length;
    },
    componentProps: formData => {
        return {
            defaultExpand: formData.storeMobileUrl === storeMobileUrlRadioMap.CUSTOM
        };
    }
};

const baseGroups = {
    pcUrlGroup,
    mobileUrlGroup,
    settingsGroup
};

export const remoteConfigLoaders = {
    [MARKET_TARGET.B2B]: () => import('./B2B'),
    [MARKET_TARGET.STORE]: () => import('./STORE'),
    [MARKET_TARGET.CPQL]: () => import('./CPQL'),
    [MARKET_TARGET.WEB]: () => import('./WEB'),
    [MARKET_TARGET.APP]: () => import('./APP'),
    [MARKET_TARGET.NATIVE]: () => import('./NATIVE')
};

const baseWatch = {
    osType: function (value, formData) {
        formData.appSelect = {};
    },
    appSelect: function (value, formData) {
        const mobileFinalUrl = formData.mobileFinalUrl;
        if (mobileFinalUrl && (value?.sid || value?.appUrl || value?.downloadUrl)) {
            formData.mobileFinalUrl = '';
            formData.mobileTrackParam = '';
            formData.mobileTrackTemplate = '';
        }
    },
    adgroupAgentUrl: (value, formData) => {
        if (value) {
            formData.useAgentUrl = true;
        }
    }
};

const baseFormFieldMapForBackend = {};

export async function getFormConfig(props) {
    const {marketingTargetId, adType, subMarketingTargetId} = props;
    const adgroupFields = getAdgroupFields({marketingTargetId, adType, subMarketingTargetId});
    const fieldsConfig = adgroupFields.config;

    let remoteConfig = {};
    try {
        remoteConfig = await remoteConfigLoaders[marketingTargetId]();
    }
    catch (e) {
        console.error(`(marketingTargetId: ${marketingTargetId})是否存在配置文件`);
    }

    return {
        adgroupFields,
        remoteConfig,
        fields: fieldsConfig.map(({group, title, fields, slots}) => {
            return {
                group,
                use: ['Group', {title, slots}],
                fields: fields.map(field => convertFieldToConfig({
                    field,
                    baseFields,
                    baseGroups,
                    remoteConfig,
                    props
                }))
            };
        })
    };
}

function MyCustomGroupComponent({children}) {
    return (
        <>
            {children}
        </>
    );
}

export async function getAdgroupUrlFormConfig(props) {
    const {marketingTargetId} = props;
    const adgroupFields = getCPQLAdgroupUrlFields();
    const fieldsConfig = adgroupFields.config;
    let remoteConfig = {};
    try {
        remoteConfig = await remoteConfigLoaders[marketingTargetId]();
    }
    catch (e) {
        console.error(`(marketingTargetId: ${marketingTargetId})是否存在配置文件`);
    }
    return {
        adgroupFields,
        remoteConfig,
        fields: fieldsConfig.map(({group, title, fields}) => {
            return {
                group,
                use: [MyCustomGroupComponent, {title}],
                fields: fields.map(field => convertFieldToConfig({
                    field,
                    baseFields,
                    baseGroups,
                    remoteConfig,
                    props
                }))
            };
        })
    };
}

export async function initialAdgroupForm({v2, getFormConfigFn = getFormConfig, ...props}) {
    const {marketingTargetId, subMarketingTargetId} = props;
    const {fields, remoteConfig} = await getFormConfigFn(props);

    const watch = filterWatchByFields({
        watch: {
            ...baseWatch,
            ...remoteConfig.watch
        },
        fields
    });
    return {
        config: {
            fields,
            watch
        },
        steps: getAdMainProcessSteps({marketingTargetId, v2, subMarketingTargetId}),
        /**
         * 后端在接口错误时会返回需要setFieldsError的字段, 但是这些字段可能不在当前表单中
         * 所以需要在这里配置一下，以便在接口错误时，能够正确的设置错误
         */
        formFieldMapForBackend: {
            ...baseFormFieldMapForBackend,
            ...(remoteConfig.formFieldMapForBackend || {})
        }
    };
}

export async function getInitialData(props) {
    const {marketingTargetId} = props;
    const {defaultInitialData, ...campaignInfo} = props;
    const remoteConfig = await remoteConfigLoaders[marketingTargetId]();
    const initialData = await remoteConfig.getInitialData(campaignInfo, defaultInitialData);
    return {initialData};
}
