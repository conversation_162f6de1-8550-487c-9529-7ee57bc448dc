/*
 * @file: todo
 * <AUTHOR>
 * @date Tue May 09 2023
 */

import {PageTips} from '@baidu/one-ui-pro';
import {Button} from '@baidu/one-ui';

export const renderError = (err, {recover}, ref) => {

    if (ref && ref.current) {
        ref.current.recover = recover;
    }
    const operator = (
        <Button
            onClick={recover}
            type="text-strong"
            style={{verticalAlign: 'baseline', marginLeft: '4px'}}
        >
            刷新
        </Button>
    );
    return (
        <PageTips.Error
            type="frontEnd"
            question="页面加载失败"
            operator={operator}
        />
    );
};
