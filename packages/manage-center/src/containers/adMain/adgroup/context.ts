import {createContext, useContext} from 'react';
import {create} from 'commonLibs/magicSuspenseBoundary';

export const {
    Boundary: ProcessAdgroupBoundary,
    useResource: useProcessAdgroupResource
} = create({cacheContextDisplayName: 'ProcessAdgroup'});

const CampaignInfoContext = createContext({});
export const CampaignInfoProvider = CampaignInfoContext.Provider;
export const useCampaignInfo = () => useContext(CampaignInfoContext);

const FormInstanceContext = createContext({});
export const FormInstance = FormInstanceContext.Provider;
export const useFormInstance = () => useContext(FormInstanceContext);


const StructContentInstanceContext = createContext({});
export const StructContentInstance = StructContentInstanceContext.Provider;
export const useStructContentInstance = () => useContext(StructContentInstanceContext);
