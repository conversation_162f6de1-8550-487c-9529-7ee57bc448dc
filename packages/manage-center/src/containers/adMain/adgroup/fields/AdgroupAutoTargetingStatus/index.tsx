import {IconInfoCircleSolid} from 'dls-icons-react';
import {StructuredCategoryStatusEnum} from 'commonLibs/components/productSelect/config';
import {productCategoryTypeEnum} from 'commonLibs/config/enumEntry';
import AutoOrientationStatus from 'app/containers/fcNew/adgroup/adgroupAutoTargetingStatus';
import './style.less';

export default function AutoOrientationTargetingStatus(props) {
    const {catalogId, newAdgroupStructuredCategoryType, productCategoryType} = props;
    const tourismSelectedProduct = (
        newAdgroupStructuredCategoryType === StructuredCategoryStatusEnum.BY_PROJECT || catalogId?.length > 0)
        && productCategoryType === productCategoryTypeEnum.LXT;
    return (
        <div className="auto-orientation-targeting-status">
            <AutoOrientationStatus
                {...props}
                disabled={tourismSelectedProduct}
            />
            {tourismSelectedProduct && (
                <span className="tip">
                    <IconInfoCircleSolid className="icon" />
                    您当前已关联旅行社相关产品，为了更好地理解您的业务信息，帮助您全面覆盖目标人群，不支持关闭自动定向
                </span>
            )}
        </div>
    );
}
