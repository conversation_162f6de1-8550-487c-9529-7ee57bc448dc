/*
 * @file: b2b 落地页配置
 * <AUTHOR>
 * @date Sun Apr 30 2023
 */

import {promotionSceneType} from 'commonLibs/config/promotionScene';
import {getConfigFromDataSource} from 'commonLibs/types/base/config';


const pageTypeDataSource = [
    [promotionSceneType.store, 'store', '店铺推广页'],
    [promotionSceneType.product, 'product', '商品推广页']
] as const;

const {
    valueMapByKey: pageTypeMap,
    nameMapByValue: pageTypeNameMap
} = getConfigFromDataSource(pageTypeDataSource);

// 推广商品：https://b2bwork.baidu.com/promote/wise/prodpage
// 推广店铺：https://b2bwork.baidu.com/promote/wise/shoppage
export const newB2BUrlMap = {
    [pageTypeMap.store]: 'https://b2bwork.baidu.com/promote/wise/shoppage',
    [pageTypeMap.product]: 'https://b2bwork.baidu.com/promote/wise/prodpage'
} as const;


export const filterValueMap = {
    all: 0,
    selectable: 1
} as const;

export const filterOptions = [
    {label: '可选落地页', value: filterValueMap.selectable},
    {label: '全部落地页', value: filterValueMap.all}
] as const;

export {
    pageTypeMap,
    pageTypeNameMap
};

export const pageTypeList = pageTypeDataSource.map(([value, key, label]) => ({
    label,
    value
}));