/*
 * @file: 爱采购-店铺落地页url （移动最终访问网址）
 * <AUTHOR>
 * @date Thu Apr 27 2023
 */

// 注意：爱采购智能创编也使用了这个组件，所以如果有较大升级的话请同步测试一下智能创编

import {useState, useImperativeHandle, forwardRef, useCallback, useEffect, useRef, useMemo} from 'react';
import {useBoolean, useRequest} from 'huse';
import {
    Drawer, Button, Pagination, SearchBox,
    Message, Checkbox, Empty, Loading, Popover, Tabs,
    Alert
} from '@baidu/one-ui';
import {useControl} from 'commonLibs/hooks/externalControl';
import {useCampaignInfo} from '../../context';
import {useTablePagination, toOneUIPaginationProps} from 'commonLibs/hooks/pagination';
import {IconEye, IconEdit, IconPlus} from 'dls-icons-react';
import {UISelect} from '@baidu/react-formulator';
import {fetchB2bPromotionPageList, fetchB2bComponentName, fetchLandingPagesByProduct} from 'commonLibs/apis/b2b';
import PagePreviewModal from '@baidu/m-vibranium-react/lib/components/pagePreview/pagePreviewModal';
import {create} from 'commonLibs/magicSuspenseBoundary';
import SaveButton from 'commonLibs/components/saveButton';
import copy from 'commonLibs/utils/copy';

import './style.less';

// toFix: 不能这样在组件引入吗？看起来现在都是分别在less文件里面引入的
// import '@baidu/m-vibranium-react/lib/components/pagePreview/index.css';

const {
    Boundary,
    useResource
} = create({cacheContextDisplayName: 'B2BMobileUrlCache'});

import {pageTypeList, filterValueMap, filterOptions, pageTypeNameMap, pageTypeMap, newB2BUrlMap} from './config';
import {renderError} from '../../config/uiConfig';
import {formatTransTypesToB2BCTList} from 'commonLibs/config/ocpc';

const modeMap = {
    single: 'single',
    multiple: 'multiple'
};
export const B2BMobileUrlDrawer = forwardRef(B2BMobileUrlDrawer_);

export function B2BMobileUrl({value: pageInfo, onChange}) {
    const [B2BMobileUrlEditor, {open}] = useControl(B2BMobileUrlDrawer);
    const onPageSelect = useCallback(pageInfo => {
        onChange(pageInfo);
    }, [onChange]);
    const copyPageUrl = () => {
        copy(pageInfo?.onlineUrl);
    };
    return (
        <div>
            {
                pageInfo?.onlineUrl
                    ? (
                        <>
                            <div className="b2b-page-selected-preview">
                                <div
                                    className="b2b-page-selected-preview-img"
                                    style={{backgroundImage: `url(${pageInfo.thumbnailUrl})`}}
                                    alt={pageInfo.pageName}
                                />
                                <div className="b2b-page-selected-preview-info">
                                    <div className="b2b-page-selected-preview-info-name">
                                        {pageInfo.pageName}{pageInfo.pageType === pageTypeMap.store ? '-全部落地页' : ''}
                                    </div>
                                    <div className="b2b-page-selected-preview-info-id">
                                        <span className="b2b-page-selected-preview-info-id-label">ID：</span>
                                        <span className="b2b-page-selected-preview-info-id-value">
                                            {pageInfo.pageId}
                                        </span>
                                    </div>
                                </div>
                                <Button
                                    type="text-strong"
                                    className="b2b-page-selected-preview-btn"
                                    onClick={open}
                                >
                                    更换
                                </Button>
                            </div>
                            <div className="copy-b2b-page-url">
                                <Button type="text-strong" onClick={copyPageUrl}>复制链接</Button>
                            </div>
                        </>
                    )
                    : (
                        <Button
                            onClick={open}
                            icon={IconPlus}
                            className='adgroup-page-select-btn'
                        >
                            选择落地页
                        </Button>
                    )
            }
            <B2BMobileUrlEditor pageInfo={pageInfo} onChange={onPageSelect} />
        </div>
    );
}

function B2BMobileUrlDrawer_({pageInfo, onChange, mode, isSmartCompose, productIds, maxSelectNum}, ref) {
    const [visible, {on: open, off: close}] = useBoolean(false);

    useImperativeHandle(ref, () => ({open, close}));
    const {promotionScene, campaignTransTypes} = useCampaignInfo();

    const [localPageInfo, setLocalPageInfo] = useState(pageInfo);
    useEffect(() => {
        setLocalPageInfo(pageInfo);
    }, [pageInfo]);

    const onOk = pageInfo => {
        onChange(pageInfo);
        close();
    };

    const bottomDesc = localPageInfo
        ? (
            <div className="b2b-pages-drawer-select-msg">
                {
                    mode === modeMap.multiple
                        ? `已选择${localPageInfo.length}项`
                        : `已选择 ${pageTypeNameMap[localPageInfo.pageType] || ''} 1项`
                }
            </div>
        )
        : null;

    return (
        <Drawer
            placement="right"
            visible={visible}
            onClose={close}
            size="small"
            destroyOnClose
            width={864}
            title="选择落地页"
        >
            <B2BPageInfoInput
                value={localPageInfo}
                onChange={setLocalPageInfo}
                promotionScene={promotionScene}
                campaignTransTypes={campaignTransTypes}
                mode={mode}
                isSmartCompose={isSmartCompose}
                maxSelectNum={maxSelectNum}
                productIds={productIds}
            />
            <div className="b2b-pages-drawer-footer">
                <div className="b2b-pages-drawer-footer-inner">
                    <SaveButton
                        className="b2b-pages-drawer-footer-btn"
                        onClick={() => onOk(localPageInfo)}
                        disabled={!localPageInfo}
                        type="primary"
                    >
                        确定
                    </SaveButton>
                    <Button className="b2b-pages-drawer-footer-btn" onClick={close}>取消</Button>
                    {bottomDesc}
                </div>
            </div>
        </Drawer>
    );
}

const ctListDisableReason = '当前落地页缺少和优化目标相匹配的转化组件，不可用于投放';
const maxNumDisableReason = '当前已选落地页数量达到最大值';

function checkItemStatus(item, {hasReachMaxSelectNum, isSelected, b2bCTList}) {
    const {ctList} = item;
    // 如果ctList不包含计划的转化类型的某一项，说明该落地页不可用
    const disabledByCTList = b2bCTList.length
        && ctList.every(ct => !b2bCTList.includes(ct));
    const disabledByMaxNum = !isSelected && hasReachMaxSelectNum;
    const isDisabled = disabledByCTList || disabledByMaxNum;
    const disabledReason = isDisabled
        ? (disabledByCTList ? ctListDisableReason : maxNumDisableReason)
        : '';
    return {
        isDisabled,
        disabledReason
    };
}

const B2BMobileList = forwardRef(({
    value: pageInfo,
    onChange,
    pageType,
    campaignTransTypes = [],
    query,
    isAll,
    pagination,
    paginationMethods,
    mode = modeMap.single,
    isSmartCompose, // 是否智能创编
    productIds,
    maxSelectNum // multiple mode下，最多选择多少个，为0或不传则为不限制
}, ref) => {

    const [previewVisible, {on: openPreview, off: closePreview}] = useBoolean(false);
    const [previewUrl, setPreviewUrl] = useState('');

    const b2bCTList = formatTransTypesToB2BCTList(campaignTransTypes);
    // 提前把已选中的数据换算成map，这样检查item是否已选中时不用每次都计算这个一遍
    const selectedMap = useMemo(() => {
        return mode === modeMap.multiple && pageInfo.reduce((res, curr) => {
            return {...res, [curr.onlineUrl]: true};
        }, {});
    }, [mode, pageInfo]);
    // 是否已经到达选多选择数量，如果已经到达，则不能再选更多落地页
    const hasReachMaxSelectNum = mode === modeMap.multiple && maxSelectNum && pageInfo.length >= maxSelectNum;

    const requestPageListArgs = [
        fetchB2bPromotionPageList,
        {
            ...pagination,
            query,
            ...(
                (isAll || !b2bCTList.length) ? {} : {ctList: b2bCTList}
            ),
            pageType
        }
    ];
    const requestPageListByProductIdsArgs = [
        fetchLandingPagesByProduct,
        {
            productIds,
            ctList: isAll ? undefined : b2bCTList
        }
    ];

    // 智能创编模式，商品的落地页用新接口 fetchLandingPagesByProduct，其他情况保持原样
    const useResourceArgs = isSmartCompose ? requestPageListByProductIdsArgs : requestPageListArgs;

    const [{
        totalNum, pageList, b2bShopUrl, b2bGoodsUrl
    }, {expire, refresh: refreshList}] = useResource(...useResourceArgs);

    const refresh = useCallback(() => {
        expire();
        if (isSmartCompose) {
            refreshList();
        }
        else {
            paginationMethods.resetPage();
        }
    }, [expire, paginationMethods, isSmartCompose, refreshList]);

    useImperativeHandle(ref, () => ({
        refresh,
        linkMapByPageType: {
            [pageTypeMap.store]: b2bShopUrl,
            [pageTypeMap.product]: b2bGoodsUrl
        }
    }), [refresh, b2bShopUrl, b2bGoodsUrl]);

    const paginationProps = toOneUIPaginationProps({...pagination, ...paginationMethods}, totalNum);


    const previewProps = {
        visible: previewVisible,
        onModalClose: closePreview,
        isShowPcLink: false, // 是否展示Pc
        isShowCopyLink: false,
        isShowPageSelect: false,
        previewText: '预览页面链接，不可用于投放，且仅做样式展示',
        pageList: [{
            id: 0,
            previewUrl: previewUrl
        }]
    };

    return (
        <div>
            {
                !!pageList.length && (
                    <>
                        <div className="b2b-pages-list">
                            {
                                pageList.map(item => {

                                    const {
                                        pageId,
                                        onlineUrl,
                                        thumbnailUrl,
                                        pageName,
                                        previewUrl,
                                        editUrl,
                                        pageType
                                    } = item;

                                    const isProduct = pageType === pageTypeMap.product;
                                    const isSelected = mode === modeMap.multiple
                                        ? selectedMap[onlineUrl]
                                        : onlineUrl === pageInfo?.onlineUrl;
                                    // 检查当前页是否可以选择
                                    const {isDisabled, disabledReason} = checkItemStatus(
                                        item,
                                        {hasReachMaxSelectNum, isSelected, b2bCTList}
                                    );

                                    const onClick = () => {
                                        if (isDisabled) {
                                            return;
                                        }
                                        if (mode === modeMap.multiple) {
                                            if (isSelected) {
                                                onChange(pageInfo.filter(({onlineUrl}) => {
                                                    return onlineUrl !== item.onlineUrl;
                                                }));
                                            }
                                            else {
                                                onChange([...pageInfo, item]);
                                            }
                                        }
                                        else {
                                            onChange(isSelected ? null : item);
                                        }
                                    };
                                    const onPreview = e => {
                                        setPreviewUrl(previewUrl);
                                        openPreview();
                                        e.stopPropagation();
                                    };

                                    const onEdit = e => {
                                        window.open(editUrl, '_blank');
                                        e.stopPropagation();
                                    };

                                    return (
                                        <Popover key={pageId} content={disabledReason}>
                                            <div
                                                className={
                                                    `
                                                        b2b-pages-item
                                                        ${isSelected ? 'active' : ''}
                                                        ${isDisabled ? 'disabled' : ''}
                                                    `
                                                }
                                                onClick={onClick}
                                            >
                                                <div
                                                    className={
                                                        `
                                                            b2b-pages-item-img-box
                                                            ${isProduct ? 'b2b-pages-item-img-box-product' : ''}
                                                        `
                                                    }
                                                    style={{
                                                        backgroundImage: `url(${thumbnailUrl})`
                                                    }}
                                                >
                                                    <Checkbox
                                                        value={pageId}
                                                        checked={isSelected}
                                                        className="b2b-pages-item-checkbox"
                                                        disabled={isDisabled}
                                                    />
                                                </div>
                                                <div className="b2b-pages-item-info">
                                                    <div className="b2b-pages-item-name">{pageName}</div>
                                                    <div className="b2b-pages-item-sub-info">
                                                        <div className="b2b-pages-id">
                                                            ID: {pageId}
                                                        </div>
                                                        <div className="b2b-pages-links">
                                                            <Button
                                                                className="b2b-pages-link"
                                                                type="text-strong"
                                                                onClick={onPreview}
                                                                size="small"
                                                            >
                                                                <IconEye />
                                                                <span className="b2b-pages-link-text">预览</span>
                                                            </Button>
                                                            <Button
                                                                className="b2b-pages-link"
                                                                type="text-strong"
                                                                onClick={onEdit}
                                                                size="small"
                                                            >
                                                                <IconEdit />
                                                                <span className="b2b-pages-link-text">编辑</span>
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </Popover>

                                    );
                                })
                            }
                        </div>
                        <Pagination {...paginationProps} hideOnSinglePage />
                    </>
                )
            }
            {
                !pageList.length && (
                    <Empty className="b2b-page-center-content" description="暂无可选落地页" />
                )
            }
            <PagePreviewModal {...previewProps} />
        </div>
    );
});


export function B2BPageInfoInput({
    value: pageInfo, onChange, promotionScene, campaignTransTypes,
    mode, isSmartCompose, productIds, maxSelectNum
}) {
    const {data = {}} = useRequest(fetchB2bComponentName, {campaignTransTypes});
    const {componentList = []} = data;

    const [pageTypeKey, setPageTypeKey] = useState(`${promotionScene || pageTypeList[0].value}`);
    const [query, setQuery] = useState('');
    const [searchInputValue, setSearchInputValue] = useState('');
    const [isAll, setIsAll] = useState(false);
    const recoverRef = useRef({});
    const ref = useRef({});
    const [pagination, paginationMethods] = useTablePagination({
        pageNo: 1,
        pageSize: 20
    });

    const pageType = +pageTypeKey;

    const searchProps = {
        size: 'medium',
        value: searchInputValue,
        onChange: e => setSearchInputValue(e.target.value),
        placeholder: '请输入推广页名称或ID',
        onSearch: e => {
            setQuery(e.target.value);
            paginationMethods.resetPage();
        },
        onClearClick: () => {
            setQuery('');
            paginationMethods.resetPage();
        },
        className: 'b2b-pages-search'
    };

    const onTabChange = useCallback(key => {
        setPageTypeKey(key);
        setSearchInputValue('');
        setQuery('');
        paginationMethods.resetPage();
    }, [paginationMethods]);

    const hasPromotionScene = promotionScene && pageTypeNameMap[promotionScene];
    const isProductPromotion = promotionScene && promotionScene === pageTypeMap.product;

    const title = isProductPromotion
        ? null
        : (
            <>
                {
                    // 批量时没有具体落地页类型，修改时提示用户仅对同类型落地页生效
                    !hasPromotionScene && (
                        <Alert
                            content="当前修改操作仅对同类型落地页生效，请核对后提交"
                            type="warning"
                            showIcon
                            className="b2b-pages-drawer-alert-with-tabs"
                        />
                    )
                }
                <Tabs
                    bordered={false}
                    activeKey={pageTypeKey}
                    onChange={onTabChange}
                >
                    {
                        pageTypeList.map(({label, value}) => (
                            <Tabs.TabPane
                                key={`${value}`}
                                tab={`${label}`}
                            />
                        ))
                    }
                </Tabs>
            </>
        );

    const selectProps = {
        options: filterOptions,
        value: isAll ? filterValueMap.all : filterValueMap.selectable,
        onChange: useCallback(value => {
            setIsAll(value === filterValueMap.all);
            paginationMethods.resetPage();
        }, [setIsAll, paginationMethods]),
        width: 145
    };

    const onRefresh = useCallback(() => {
        if (ref.current?.refresh) {
            ref.current.refresh();
        }
        else {
            recoverRef.current?.recover?.();
        }
    }, []);

    return (
        <div className="b2b-pages-drawer-container">
            <div className="b2b-pages-title">
                {title}
                {
                    hasPromotionScene && (
                        <Alert
                            content={`落地页需要包含至少一个您所选优化目标对应的组件:${componentList.join('、')}`}
                            type="info"
                            showIcon
                            className="b2b-pages-drawer-alert"
                        />
                    )
                }
                <div className="b2b-pages-control">
                    {/* 智能创编模式下，用了新接口，没有搜索过滤了 */}
                    {!isSmartCompose && (
                        <div>
                            <SearchBox {...searchProps} />
                            <UISelect {...selectProps} />
                        </div>
                    )}

                    {/* 这里比较无语， Link组件不支持onClick, 只能改成Button， 还得多加个baseline的样式对齐 */}
                    <Message type="aux">
                        <>未找到落地页？请 </>
                        <Button
                            style={{verticalAlign: 'baseline'}}
                            type="text-strong"
                            onClick={onRefresh}
                        >
                            刷新
                        </Button>
                        <> 或前往 </>
                        <Button
                            style={{verticalAlign: 'baseline'}}
                            type="text-strong"
                            onClick={() => {
                                const defaultLink = newB2BUrlMap[pageType];
                                const linkFromBackend = ref.current?.linkMapByPageType?.[pageType];
                                window.open(linkFromBackend || defaultLink);
                            }}
                        >
                            爱采购
                        </Button>
                        <> 添加</>
                    </Message>
                </div>
            </div>
            <div className="b2b-pages-drawer">
                <Boundary
                    pendingFallback={
                        <Loading
                            className="b2b-page-center-content b2b-page-center-loading"
                            tip={<span className="b2b-page-center-loading-text">加载中</span>}
                            textDirection="vertical"
                            type="strong"
                        />
                    }
                    renderError={(...args) => renderError(...args, recoverRef)}
                >
                    <B2BMobileList
                        ref={ref}
                        onChange={onChange}
                        value={pageInfo}
                        pageType={pageType}
                        campaignTransTypes={campaignTransTypes}
                        query={query}
                        isAll={isAll}
                        pagination={pagination}
                        paginationMethods={paginationMethods}
                        mode={mode}
                        isSmartCompose={isSmartCompose}
                        productIds={productIds}
                        maxSelectNum={maxSelectNum}
                    />
                </Boundary>
            </div>
        </div>
    );
};
