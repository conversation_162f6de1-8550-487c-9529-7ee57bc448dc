
.b2b-pages-drawer-container {
    --dls-tab-menu-padding: 0;
    .b2b-pages-drawer-alert {
        margin-bottom: @dls-padding-unit * 4;
    }
    .b2b-pages-drawer-alert-with-tabs {
        margin-bottom: @dls-padding-unit * 2;
    }
    .b2b-pages-drawer-title {
        margin: @dls-padding-unit*6 0;
        font-size: @dls-font-size-2;
        line-height: @dls-line-height-1;
        color: @dls-color-gray-9;
        font-weight: @dls-font-weight-3;
    }
    .b2b-page-center-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    .b2b-page-center-loading {
        font-size: 40px;
        .b2b-page-center-loading-text {
            font-size: @dls-font-size-1;
            color: @dls-color-gray-9;
        }
    }
    .b2b-pages-control {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: @dls-padding-unit * 4;
    }
    .b2b-pages-title {
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: #fff;
    }
}

.b2b-page-selected-preview {
    display: inline-flex;
    width: 400px;
    background-color: @dls-color-gray-1;
    box-sizing: border-box;
    padding-right: @dls-padding-unit * 4;
    border-radius: @dls-border-radius-1;
    overflow: hidden;
    .b2b-page-selected-preview-img {
        width: 64px;
        height: 64px;
        border: 1px solid @dls-color-gray-2;
        background-position: center;
        background-size: cover;
    }
    .b2b-page-selected-preview-info {
        flex: 1;
        width: 0; // 用于保持 flex 1 的宽度 而不是根据内容撑开
        font-size: @dls-font-size-1;
        line-height: @dls-line-height-1;
        padding: 14px @dls-padding-unit*4;
    }
    .b2b-page-selected-preview-info-name {
        margin-bottom: @dls-padding-unit;
        font-weight: @dls-font-weight-2;
        color: @dls-color-gray-9;
        line-height: @dls-font-size-3;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .b2b-page-selected-preview-info-id {
        color: @dls-color-gray-7;
        line-height: @dls-font-size-2;
    }
    .b2b-page-selected-preview-btn {
        align-self: center;
    }
}
.copy-b2b-page-url {
    display: inline-block;
    vertical-align: top;
    margin: @dls-padding-unit * 6 0 0 @dls-padding-unit * 4;
}

.b2b-pages-drawer {
    background-color: #fff;

    padding-bottom: @dls-padding-unit * 24;
    .b2b-pages-search {
        margin-right: @dls-padding-unit * 3;
        width: 210px;
    }
    .b2b-pages-list {
        display: flex;
        flex-wrap: wrap;
        margin-right: -@dls-padding-unit * 5;
        margin-bottom: @dls-padding-unit * 4;
    }
    .b2b-pages-item {
        display: flex;
        flex-direction: column;
        width: 140px;
        background-color: @dls-color-gray-0;
        border: 1px solid @dls-color-gray-3;
        border-radius: @dls-border-radius-1;
        overflow: hidden;
        cursor: pointer;
        margin-right: @dls-padding-unit * 5;
        margin-bottom: @dls-padding-unit * 4;
        transition: all 0.2s ease;
        .b2b-pages-item-img-box {
            position: relative;
            user-select: none;
            height: 230px;
            background-size: cover;
            background-position: center;
        }
        .b2b-pages-item-checkbox {
            position: absolute;
            top: @dls-padding-unit * 2;
            left: @dls-padding-unit * 2;
            line-height: 1; // 用于取消默认高度，不然间距不对
        }
        .b2b-pages-item-info {
            height: 66px;
            padding: @dls-padding-unit * 3;
            box-sizing: border-box;
            line-height: @dls-line-height-1;
            font-size: @dls-font-size-0;
        }
        .b2b-pages-item-name {
            color: @dls-color-gray-11;
            font-weight: @dls-font-weight-2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .b2b-pages-item-sub-info {
            margin-top: @dls-padding-unit * 2;
            color: @dls-color-gray-7;
        }

        &.disabled {
            .b2b-pages-item-img-box,
            .b2b-pages-item-name,
            .b2b-pages-item-sub-info {
                cursor: not-allowed;
                opacity: 0.5;
            }
        }
        &.active,
        &:hover {
            box-shadow: 0 6px 32px 2px rgba(0, 0, 0, 0.06), 0 5px 30px 1px rgba(0, 0, 0, 0.05), 0 4px 28px 1px rgba(0, 0, 0, 0.04);
            &.disabled {
                .b2b-pages-item-sub-info {
                    cursor: auto;
                    opacity: 1;
                }
            }
        }
        &.active {
            border: 1px solid @dls-color-brand-7;
        }

        .b2b-pages-links {
            display: none;
            justify-content: space-between;
            align-items: center;
            padding: @dls-padding-unit;
            .b2b-pages-link-text {
                margin-left: @dls-padding-unit;
            }
        }

        .b2b-pages-id {
            display: flex;
            align-items: center;
            padding: @dls-padding-unit 0;
        }

        &:hover {
            .b2b-pages-links {
                display: flex;
            }
            .b2b-pages-id {
                display: none;
            }
        }
    }
}

.b2b-pages-drawer-footer {
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;

    .b2b-pages-drawer-footer-inner {
        display: flex;

        padding: @dls-padding-unit*4 @dls-padding-unit*8;
        align-items: center;
        box-shadow: 0 -4px 4px rgba(169, 169, 169, 0.25);
        background-color: #fff;
    }

    .b2b-pages-drawer-footer-btn + .b2b-pages-drawer-footer-btn {
        margin-left: @dls-padding-unit * 3;
    }

    .b2b-pages-drawer-select-msg {
        margin-left: @dls-padding-unit * 4;
        font-size: @dls-font-size-1;
        color: @dls-color-gray-7;
    }
}
