import {useCallback, useMemo} from 'react';
import {isEmpty, get} from 'lodash-es';
import IdType from 'commonLibs/config/idType';
import {isOptimizedPageUser} from 'commonLibs/utils/getFlag';
import {CPQL, APP} from 'commonLibs/config/marketTarget';
import AdType from 'commonLibs/config/adType';
import FinalUrl from 'app/containers/fcNew/creative/url/finalUrl';
import {UrlDrawerEditor} from 'app/components/urlEditor';
import {useCampaignInfo, useFormInstance} from '../context';

interface Props {
    value: string;
    onChange: (url: string) => void;
    fieldName: string;
    className?: string;
    appSelect?: object;
    isUseUrlDrawerEditorFromProps?: boolean;
}
export function AdgroupFinalUrl({
    value, onChange, fieldName, className, appSelect, isHideAgentUrl, isNeedBlur = true,
    catalogId = [], autoProductContent = {}, isUseUrlDrawerEditorFromProps = false
}: Props) {
    const {campaignId, marketingTargetId, shopType, adType} = useCampaignInfo();
    const {setFieldsValue, getFieldError, setFields} = useFormInstance();

    const onValueChange = useCallback((e: {value: string} | string) => {
        if (typeof e === 'object') {
            onChange(e.value);
        }
        else {
            onChange(e);
        }
    }, [onChange]);

    const isUseUrlDrawerEditor = (
        (marketingTargetId === APP && adType === AdType.NORMAL)
        || (marketingTargetId === CPQL && fieldName === 'mobileFinalUrl' && isOptimizedPageUser())
        || isUseUrlDrawerEditorFromProps
    );

    const adgroupInfo = useMemo(
        () => {
            return {
                campaignId, shopType, marketingTargetId, url: value,
                channelId: appSelect?.channelId
            };
        },
        [campaignId, shopType, marketingTargetId, value, appSelect?.channelId]
    );

    const showAutoProductContentTip = useMemo(
        () => (
            marketingTargetId === CPQL
            && fieldName === 'mobileFinalUrl'
            && !isEmpty(catalogId)
            && !isEmpty(autoProductContent)
            && value
            && get(catalogId, '[0].catalogId', 0) === get(autoProductContent, 'catalogId', 0)
        ),
        [marketingTargetId, fieldName, catalogId, autoProductContent, value]
    );

    if (isUseUrlDrawerEditor) {
        return (
            <>
                <UrlDrawerEditor
                    value={value}
                    isHideAgentUrl={isHideAgentUrl}
                    onChange={onValueChange}
                    marketingTargetId={marketingTargetId}
                    adgroupInfo={adgroupInfo}
                />
                {
                    showAutoProductContentTip ? (
                        <span className="adgroup-product-recommend">
                            系统将根据您填写的落地页信息自动匹配/生成对应的关联产品，推荐您前往“投放产品”处确认并关联使用，以助力跑量和提升投放效果
                        </span>
                    ) : null
                }
            </>
        );
    }
    return (
        <FinalUrl
            form={{setFieldsValue, getFieldError, setFields}}
            value={value}
            onChange={onChange}
            field={fieldName}
            marketingTargetId={marketingTargetId}
            shopType={shopType}
            idType={IdType.UNIT_LEVEL}
            campaignId={campaignId}
            finalUrlName={fieldName}
            inputWidth={400}
            classNameForInput={className}
            isNeedBlur={isNeedBlur}
        />
    );
}
