import {useState, useImperativeHandle, forwardRef, useMemo} from 'react';
import {useBoolean, useRequest} from 'huse';
import {Drawer, Button, Popover} from '@baidu/one-ui';
import {useControl} from 'commonLibs/hooks/externalControl';
import KrKeyword, {KrConfigContext, KR_SOURCE} from 'toolsCenter/Kr/Keyword';
import SuspenseBoundary from 'commonLibs/suspenseBoundary';
import {useCampaignInfo} from '../context';
import {preValidate, getKeywordRecommendTotalCount} from '../api';
import {IconPlus, IconExclamationTriangleSolid} from 'dls-icons-react';
import {isBjhSubMarket} from 'commonLibs/config/marketTarget';
import '../style.less';

const KeywordsDrawer = forwardRef(KeywordsDrawer_);

export function Keywords({value, onChange, subMarketingTargetId, displayUrl, campaignId}) {
    const [KeywordsEditor, {open}] = useControl(KeywordsDrawer);
    const firstWord = value?.[0]?.keyValue;
    const isNative = isBjhSubMarket(subMarketingTargetId);
    const [visible, {on: showAddKeyWord}] = useBoolean(!isNative);

    const {data: keywordTotalCount, pending} = useRequest(getKeywordRecommendTotalCount, {campaignId, displayUrl});

    return (
        <div>
            {
                isNative && (
                    <div className="keywords-tips">
                        系统将结合对推广内容的理解，为您智能拓展更多目标人群。
                        <Button type="text-strong" onClick={showAddKeyWord}>自定义关键词</Button>
                    </div>
                )
            }
            {
                value.length
                    ? (
                        <div className="btn-type">
                            <span>{firstWord && `已选择 『${firstWord}』… 等`} 数量: {value.length} 个</span>
                            <Button type="text-strong" onClick={open}>更换</Button>
                        </div>
                    )
                    : visible ? (
                        <Button
                            onClick={open}
                            icon={<IconPlus />}
                            className='adgroup-page-select-btn'
                        >
                            设置关键词
                        </Button>
                    ) : null
            }
            {
                visible && !pending && keywordTotalCount > 0 && (
                    <div className="show-recommend-keywords">
                        <span>为您找到{keywordTotalCount}个相关关键词</span>
                        <Button type="text-strong" onClick={open}>点击查看</Button>
                    </div>
                )
            }
            <KeywordsEditor
                value={value}
                onChange={onChange}
                displayUrl={displayUrl}
                initialRecommendCount={keywordTotalCount}
            />
        </div>
    );
}


function ErrorTip({errorList}) {
    const errorTipContent = useMemo(
        () => {
            return (
                <>
                    {errorList.map(v => (
                        <div key={`${v.line}${v.keyValue}${v.error}`}>
                            第{v.line + 1}行({v.keyValue})：{v.error}
                        </div>
                    ))}
                </>
            );
        },
        [errorList]
    );

    if (!errorList.length) {
        return null;
    }
    return (
        <Popover
            placement="topRight"
            content={<>{errorTipContent}</>}
            trigger="click"
            getPopupContainer={node => node.parentNode}
            overlayClassName="adgroup-and-creative-errors-content"
        >
            <div className="errors-tip">
                <IconExclamationTriangleSolid />
                <span>部分关键词填写错误，请修改</span>
            </div>
        </Popover>
    );
}

function KeywordsDrawer_({value, onChange, displayUrl, initialRecommendCount}, ref) {
    const [visible, {on: open, off: close}] = useBoolean();
    const {campaignName, campaignId} = useCampaignInfo();
    const [keywords, setKeywords] = useState(value);
    const words = useMemo(() => keywords.filter(v => v.keyValue), [keywords]);

    useImperativeHandle(ref, () => ({open, close}));

    const onOk = async () => {
        try {
            await preValidate(words, {campaignId});
            onChange(words);
            close();
        }
        catch (e) {
            setKeywords(e);
        }
    };

    const errorList = useMemo(() => keywords.map((v, line) => ({...v, line})).filter(v => v.error), [keywords]);

    return (
        <Drawer
            title="添加关键词"
            placement="right"
            visible={visible}
            onClose={close}
            destroyOnClose
            width={1180}
            className="adgroup-process-keyword-drawer"
            footer={[
                <Button key="ok" type="primary" onClick={onOk}>确定</Button>,
                <Button key="cancel" onClick={close}>取消</Button>,
                <span key="desc" className='adgroup-keyword-footer-tip'>已选择: {words.length}个关键词</span>,
                <ErrorTip key="error" errorList={errorList} />,
                <span key="desc" className='adgroup-keyword-footer-law-describe'>
                    请复核您添加和使用的内容，确保符合《中华人民共和国广告法》等法律法规，不侵犯他人合法权益。
                    若您的内容出现违法违规情形，百度发现后将进行断链、下线等处理，并报相关监管部门。
                </span>
            ]}
        >
            <SuspenseBoundary>
                <KrConfigContext.Provider value={{scene: KR_SOURCE.adgroupProcess}}>
                    <KrKeyword
                        type="keyword"
                        prefix="ad-main-process-kr"
                        isShowKeywordGroupTab={false}
                        isShowKrFooterTip={false}
                        isShowPromptGenerate
                        hasSelector={false}
                        initialRecommendCount={initialRecommendCount}
                        onChange={setKeywords}
                        initialBufferDataSource={value}
                        campaignName={campaignName}
                        enableDraft={false}
                        initSelect={{
                            displayUrl
                        }}
                    />
                </KrConfigContext.Provider>
            </SuspenseBoundary>
        </Drawer>
    );
}
