import {Button, Loading, Pagination, Radio, SearchBox, Select, Tooltip} from '@baidu/one-ui';
import BlankPage from 'app/containers/campaigns/list/blankPage';
import {noop} from 'lodash-es';
import classNames from 'classnames';
import {IconInfoCircleSolid, IconSync} from 'dls-icons-react';
import {useRequest} from 'huse';
import {fetchBjhContentList, fetchBjhUserInfoList} from 'app/api/bjh';
import {useState} from 'react';
import './style.less';

export interface bjhContent {
    nativeId: string;
    text: string;
    prewImageUrl: string;
    url: string;
}
const PAGE_SIZE = 12;

export const BJHContentRadioGroup = ({value = [], onChange, bjhUserId}: {
    value: bjhContent[];
    onChange: (v: bjhContent[]) => void;
    bjhUserId: string;
}) => {
    const [searchValue, setSearchValue] = useState('');
    const [searchInputValue, setSearchInputValue] = useState('');
    const [pageNo, setPageNo] = useState(1);
    const {data: {bjhContentInfoList = [], totalRowCount} = {}, pending, refresh} = useRequest(fetchBjhContentList, {
        bjhUserId,
        nativeIds: searchValue ? [searchValue] : [],
        limit: [(pageNo - 1) * PAGE_SIZE, pageNo * PAGE_SIZE]
    });
    const {data: bjhUserInfoList = [], pending: bjhUserLoading} = useRequest(fetchBjhUserInfoList, {
        bjhUserIds: [bjhUserId]
    });
    const {bjhUserLogo, bjhUserName} = bjhUserInfoList[0] || {};
    const getMaterialById = (id: string) => {
        return bjhContentInfoList.find(item => item.nativeId === id);
    };
    const onRadioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        onChange([
            {
                ...getMaterialById(e.target.value),
                // 塞入百家号user的信息 便于预览使用
                bjhUserLogo,
                bjhUserName
            }
        ]);
    };
    const searchProps = {
        width: 220,
        placeholder: '搜索素材 ID',
        value: searchInputValue,
        onChange: e => setSearchInputValue(e.target.value),
        onSearch: e => setSearchValue(e.target.value)
    };
    return (
        <div className="bjh-content">
            <div className="bjh-content-header">
                <Select disabled value={bjhUserName} width={200}>
                    <Select.Option value={bjhUserName}>
                        <div className="bjh-content-header-user">
                            <img src={bjhUserLogo} className="bjh-content-header-user-img" />
                            {bjhUserName}
                        </div>
                    </Select.Option>
                </Select>
                <div>
                    <Button disabled={pending} onClick={refresh} className="bjh-content-header-refresh">
                        <Loading loading={pending}>
                            {!pending && <IconSync />}
                        </Loading>
                    </Button>
                    <SearchBox {...searchProps} />
                </div>
            </div>
            <div className="bjh-content-radio-group">
                {
                    pending
                        ? <Loading />
                        : bjhContentInfoList.length
                            ? bjhContentInfoList.map(item => {
                                const {
                                    nativeId, prewImageUrl, safeStatus, qualityStatus,
                                    bizType, text
                                } = item;
                                const isSelected = value.map(i => i.nativeId).includes(nativeId);
                                const itemDisabled = safeStatus !== 0 || qualityStatus !== 0;
                                const suggestTip = !bizType ? '未挂载转化组件，可能导致您损失潜在的转化机会、无法追踪广告转化数据，建议您前往百家号添加转化组件' : '';
                                const imgCls = classNames('bjh-content-item-img', {
                                    'bjh-content-item-img-disabled': itemDisabled,
                                    'bjh-content-item-img-selected': isSelected
                                });
                                const descCls = classNames('bjh-content-item-desc', {
                                    'bjh-content-item-desc-disabled': itemDisabled
                                });
                                return (
                                    <div className="bjh-content-item" key={nativeId}>
                                        <Radio
                                            disabled={itemDisabled}
                                            checked={isSelected}
                                            value={nativeId}
                                            onChange={itemDisabled ? noop : onRadioChange}
                                            className="bjh-content-item-radio"
                                        />
                                        {
                                            itemDisabled && (
                                                <span className="bjh-content-item-disabled-tip">
                                                    <IconInfoCircleSolid
                                                        className="bjh-content-item-disabled-tip-icon"
                                                    />
                                                    不可投放
                                                </span>
                                            )
                                        }
                                        {
                                            suggestTip && !itemDisabled && (
                                                <Tooltip title={suggestTip}>
                                                    <span className="bjh-content-item-suggest-tip">
                                                        <IconInfoCircleSolid
                                                            className="bjh-content-item-suggest-tip-icon"
                                                        />
                                                        建议调整
                                                    </span>
                                                </Tooltip>
                                            )
                                        }
                                        <div
                                            className="bjh-content-item-img-wrapper"
                                            onClick={
                                                itemDisabled
                                                    ? noop
                                                    : () => onChange([
                                                        {
                                                            ...getMaterialById(nativeId),
                                                            // 塞入百家号user的信息 便于预览使用
                                                            bjhUserLogo,
                                                            bjhUserName
                                                        }
                                                    ])
                                            }
                                        >
                                            <img
                                                className={imgCls}
                                                src={prewImageUrl}
                                                key={nativeId}
                                            />
                                            {itemDisabled && <div className="bjh-content-item-img-disabled-mask"></div>}
                                        </div>
                                        <div className={descCls}>{text}</div>
                                    </div>
                                );
                            })
                            : (
                                <BlankPage />
                            )
                }
            </div>
            <Pagination
                className="bjh-content-pagination"
                showTotal
                size="small"
                showSizeChange={false}
                showPageJumper={false}
                total={totalRowCount}
                pageSize={PAGE_SIZE}
                pageNo={totalRowCount ? pageNo : 1}
                onPageNoChange={e => setPageNo(e.target.value)}
            />
        </div>


    );
};