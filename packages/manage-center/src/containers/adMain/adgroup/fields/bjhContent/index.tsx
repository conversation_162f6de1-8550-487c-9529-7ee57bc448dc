import {<PERSON><PERSON>, Drawer, ProviderConfig} from '@baidu/one-ui';
import {useState} from 'react';
import {bjhContent, BJHContentRadioGroup} from './BJHContentRadioGroup';
import {useBoolean} from 'huse';
import {IconPlusSquare, IconSwitch} from 'dls-icons-react';
import {useCampaignInfo} from '../../context';


export const BJHContent = ({value, onChange}: {
    value: bjhContent[];
    onChange: (value: bjhContent[]) => void;
}) => {

    const {bjhUserInfo} = useCampaignInfo();
    const {bjhUserId} = bjhUserInfo || {};

    const [selectedValue, setSelectedValue] = useState(value);

    const [visible, {on: open, off: close}] = useBoolean();
    const onOk = () => {
        onChange(selectedValue);
        close();
    };

    return (
        <ProviderConfig theme="light-ai">
            <div className="fc-bjh-content">
                {
                    value.length
                        ? (
                            <div className="fc-bjh-selected-form-content">
                                <img className="fc-bjh-selected-form-content-img" src={value[0].prewImageUrl} />
                                <div className="fc-bjh-selected-form-content-text">
                                    <div className="fc-bjh-selected-form-content-text-title">{value[0].text}</div>
                                    <div className="fc-bjh-selected-form-content-text-desc">
                                        内容 ID：{value[0].nativeId}
                                    </div>
                                </div>
                                <Button className="fc-bjh-selected-form-content-btn" type="text-strong" onClick={open}>
                                    <IconSwitch style={{marginRight: 4}} />
                                    切换推广内容
                                </Button>
                            </div>
                        )
                        : (
                            <div className="fc-bjh-unselected-form-content" onClick={open}>
                                <IconPlusSquare className="icon" />
                                选择推广内容
                            </div>
                        )
                }

                <Drawer
                    className="fc-bjh-content-drawer"
                    title="添加推广内容"
                    visible={visible}
                    width={650}
                    onClose={close}
                    destroyOnClose
                    footer={[
                        <Button key="ok" type="primary" onClick={onOk} disabled={!selectedValue.length}>确定</Button>,
                        <Button key="cancel" onClick={close}>取消</Button>,
                        <span key="desc" className='adgroup-keyword-footer-tip'>已选: {selectedValue.length}/1</span>
                    ]}
                >
                    <BJHContentRadioGroup bjhUserId={bjhUserId} value={selectedValue} onChange={setSelectedValue} />
                </Drawer>
            </div>
        </ProviderConfig>

    );
};