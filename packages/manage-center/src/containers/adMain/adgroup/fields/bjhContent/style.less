.bjh-content {
    .bjh-content-header {
        display: flex;
        height: 36px;
        justify-content: space-between;

        &-user {
            display: flex;
            align-items: center;
            gap: 4px;
            height: 34px;
            font-size: 14px;
            line-height: 20px;

            &-img {
                width: 16px;
                height: 16px;
                border-radius: 50%;
                border: 0.36px solid var(--cos-color-border-tiny, #dedfe0);
            }
        }

        &-refresh {
            width: 36px;
            margin-right: 8px;
        }
    }
    .bjh-content-radio-group {
        display: flex;
        gap: 16px;
        flex-wrap: wrap;
        margin-top: 16px;

        .bjh-content-item {
            position: relative;
            width: 136px;

            .bjh-content-item-radio {
                position: absolute;
                left: 4px;
                z-index: 1;
            }

            .bjh-content-item-tip-common() {
                position: absolute;
                left: 8px;
                bottom: 54px;
                z-index: 1;
                border-radius: 6px;
                padding: 4px 6px;
                font-weight: 500;
                font-size: 12px;
                line-height: 16px;
                cursor: pointer;
            }

            .bjh-content-item-disabled-tip {
                .bjh-content-item-tip-common();
                background: var(--Red-1, #ffebeb);
                color: var(--Red-7, #f53f3f);

                &-icon {
                    width: 14px;
                    color: var(--Red-7, #f53f3f);
                    margin-right: 4px;
                }
            }

            .bjh-content-item-suggest-tip {
                .bjh-content-item-tip-common();
                background: var(--Red-1, #ebf2ff);
                color: var(--Red-7, #0054e6);

                &-icon {
                    width: 14px;
                    color: var(--Red-7, #0054e6);
                    margin-right: 4px;
                }
            }

            .bjh-content-item-img-wrapper {
                position: relative;

                .bjh-content-item-img {
                    width: 100%;
                    height: 180px;
                    border-radius: 6px;
                    border: 1.5px solid transparent;
                }

                .bjh-content-item-img-disabled-mask {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    height: 182px;
                    width: 138px;
                    border-radius: 6px;
                    opacity: 0.6;
                    background-color: var(--Gray-3, #e2e6f0); /* 设置灰色背景，调整 alpha 值控制透明度 */
                    cursor: not-allowed;
                }

                .bjh-content-item-img-selected {
                    border: 1.5px solid var(--Brand-7, #3a5bfd);
                }
            }

            .bjh-content-item-desc {
                height: 28px;
                margin-top: 8px;
                font-weight: 500;
                font-size: 12px;
                line-height: 14px;
                color: var(--cos-color-text, #000311);
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
            }

            .bjh-content-item-desc-disabled {
                color: var(--Gray-6, #a8b0bf);
            }
        }
    }

    .bjh-content-pagination {
        position: sticky;
        bottom: 0;
        background-color: #fff;
        z-index: 2;
    }
}

.fc-bjh-selected-form-content {
    width: 404px;
    height: 88px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 10px;
    padding-right: 24px;
    background: #f6f7fa;

    &-img {
        width: 66px;
        height: 88px;
    }

    &-text {
        width: 170px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        gap: 4px;

        &-title {
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            color: #0e0f11;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
        }

        &-desc {
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            color: #848b99;
        }
    }
}

.fc-bjh-unselected-form-content {
    width: 780px;
    height: 20px;
    border-radius: 6px;
    padding: 12px 16px;
    background: #6ea0f712;
    border: 1px dashed var(--Brand-5, #729cfe);
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: var(--Brand-7, #3a5bfd);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .icon {
        margin-right: 4px;
        color: var(--Brand-7, #3a5bfd);
    }
}
