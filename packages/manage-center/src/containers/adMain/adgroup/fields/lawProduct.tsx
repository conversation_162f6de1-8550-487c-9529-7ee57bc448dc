/**
 * @file 法律行业关联产品
 * <AUTHOR>
 * @date 2025/01/12
*/

import {useMemo} from 'react';
import {Cascader} from '@baidu/one-ui';
import {useResource} from 'commonLibs/suspenseBoundary';
import {getBusinessPointInfo} from 'app/containers/fcNew/actions/adgroup/api';

interface BusinessResponse {
    businessId: string;
    businessName: string;
    children?: BusinessResponse[];
}

interface InfoMap {
    [id: string]: {
        businessId: string;
        businessName: string;
        parentId: string | null;
        children?: BusinessResponse[];
    };
}
interface OptionsItem {
    label: string;
    value: string;
    children?: OptionsItem[];
}

interface LawCascaderProps {
    onChange: (val: any[]) => void;
    value: string[];
    disabled?: boolean;
}

// 将businessInfo信息转换成cascader的选项，并在第二级之后加上全部选项
const transfromBusinessInfoToOptions = (
    infoList: BusinessResponse[],
    infoMap: InfoMap,
    parentId: string | null,
): [OptionsItem[], InfoMap] => {
    const options = infoList.reduce((result: OptionsItem[], cur: BusinessResponse) => {
        const {businessId, businessName, children} = cur;
        infoMap[businessId] = {
            ...cur,
            parentId
        };

        result.push({
            label: businessName,
            value: businessId,
            ...(children ? {
                children: [
                    // 当第二级以后（如果父级不为空），则在前面添加一个全部选项，代表父级下面的所有选项都全选
                    {label: '全部', value: businessId},
                    ...transfromBusinessInfoToOptions(children, infoMap, businessId)[0]
                ]
            } : {})
        });
        return result;
    }, []);
    return [options, infoMap];
};

// 根据子节点得到全部的节点路径
const getLevelPath = (id: string, infoMap: InfoMap) => {
    if (!id) {
        return [];
    }
    const path = [id];
    const cur = infoMap[id];
    let parentId = cur?.parentId;
    while (parentId) {
        path.unshift(parentId);
        parentId = infoMap[parentId]?.parentId;
    }
    // 当前节点并不是叶子节点，代表选择了【全部】，则在最后添加当前节点的id
    if (cur?.children) {
        path.push(id);
    }
    return path;
};

const LawSsgTradeId2nd = 200604;

export default function LawProduct(props: LawCascaderProps) {
    const {onChange, value, disabled = false} = props;
    const [lawBusinessInfo] = useResource(getBusinessPointInfo, {
        businessIdType: LawSsgTradeId2nd
    });

    const [options, infoMap] = useMemo(() => {
        return transfromBusinessInfoToOptions(lawBusinessInfo, {}, null);
    }, [lawBusinessInfo]);

    const handleChange = value => {
        onChange(value[value.length - 1] == null ? [] : [value[value.length - 1]]);
    };

    return (
        <div>
            <Cascader
                menuWidth={180}
                options={options}
                value={getLevelPath(value?.[0], infoMap)}
                placeholder="请选择法律业务类目"
                expandTrigger="hover"
                onChange={handleChange}
                disabled={disabled}
            />
        </div>
    );
};
