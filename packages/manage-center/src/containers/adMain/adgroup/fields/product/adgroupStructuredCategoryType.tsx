import {Radio} from '@baidu/one-ui';
import {StructuredCategoryStatusEnum} from 'commonLibs/components/productSelect/config';

const RadioButton = Radio.Button;
const RadioGroup = Radio.Group;

export default function ({value, onChange}: {value: StructuredCategoryStatusEnum, onChange: (e: any) => void}) {
    const radioGroupProps = {
        value,
        onChange: (param: any) => {
            onChange(param.target.value);
        }
    };
    return (
        <RadioGroup {...radioGroupProps}>
            <RadioButton value={StructuredCategoryStatusEnum.BY_PROJECT} key={StructuredCategoryStatusEnum.BY_PROJECT}>
                使用项目设置
            </RadioButton>
            <RadioButton value={StructuredCategoryStatusEnum.BY_ADGROUP} key={StructuredCategoryStatusEnum.BY_ADGROUP}>
                为单元单独设置
            </RadioButton>
        </RadioGroup>
    );
}