import {useMemo} from 'react';
import {get, isEmpty} from 'lodash-es';
import {Button} from '@baidu/one-ui';
import {Skeleton} from '@baidu/light-ai-react';
import {IconPlusSquare} from 'dls-icons-react';
import {useControl} from 'commonLibs/hooks/externalControl';
import {
    ProductSelectDrawer,
    ProductWriteBackRender,
    LXTProductWriteBackRender
} from 'commonLibs/components/productSelect';
import {StructuredCategoryStatusEnum} from 'commonLibs/components/productSelect/config';
import {productCategoryTypeEnum} from 'commonLibs/config/enumEntry';
import sendMonitor from 'commonLibs/utils/sendHm';
import LawProduct from '../lawProduct';
import {useFormInstance, useStructContentInstance} from '../../context';
import './style.less';

function sendLog({type, item}) {
    sendMonitor('click', {type, level: 'adgroup', source: 'struct_product', item});
}

export default ({
    value, newAdgroupStructuredCategoryType, projectProduct, onChange,
    productCategoryType, autoProductContent
}) => {
    const {setFieldsValue} = useFormInstance();
    const [ProductSelector, {open}] = useControl(ProductSelectDrawer);
    const {stopGenerateStructContent, pending, cancelable, error, recommendMessage} = useStructContentInstance();
    const selectedProduct = newAdgroupStructuredCategoryType === StructuredCategoryStatusEnum.BY_PROJECT
        ? projectProduct
        : value;
    const items = selectedProduct.map(item => {
        return {
            name: item?.productName,
            id: item?.productId,
            imgUrl: item?.image?.[0],
            open
        };
    });
    const itemProps = {
        items,
        productCategoryType,
        editable: newAdgroupStructuredCategoryType === StructuredCategoryStatusEnum.BY_ADGROUP
    };
    const lxtItemProps = {
        ...itemProps,
        autoProductContent,
        onDelete: () => {
            onChange([]);
        }
    };

    if (productCategoryType === productCategoryTypeEnum.LAW) {
        return (
            <LawProduct
                value={value}
                onChange={onChange}
                disabled={newAdgroupStructuredCategoryType !== StructuredCategoryStatusEnum.BY_ADGROUP}
            />
        );
    }

    const onChange_ = (product, options) => {
        onChange(product);
        if (options?.isJmyChange) {
            setFieldsValue({
                autoProductContent: {...autoProductContent, productId: product[0]?.productId}
            });
        }
    };

    const onCancelGenerate = () => {
        stopGenerateStructContent();
        sendLog({type: productCategoryType, item: 'url_manual_parse_cancel'});
    };

    const selectProduct = () => {
        open();
        sendLog({type: productCategoryType, item: 'select_product'});
    };

    const selectorProps = {
        value,
        onChange: onChange_,
        productCategoryType,
        autoProductContent,
        newAdgroupStructuredCategoryType
    };

    const showAutoProductContentTip = useMemo(
        () => (
            !isEmpty(selectedProduct)
            && !isEmpty(autoProductContent)
            && get(selectedProduct, '[0].catalogId', 0) === get(autoProductContent, 'catalogId', 0)
        ),
        [selectedProduct, autoProductContent]
    );
    return (
        <div className="adgroup-product-selector-container">
            {
                value.length
                    ? (
                        <>
                            {
                                productCategoryType === productCategoryTypeEnum.LXT
                                    ? <LXTProductWriteBackRender {...lxtItemProps} />
                                    : <ProductWriteBackRender {...itemProps} />
                            }
                            {recommendMessage && showAutoProductContentTip && (
                                <div className='adgroup-product-recommend'>{recommendMessage}</div>
                            )}
                        </>
                    )
                    : (
                        pending ? (
                            <>
                                <Skeleton.Paragraph rows={2} />
                                <span className="adgroup-product-recommend">关联产品自动识别生成中，预计需要90秒</span>
                                {
                                    cancelable ? (
                                        <Button
                                            type="text-strong"
                                            size="small"
                                            className="adgroup-product-recommend-cancel-btn"
                                            onClick={onCancelGenerate}
                                        >
                                            取消
                                        </Button>
                                    ) : null
                                }
                            </>
                        ) : (
                            <>
                                <div onClick={selectProduct} className='adgroup-page-select-product-btn'>
                                    <span><IconPlusSquare />选择产品</span>
                                </div>
                                {
                                    error ? (
                                        <div className="adgroup-product-recommend-error">
                                            落地页解析失败，请您选择合适的落地页后再点击识别生成产品或直接选择产品
                                        </div>
                                    ) : null
                                }
                            </>
                        )
                    )
            }
            <ProductSelector {...selectorProps} />
        </div>
    );
};
