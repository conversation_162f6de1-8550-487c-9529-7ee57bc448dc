.ad-main-new-adgroup-page-form,
.adgroup-form-area {
    .adgroup-product-selector-container {
        width: @dls-height-unit * 197;
    }
    .selected-product-container {
        width: 100%;
    }
    .adgroup-product-recommend-error,
    .adgroup-product-recommend {
        margin-top: @dls-padding-unit * 2;
    }
    .adgroup-product-recommend {
        color: @dls-color-gray-8;
        font-size: @dls-font-size-0;

        &-cancel-btn {
            margin-left: @dls-padding-unit * 2;
        }
    }
    .adgroup-product-recommend-error {
        color: @dls-color-error-7;
        font-size: @dls-font-size-0;
    }
    .adgroup-page-select-product-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        height: 70px;
        color: @dls-color-brand-7;
        border-radius: @dls-border-radius-1;
        border-width: 1px;
        border: 1px dashed @dls-color-brand-5;
        background: #6ea0f712;

        .dls-icon {
            margin-right: 6px;
        }
    }
}
