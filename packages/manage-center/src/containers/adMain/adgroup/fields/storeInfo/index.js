import {useBoolean} from '@huse/boolean';
import {Button, Drawer} from '@baidu/one-ui';
import {create} from 'commonLibs/magicSuspenseBoundary';
import {IconPlus} from 'dls-icons-react';
import {EditStoreInfo, StoreListDrawer} from 'commonLibs/components/storeInfo';
import {fetchStoreRegions} from 'commonLibs/apis/store';
import {useCampaignInfo} from '../../context';

const {
    Boundary,
    useResource
} = create({cacheContextDisplayName: 'StoreListCache'});

const StoreInfo = ({value, onChange, storeDistance: storeDistanceFromProps, regionType: regionTypeFromProps}) => {
    const [storeListDrawerVisible, {off: closeDrawer, on: openDrawer}] = useBoolean();
    const [{provinceInfoList}] = useResource(fetchStoreRegions);
    const handleStoreInfo = value => {
        onChange(value);
        closeDrawer();
    };
    const {storeDistance, regionType} = useCampaignInfo();
    const storeDistanceValue = storeDistanceFromProps ?? storeDistance;
    const regionTypeValue = regionTypeFromProps ?? regionType;

    return (
        <>
            {
                value.length && value[0]?.storeId
                    ? (
                        <>
                            {/* 目前是单选，多选交互未确认 使用value[0] */}
                            <EditStoreInfo storeInfo={value[0]} onClick={openDrawer} />
                        </>
                    )
                    : (
                        <Button onClick={openDrawer} icon={IconPlus} className='store-list-btn'>
                            选择店铺
                        </Button>
                    )
            }
            <Drawer
                title='选择店铺'
                width={800}
                onClose={closeDrawer}
                visible={storeListDrawerVisible}
                destroyOnClose
                footer={[]}
            >
                <StoreListDrawer
                    value={value}
                    onOk={handleStoreInfo}
                    onClose={closeDrawer}
                    provinceInfos={provinceInfoList}
                    storeDistance={storeDistanceValue}
                    regionType={regionTypeValue}
                />
            </Drawer>
        </>
    );
};

export default StoreInfo;