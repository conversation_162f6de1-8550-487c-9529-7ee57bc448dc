import {storePageNameMapByValue} from '../config';
import {IconTimes} from 'dls-icons-react';
import {Button, Empty, Loading} from '@baidu/one-ui';
import classNames from 'classnames';
import STORE_DEFAULT from 'app/resource/img/store_default.png';


const StoreMobileUrlCard = ({id, title, desc, picUrl, onDelete}) => {
    return (
        <div className="store-mobile-url-card">
            <div className="store-mobile-url-card-area">
                <img src={picUrl || STORE_DEFAULT} className="store-mobile-url-card-pic" />
                <div>
                    <div className="store-mobile-url-card-title">{title}</div>
                    <div className="store-mobile-url-card-desc">{desc}</div>
                </div>
            </div>
            <IconTimes onClick={() => onDelete(id)} className="store-mobile-url-card-icon" />
        </div>
    );
};

export const StoreMobileUrlEditor = ({value = [], onChange, loading}) => {
    const onDelete = pageId => {
        const value_ = value.filter(item => item.pageId !== pageId);
        onChange(value_);
    };
    const onClear = () => {
        onChange([]);
    };
    const listCls = classNames({
        'store-textline-container-list': true,
        'store-textline-container-list-empty': !value.length || loading
    });
    return (
        <div className='store-textline-container'>
            <div className='store-textline-container-title'>
                已选落地页（{value.length}）
                <Button type='text-strong' onClick={onClear}>清空</Button>
            </div>
            <div className={listCls}>
                {
                    loading && <Loading size="large" type="strong" />
                }
                {
                    !value.length && !loading && <Empty description='请选择落地页' />
                }
                {
                    !!value.length && !loading && (
                        value.map(item => {
                            return (
                                <StoreMobileUrlCard
                                    key={item.pageId}
                                    id={item.pageId}
                                    title={item.pageName}
                                    desc={storePageNameMapByValue[item.pageType]}
                                    picUrl={item.picture}
                                    onDelete={onDelete}
                                />
                            );
                        })
                    )
                }
            </div>
        </div>
    );
};