import {useState, useCallback} from 'react';
import {Table, Button, SearchBox, Message, Link, Pagination} from '@baidu/one-ui';
import PagePreviewModal from '@baidu/m-vibranium-react/lib/components/pagePreview/pagePreviewModal';
import {useBoolean} from '@huse/boolean';
import {UISelect} from '@baidu/react-formulator';
import {fetchStorePageList} from 'commonLibs/apis/store';
import {useRegister} from 'commonLibs/hooks/materialList/register';
import {useColumns, useSortableColumns} from 'commonLibs/hooks/materialList/columns';
import {useTablePagination, toOneUIPaginationProps} from 'commonLibs/hooks/pagination';
import {formatColumnConfiguration} from 'commonLibs/utils/materialList/columns';
import {useTableSort} from 'commonLibs/hooks/sorter';
import {useHybridRequest} from 'commonLibs/hooks/request';
import useUserInfo from 'commonLibs/hooks/useUserInfo';
import {tablePropsWithoutScroll} from 'app/config/table';
import {storePageSelectOptions, storePageTypeMap, customFields, allColumns} from '../config';
import {tableFieldsMap} from '../tableFields';
import {StoreMobileUrlEditor} from './StoreMobileUrlEditor';
import '../style.less';

const initialData = {totalRowCount: 0, storeInfoList: []};

const columnConfiguration = formatColumnConfiguration({customFields, columnConfigs: allColumns});

function StoreMobileList({value, onCancel, onOk, storeInfo}) {
    const [pageTypeValue, setPageTypeValue] = useState(storePageTypeMap.all);
    const [pageSearchValue, setPageSearchValue] = useState('');
    const {userId} = useUserInfo();

    const [pagination, paginationMethods] = useTablePagination({
        pageNo: 1,
        pageSize: 20
    });
    const [refresh, {
        error,
        pending,
        data: storeListData = initialData
    }] = useHybridRequest(fetchStorePageList, {
        ...pagination,
        pageTypeValue,
        pageSearchValue,
        storeInfo
    });
    const {totalRowCount: totalNum, storeInfoList: storeList} = storeListData;

    // 兼容一下 未来可能会多选店铺
    const pageList = storeList.reduce((pageArr, item) => {
        const pages = item.storePageDataList.map(v => ({
            ...v,
            // 加上落地页所属的店铺id，传参需要
            storeId: item.storeId
        }));
        return pageArr.concat(pages);
    }, []);

    // checkbox选中的店铺信息
    const [selectedPageLists, setSelectedPageLists] = useState(value);

    const selectedRowKeys = selectedPageLists.map(item => pageList.findIndex(i => i.pageId === item.pageId)) || [];


    const paginationProps = toOneUIPaginationProps({...pagination, ...paginationMethods}, totalNum);
    const {resetPage} = paginationMethods;

    const refreshFunc = useCallback(() => {
        refresh();
        resetPage();
    }, [refresh, resetPage]);

    const onSelectChange = (value, selectedRows) => {
        const pageIdToValueMap = pageList.reduce((obj, page) => {
            obj[page.pageId] = page;
            return obj;
        }, {});
        const pageIds = pageList.map(page => page.pageId);
        const nextValue = selectedPageLists
            .filter(({pageId}) => !pageIds.includes(pageId))
            .concat(selectedRows.map(({pageId}) => pageIdToValueMap[pageId]));
        setSelectedPageLists(nextValue);
    };
    const submitStoreUrl = () => {
        onOk(selectedPageLists);
    };
    const onTextLineChange = value => {
        setSelectedPageLists(value);
    };
    const {
        columns: columns_, handleColumnAction
    } = useColumns({columnConfiguration, tableFieldsMap, extraConfig: {enableParamsToQuery: true}});
    // todo 排序 && 分页
    const [sorter, onSort] = useTableSort({sortType: '', sortField: ''});
    const columns = useSortableColumns(columns_, sorter);

    const tableListProps = {
        ...tablePropsWithoutScroll,
        headerFixTop: 0,
        loading: pending,
        onSortClick: onSort,
        columns,
        dataSource: pageList,
        rowSelection: {
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange
        },
        locale: {
            emptyText: error ? '出错了, 请点击刷新或联系管理员' : '未找到落地页，请点击右上角前往基木鱼开店添加'
        }
        // headBordered,
        // headerBottom: <AsyncBanner {...asyncTasks} refresh={refreshAndResetRowSelection} />,
    };
    const searchBoxProps = {
        size: 'medium',
        style: {width: 211},
        placeholder: '请输入落地页名称',
        onSearch: useCallback(e => {
            setPageSearchValue(e.target.value);
            resetPage();
        }, []),
        onClearClick: useCallback(() => setPageSearchValue(''), [setPageSearchValue]),
        className: 'store-mobile-url-drawer-operate-search'
    };
    const selectProps = {
        className: 'store-mobile-url-drawer-operate-select',
        width: 128,
        value: pageTypeValue,
        onChange: value => {
            setPageTypeValue(value);
            resetPage();
        }
    };
    const editorProps = {
        value: selectedPageLists,
        onChange: value => onTextLineChange(value),
        loading: pending
    };
    const [previewUrlList, setPreviewUrlList] = useState([]);
    const [previewVisible, {on: openPreview, off: closePreview}] = useBoolean();
    const previewProps = {
        visible: previewVisible,
        onModalClose: closePreview,
        isShowPcLink: false, // 是否展示Pc
        isShowCopyLink: true, // 是否展示复制链接
        isShowPageSelect: true, // 是否展示多页选择
        isShowPageId: true, // 是否展示pageId
        previewText: '预览页面链接，不可用于投放，且仅做样式展示',
        pageList: previewUrlList,
        modalWidth: 640
    };

    const onPreview = useCallback(info => {
        const {event, record} = info;
        if (record?.pageUrl) {
            const previewUrls = [
                {
                    id: record.storeId || record.pageId,
                    previewUrl: record.pageUrl,
                    name: record.pageName
                }
            ];
            setPreviewUrlList(previewUrls);
            openPreview();
            event.stopPropagation();
        }
    }, [setPreviewUrlList, openPreview]);

    const registerPreview = useCallback(() => {
        return handleColumnAction('onPreview', onPreview);
    }, [handleColumnAction, onPreview]);
    useRegister(registerPreview);

    return (
        <>
            <div className='store-mobile-url-drawer-operate'>
                <div className='store-mobile-url-drawer-operate-area'>
                    <UISelect {...selectProps} options={storePageSelectOptions} />
                    <SearchBox {...searchBoxProps} />
                </div>
                <Message type="aux">
                    未找到落地页？请 <Button style={{verticalAlign: 'baseline'}} type="text-strong" onClick={refreshFunc}>刷新</Button> 或前往 <Link type="strong" target="_blank" toUrl={`https://kaidian.baidu.com/localplat/localshop/shop/shopList?ucId=${userId}&fromType=1`}>基木鱼</Link> 添加
                </Message>
            </div>
            <div className='store-mobile-url-drawer-container'>
                <div className='store-mobile-url-drawer-container-area'>
                    <div className='store-mobile-url-drawer-table'>
                        <Table {...tableListProps} />
                    </div>
                    <Pagination {...paginationProps} hideOnSinglePage className='store-mobile-url-drawer-pagination' />
                    <div className='store-mobile-url-drawer-footer'>
                        <Button
                            onClick={submitStoreUrl}
                            disabled={!selectedRowKeys.length}
                            className='store-mobile-url-drawer-footer-btn'
                            type='primary'
                        >
                            确定
                        </Button>
                        <Button onClick={onCancel} className='store-mobile-url-drawer-footer-btn'>取消</Button>
                        <div>已选择：{selectedRowKeys.length}个落地页</div>
                    </div>
                </div>
                <StoreMobileUrlEditor {...editorProps} />
            </div>
            <PagePreviewModal {...previewProps} />
        </>
    );
};

export default StoreMobileList;
