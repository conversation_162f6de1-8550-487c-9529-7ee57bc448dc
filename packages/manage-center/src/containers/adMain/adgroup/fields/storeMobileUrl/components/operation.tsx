import {useCallback} from 'react';
import {Button} from '@baidu/one-ui';
import {createCustomRender} from 'commonLibs/utils/render';

interface Props {
    record: {
        pageUrl: string;
        [key: string]: unknown;
    };
    trigger: (methodName: string, materialInfo: unknown) => unknown;
}

const Operation = (props: Props) => {
    const {record, trigger} = props;
    const {pageUrl} = record;
    const onClickPreview = useCallback(event => {
        trigger('onPreview', {event, record});
    }, [record, trigger]);

    return pageUrl ? (
        <Button size="small" type="text-strong" onClick={onClickPreview}>
            预览
        </Button>
    ) : null;
};

export default {
    render: createCustomRender((configs, {trigger}) => (
        (text, record) => (
            <Operation
                record={record}
                trigger={trigger}
            />
        )
    ))
};
