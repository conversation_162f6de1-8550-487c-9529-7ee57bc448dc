import {getConfigFromDataSource} from 'commonLibs/types/base/config';

export const customFields = ['pageName', 'pageType', 'operation'] as const;

export const allColumns = {
    pageName: {
        category: '属性',
        columnName: 'pageName',
        columnText: '页面名称',
        columnType: 'STRING',
        filterable: false,
        optional: false,
        sortable: false
    },
    pageType: {
        category: '属性',
        columnName: 'pageType',
        columnText: '页面类型',
        columnType: 'STRING',
        filterable: false,
        optional: false,
        sortable: false
    },
    operation: {
        category: '属性',
        columnName: 'operation',
        columnText: '操作',
        columnType: 'STRING',
        filterable: false,
        optional: false,
        sortable: false
    }
} as const;

export const storePageTypeDataSource = [
    [0, 'all', '全部页面'],
    [1, 'store', '店铺页'],
    [2, 'product', '商品页'],
    [3, 'activity', '活动页']
] as const;

const {
    valueMapByKey: storePageTypeMap,
    nameMapByValue: storePageNameMapByValue
} = getConfigFromDataSource(storePageTypeDataSource);

const storePageSelectOptions = storePageTypeDataSource.map(([value, key, label]) => {
    return {
        label,
        value
    };
});

export {
    storePageTypeMap,
    storePageSelectOptions,
    storePageNameMapByValue
};