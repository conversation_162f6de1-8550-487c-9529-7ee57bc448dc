import {useImperativeHandle, forwardRef} from 'react';
import {But<PERSON>, Drawer} from '@baidu/one-ui';
import {IconPlus} from 'dls-icons-react';
import StoreMobileList from './components/StoreMobileUrlList';
import {useControl} from 'commonLibs/hooks/externalControl';
import {truncate} from 'commonLibs/utils/string';
import {useBoolean} from '@huse/boolean';
import {isArray} from 'lodash-es';


const StoreMobileUrlDrawer = forwardRef(StoreMobileUrlDrawer_);


function StoreMobileUrlList({value = [], onChange, storeInfo}) {
    const [StoreMobileUrlEditor, {open}] = useControl(StoreMobileUrlDrawer);
    const drawerProps = {
        value,
        storeInfo,
        onChange
    };
    return (
        <>
            {
                isArray(value) && value.length > 0
                    ? (
                        <div className='store-mobile-url-editor'>
                            <div className='store-mobile-url-editor-desc'>
                                已选择『{truncate(value[0].pageName, 20)}』等落地页 数量{value.length}个
                            </div>
                            <Button onClick={open} type='text-strong'>
                                更换
                            </Button>
                        </div>
                    )
                    : (
                        <Button icon={IconPlus} onClick={open} className='store-mobile-url-btn'>选择落地页</Button>
                    )
            }
            <StoreMobileUrlEditor {...drawerProps} />
        </>
    );
}

function StoreMobileUrlDrawer_({value, storeInfo, onChange}, ref) {
    const [visible, {on: open, off: close}] = useBoolean();
    useImperativeHandle(ref, () => ({open, close}));
    const onOk = value => {
        onChange(value);
        close();
    };
    return (
        <Drawer
            title='选择落地页'
            width={1200}
            onClose={close}
            visible={visible}
            destroyOnClose
            footer={[]}
            className='store-mobile-url-drawer'
        >
            <StoreMobileList value={value} onCancel={close} onOk={onOk} storeInfo={storeInfo} />
        </Drawer>
    );
}


export default StoreMobileUrlList;