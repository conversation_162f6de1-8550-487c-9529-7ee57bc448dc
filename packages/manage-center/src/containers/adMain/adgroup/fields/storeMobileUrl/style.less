.store-mobile-url-drawer {
    &-container {
        display: flex;
        height: calc(100% - @dls-padding-unit * 12);
        &-area {
            display: flex;
            flex-direction: column;
        }
    }
    &-footer {
        background-color: @dls-color-brand-0;
        display: flex;
        align-items: center;
        position: absolute;
        width: 100%;
        bottom: 0;
        padding: @dls-padding-unit * 6 0;
        color: @dls-color-gray-7;
        font-size: @dls-padding-unit * 3.5;
        line-height: @dls-padding-unit * 6.5;
        &-btn {
            margin-right: @dls-padding-unit * 2;
        }
    }
    &-operate {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: @dls-padding-unit * 4;
        &-select {
            margin-right: @dls-padding-unit * 3;
        }
        &-search {
            margin-right: @dls-padding-unit * 2.5;
        }
    }
    &-table {
        overflow: auto;
        margin-bottom: @dls-padding-unit * 31;
    }
    &-pagination {
        position: absolute;
        bottom: @dls-padding-unit * 20;
        right: @dls-padding-unit * 78;
    }
}
.store-mobile-url-textline-title {
    display: flex;
    align-items: center;
    &-btn {
        margin-right: @dls-padding-unit * 5;
    }
}
.store-mobile-url-btn {
    padding: 0 @dls-padding-unit * 6;
}
.store-mobile-url-editor {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: @dls-padding-unit * 1.5 @dls-padding-unit * 6 @dls-padding-unit * 1.5 @dls-padding-unit * 3;
    width: @dls-padding-unit * 125.5;
    height: @dls-padding-unit * 8;
    background: @dls-color-gray-1;
    border-radius: @dls-padding-unit;
    font-size: @dls-padding-unit * 3.5;
    line-height: @dls-padding-unit * 5;
    color: @dls-color-gray-9;
}
.store-page-name {
    display: flex;
    align-items: flex-start;
    &-img {
        width: @dls-padding-unit * 16;
        height: @dls-padding-unit * 16;
        border-radius: @dls-padding-unit;
        margin-right: @dls-padding-unit * 2.5;
    }
    &-flex {
        flex: 1;
    }
}

.store-textline-container {
    width: @dls-padding-unit * 66;
    height: calc(100% - @dls-padding-unit * 20.5);
    background-color: @dls-color-brand-0;
    margin-left: @dls-padding-unit * 6;
    border: 1px solid @dls-color-gray-4;
    border-radius: @dls-padding-unit;
    &-title {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        padding: @dls-padding-unit * 2 @dls-padding-unit * 3;
        box-shadow: inset 0 -1px 0 @dls-color-gray-3;
        width: @dls-padding-unit * 60;
        height: @dls-padding-unit * 5;
        font-size: @dls-padding-unit * 3.5;
        line-height: @dls-padding-unit * 5;
        color: @dls-color-gray-9;
        background: @dls-color-gray-1;
    }
    &-list {
        height: calc(100% - @dls-padding-unit * 9);
        overflow-y: scroll;
    }
    &-list-empty {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
.store-mobile-url-card {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: @dls-padding-unit * 2 @dls-padding-unit * 3;
    justify-content: space-between;
    width: @dls-padding-unit * 56;
    height: @dls-padding-unit * 12;
    background: @dls-color-brand-0;
    &-area {
        display: flex;
        align-items: center;
    }
    &-title {
        font-size: @dls-padding-unit * 3.5;
        line-height: @dls-padding-unit * 5;
        width: @dls-padding-unit * 35.5;
        color: @dls-color-gray-9;
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }
    &-desc {
        font-size: @dls-padding-unit * 3;
        line-height: @dls-padding-unit * 4;
        color: @dls-color-gray-7;
    }
    &-pic {
        width: @dls-padding-unit * 12;
        height: @dls-padding-unit * 12;
        border: 1px solid @dls-color-gray-3;
        border-radius: @dls-padding-unit;
        margin-right: @dls-padding-unit * 3;
    }
    &-icon {
        width: @dls-padding-unit * 2.5;
        height: @dls-padding-unit * 2.5;
        color: @dls-color-gray-7;
    }
}