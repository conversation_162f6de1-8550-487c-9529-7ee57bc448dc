import {useCallback, useMemo, useState, useEffect} from 'react';
import {useSwitch} from '@huse/boolean';
import {useRequestCallback, useRequest} from '@huse/request';
import {useFormDefinition} from '@baidu/react-formulator';
import {reduce, isEmpty, get} from 'lodash-es';
import {CPQL, isLiveSubMarket} from 'commonLibs/config/marketTarget';
import {useControllableStableInterval} from 'commonLibs/hooks/interval';
import {StructuredCategoryStatusEnum, AutoProductSceneEnum} from 'commonLibs/components/productSelect/config';
import {isSupportRecommendStructContentByUrlUser} from 'commonLibs/components/selectCard/product/config';
import {isAgentUrlType} from 'commonLibs/components/commonEntry';
import sendMonitor from 'commonLibs/utils/sendHm';
import {initialData as initialCpqlAdgroupData} from '../config/CPQL';

import {FormInstance} from '../context';
import {getPreSavedUrlStructResult, getRealtimeUrlStructResult} from '../api';

export function useAdgroupForm(config, initialData, reactiveVerificationData) {
    const {
        Form,
        formData,
        validateFields,
        setFieldsError,
        setFieldsValue,
        getFieldError,
        verification: {results: verificationResults},
        scrollToField,
        getConfigByField
    } = useFormDefinition(config, initialData, reactiveVerificationData);

    const setFields = useCallback(
        function (fieldsMap) {
            const {valueFieldsMap, errorFieldsMap} = reduce(
                fieldsMap,
                function (result, field, fieldName) {
                    const {value, errors} = field;
                    result.valueFieldsMap[fieldName] = value;
                    if (errors) {
                        result.errorFieldsMap[fieldName] = errors[0].message;
                    }
                    return result;
                },
                {valueFieldsMap: {}, errorFieldsMap: {}}
            );
            setFieldsValue(valueFieldsMap);
            setFieldsError(errorFieldsMap);
        },
        [setFieldsValue, setFieldsError]
    );

    const FormInstance_ = useCallback(
        function ({children}) {
            return (
                <FormInstance
                    value={{
                        setFieldsValue,
                        getFieldError,
                        setFields,
                        setFieldsError
                    }}
                >
                    {children}
                </FormInstance>
            );
        },
        [setFieldsValue, getFieldError, setFields, setFieldsError]
    );
    return [
        {Form, FormInstance: FormInstance_, formData, verificationResults},
        {validateFields, setFieldsError, setFieldsValue, getFieldError, setFields, scrollToField, getConfigByField}
    ];
}

const EMPTY_STRUCT_LIST = [];
// 根据单元落地页推荐结构化内容
export function useRecommendStructContentByUrl({
    campaignInfo,
    setFieldsValue,
    triggerManualRecommendDialog,
    cancelManualRecommendDialog
}) {
    // 单元信息
    const [adgroupInfo, onAdgroupFieldsChange] = useState(initialCpqlAdgroupData);
    // 单元changedFields
    const [adgroupChangedFields, setAdgroupChangedFields] = useState([]);

    // 支持根据url推荐关联产品: 账户是指定行业,销售线索项目且非直播场景且单元有移动端落地页，单元没有关联结构化产品
    const isSupportRecommendStructContentByUrl = useMemo(
        () => (
            isSupportRecommendStructContentByUrlUser()
            && [CPQL].includes(campaignInfo?.marketingTargetId)
            && !isLiveSubMarket(campaignInfo?.subMarketingTargetId)
            && !!adgroupInfo.mobileFinalUrl
            && !isAgentUrlType(adgroupInfo.adgroupUrlType)
            && adgroupInfo.newAdgroupStructuredCategoryType === StructuredCategoryStatusEnum.BY_ADGROUP
            && isEmpty(adgroupInfo.catalogId)
        ),
        [campaignInfo, adgroupInfo.adgroupUrlType,
            adgroupInfo.catalogId, adgroupInfo.mobileFinalUrl, adgroupInfo.newAdgroupStructuredCategoryType]
    );
    const adgroupUrls = useMemo(
        () => (
            isSupportRecommendStructContentByUrl ? [adgroupInfo.mobileFinalUrl] : []
        ),
        [isSupportRecommendStructContentByUrl, adgroupInfo.mobileFinalUrl]
    );

    // 如果没有关联结构化产品，则通过单元的移动落地页自动推荐结构化产品
    // eslint-disable-next-line max-len
    const {data: preSavedStructContentList = EMPTY_STRUCT_LIST, pending} = useRequest(getPreSavedUrlStructResult, {
        urls: adgroupUrls
    });

    // eslint-disable-next-line max-len
    const [poll, {data: realTimeStructContentList = EMPTY_STRUCT_LIST, error: realTimeStructContentError}] = useRequestCallback(
        getRealtimeUrlStructResult,
        {urls: adgroupUrls}
    );

    const [start, pause, {isPause}] = useControllableStableInterval(
        poll,
        3 * 1000, // 3s轮询一次,
        {autoStart: false}
    );

    const [error, showError, clearError] = useSwitch();

    const [recommendMessage, setRecommendMessage] = useState('');

    const setStructContentFields = useCallback(
        (autoProductContent = {}, isPreparse) => {
            const {productId, productContent, structuredProductId, catalogId} = autoProductContent;
            // 预解析出了 productContent 或 productId，回填到表单中，如果表单刚刚修改了关联产品字段，则不进行覆盖
            if (
                isSupportRecommendStructContentByUrl
                && (!isEmpty(productContent) || catalogId || productId || structuredProductId)
            ) {
                setFieldsValue({
                    catalogId: [{
                        ...(catalogId ? {catalogId} : {}),
                        ...(structuredProductId ? {structuredProductId} : {}),
                        ...(productId ? {productId} : {}),
                        productName: get(productContent, 'productTitle', ''),
                        image: get(productContent, 'image[0].value[0].url')
                            ? [get(productContent, 'image[0].value[0].url')] : []
                    }],
                    autoProductContent: {
                        ...autoProductContent,
                        triggerScene: isPreparse // 前端存一个触发场景字段，埋点区分场景用
                            ? (productId ? AutoProductSceneEnum.matched : AutoProductSceneEnum.autoCreate)
                            : AutoProductSceneEnum.manualIdentify
                    }
                });
                clearError();
                pause();
                setRecommendMessage(structuredProductId
                    ? '基于落地页包含的推广信息，识别到您的推广产品如下'
                    : '基于您的推广信息，已为您自动生成匹配的推广产品'
                );
                if (isPreparse) {
                    sendMonitor('action', {
                        level: 'adgroup',
                        source: 'struct_product',
                        item: 'url_preparse_success',
                        value: productId ? AutoProductSceneEnum.matched : AutoProductSceneEnum.autoCreate
                    }); // 有预解析结果上报
                }
                else {
                    sendMonitor('action', {
                        level: 'adgroup',
                        source: 'struct_product',
                        item: 'url_manual_parse_success'
                    }); // 手动解析成功上报
                }
                return true;
            }
            return false;
        },
        [isSupportRecommendStructContentByUrl, setFieldsValue, clearError, pause]
    );

    /**
     * 对预解析结果分类讨论
     * 1、有 productId 则表示有匹配的关联产品
     * 2、没有 productId , 但有 productContent，则表示有预解析结果但属于无匹配产品的场景，后端自动生成的 productContent
     * 3、没有 productId，也没有 productContent，则表示无预解析结果，出浮层
     */
    useEffect(
        () => {
            if (
                isSupportRecommendStructContentByUrl
                && !pending
                && adgroupChangedFields.some(field => /(adgroupUrlType|mobileFinalUrl)/.test(field))
            ) {
                // 预解析出了 productContent 或 productId，回填到表单中
                const ifFilled = setStructContentFields(preSavedStructContentList?.[0], true);
                if (ifFilled) {
                    // 如果已经回填，要关闭浮层
                    cancelManualRecommendDialog();
                }
                else if (adgroupInfo.mobileFinalUrl) {
                    // 没有预解析结果，则触发浮层，并上报埋点
                    sendMonitor('action', {level: 'adgroup', source: 'struct_product', item: 'url_preparse_fail'});
                    triggerManualRecommendDialog();
                }
            }
        },
        [
            preSavedStructContentList, pending, isSupportRecommendStructContentByUrl, setStructContentFields,
            adgroupInfo.mobileFinalUrl, triggerManualRecommendDialog, showError, cancelManualRecommendDialog,
            adgroupChangedFields
        ]
    );

    // 实时解析结果
    useEffect(
        () => {
            const {realTimeCrawlling, url} = realTimeStructContentList?.[0] || {};
            if (realTimeStructContentError) {
                if (!error) {
                    // 手动解析失败上报
                    sendMonitor('action', {level: 'adgroup', source: 'struct_product', item: 'url_manual_parse_fail'});
                }
                showError();
                pause();
            }
            else if (
                !realTimeCrawlling
                && !isPause
                && url === adgroupInfo.mobileFinalUrl
            ) {
                const ifFilled = setStructContentFields(realTimeStructContentList?.[0], false);
                if (!ifFilled && isEmpty(realTimeStructContentList?.[0]?.productContent)) {
                    showError();
                    pause();
                    // 手动解析失败上报
                    sendMonitor('action', {level: 'adgroup', source: 'struct_product', item: 'url_manual_parse_fail'});
                }
            }
        },
        [realTimeStructContentList, isSupportRecommendStructContentByUrl, showError, pause,
            setStructContentFields, adgroupInfo.mobileFinalUrl, isPause, realTimeStructContentError]
    );

    // 如果没有填写落地页，或者关联了结构化产品，则关闭浮层并停止解析请求
    useEffect(
        () => {
            if (!isSupportRecommendStructContentByUrl) {
                cancelManualRecommendDialog();
                pause();
                clearError();
            }
        },
        [isSupportRecommendStructContentByUrl, adgroupInfo.mobileFinalUrl,
            adgroupInfo.catalogId, cancelManualRecommendDialog, pause, clearError]
    );

    const startGenerateStructContent = useCallback(
        () => {
            start();
            clearError();
            setRecommendMessage('');
        },
        [start, clearError, setRecommendMessage]
    );

    const stopGenerateStructContent = useCallback(
        () => {
            pause();
            clearError();
            setRecommendMessage('');
        },
        [pause, clearError, setRecommendMessage]
    );

    useEffect(
        () => {
            stopGenerateStructContent();
        },
        []
    );

    return [
        {
            pending: (
                (pending && adgroupChangedFields.some(field => /(adgroupUrlType|mobileFinalUrl)/.test(field)))
                || !isPause
            ),
            cancelable: !isPause,
            error,
            recommendMessage
        },
        {
            onAdgroupFieldsChange,
            startGenerateStructContent,
            stopGenerateStructContent,
            setAdgroupChangedFields
        }
    ];
}
