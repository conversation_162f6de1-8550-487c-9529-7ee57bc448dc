#main-content-container .ad-main-new-adgroup-page-container {
    min-width: calc(100% - 620px);
}

.ad-main-new-adgroup-page {
    --rf-form-item-label-width: 200px;

    display: flex;
    &-steps__wrapper {
        background: @dls-color-gray-0;
        z-index: 1;
        box-shadow: 0 4px 4px rgba(0, 0, 0, 0.06), 0 1px 10px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.04);
        min-height: calc(100vh - 80px);
    }
    &-steps {
        position: sticky;
        top: 0;
    }
    &-container {
        flex: 1;
        width: calc(100% - 212px);
    }
    &-tabs {
        .one-tabs-bar {
            background-color: #fff;
            border-radius: @dls-border-radius-2 @dls-border-radius-2 0 0;
            margin-bottom: 0;
        }
        .adgroup-form-area,
        .ad-main-create-in-adgroup {
            margin: 20px;
            position: relative;
            .creative-rich-degree-new {
                display: flex;

                .rich-text-container-new {
                    display: flex;
                    align-items: center;
                    flex: 1;
                    .rich-title {
                        display: flex;
                        align-items: center;
                        margin-bottom: 0;
                        margin-right: @dls-padding-unit * 3;
                        .title {
                            font-size: @dls-font-size-2;
                            line-height: 22px;
                            font-weight: 500;
                            margin-right: 0;
                            color: @dls-color-gray-9;
                        }
                        .tip {
                            color: @dls-icon-color-aux;
                        }
                        .rich-degree-tip {
                            font-size: 16px;
                        }
                    }
                    .summary {
                        color: @dls-color-gray-8;
                        font-size: @dls-font-size-1;
                        line-height: 20px;
                    }
                }

                .rich-degree {
                    svg {
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }
        .error-tip {
            color: @dls-color-error;
            margin-left: 4px;
        }
    }
    &-footer {
        background: #fff;
        display: flex;
        align-items: center;
        padding: 0 20px;
        margin: 0 20px;
        justify-content: space-between;
        &-right {
            display: flex;
        }
        .errors-tip {
            cursor: pointer;
            color: @dls-color-error;
            margin-left: 8px;
            > span {
                margin-left: 8px;
            }
        }
    }
    .adgroup-and-creative-errors-content {
        .one-popover-inner {
            width: 325px;
            height: 364px;
        }
        .title {
            font-weight: bold;
            margin: @dls-padding-unit 0;
        }
    }
    .campaign-name-title {
        margin-bottom: -4 * @dls-padding-unit;
        margin-left: -3 * @dls-padding-unit;
    }
    .btn-type {
        height: @dls-height-unit * 8;
        width: @dls-height-unit * 128;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: @dls-color-gray-1;
        border-radius: @dls-border-radius-1;
        padding: 0 @dls-padding-unit * 3;
        color: @dls-color-gray-9;
        font-size: @dls-font-size-1;
    }

    .show-recommend-keywords {
        color: @dls-color-gray-9;
        font-size: @dls-font-size-1;
        margin-top: @dls-padding-unit * 3;

        button {
            margin-left: @dls-padding-unit * 3;
        }
    }

    &-form {
        padding: 16px 20px 0;
        &-footer {
            background-color: @dls-color-gray-0;
            border-radius: 0 0 6px 6px;
            padding: 24px;
            margin: 16px 0;
            button + button {
                margin-left: 12px;
            }
        }
    }
    .add-adgroup-button {
        padding: 24px;
        background: @dls-color-gray-0;
        border-radius: 6px;
    }
    .fcNew-adgroup-bar-container {
        margin-bottom: 0;
        .empty .info {
            border-color: @dls-color-brand;
        }
        .fc-manage-new-adgroup-bar .info {
            height: 130px;
            padding: 12px;
            .description {
                color: @dls-color-gray-7;
            }
        }
        .completed-adgroups-carousel {
            margin: 0 0 0 32px;
        }
        .completed-title {
            padding: @dls-padding-unit * 6 @dls-padding-unit * 8 @dls-padding-unit * 4;
        }
    }
    .batch-final-url-editor-form-preview-url {
        line-height: 2;
    }
}

.ad-main-process-kr-container {
    display: flex;
    height: 100%;
    overflow: hidden;
    .ad-main-process-kr-main {
        display: flex;
        margin-top: @dls-padding-unit*6;
        &-left {
            flex: 1;
            min-width: 0;
            border: 1px solid @dls-color-gray-3;
            border-radius: @dls-border-radius-1;
            padding: 16px;
            margin-right: 16px;
            .kr-search-box {
                gap: 12px;

                .header {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 12px;
                    .title {
                        font-weight: 500;
                        font-size: @dls-font-size-1;
                        color: @dls-color-gray-9;
                    }
                }
                .kr-keyword-url-and-business-expand {
                    margin-top: 0;

                    .kr-keyword-business {
                        margin-left: 0;
                    }
                }

                .kr-search-box-seed {
                    width: 100%;

                    /* stylelint-disable-next-line selector-class-pattern */
                    .one-ui-pro-multiLabel {
                        width: calc(100% - 252px);
                    }
                    /* stylelint-disable-next-line selector-class-pattern */
                    .one-ui-pro-multiLabel-input-area {
                        width: 100%;
                    }
                    /* stylelint-disable-next-line selector-class-pattern */
                    .one-ui-pro-multiLabel-input-area-input {
                        width: 100%;
                    }
                    /* stylelint-disable-next-line selector-class-pattern */
                    .one-ui-pro-multiLabel-label {
                        display: flex;
                        align-items: baseline;
                        justify-content: space-between;
                        padding: 0 8px;
                        cursor: pointer;
                        height: 24px;
                        background-color: rgba(84, 91, 102, 0.1);
                        border-radius: 4px;
                        color: #545b66;
                        box-sizing: border-box;
                        font-size: 12px;
                        margin-right: 3px;
                        margin-top: 3px;
                    }
                }
            }
            .kr-material-list-container {
                margin-top: 12px;
                margin-bottom: 0;
            }
        }
        &-right {
            flex: 0 0 350px;
            box-sizing: border-box;
            .kr-common-buffer-container {
                width: 440px;
            }
        }
    }
}
.product-tip {
    color: @dls-color-gray-7;
    font-weight: @dls-font-weight-1;
    font-size: @dls-font-size-1;
    margin-left: @dls-padding-unit * 4;
    .name {
        color: @dls-color-gray-9;
    }
}

.adgroup-process-keyword-drawer .one-drawer-footer {
    display: flex;
    align-items: center;

    .adgroup-keyword-footer-tip {
        font-size: @dls-padding-unit * 3.5;
        line-height: @dls-padding-unit * 6.5;
        color: @dls-color-gray-7;
    }

    .errors-tip {
        color: @dls-color-error-7;
        text-align: right;
        flex-grow: 1;

        .dls-icon {
            margin-right: @dls-padding-unit;
        }
    }

    .adgroup-keyword-footer-law-describe {
        position: absolute;
        font-size: @dls-padding-unit * 3;
        line-height: @dls-padding-unit * 3;
        color: @dls-color-gray-7;
        bottom: @dls-padding-unit;
    }
}

.keywords-tips {
    color: #545b66;
    font-size: 14px;
    line-height: 32px;
    margin-bottom: @dls-padding-unit * 2;
}