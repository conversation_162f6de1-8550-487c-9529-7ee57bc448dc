import {uniqBy, cloneDeep} from 'lodash-es';
import {AdgroupUrlTypeEnum} from 'commonLibs/components/commonEntry';
import {isUrlTemplateUser, isUnitAgentUrlUser} from 'commonLibs/utils/getFlag';
import {campaignBidTypeConfig} from 'commonLibs/config/ocpc';
import {CPQL, isBjhSubMarket, isLiveSubMarket, WEB, NATIVE} from 'commonLibs/config/marketTarget';
import {autoOrientationConfig} from 'app/containers/fcNew/adgroup/adgroupAutoTargetingStatus/config';
import {KeywordProcessor} from './keyword';
import {BroadCastModeType} from 'app/api/adgroup/anchor';
import {isInQinggeIframe} from 'commonLibs/utils/isInIframe';

const NATIVE_TYPE_BIJI = 2;

export function adgroupParamsPipeline(generators, initial) {
    return function (campaignInfo, adgroupValues) {
        return generators.reduce(
            (result, generator) => generator(campaignInfo, adgroupValues, result),
            cloneDeep(initial)
        );
    };
}

export function adgroupAnchorParams(
    {subMarketingTargetId},
    {relativeAnchor},
    result
) {
    if (relativeAnchor && isLiveSubMarket(subMarketingTargetId) && isInQinggeIframe) {
        result.adgroupType.anchorId = relativeAnchor;
        result.adgroupType.broadCastMode = BroadCastModeType.CON_ADVERTISING;
    }
    return result;
}

export function adgroupTypeParams(
    {campaignId, campaignBidType, subMarketingTargetId},
    {adgroupName, adgroupPrice, adgroupAutoTargetingStatus},
    result
) {
    result.adgroupType.campaignId = +campaignId;
    result.adgroupType.adgroupName = adgroupName;
    result.adgroupType.adgroupAutoTargetingStatus = adgroupAutoTargetingStatus === autoOrientationConfig.on;
    // 计划自动出价后，分转化和点击
    if (campaignBidType === campaignBidTypeConfig.cpc) {
        if (!adgroupPrice) {
            // 支持不填出价，传-1
            result.adgroupType.maxPrice = -1;
        }
        else {
            result.adgroupType.maxPrice = +adgroupPrice;
        }
    }
    else {
        result.adgroupType.maxPrice = 1;
    }
    return result;
}

export function adgroupBrandParams(_, {brandName}, result) {
    result.segmentBindTypes.push({
        segmentId: +brandName?.segmentId,
        segmentSign: brandName?.segmentSign,
        segmentType: 510
    });
    return result;
}

export function keywordsTypeParams({campaignId}, {keywords}, result) {
    const nonEmptyWords = keywords.filter(item => !!item.keyValue);
    const uniqueWords = uniqBy(nonEmptyWords, 'keyValue');
    result.keywordTypes = uniqueWords.map(item => ({
        ...new KeywordProcessor(item).toParams(),
        campaignId
    }));
    return result;
}

export function adgroupUrlParams(
    {marketingTargetId, subMarketingTargetId},
    {
        isShowUseAgentUrlField, useAgentUrl,
        adgroupUrlType = AdgroupUrlTypeEnum.normal,
        adgroupAgentUrl, adgroupAgentParam,
        pcFinalUrl, pcTrackParam, pcTrackTemplate, mobileFinalUrl, mobileTrackParam, mobileTrackTemplate,
        bjhContent = []
    },
    result
) {
    if (isLiveSubMarket(subMarketingTargetId)) {
        return result;
    }
    // 落地页访问链接和监控后缀
    const handleNormalUrl = () => {
        result.adgroupType.pcFinalUrl = pcFinalUrl || '';
        result.adgroupType.pcTrackParam = pcTrackParam || '';
        result.adgroupType.mobileFinalUrl = mobileFinalUrl || '';
        result.adgroupType.mobileTrackParam = mobileTrackParam || '';
        if (isUrlTemplateUser()) {
            result.adgroupType.pcTrackTemplate = pcTrackTemplate || '';
            result.adgroupType.mobileTrackTemplate = mobileTrackTemplate || '';
        }
    };
    // 商家智能体访问链接和监控后缀
    const handleAgentUrl = () => {
        result.adgroupType.adgroupAgentUrl = adgroupAgentUrl || '';
        result.adgroupType.adgroupAgentParam = adgroupAgentParam || '';
    };

    if ([WEB, CPQL].includes(marketingTargetId) && isUnitAgentUrlUser()) {
        result.adgroupType.adgroupUrlType = adgroupUrlType;
        if (isShowUseAgentUrlField) {
            result.adgroupType.adgroupUrlType = useAgentUrl ? AdgroupUrlTypeEnum.agent : AdgroupUrlTypeEnum.normal;
        }
    }
    if (adgroupUrlType === AdgroupUrlTypeEnum.agent) {
        handleAgentUrl();
    }
    else {
        handleNormalUrl();
    }
    if (marketingTargetId === NATIVE && isBjhSubMarket(subMarketingTargetId)) {
        result.adgroupType.adgroupUrlType = AdgroupUrlTypeEnum.bjh;
        result.adgroupType.mobileFinalUrl = bjhContent[0]?.url;
    }
    return result;
}

export function adgroupBjhParams(
    {},
    {bjhContent},
    result
) {
    result.adgroupType.nativeContentInfos = bjhContent.map(item => (
        {nativeId: item.nativeId, nativeType: NATIVE_TYPE_BIJI}
    ));
    return result;
}
