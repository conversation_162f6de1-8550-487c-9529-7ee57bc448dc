import dayjs from 'dayjs';
import {getSingleErrorMessage} from 'commonLibs/utils/getErrorTextByCode';
import {randomString} from 'commonLibs/utils/string';
import {isFunction, find} from 'lodash-es';
import {isAdgroupProductRequiredUser, isLXTUser} from 'commonLibs/utils/getFlag';

export function formatSaveAdgroupErrors(error, values, formFieldMapForBackend = {}) {
    const {adgroupError = {}, keywordErrors = []} = error || {};
    // 处理单元相关field报错。
    const {field, code} = adgroupError;
    const errorMessage = getSingleErrorMessage(adgroupError);
    const formField = isFunction(formFieldMapForBackend[field])
        ? formFieldMapForBackend[field](code)
        : (formFieldMapForBackend[field] || field);
    const errorFieldsMap = {};
    const alertError = [];
    if (formField) {
        if (values.hasOwnProperty(formField)) {
            errorFieldsMap[formField] = errorMessage;
        }
        else {
            alertError.push({code, message: errorMessage});
        }
    }
    if (keywordErrors.length) {
        values.keywords.forEach((item, index) => {
            const error = find(keywordErrors, item => item.id === index);
            if (error) {
                const errorMessage = getSingleErrorMessage(error);
                item.error = errorMessage;
            }
        });
        errorFieldsMap.keywords = '关键词设置错误，请检查';
    }
    return {errorFieldsMap, alertError, keywords: values.keywords};
}

export function getDefaultAdgroupName() {
    const currentTime = dayjs().format('MM_DD_HH:mm');
    return `单元${randomString(3)}_${currentTime}`;
}

export function isAdgroupProductRequired(formData) {
    if (isAdgroupProductRequiredUser()) {
        return true;
    }
    return false;
}
