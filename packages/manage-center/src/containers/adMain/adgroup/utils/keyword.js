import {wmatchpreferMap} from 'commonLibs/config/matchType';

export class KeywordProcessor {
    constructor(keyword) {
        this.separator = '-';
        this.keyword = keyword;
    }

    // 注意：如果更改 toParams 处理逻辑，需要成对更改fromParams 的处理逻辑。
    toParams() { // 映射成接口参数
        const {keyValue, price, matchType: mixMatchType, addFrom, suggestSource} = this.keyword;
        const [matchType, phraseType] = mixMatchType.split(this.separator);

        const param = {
            keyword: keyValue.trim(),
            price: price === '' ? null : +price,
            wmatchprefer: wmatchpreferMap.SELF,
            matchType,
            phraseType,
            addFrom,
            suggestSource
        };
        // 如果addFrom是undefined 就删掉addFrom
        if (!param.addFrom) {
            delete param.addFrom;
        }
        return param;
    }

    // 注意：如果更改 fromParams 处理逻辑，需要成对更改 toParams 的处理逻辑。
    fromParams() { // 初始化
        const item = this.keyword;
        return {
            keyValue: item.keyword,
            matchType: [item.matchType, item.phraseType].join(this.separator),
            price: item.price + ''
        };
    }
}