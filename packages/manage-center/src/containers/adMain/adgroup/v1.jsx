import {ProcessSteps, Title} from '@baidu/one-ui-pro';
import {useParams} from 'commonLibs/route';
import {useKeyOrientedArray} from 'commonLibs/hooks/collection/array';
import {fetchCampaignInfo} from 'app/containers/fcNew/actions/adgroup/api';
import {useRecordByComponent} from 'commonLibs/hooks/record';
import {ProcessAdgroupBoundary, useProcessAdgroupResource, CampaignInfoProvider} from './context';
import {CompletedAdgroups} from './CompletedAdgroups';
import {AdgroupForm, useSwitchForm} from './Form';
import {initialAdgroupForm} from './config';
import {renderError} from './config/uiConfig';
import './style.less';

const initialCompletedAdgroups = [];
function getKey(data) {
    return data.adgroupType.adgroupId;
}

function AdgroupPage({campaignId}) {
    const [campaignInfo] = useProcessAdgroupResource(fetchCampaignInfo, campaignId);
    const [
        completedAdgroups,
        {push: addAdgroupToCompleted, removeByKey: deleteAdgroupById}
    ] = useKeyOrientedArray(initialCompletedAdgroups, {getKey});
    const [
        {isFormOpen, autoKey},
        {createAndOpenForm, closeForm, refreshForm}
    ] = useSwitchForm(true);
    useRecordByComponent({tag: 'create_adgroup'});

    return (
        <div className="ad-main-new-adgroup-page" id="ad-main-new-adgroup-page">
            <Steps campaignInfo={campaignInfo} autoKey={autoKey} />
            <div className="ad-main-new-adgroup-page-container" id="ad-main-new-adgroup-page">
                {
                    campaignInfo.campaignName && (
                        <Title
                            label="新建推广单元"
                            className="campaign-name-title"
                            desc={`为『${campaignInfo.campaignName}』新建单元`}
                        />
                    )
                }
                <CampaignInfoProvider value={campaignInfo}>
                    {
                        completedAdgroups.length > 0 && (
                            <CompletedAdgroups
                                completedAdgroups={completedAdgroups}
                                deleteAdgroupById={deleteAdgroupById}
                                getKey={getKey}
                                isFormOpen={isFormOpen}
                                closeForm={closeForm}
                            />
                        )
                    }
                    <AdgroupForm
                        isFormOpen={isFormOpen}
                        autoKey={autoKey}
                        createAndOpenForm={createAndOpenForm}
                        refreshForm={refreshForm}
                        completedAdgroups={completedAdgroups}
                        addAdgroupToCompleted={addAdgroupToCompleted}
                        campaignInfo={campaignInfo}
                    />
                </CampaignInfoProvider>
            </div>
        </div>
    );
};

function Steps({campaignInfo}) {
    const [{steps}] = useProcessAdgroupResource(initialAdgroupForm, campaignInfo);
    const stepsProps = {
        type: 'container',
        current: 1,
        dataSource: steps,
        className: 'ad-main-new-adgroup-page-steps'
    };
    return (
        <div className="ad-main-new-adgroup-page-steps__wrapper">
            <ProcessSteps {...stepsProps} />
        </div>
    );
}

export default function () {
    const {campaignId} = useParams();
    return (
        <ProcessAdgroupBoundary renderError={renderError}>
            <AdgroupPage campaignId={campaignId} />
        </ProcessAdgroupBoundary>
    );
};
