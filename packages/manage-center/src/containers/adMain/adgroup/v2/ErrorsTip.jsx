import {forwardRef, useImperativeHandle, useMemo, useState} from 'react';
import {useBoolean} from 'huse';
import {Button, Popover, Tabs} from '@baidu/one-ui';
import {atom, useRecoilValue} from 'recoil';
import {IconExclamationTriangleSolid} from 'dls-icons-react';
import {creativeTypeEnum} from 'commonLibs/config/creativeText';

export const adgroupAndCreativeErrorsState = atom({
    key: 'adgroupAndCreativeErrors',
    default: {}
});

function ErrorTipContent({errorList}) {
    const [activeKey, changeActiveKey] = useState(`${errorList[0][0]}`);
    const realActiveKey = useMemo(
        () => errorList.find(([key]) => activeKey === `${key}`)?.[0] || errorList[0][0],
        [errorList, activeKey]
    );
    return (
        <Tabs activeKey={`${realActiveKey}`} onChange={changeActiveKey}>
            {
                errorList.map(([adgroupIndex, adgroupErrorItems, creativeErrorItems]) => {
                    return (
                        <Tabs.TabPane key={adgroupIndex} tab={`单元${adgroupIndex + 1}`}>
                            <div>
                                {adgroupErrorItems}
                                {
                                    !!creativeErrorItems.length && (
                                        <div>
                                            <div className="title">创意设置</div>
                                            {creativeErrorItems}
                                        </div>
                                    )
                                }
                            </div>
                        </Tabs.TabPane>
                    );
                })
            }
        </Tabs>
    );
}

export const ErrorsTip = forwardRef(ErrorsTip_);
function ErrorsTip_({adgroups, changeAdgroupTab}, ref) {
    const [visible, {toggle, on}] = useBoolean();
    const errors = useRecoilValue(adgroupAndCreativeErrorsState);
    useImperativeHandle(ref, () => ({showErrorTip: on}));

    const errorList = useMemo(
        () => adgroups
            .map(({key: adgroupKey, ...props}, adgroupIndex) => {
                const {
                    adgroupErrorItems,
                    creativeErrorItems
                } = unionAdgroupAndCreativeErrorItems(adgroupKey, errors, {changeAdgroupTab, ...props});
                if (adgroupErrorItems.length || creativeErrorItems.length) {
                    return [adgroupIndex, adgroupErrorItems, creativeErrorItems];
                }
            })
            .filter(v => v),
        [adgroups, errors]
    );

    if (!errorList.length) {
        return null;
    }
    return (
        <Popover
            visible={visible}
            placement="topRight"
            content={<ErrorTipContent errorList={errorList} />}
            trigger="click"
            onVisibleChange={toggle}
            getPopupContainer={node => node.parentNode}
            overlayClassName="adgroup-and-creative-errors-content"
        >
            <div className="errors-tip">
                <IconExclamationTriangleSolid />
                <span>部分项目填写错误，请修改</span>
            </div>
        </Popover>
    );
}

function createAdgroupErrorItems(adgroupKey, adgroupErrors = {}, {adgroupFormRef, changeAdgroupTab}) {
    return Object.keys(adgroupErrors).map(field => {
        if (!adgroupFormRef.current) {
            return null;
        }
        const messages = adgroupErrors[field];
        const {label: fieldName} = adgroupFormRef.current.getConfigByField(field);
        const switchTabAndScrollToField = () => {
            changeAdgroupTab(adgroupKey);
            adgroupFormRef.current.scrollToField(field);
        };
        return (
            <div key={adgroupKey}>
                <div className="title">{fieldName}</div>
                {
                    messages.map((msg, index) => (
                        <div key={msg}>
                            <Button type="text-aux" size="medium" onClick={switchTabAndScrollToField}>
                                {index + 1}{'. '}{msg}
                            </Button>
                        </div>
                    ))
                }
            </div>
        );
    });
}

function createCreativesErrorItems(
    adgroupKey,
    creativeErrors = {},
    {creativeTypeInstance, adgroupTypeEmitter, changeAdgroupTab}
) {
    return Object.keys(creativeErrors).map(key => {
        const [field, creativeIndex] = key.split('.');
        const {type, methods: {getConfigByField, scrollToField}} = creativeTypeInstance;
        const {name: fieldName} = getConfigByField(field);
        const switchTabAndScrollToField = () => {
            changeAdgroupTab(adgroupKey);
            if (type === creativeTypeEnum.customize && creativeIndex != null) {
                adgroupTypeEmitter.triggerSync('changeCustomizeCreativeTabIndex', creativeIndex);
            }
            scrollToField(field);
        };
        if (!fieldName) { // 非表单field报错，只展示错误原因，不支持定位
            return (
                <div key={key}>
                    <Button type="text-aux" size="medium">
                        <span>{creativeErrors[key]}</span>
                    </Button>
                </div>
            );
        }
        return (
            <div key={key}>
                <Button type="text-aux" size="medium" onClick={switchTabAndScrollToField}>
                    <span>{fieldName}{creativeIndex != null && Number(creativeIndex) + 1}：</span>
                    <span>{creativeErrors[key]}</span>
                </Button>
            </div>
        );
    });
}

function unionAdgroupAndCreativeErrorItems(
    adgroupKey,
    errors,
    {
        adgroupTypeEmitter,
        adgroupFormRef,
        reactiveCreativeInfo,
        programmaticCreativeInstance,
        customizeCreativeInstance,
        changeAdgroupTab
    }
) {
    const creativeTypeInstance = reactiveCreativeInfo.$get('creativeType') === creativeTypeEnum.programmatic
        ? programmaticCreativeInstance
        : customizeCreativeInstance;
    const adgroupErrorItems = createAdgroupErrorItems(
        adgroupKey,
        errors[adgroupKey]?.adgroupErrors,
        {adgroupFormRef, changeAdgroupTab}
    );
    const creativeErrorItems = createCreativesErrorItems(
        adgroupKey,
        errors[adgroupKey]?.creativeErrors,
        {creativeTypeInstance, adgroupTypeEmitter, changeAdgroupTab}
    );
    return {adgroupErrorItems, creativeErrorItems};
}
