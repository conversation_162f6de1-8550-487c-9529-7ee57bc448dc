import {useState, useCallback, createRef, useMemo, useEffect, useRef} from 'react';
import {nanoid} from 'nanoid';
import {v4 as uuidv4} from 'uuid';
import {useMap} from '@huse/collection';
import {createReactiveData, useFormErrors} from '@baidu/react-formulator';
import {Tabs, Dialog, Layout, Button} from '@baidu/one-ui';
import SaveFooter from 'commonLibs/SaveFooter';
import {useFormulatorLogMethods} from 'commonLibs/hooks/funnelLog';
import {IconExclamationCircle, IconEdit, IconEye} from 'dls-icons-react';
import {ProcessSteps, Title} from '@baidu/one-ui-pro';
import {useControl} from 'commonLibs/hooks/externalControl';
import {FLOW_SCOPE} from 'commonLibs/config/ocpc';
import {useParams} from 'commonLibs/route';
import {useRecordByComponent, sendScreenRecord} from 'commonLibs/hooks/record';
import {isSupportRecommendStructContentByUrlUser} from 'commonLibs/components/selectCard/product/config';
import {compact, isEmpty, mapValues, groupBy, map, keys, partial, get, pick, flatten, concat} from 'lodash-es';
import {promisifyConfirm} from 'commonLibs/hooks/dialog';
import {RecoilRoot, useRecoilValue, useSetRecoilState} from 'recoil';
import {useAccountInfo, useUserInfo} from 'commonLibs/context/sharedInfo';
import {useRequestAgentAgentUrlList} from 'commonLibs/components/commonEntry';
import {isUnitAgentUrlUser, isCannotUseAgentUrlRegUser} from 'commonLibs/utils/getFlag';
import {EventEmitter} from 'commonLibs/utils/EventEmitter';
import {useKeyOrientedArray} from 'commonLibs/hooks/collection/array';
import {fetchCampaignInfo} from 'app/containers/fcNew/actions/adgroup/api';
import {ProcessAdgroupBoundary, useProcessAdgroupResource, CampaignInfoProvider} from '../context';
import {InternalForm} from '../Form';
import {initialAdgroupForm} from '../config';
import {renderError} from '../config/uiConfig';
import {creativeTypeEnum} from 'commonLibs/config/creativeText';
import {ErrorsTip, adgroupAndCreativeErrorsState} from './ErrorsTip';
import {formatSaveAdgroupErrors} from '../utils';
import {formatCretiveTypeError} from '../../creative/separateNew';
import RecommendAdgroups from 'app/containers/fcNew/adgroup/recommend/v2';
import {validateSegmentsError} from 'app/containers/fcNew/creative/utils/error';
import {CreativesInAdgroup} from '../../creative/processNew';
import {validateAllAdgroupsAndCreatives, generateParams, formatVideoError} from './utils';
import {preValidate, saveAdgroupsAndCreatives} from '../api';
import '../style.less';
import {useBotContextInfo} from 'commonLibs/context/botInfo';
import {toPithySteps} from 'app/containers/fcNew/creative/utils';
import {sendCreativeCopilotMonitor} from 'commonLibs/utils/copilotMonitor';
import {useInitAichatCreativeV2, useOpenDigitalHumanComposer} from 'app/hooks/useAichatCreative';
import {getNewProcessCreativeData} from '../../creative/utils/aichatCreative';
import sendMonitor, {
    CreativeRecommendTarget, sendCreativeRecommendMonitor, PagePathType
} from 'commonLibs/utils/sendHm';
import {isOcpcCampaign} from 'commonLibs/utils/campaign';
import {WEB, APP, CPQL, isLiveSubMarket} from 'commonLibs/config/marketTarget';
import {useBackToAdgroupList} from 'app/containers/fcNew/common/backUrl';
import {PostMessageEventType} from 'commonLibs/utils/postMessage';
import {AcceptFunnelItem, sendKeywordAcceptFunnel} from 'toolsCenter/kr/util';

const {Sidebar, Content, Header, Footer} = Layout;

const isShowAdgroupRecommend = ({marketingTargetId, adType, campaignBidType} = {}) => {
    return marketingTargetId === WEB || (
        marketingTargetId === APP && isOcpcCampaign({campaignBidType, adType})
    );
};

function AdgroupPage({campaignId}) {
    const [campaignInfo] = useProcessAdgroupResource(fetchCampaignInfo, campaignId);
    const {registerDomain} = useAccountInfo();
    const operationId = useRef(uuidv4());
    const transMonitorId = useMemo(() => uuidv4(), []);

    const {pageList, pending} = useRequestAgentAgentUrlList();
    const {userId} = useUserInfo();
    // 是否能够选择是使用落地页链接还是商家智能体
    const isShowAdgroupUrlType = useMemo(
        () => (
            isUnitAgentUrlUser()
            && [CPQL, WEB].includes(campaignInfo.marketingTargetId)
            && !isLiveSubMarket(campaignInfo.subMarketingTargetId)
        ),
        [campaignInfo]
    );
    const isAgentUrlTypeDisabled = useMemo(
        () => isEmpty(pageList) && !pending,
        [pending, pageList]
    );
    // 落地页链接是否禁选商家智能体类型Url
    const isDisabledAgentReg = useMemo(
        () => isShowAdgroupUrlType && isCannotUseAgentUrlRegUser(),
        [isShowAdgroupUrlType]
    );
    const formInitProps = useMemo(() => ({
        isShowAdgroupUrlType,
        isAgentUrlTypeDisabled,
        isDisabledAgentReg,
        pending,
        pageList,
        agentCreateUrl: `https://aiagent.baidu.com/mbot/user/${userId}/knowledge?ucUserId=${userId}`
    }), [isShowAdgroupUrlType, isAgentUrlTypeDisabled, isDisabledAgentReg, pending, pageList, userId]);
    const [isPreview, setIsPreview] = useState(false); // 创意预览是否展示
    const {visible: botVisible} = useBotContextInfo();
    // 标记表单是否已经编辑过
    const [adgroupsFormChangedStateMap, {set, clear}] = useMap();
    // 屏幕过大时，右侧预览区显示在右侧
    const isShowPreviewOnRightSection = document.body.clientWidth >= 1920 || !botVisible;
    const [
        [adgroups, currentAdgroup],
        {onAddAdgroup, onDeleteAdgroup, changeAdgroupTab, methodsForCreativeInstances, getItemByKey, onBatchAddAdgroup}
    ] = useAdgroupAndCreativeForms();
    const {adgroupFormRef} = adgroups.find(item => item.key === currentAdgroup);

    const goBack = useBackToAdgroupList({
        blockAndSaveDraft: adgroupFormRef?.current?.blockAndSaveDraft,
        getFormStatusChanged: adgroupFormRef?.current?.getFormStatusChanged
    });

    const [AdgroupAndCreativeErrorsTip, {showErrorTip}] = useControl(ErrorsTip);

    useEffect(() => {
        sendCreativeRecommendMonitor({
            target: CreativeRecommendTarget.enter_creative,
            id: operationId.current,
            source: PagePathType.createCreativeInProcess
        });
    }, []);

    const {updateAdgroupByKey, adgroupsMap} = useAigcCreative({
        currentAdgroup, getItemByKey, changeAdgroupTab,
        operationId: operationId.current, pagePathType: PagePathType.createCreativeInProcess,
        ...pick(campaignInfo, ['campaignId', 'marketingTargetId', 'businessPointName', 'adType', 'equipmentType'])
    });
    const {
        logOnSubmitClick,
        logOnSubmitSuccess,
        logOnSubmitFail,
        logOnCancelClick,
        logOnAreaClick
    } = useFormulatorLogMethods({
        shouldSendChangeLog: true,
        source: 'AdgroupAndCreativeForm',
        config: {
            commonTrackParams: {
                marketingtargetid: campaignInfo.marketingTargetId,
                type: FLOW_SCOPE[campaignInfo.equipmentType]
            }
        }
    });

    const logOnClickSubmit = ({items = []}) => {
        items.forEach(adgroupItem => {
            sendMonitor('click', {
                level: 'adgroupV2',
                marketingtargetid: campaignInfo?.marketingTargetId,
                item: 'submit-adgroup'
            });
            if (
                !isEmpty(get(adgroupItem, 'recommendVideos'))
                || !isEmpty(get(adgroupItem, 'recommendImageMap'))
                || !isEmpty(get(adgroupItem, 'recommendTextIds'))
            ) {
                sendCreativeCopilotMonitor({
                    value: '创意素材采纳并提交',
                    // eslint-disable-next-line max-len
                    ideaType: isEmpty(get(adgroupItem, 'creativeTypes[0].programTitles')) ? creativeTypeEnum.customize : creativeTypeEnum.programmatic
                });
            }
            if (!isEmpty(get(adgroupItem, 'creativeTypes[0].programTitles'))) {
                sendMonitor('click', {
                    level: 'adgroupV2',
                    marketingtargetid: campaignInfo?.marketingTargetId,
                    item: 'submit-creative',
                    label: '自适应创意'
                });
            }
            else if (get(adgroupItem, 'creativeTypes[0].title')) {
                sendMonitor('click', {
                    level: 'adgroupV2',
                    marketingtargetid: campaignInfo?.marketingTargetId,
                    item: 'submit-creative',
                    label: '自定义创意'
                });
            }
        });
    };

    const [hadAlertNotice, setHadAlertNotice] = useState(false); // 是否已经弹过提示框
    const onSave = async () => {
        showErrorTip();
        logOnSubmitClick();
        const values = await validateAllAdgroupsAndCreatives(adgroups);
        const originKeywordTypes = values.map(item => item.adgroupType.keywords ?? []);
        const uploadKeywordCount = flatten(originKeywordTypes).filter(item => item.isFromUpload).length;
        const onSubmit = async () => {
            const adgroupAndCreativeAndSegmentBindParams = await generateParams({
                registerDomain, ...campaignInfo}, values
            );
            logOnClickSubmit(adgroupAndCreativeAndSegmentBindParams);
            try {
                await saveAdgroupsAndCreatives(adgroupAndCreativeAndSegmentBindParams);
                const {adgroupFormRef} = adgroups.filter(item => item.key === currentAdgroup)[0];
                const keywordTypes = adgroupAndCreativeAndSegmentBindParams.items.map(item => item.keywordTypes);
                const acceptedKeywordCount = flatten(keywordTypes).filter(item => item.addFrom).length;
                sendKeywordAcceptFunnel({
                    transMonitorId,
                    action: AcceptFunnelItem.accept,
                    count: acceptedKeywordCount,
                    uploadCount: uploadKeywordCount,
                    addFroms: flatten(keywordTypes).filter(item => item.addFrom).map(item => item.addFrom),
                    // eslint-disable-next-line max-len
                    recommendStrategy: flatten(originKeywordTypes).filter(item => item.recommendStrategy).map(item => item.recommendStrategy)
                });
                logOnSubmitSuccess();
                sendCreativeRecommendMonitor({
                    target: CreativeRecommendTarget.submit_creative_recommend,
                    id: operationId.current,
                    source: PagePathType.createCreativeInProcess,
                    // 串联新建多个单元视为一个流程 有一个使用了推荐素材就计数
                    totaltime: adgroupAndCreativeAndSegmentBindParams?.items?.map(
                        item => item.recommendTextIds?.length || 0
                    ).reduce((a, b) => a + b, 0),
                    count: adgroupAndCreativeAndSegmentBindParams?.items?.map(
                        item => Object.keys(item?.recommendImageMap || {})?.length || 0
                    ).reduce((a, b) => a + b, 0),
                    onecount: adgroupAndCreativeAndSegmentBindParams?.items?.map(
                        item => item.recommendVideos?.length || 0
                    ).reduce((a, b) => a + b, 0)
                });
                if (isSupportRecommendStructContentByUrlUser()) {
                    values.forEach(item => {
                        const {autoProductContent, catalogId} = item?.adgroupType || {};
                        const {productId, triggerScene} = autoProductContent || {};
                        const {productId: catalogProductId} = catalogId?.[0] || {};
                        if (!!catalogProductId && catalogProductId === productId) {
                            sendMonitor('action', {
                                level: 'adgroup',
                                source: 'struct_product',
                                item: 'autoProduct_save_success',
                                value: triggerScene
                            });
                        }
                    });
                }
                adgroupFormRef.current.markCompleted();
                goBack(PostMessageEventType.ON_SAVE_SUCCESS);
            }
            catch (adgroupAndKeywordAndCreativeError) {
                sendScreenRecord('adgroup_save_error');
                const errorsToAlert_ = adgroupAndKeywordAndCreativeError.errors.map(error => {
                    const adgroupIndex = error.id;
                    const {
                        adgroupFormRef,
                        reactiveCreativeInfo,
                        programmaticCreativeInstance,
                        customizeCreativeInstance
                    } = adgroups[adgroupIndex];

                    // 处理单元报错
                    const {errorFieldsMap: adgroupErrorFieldsMap, alertError: adgroupAlertError,
                        keywords: keywordsWithErrorMessage
                    } = formatSaveAdgroupErrors(
                        error,
                        values[adgroupIndex].adgroupType,
                        adgroupFormRef.current.formFieldMapForBackend
                    );
                    if (adgroupErrorFieldsMap) {
                        adgroupFormRef.current.setFieldsError(adgroupErrorFieldsMap);
                        // 这样写有问题，可能是因为同时调用了setFieldsValue和setFieldsError
                        // 关键词错误信息显示，需要更新表单项
                        // if (adgroupErrorFieldsMap.keywords && !isEmpty(keywordsWithErrorMessage)) {
                        //     adgroupFormRef.current.setFieldsValue({keywords: keywordsWithErrorMessage});
                        // }
                    }
                    const alertError = [...adgroupAlertError];

                    logOnSubmitFail({
                        ...adgroupAndKeywordAndCreativeError,
                        errors: concat(
                            [],
                            get(error, 'adgroupError') ? [error.adgroupError] : [],
                            get(error, 'keywordErrors', []),
                            get(error, 'creativeErrors', []),
                            get(error, 'pictureSegmentBindErrors', []),
                            error.videoSegmentBindErrors?.[0]?.errorInfos || error.videoSegmentBindErrors || []
                        )
                    });

                    // 处理创意文案报错
                    if (error.creativeErrors) {
                        const creativeFormInstance
                            = reactiveCreativeInfo.$get('creativeType') === creativeTypeEnum.programmatic
                                ? programmaticCreativeInstance
                                : customizeCreativeInstance;
                        const formattedCretiveTypeError = formatCretiveTypeError(
                            {
                                creativeErrors: error.creativeErrors,
                                status: error.status
                            },
                            {creativeFormInstance},
                            {adgroupList: [{}]}
                        );
                        const creativeErrorFieldsMap = formattedCretiveTypeError.errorFieldsMap;
                        const creativeAlertError = formattedCretiveTypeError.toAlert;
                        if (creativeErrorFieldsMap) {
                            creativeFormInstance.methods.setFieldsError(creativeErrorFieldsMap);
                        }
                        alertError.push(...creativeAlertError);
                    }

                    // 处理素材报错
                    const segmentErrorToAlert = validateSegmentsError({
                        addAdgroupSegmentBindErrors: error.pictureSegmentBindErrors
                    });
                    const formattedVideoError = formatVideoError(error.videoSegmentBindErrors);
                    const videoErrorToAlert = formattedVideoError.toAlert;
                    alertError.push(...segmentErrorToAlert, ...videoErrorToAlert);

                    if (alertError.length) {
                        return [adgroupIndex, alertError];
                    }
                });
                const errorsToAlert = compact(errorsToAlert_);
                if (errorsToAlert.length) {
                    Dialog.alert({
                        title: '新建单元失败',
                        content: errorsToAlert.map(([adgroupIndex, alerts]) => (
                            <div key={adgroupIndex}>
                                <h3>单元{adgroupIndex + 1}</h3>
                                {alerts.map(err => <div key={err.code}>{err.message}</div>)}
                            </div>
                        ))
                    });
                }
            };
        };
        // 如果有未保存的关联产品，则弹出确认框
        const incompleteAdgroupInx = values.findIndex(
            item => !isEmpty(item.adgroupType.catalogId) && !get(item, 'adgroupType.catalogId[0].catalogId')
        );
        if (
            incompleteAdgroupInx > -1 && !hadAlertNotice && isSupportRecommendStructContentByUrlUser()
        ) {
            setHadAlertNotice(true);
            await promisifyConfirm({
                content: '系统自动生成的新关联产品您还未保存并提交审核，未保存的产品将无法成功关联。确认继续保存单元吗？',
                onOk: onSubmit,
                onCancel: () => {
                    changeAdgroupTab(adgroups[incompleteAdgroupInx].key);
                    adgroups[incompleteAdgroupInx].adgroupFormRef.current.scrollToField('catalogId');
                },
                okText: '确认',
                cancelText: '取消，去保存产品'
            });
        }
        else {
            onSubmit();
        }
    };

    const onCancel = async () => {
        if (await promisifyConfirm({content: '当前尚未保存任何单元，确定退出吗？'})) {
            goBack();
            logOnCancelClick();
        }
    };

    const preValidateAdgroupInfo = useCallback(
        adgroupInfo => {
            return new Promise(resolve => {
                if (isEmpty(adgroupInfo?.keywords)) {
                    resolve(adgroupInfo);
                }
                else {
                    preValidate(adgroupInfo.keywords, {campaignId}).then(
                        () => resolve(adgroupInfo),
                        keywords => resolve({...adgroupInfo, keywords})
                    );
                }
            });
        },
        [campaignId]
    );

    const acceptAdgroup = useCallback(
        (adgroupId, newAdgroupInfo) => {
            const adgroup = getItemByKey(adgroupId);
            if (adgroup?.adgroupFormRef?.current?.setFieldsValue && !isEmpty(newAdgroupInfo)) {
                preValidateAdgroupInfo(newAdgroupInfo).then(res => {
                    adgroup?.adgroupFormRef?.current?.setFieldsValue(res);
                });
            }
        },
        [getItemByKey, preValidateAdgroupInfo]
    );

    // 采纳1个单元时，如果正在编辑的单元有修改，则增加一个单元，否则覆盖当前单元设置
    const onEditAdgroup = useCallback(
        newAdgroupInfo => {
            if (adgroupsFormChangedStateMap.has(currentAdgroup)) {
                onBatchAddAdgroup([newAdgroupInfo]);
            }
            else {
                acceptAdgroup(currentAdgroup, newAdgroupInfo);
            }
        },
        [currentAdgroup, adgroupsFormChangedStateMap, acceptAdgroup, onBatchAddAdgroup]
    );

    const onBatchValidateAndAddAdgroup = useCallback(
        (adgroupItems = []) => {
            Promise.all(adgroupItems.map(preValidateAdgroupInfo)).then(onBatchAddAdgroup);
        },
        [onBatchAddAdgroup, preValidateAdgroupInfo]
    );

    const [adgroupsOsTypeMap, adgroupsOsTypeMapMethods] = useMap();
    const onWatchedFieldsChange = useCallback(
        (changedAdgroupKey, changedValues, changedFields) => {
            if (changedFields.includes('osType')) {
                adgroupsOsTypeMapMethods.set(
                    changedAdgroupKey, {...adgroupsOsTypeMap.get(changedAdgroupKey) || {}, ...changedValues}
                );
            }
            else {
                updateAdgroupByKey(changedAdgroupKey, changedValues);
            }
        },
        [updateAdgroupByKey, adgroupsOsTypeMapMethods, adgroupsOsTypeMap]
    );
    useEffect(() => {
        return () => clear();
    }, []);

    useRecordByComponent({tag: 'create_adgroup'});

    return (
        <Layout className="ad-main-new-adgroup-page" id="ad-main-new-adgroup-page">
            <Sidebar
                sticky
                style={botVisible ? {width: '80px'} : null}
            >
                <Steps campaignInfo={campaignInfo} currentAdgroup={currentAdgroup} />
            </Sidebar>
            <Layout className="ad-main-new-adgroup-page-container" id="ad-main-new-adgroup-page">
                <Header sticky>
                    {
                        campaignInfo.campaignName && (
                            <Title
                                label="新建推广单元"
                                className="campaign-name-title"
                                desc={`为『${campaignInfo.campaignName}』新建单元`}
                            />
                        )
                    }
                </Header>
                <Content onClick={logOnAreaClick}>
                    <CampaignInfoProvider value={campaignInfo}>
                        {isShowAdgroupRecommend(campaignInfo) ? (
                            <RecommendAdgroups
                                campaignInfo={campaignInfo}
                                hasNoMargin
                                hideEditBtn
                                onEditAdgroup={onEditAdgroup}
                                adgroupNum={adgroups.length}
                                onBatchAcceptAdgroup={onBatchValidateAndAddAdgroup}
                            />
                        ) : null}
                        <Tabs
                            className="ad-main-new-adgroup-page-tabs"
                            activeKey={currentAdgroup}
                            onChange={changeAdgroupTab}
                            onBeforeDelete={onDeleteAdgroup}
                            showAdd={adgroups.length < 15}
                            addButtonText="添加"
                            onAdd={onAddAdgroup}
                        >
                            {
                                adgroups.map((form, index) => {
                                    const {
                                        key,
                                        adgroupFormRef,
                                        adgroupTypeEmitter,
                                        adgroupVerificationData,
                                        programmaticCreativeInstance,
                                        customizeCreativeInstance,
                                        reactiveCreativeInfo,
                                        defaultInitialData
                                    } = form;
                                    return (
                                        <Tabs.TabPane
                                            key={key}
                                            closable={adgroups.length > 1}
                                            tab={
                                                <AdgroupTabTitle
                                                    index={index}
                                                    autoKey={key}
                                                    adgroupVerificationData={adgroupVerificationData}
                                                    reactiveCreativeInfo={reactiveCreativeInfo}
                                                    programmaticCreativeInstance={programmaticCreativeInstance}
                                                    customizeCreativeInstance={customizeCreativeInstance}
                                                    adgroupsMap={adgroupsMap}
                                                />
                                            }
                                        >
                                            <ProcessAdgroupBoundary renderError={renderError}>
                                                <InternalForm
                                                    autoKey={key}
                                                    ref={adgroupFormRef}
                                                    initialVerificationData={adgroupVerificationData}
                                                    onWatchedFieldsChange={partial(onWatchedFieldsChange, key)}
                                                    watchedFields={['mobileFinalUrl', 'osType']}
                                                    onFieldsChange={partial(set, key)}
                                                    formInitProps={formInitProps}
                                                    defaultInitialData={defaultInitialData}
                                                />
                                                <CreativesInAdgroup
                                                    canUseAIChat
                                                    isInAdgroupProcess
                                                    isDisabledAgentReg={isDisabledAgentReg}
                                                    isShowPreviewOnRightSection={isShowPreviewOnRightSection}
                                                    isShowPreviewOnMainSection={
                                                        isPreview && !isShowPreviewOnRightSection
                                                    }
                                                    osType={adgroupsOsTypeMap.get(key)?.osType}
                                                    adgroupKey={key}
                                                    currentAdgroupInfo={adgroupFormRef?.current?.formData}
                                                    campaignInfo={campaignInfo}
                                                    adgroupTypeEmitter={adgroupTypeEmitter}
                                                    programmaticCreativeInstance={programmaticCreativeInstance}
                                                    customizeCreativeInstance={customizeCreativeInstance}
                                                    reactiveCreativeInfo={reactiveCreativeInfo}
                                                    onCreativeDataChange={partial(updateAdgroupByKey, key)}
                                                    {...methodsForCreativeInstances}
                                                />
                                            </ProcessAdgroupBoundary>
                                        </Tabs.TabPane>
                                    );
                                })
                            }
                        </Tabs>
                    </CampaignInfoProvider>
                </Content>
                <Footer sticky className="ad-main-new-adgroup-page-footer">
                    <SaveFooter onSave={onSave} onCancel={onCancel} saveLabel="保存并关闭" />
                    <div className="ad-main-new-adgroup-page-footer-right">
                        {
                            isShowPreviewOnRightSection ? null : (
                                <Button
                                    type="text-strong"
                                    className="extra-content-button"
                                    onClick={() => setIsPreview(p => !p)}
                                    icon={isPreview ? <IconEdit /> : <IconEye />}
                                >
                                    {isPreview ? '返回填写' : '查看预览'}
                                </Button>
                            )
                        }
                        <AdgroupAndCreativeErrorsTip adgroups={adgroups} changeAdgroupTab={changeAdgroupTab} />
                    </div>
                </Footer>
            </Layout>
        </Layout>
    );
};

function AdgroupTabTitle({
    index,
    autoKey,
    adgroupVerificationData,
    programmaticCreativeInstance,
    customizeCreativeInstance,
    adgroupsMap
}) {
    const {creativeType, isShowCreativeForm} = adgroupsMap.get(autoKey) || {};
    const creativeTypeInstance = useMemo(
        () => (
            creativeType === creativeTypeEnum.programmatic ? programmaticCreativeInstance : customizeCreativeInstance
        ),
        [creativeType, programmaticCreativeInstance, customizeCreativeInstance]
    );
    const defaultReactiveData = useMemo(() => createReactiveData({}), []);
    const creativeErrors = useFormErrors(creativeTypeInstance?.verificationResults || defaultReactiveData);
    const adgroupErrors = useFormErrors(adgroupVerificationData);
    const updateErrorsState = useSetRecoilState(adgroupAndCreativeErrorsState);
    useEffect(() => {
        updateErrorsState(errors => ({...errors, [autoKey]: {
            creativeErrors: isShowCreativeForm ? creativeErrors : {},
            adgroupErrors
        }}));
    }, [creativeErrors, adgroupErrors, isShowCreativeForm]);
    return (
        <span>
            单元{index + 1}
            {
                (!isEmpty(adgroupErrors) || !isEmpty(creativeErrors))
                    ? <IconExclamationCircle className="error-tip" />
                    : null
            }
        </span>
    );
}

function Steps({campaignInfo, currentAdgroup}) {
    const {visible: botVisible} = useBotContextInfo();
    const [{steps}] = useProcessAdgroupResource(initialAdgroupForm, {...campaignInfo, v2: true});
    const {[currentAdgroup]: errorsMap = {}} = useRecoilValue(adgroupAndCreativeErrorsState);
    const {creativeErrors, adgroupErrors} = errorsMap;
    const {segmentBinds, ...creativeTextErrors} = creativeErrors || {};
    const stepConfig = {
        type: 'container',
        current: 1,
        dataSource: steps,
        className: 'ad-main-new-adgroup-page-steps',
        errorsMap: {
            ...adgroupErrors,
            segmentBinds$: segmentBinds,
            ...mapValues(
                groupBy(keys(creativeTextErrors), key => `${key.split('.')[2]}$`),
                keys => map(keys, key => creativeTextErrors[key]).flat()
            )
        }
    };
    const stepsProps = botVisible ? toPithySteps(stepConfig) : stepConfig;
    return <ProcessSteps {...stepsProps} />;
}

export default function () {
    const {campaignId} = useParams();
    return (
        <ProcessAdgroupBoundary renderError={renderError}>
            <RecoilRoot>
                <AdgroupPage campaignId={campaignId} />
            </RecoilRoot>
        </ProcessAdgroupBoundary>
    );
};


function createInitialAdgroupAndCreativeInstance(defaultInitialData = {}) {
    return {
        key: nanoid(),
        adgroupVerificationData: createReactiveData({}),
        defaultInitialData,
        adgroupTypeEmitter: new EventEmitter(),
        adgroupFormRef: createRef({}),
        reactiveCreativeInfo: createReactiveData({
            creativeType: creativeTypeEnum.programmatic,
            creativeTextOptimizationStatus: true,
            segmentRecommendStatus: true,
            videoAutoOptimizationStatus: false,
            isInAdgroupProcess: true,
            isShowCreativeForm: true
        }),
        programmaticCreativeInstance: {type: creativeTypeEnum.programmatic},
        customizeCreativeInstance: {type: creativeTypeEnum.customize}
    };
}
function initialAdgroupAndCreatives() {
    return [createInitialAdgroupAndCreativeInstance()];
}
function getAdgroupKey(data) {
    return data.key;
}
function useAdgroupAndCreativeForms() {
    const [
        adgroups,
        {
            push: addAdgroupFormInstance,
            removeByKey: deleteAdgroupFormInstance,
            updateByKey: updateAdgroupFormInstance,
            getItemByKey
        }
    ] = useKeyOrientedArray(initialAdgroupAndCreatives, {getKey: getAdgroupKey});
    const [currentAdgroup, changeAdgroupTab] = useState(adgroups[0].key);

    const onAddAdgroup = useCallback(
        () => {
            const adgroup = createInitialAdgroupAndCreativeInstance();
            addAdgroupFormInstance(adgroup);
            changeAdgroupTab(adgroup.key);
        },
        [addAdgroupFormInstance, changeAdgroupTab]
    );


    const onBatchAddAdgroup = useCallback(
        (initialFormItems = []) => {
            const adgroups = initialFormItems.map(createInitialAdgroupAndCreativeInstance);
            addAdgroupFormInstance(adgroups);
            if (adgroups.length > 0) {
                changeAdgroupTab(adgroups[adgroups.length - 1].key);
            }
        },
        [addAdgroupFormInstance, changeAdgroupTab]
    );

    const onDeleteAdgroup = useCallback(
        async key => {
            if (key === currentAdgroup) {
                let i = adgroups.findIndex(f => f.key === key);
                i = Math.max(0, i - 1);
                changeAdgroupTab(adgroups[i].key);
            }
            deleteAdgroupFormInstance(key);
            return true;
        },
        [currentAdgroup, adgroups, changeAdgroupTab, deleteAdgroupFormInstance]
    );

    const mountProgrammaticCreativeInstance = useCallback(
        (adgroupKey, instance) => {
            const {programmaticCreativeInstance} = getItemByKey(adgroupKey);
            updateAdgroupFormInstance(
                adgroupKey,
                {programmaticCreativeInstance: {...programmaticCreativeInstance, ...instance}}
            );
        },
        [updateAdgroupFormInstance, getItemByKey]
    );
    const mountCustomizeCreativeInstance = useCallback(
        (adgroupKey, instance) => {
            const {customizeCreativeInstance} = getItemByKey(adgroupKey);
            updateAdgroupFormInstance(
                adgroupKey,
                {customizeCreativeInstance: {...customizeCreativeInstance, ...instance}}
            );
        },
        [updateAdgroupFormInstance, getItemByKey]
    );

    const methodsForCreativeInstances = useMemo(
        () => ({mountProgrammaticCreativeInstance, mountCustomizeCreativeInstance}),
        [mountProgrammaticCreativeInstance, mountCustomizeCreativeInstance]
    );

    return [
        [adgroups, currentAdgroup],
        {onAddAdgroup, onDeleteAdgroup, changeAdgroupTab, methodsForCreativeInstances, getItemByKey, onBatchAddAdgroup}
    ];
}

const defaultAgroup = {};

function useAigcCreative({
    currentAdgroup, getItemByKey, changeAdgroupTab, operationId, pagePathType, ...campaignInfo
}) {
    const [adgroupsMap, {set, clear}] = useMap();
    const currentAdgroupInfo = adgroupsMap.get(currentAdgroup);

    const {creativeType = creativeTypeEnum.programmatic} = currentAdgroupInfo || {};

    const updateAdgroupByKey = useCallback((key, item) => {
        set(key, {...adgroupsMap.get(key) || {}, ...item});
    }, [set, adgroupsMap]);

    const getCreativeReactiveFormDataByKey = useCallback((adgroupKey, ideaType = creativeTypeEnum.customize) => {
        if (getItemByKey(adgroupKey)) {
            const {
                reactiveCreativeInfo, programmaticCreativeInstance, customizeCreativeInstance
            } = getItemByKey(adgroupKey);
            const creativeReactiveFormData = ideaType === creativeTypeEnum.programmatic
                ? programmaticCreativeInstance?.formData
                : customizeCreativeInstance?.formData;
            return {
                reactiveCreativeInfo,
                reactiveFormData: creativeReactiveFormData,
                customizeCreativeInstance,
                programmaticCreativeInstance
            };
        }
        return {};
    }, [getItemByKey]);


    const {
        programmaticCreativeInstance, customizeCreativeInstance
    } = getItemByKey(currentAdgroup) ?? defaultAgroup;
    const creativeReactiveFormData = creativeType === creativeTypeEnum.programmatic
        ? programmaticCreativeInstance?.formData
        : customizeCreativeInstance?.formData;
    // 添加数字人视频
    const {
        getDigitalHumanVideo,
        onAddDigitalHumanVideo
    } = useOpenDigitalHumanComposer(creativeReactiveFormData);

    const onAichatSubmit = useCallback(
        data => {
            const {creativeType: ideaType, tempAdgroupId} = data;
            const {
                reactiveCreativeInfo,
                reactiveFormData,
                customizeCreativeInstance
            } = getCreativeReactiveFormDataByKey(tempAdgroupId, ideaType);

            if (reactiveFormData && customizeCreativeInstance) {
                const newFormData = getNewProcessCreativeData({
                    submitData: data,
                    originData: reactiveFormData,
                    currentCreative: (customizeCreativeInstance?.formData.creativeTexts || [])[
                        customizeCreativeInstance?.formData.tabIndex
                    ]
                });

                const {segmentBinds} = newFormData;
                reactiveFormData.segmentBinds = segmentBinds;
                if (ideaType === creativeTypeEnum.customize) {
                    const {tabIndex, creativeTexts} = newFormData;
                    reactiveFormData.tabIndex = tabIndex;
                    reactiveFormData.creativeTexts = creativeTexts;
                }
                else {
                    const {programTitles, programDescs} = newFormData;
                    reactiveFormData.programTitles = programTitles;
                    reactiveFormData.programDescs = programDescs;
                }
                reactiveCreativeInfo.isShowCreativeForm = true;
                // 切换到应用的单元
                changeAdgroupTab(tempAdgroupId);
            }
        },
        [getCreativeReactiveFormDataByKey, changeAdgroupTab]
    );
    const adgroupListForAichatCreative = useMemo(
        () => {
            return [{
                tempAdgroupId: currentAdgroup,
                landingPageUrl: currentAdgroupInfo?.mobileFinalUrl,
                ...campaignInfo
            }];
        },
        [currentAdgroupInfo?.mobileFinalUrl, currentAdgroup, campaignInfo]
    );

    useInitAichatCreativeV2({
        creativeType, marketingTargetId: campaignInfo.marketingTargetId,
        getDigitalHumanVideo, onAichatSubmit, onAddDigitalHumanVideo,
        adgroupInfo: adgroupListForAichatCreative?.[0],
        adgroupsList: adgroupListForAichatCreative,
        isInAdgroupProcess: true, campaignList: [campaignInfo],
        operationId,
        pagePathType
    });

    useEffect(() => {
        return () => {
            clear();
        };
    }, []);

    return {updateAdgroupByKey, adgroupsMap};
}
