import {concat, isNil} from 'lodash-es';
import {Toast} from '@baidu/one-ui';
import {creativeTypeEnum} from 'commonLibs/config/creativeText';
import {createError} from 'commonLibs/utils/materialList/error';
import {getSingleErrorMessage} from 'commonLibs/utils/getErrorMessage';
import {isAutoCreativeUser} from 'commonLibs/utils/getFlag';
import {
    generateAdgroupSegmentBinds,
    generateAdgroupVideoBinds
} from 'app/containers/fcNew/creative/processNew/util';
import {remoteConfigLoaders as adgroupRemoteConfigLoaders} from '../config';
import {generateCreativeParams} from '../../creative/programmaticCreative/config';

export async function validateAllAdgroupsAndCreatives(adgroups) {
    const adgroupValidators = adgroups.map(
        (
            {adgroupFormRef, reactiveCreativeInfo, programmaticCreativeInstance, customizeCreativeInstance},
            index
        ) => {
            return async function () {
                if (adgroupFormRef.current.pending) {
                    Toast.error({content: '关联产品生成中，请生成后再保存'});
                    throw new Error('关联产品生成中，请生成后再保存');
                }
                const promises = [
                    adgroupFormRef.current.validateFields()
                ];
                const {
                    creativeType, isShowCreativeForm,
                    segmentRecommendStatus, creativeTextOptimizationStatus,
                    videoAutoOptimizationStatus
                } = reactiveCreativeInfo;
                const isProgrammatic = creativeType === creativeTypeEnum.programmatic;

                if (isShowCreativeForm) {
                    promises.push(
                        isProgrammatic
                            ? programmaticCreativeInstance.methods.validateFields()
                            : customizeCreativeInstance.methods.validateFields()
                    );
                }
                else {
                    promises.push(Promise.resolve());
                }

                const [adgroupResult, creativeResult] = await Promise.allSettled(promises);
                if (adgroupResult.status === 'fulfilled' && creativeResult.status === 'fulfilled') {
                    return {
                        adgroupType: adgroupResult.value,
                        creativeTypes: creativeResult.value,
                        isShowCreativeForm,
                        segmentRecommendStatus,
                        creativeTextOptimizationStatus,
                        videoAutoOptimizationStatus
                    };
                }
                throw new Error(`单元${index + 1}校验失败`);
            };
        });
    const result = await Promise.allSettled(adgroupValidators.map(validate => validate()));
    if (result.every(({status}) => status === 'fulfilled')) { // 所有单元+创意校验通过
        return result.map(({value}) => value);
    }
    throw new Error('单元和创意校验出错');
}

function getRecommendVideoData(videos = []) {
    const recommendVideos = videos.filter(video => video.isDigitalHumanVideo);
    return recommendVideos.map(({taskId, recommendId, addFrom}) => {
        return {
            taskId,
            recommendId,
            addFrom
        };
    });
}

function getRecommendImageData(segments = []) {
    return segments.reduce((accu, currentImage) => {
        if (currentImage.recommendId) {
            accu[currentImage.imageId] = currentImage.recommendId;
        }
        return accu;
    }, {});
}

function isValidRecommendId(id) {
    return !isNil(id);
}

export async function generateParams(campaignInfo, values) {
    const {marketingTargetId} = campaignInfo;
    const {generateAdgroupParams} = await adgroupRemoteConfigLoaders[marketingTargetId]();
    const items = values.map(({
        adgroupType: adgroupType_, creativeTypes: creativeTypes_,
        segmentRecommendStatus,
        creativeTextOptimizationStatus,
        videoAutoOptimizationStatus,
        isShowCreativeForm
    }) => {
        const {adgroupType, keywordTypes} = generateAdgroupParams(campaignInfo, adgroupType_);

        if (!isShowCreativeForm) {
            return {adgroupType, keywordTypes};
        }

        // 有创意菜加这俩字段
        adgroupType.segmentRecommendStatus = segmentRecommendStatus;
        adgroupType.creativeTextOptimizationStatus = creativeTextOptimizationStatus;
        if (isAutoCreativeUser()) {
            adgroupType.videoAutoOptimizationStatus = videoAutoOptimizationStatus;
        }

        const {creativeTexts, programTitles, programDescs, segmentBinds} = creativeTypes_;
        const {segments = [], videos = []} = segmentBinds;
        let creativeTypes = [];
        let recommendTextIds: number[] = [];
        if (creativeTexts) {
            creativeTypes = creativeTexts.map(data => generateCreativeParams(campaignInfo, data).creativeType);
            recommendTextIds = creativeTexts.map(data => data.recommendId).filter(isValidRecommendId);
        }
        else {
            creativeTypes = [generateCreativeParams(campaignInfo, {programTitles, programDescs}).creativeType];
            recommendTextIds
                = concat([], programTitles, programDescs).map(data => data.recommendId).filter(isValidRecommendId);
        }
        const pictureSegmentTypes = generateAdgroupSegmentBinds({bindInfo: {segments: segments}})
            .map(({segment}) => segment);
        const videoSegmentTypes = generateAdgroupVideoBinds(
            {bindInfo: {videos}},
            {isNeedJson: false}
        ).map(({segment}) => segment);
        return {
            adgroupType, keywordTypes, creativeTypes, pictureSegmentTypes,
            videoSegmentTypes, recommendVideos: getRecommendVideoData(videos),
            recommendImageMap: getRecommendImageData(segments),
            recommendTextIds
        };
    });
    return {items};
}

export function formatVideoError(videoSegmentBindErrors) {
    const videoErrors = videoSegmentBindErrors?.[0]?.errorInfos || videoSegmentBindErrors || [];
    const videoErrorMessagesToAlert = videoErrors.map(error => ({
        code: error.code,
        message: `视频：${getSingleErrorMessage(error)}`
    }));
    const formattedVideoError = createError({errors: videoSegmentBindErrors});
    formattedVideoError.toAlert = videoErrorMessagesToAlert;
    return formattedVideoError;
}
