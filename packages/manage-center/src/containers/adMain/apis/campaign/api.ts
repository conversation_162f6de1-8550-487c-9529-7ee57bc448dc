/*
 * @file: campaign api
 * @author: <EMAIL>
 * @Date: 2023-04-18 15:30:25
 */
import {request} from '@baidu/winds-ajax';
import dayjs from 'dayjs';
import {ReportTypeEnum} from 'commonLibs/config/report';
import {CPQL} from 'commonLibs/config/marketTarget';
import {normalizeTransAssets} from 'commonLibs/TransEditor/util';
import {material_token} from 'commonLibs/tableList/config/token';
import IdType from 'commonLibs/config/idType';
import {newBuildReportType} from 'app/containers/crowds/config/basicInfo';
import {CrowdStatusKey} from 'app/containers/fcNew/campaign/crowd/config';
import {union} from 'lodash-es';
import {getRelativeAsset} from 'commonLibs/utils/getAssetAndTransTypes';
import {CrowdType} from '../../campaign/crowd/config';
import {
    isProjectMainflowCrowdUser,
    isProjectMainflowScheduleUser,
    isProjectMainflowUser
} from 'commonLibs/utils/getFlag';
import {ProjectTypeEnum} from 'commonLibs/config/project';

const initialDateRange = () => ({
    startTime: dayjs().add(-7, 'day').format('YYYY-MM-DD'),
    endTime: dayjs().format('YYYY-MM-DD')
});

export async function fetchCampaignList(params) {
    return await request({
        path: 'thunder/GET/CampaignService/getCampaign',
        params
    });
};

// 计划可加入的项目列表
export async function fetchProjectList(params) {
    const {marketingTargetId, userId} = params;
    const payload = {
        token: material_token,
        reportType: ReportTypeEnum.project,
        idType: IdType.USER_LEVEL,
        ids: [userId],
        ...initialDateRange(),
        fields: [
            'projectName', 'projectId', 'productCategoryTypeStatus', 'promotionScene', 'sharedBudgetId', 'bidType',
            'projectModeType', 'sharedBudget', 'marketingTargetId', 'subMarketingTargetId', 'productCategoryType',
            'ocpcBidType', 'ocpcBid', 'transTypes', 'ocpcDeepBid', 'assistTransTypes', 'transAsset', 'assetType',
            'transManagerMode', 'transAssetInfo', 'cvSources', 'liftBudgetStatus', 'ocpcDeepBid', 'targetRoiRatio',
            'deepTransTypeMode', 'assistTransTypes', 'deepTransTypeStatus', 'projectApplicationType',
            'transTypeMultiBidStatus', 'transTypesOcpcBidRatio', 'projectType',
            ...(isProjectMainflowUser() ? ['ocpcBidRatioType'] : []),
            ...(isProjectMainflowCrowdUser() ? ['crowdQualityOcpcBidRatio'] : []),
            ...(isProjectMainflowScheduleUser() ? ['cycOcpcBidRatio', 'ageOcpcBidRatio', 'regionOcpcBidRatio'] : [])
        ],
        fieldFilters: []
    };
    if (marketingTargetId) {
        payload.fieldFilters.push({
            field: 'marketingTargetId',
            op: 'in',
            values: [marketingTargetId]
        });
    }
    const {rows: projectList} = await request({
        path: 'puppet/GET/MaterialQueryFunction/getMaterialProjectList',
        params: payload
    });
    return projectList.filter(i => i.projectType !== ProjectTypeEnum.LITE);
};

export async function getBackendError(params) {
    return await request({
        path: 'thunder/GET/NameService/getNameValidation',
        params
    });
};

export async function getAuthInfoFromBCP(params) {
    return await request({
        path: 'rose/GET/FcTransTraceService/getAuthInfoFromBCP',
        params
    });
};

export interface CVsourceAndTransTypeInfo {
    forSelect: object;
    availableTransTypes: Array<string | number>;
    availableCvSources: Array<string | number>;
    eligibleInfo: object;
    suggestTransTypes: Array<string | number>;
    normalizedTransAssets?: {
        assetsData?: Record<string, {assetType: unknown}>;
    };
    mtAvailableTransTypes: object;
    assets?: Array<string | number>;
};

export async function fetchUserTransTypeList({
    marketingTargetId,
    ignored = false
}: {marketingTargetId: number, ignored?: boolean}): Promise<Partial<CVsourceAndTransTypeInfo>> {
    if (ignored) {
        return {};
    }
    const params = {
        path: 'rose/GET/FcOcpcService/getCVsourceAndTranstypeByUser',
        params: {fields: ['forSelect', 'suggest', 'assetTypes', 'leadsTransTypes']},
        allowCache: true
    };
    const {forSelect, suggest = {}, assetTypes = [], leadsTransTypes = []} = await request(params);
    const availableTransTypes = forSelect.reduce((cur, item) => union(cur, item.transTypes), []);
    const availableCvSources = forSelect
        .filter(item => item.transTypes.length)
        .reduce((cur, item) => ([...cur, item.cvSource]), []);
    const eligibleInfo = forSelect.reduce((cur, item) => ({...cur, [item.cvSource]: item.transTypes}), {});
    const suggestTransTypes = suggest.transTypes || [];
    // forSelect待自动出价推全后相关逻辑可去掉，此处添加forSelect是因为在组件中需要筛选数据源（自动定向筛3、4、6，自动出价不筛数据源）
    const result: CVsourceAndTransTypeInfo = {
        forSelect,
        availableTransTypes,
        availableCvSources,
        eligibleInfo,
        suggestTransTypes,
        // 这个字段后续会在接口里面新增一个字段来返回，后端还没写好
        mtAvailableTransTypes: {
            [CPQL]: leadsTransTypes
        }
    };
    if (assetTypes) {
        result.normalizedTransAssets = normalizeTransAssets(assetTypes);
    }
    if (marketingTargetId != null) {
        result.assets = getRelativeAsset({
            normalizedTransAssets: result.normalizedTransAssets,
            marketingTargetId,
            mtAvailableTransTypes: result.mtAvailableTransTypes
        });
    }
    return result;
};

const crowdFields = [
    'crowdId',
    'crowdName',
    'sex',
    'age',
    'inPeople',
    'searchTag',
    'browseTag',
    'idPack',
    'idPackTag',
    'idPackType',
    'crowdDirectType',
    'deviceProperty',
    'effectType',
    'audienceStatus'
];

const filteCrowdStatus = [CrowdStatusKey.invalid, CrowdStatusKey.fail];

export async function fetchCrowdList({
    userId, corwdType, search
}) {
    const params = {
        token: material_token,
        userId,
        reportType: newBuildReportType,
        idType: IdType.USER_LEVEL,
        ids: [userId],
        startTime: initialDateRange().startTime,
        endTime: initialDateRange().endTime,
        fields: crowdFields,
        type: 'search',
        fieldFilters: [
            {
                field: 'crowdDirectType',
                op: 'in',
                values: [0, 1, 2, 3, 4, 5, 8, 9, 10]
            },
            {
                field: 'effectType',
                op: 'in',
                values: corwdType === CrowdType.ORIENT_CROWD
                    ? [0, 2]
                    : [1]
            },
            ...(
                search ? [{
                    field: 'crowdName',
                    op: 'like',
                    values: [search]
                }] : []
            )
        ]
    };

    const result = await request({
        path: 'puppet/GET/MaterialQueryFunction/getMaterialCrowdList',
        params
    });
    const crowdIdList = [];
    const crowdMap = (result?.rows || []).reduce((crowdMap, crowd) => {
        crowd.key = crowd.crowdId || -crowd.crowdDirectType;
        if (!filteCrowdStatus.includes(crowd.audienceStatus)) {
            crowdMap[crowd.key] = crowd;
            crowdIdList.push(crowd.key);
        }
        return crowdMap;
    }, {});
    return {
        map: crowdMap,
        list: crowdIdList
    };
}