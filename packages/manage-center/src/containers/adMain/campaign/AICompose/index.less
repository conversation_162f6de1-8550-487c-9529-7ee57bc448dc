.ad-main-new-campaign-page-container.ai-campaign-edit-page {
    margin: 0 @dls-padding-unit * 6;
    .market-target-area {
        margin-bottom: @dls-padding-unit * 5;
    }

    .ad-main-campaign-form-container.ai-campaign-form {
        form {
            > div {
                &:not(:first-child) {
                    margin-top: @dls-padding-unit * 5;
                }
            }
        }

        .manage-editor-campaign-new-budget-container {
            .budget-radio-tip {
                margin-top: @dls-padding-unit;
                margin-top: 5px;
                margin-left: 0;
                max-width: 500px;
            }
            .budget-radio-group {
                display: block;
            }
        }

        .use-rf-preset-form-ui .rf-form-item .rf-form-item-label {
            flex-shrink: 0;
        }

        .common-select-card-container {
            .common-select-card.selected.disabled {
                border: 1px solid @dls-color-brand-7;
                background-color: @dls-background-color-current-disabled;

                .common-select-card-name {
                    color: @dls-color-gray-9;
                }

                .common-select-card-tip {
                    color: @dls-color-gray-7;
                }
            }
        }
    }
}
