import {useState, useMemo} from 'react';
import {useQuickForm, createReactiveData} from '@baidu/react-formulator';
import {useRecordByComponent} from 'commonLibs/hooks/record';
import {useFormulatorLogMethods} from 'commonLibs/hooks/funnelLog';
import SuspenseBoundary, {CacheProvider} from 'commonLibs/suspenseBoundary';
import {CVsourceAndTransTypeInfo} from '../../apis/campaign/api';
import DisplaySelectedMarketTargetId from '../marketingTarget/DisplaySelected';
import {getAICampaignFormConfig, getAICampaignFormData} from '../config/AI';
import {CampaignInfoProvider} from '../context';
import {useFormInitiation} from '../hooks';
import {initialCampaignForm} from '../config';
import Save from './save';
import '../style.less';
import './index.less';

interface CampaignProps extends Partial<Pick<CVsourceAndTransTypeInfo, keyof CVsourceAndTransTypeInfo>> {
    marketingTargetId: number;
    verificationData: any;
};

interface CampaignNewProcessProps {
    // ai 预生成的计划对象（注：非完整 campaign对象，仅包含用户可输入的字段）
    campaignInfo: Record<string, any>;
    onAIProcessSave: (campaignInfo: Record<string, any>) => void;
    onAIProcessCancel: () => void;
}

type CampaignFormProps = CampaignProps & CampaignNewProcessProps;

const CampaignForm = ({
    marketingTargetId,
    verificationData,
    campaignInfo,
    ...restProps
}: CampaignFormProps) => {
    const [Form, {getFieldError}] = useQuickForm();
    const [{
        assets,
        suggestTransTypes,
        normalizedTransAssets,
        eligibleInfo,
        formConfig,
        initialData
    }] = useFormInitiation({marketingTargetId, initialFormFunc: initialCampaignForm});

    const aiCampaignFormConfig = useMemo(() => {
        return getAICampaignFormConfig(formConfig);
    }, [formConfig]);

    const [reactiveData] = useState(() => {
        const formData = getAICampaignFormData({
            campaignInfo: initialData,
            aiCompositionCampaign: campaignInfo
        });
        return createReactiveData(formData);
    });

    const {
        logOnSubmitClick,
        logOnSubmitSuccess,
        logOnCancelClick
    } = useFormulatorLogMethods({
        formData: reactiveData,
        source: 'aiCompositionProcessCampaignForm',
        shouldSendChangeLog: true,
        config: {
            commonTrackParams: {
                marketingtargetid: marketingTargetId
            }
        },
        getFieldError
    });

    return (
        <div className="ad-main-campaign-form-container ai-campaign-form">
            <CampaignInfoProvider value={{marketingTargetId}}>
                <Form
                    config={aiCampaignFormConfig}
                    data={reactiveData}
                    className="use-rf-preset-form-ui use-vertical"
                    verificationData={verificationData}
                >
                    <Save
                        marketingTargetId={marketingTargetId}
                        normalizedTransAssets={normalizedTransAssets}
                        eligibleInfo={eligibleInfo}
                        suggestTransTypes={suggestTransTypes}
                        assets={assets}
                        logOnSubmitClick={logOnSubmitClick}
                        logOnSubmitSuccess={logOnSubmitSuccess}
                        logOnCancelClick={logOnCancelClick}
                        {...restProps}
                    />
                </Form>
            </CampaignInfoProvider>
        </div>
    );
};

const AIComposeCampaignProcess = ({
    campaignInfo,
    ...restProps
}: CampaignNewProcessProps) => {
    const marketingTargetId = campaignInfo.marketingTargetId;
    useRecordByComponent({tag: 'create_campaign'});

    const [verificationData] = useState(() => createReactiveData({}));
    return (
        <div className="ad-main-new-campaign-page">
            <div className="ad-main-new-campaign-page-container ai-campaign-edit-page" id="ad-main-new-campaign-page">
                <DisplaySelectedMarketTargetId
                    value={marketingTargetId}
                />
                <SuspenseBoundary loading={{tip: '正在初始化配置'}}>
                    <CampaignForm
                        key={marketingTargetId}
                        marketingTargetId={marketingTargetId}
                        verificationData={verificationData}
                        campaignInfo={campaignInfo}
                        {...restProps}
                    />
                </SuspenseBoundary>
            </div>
        </div>
    );
};

export default function (props: CampaignNewProcessProps) {
    return (
        <CacheProvider>
            <SuspenseBoundary loading={{tip: ''}}>
                <AIComposeCampaignProcess {...props} />
            </SuspenseBoundary>
        </CacheProvider>
    );
};
