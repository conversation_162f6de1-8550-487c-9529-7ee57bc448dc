import SaveFooter from 'commonLibs/SaveFooter';
import {Boundary, useResource} from 'commonLibs/suspenseBoundary';
import {useFormContext} from '@baidu/react-formulator';
import {Loading} from '@baidu/one-ui';
import {createError, displayErrorAsToast} from 'commonLibs/utils/materialList/error';
import {remoteConfigLoaders} from '../config';
import {defaultGenerateCampaignParams, submitWithCvSourceConfirm} from '../save';
import {prepareParams} from './utils';
import {fetchUserTransTypeList} from '../../apis/campaign/api';

function Save(props) {
    const {
        marketingTargetId,
        logOnSubmitClick,
        logOnSubmitSuccess,
        logOnCancelClick,
        onAIProcessSave,
        onAIProcessCancel
    } = props;
    const {validateFields, formData} = useFormContext();
    const [
        {normalizedTransAssets, suggestTransTypes, eligibleInfo, assets}
    ] = useResource(fetchUserTransTypeList, {marketingTargetId});

    const handleSubmit = async (deletedCvSources = []) => {
        logOnSubmitClick();
        const values = await validateFields();

        try {
            const {
                generateCampaignParams = defaultGenerateCampaignParams
            } = await remoteConfigLoaders[marketingTargetId]();
            const params = generateCampaignParams({
                values,
                normalizedTransAssets,
                assets,
                marketingTargetId,
                suggestTransTypes,
                deletedCvSources,
                eligibleInfo
            });

            await onAIProcessSave(prepareParams(params, values));
            logOnSubmitSuccess();
        } catch (error) {
            const err = createError(error);
            err.optName = '编辑';
            displayErrorAsToast(err);
        }
    };

    const handleCancel = () => {
        logOnCancelClick();
        onAIProcessCancel();
    };

    const onSave = async () => {
        return await submitWithCvSourceConfirm({formData, handleSubmit, eligibleInfo});
    };

    const saveFooterProps = {
        onSave,
        onCancel: handleCancel,
        isNewStyle: true,
        saveType: 'primary',
        cancelType: 'normal',
        size: 'medium',
        saveLabel: '确定'
    };
    return (
        <div className="ad-main-campaign-footer">
            <SaveFooter {...saveFooterProps} />
        </div>
    );
};

export default function (props) {
    return (
        <div className="ad-main-campaign-footer">
            <Boundary pendingFallback={<Loading />}>
                <Save {...props} />
            </Boundary>
        </div>
    );
}