/**
 * @file: 工具函数
 * @author: liuye11(<EMAIL>)
 * @Last Modified by: liuye11
 * @Last Modified time: 2023-06-28 20:09:32
 */

export function prepareParams(params: Record<string, any>, values: Record<string, any>) {
    const campaignType = params.campaignType;
    const {schedule, schedulePriceFactors} = campaignType;

    return {
        ...campaignType,
        optimizationTarget: values.optimizeTransTarget,
        ...(schedule?.length ? {schedule: JSON.stringify(schedule)} : {}),
        ...(schedulePriceFactors ? {schedulePriceFactors: JSON.stringify(schedulePriceFactors)} : {})
    };
}
