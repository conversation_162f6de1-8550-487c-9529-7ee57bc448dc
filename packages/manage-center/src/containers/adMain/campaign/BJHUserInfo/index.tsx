/**
 * @file 原生互动营销目标-BJH推广身份
 * <AUTHOR>
 * @date 2025-02-17
*/


import {Loading, Select} from '@baidu/one-ui';
import {Boundary, useResource, CacheProvider} from 'commonLibs/magicSuspenseBoundary';
import {fetchBjhUserInfoList} from 'app/api/bjh';
import './style.less';

const SelectOptionComp = ({info}) => {
    const {bjhUserName, bjhUserLogo, bjhUserId} = info;
    return (
        <span className="manage-bjh-userinfo-select-option-left">
            <img className="bjh-select-avatar" src={bjhUserLogo} />
            <div className="bjh-select-info">
                <div className="bjh-name">
                    <span className="ml-1">{bjhUserName}</span>
                </div>
                <div className="bjh-id">
                    <span className="ml-1"> ID：{bjhUserId}</span>
                </div>
            </div>
        </span>
    );
};

const selectFilter = (input: string, option: any) => {
    return String(option.props.label).toLowerCase().includes(input.toLowerCase());
};

function BJHUserInfo(props: {value: number, onChange: (info: any) => void}) {
    const {
        value,
        onChange
    } = props;

    const [bjhUserInfoList] = useResource(fetchBjhUserInfoList, {});

    const bjhSelectCustomRenderTarget = () => {
        const {bjhUserLogo, bjhUserName, bjhUserId} = value || {};
        return (
            <span className="manage-bjh-userinfo-select-option-left">
                <img className="bjh-select-avatar" src={bjhUserLogo} />
                <div className="bjh-select-info">
                    <div className="bjh-name">
                        <span className="ml-1">{bjhUserName}</span>
                    </div>
                    <div className="bjh-id">
                        <span className="ml-1"> ID：{bjhUserId}</span>
                    </div>
                </div>
            </span>
        );
    };

    const handleSelectChange = value => {
        const bjhUserInfo = bjhUserInfoList.find(item => item.bjhUserId === value);
        onChange(bjhUserInfo);
    };

    const selectProps = {
        className: 'manage-bjh-userinfo-select',
        value: value?.bjhUserId,
        onChange: handleSelectChange,
        customRenderTarget: bjhSelectCustomRenderTarget,
        showSearch: true,
        placeholder: '请选择推广身份',
        loading: false,
        filterOption: selectFilter
    };

    return (
        <div>
            <Select {...selectProps}>
                {
                    bjhUserInfoList.map(item => {
                        const {bjhUserId, bjhUserName} = item;
                        return (
                            <Select.Option
                                style={{height: 54}}
                                className="manage-bjh-userinfo-select-option"
                                key={bjhUserId}
                                value={bjhUserId}
                                label={bjhUserName}
                            >
                                <SelectOptionComp info={item} />
                            </Select.Option>
                        );
                    })
                }
            </Select>
        </div>
    );
};

export default function BJHUserInfoWithBoundary(props) {
    return (
        <Boundary
            pendingFallback={<Loading />}
        >
            <CacheProvider>
                <BJHUserInfo {...props} />
            </CacheProvider>
        </Boundary>
    );
}

