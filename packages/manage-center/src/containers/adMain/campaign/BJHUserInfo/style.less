.manage-bjh-userinfo-select-wrapper {
    .manage-bjh-userinfo-select-empty {
        margin-top: @dls-padding-unit;
        .dls-icon {
            color: #f27318;
            margin-right: @dls-padding-unit;
        }
    }
}

.manage-bjh-userinfo-init-select-option {
    line-height: 54px !important;
}

.manage-bjh-userinfo-select-option-left {
    gap: @dls-padding-unit;
    height: 54px;
    line-height: 54px;
    display: flex;
    align-items: center;

    .bjh-select-avatar {
        width: 32px;
        height: 32px;
        border: 1px solid rgba(102, 146, 222, 0.15);
        border-radius: 70px;
    }

    .bjh-select-info {
        .bjh-name {
            display: flex;
            align-items: center;
            height: 20px;
            line-height: 20px;
            font-size: 14px;
            gap: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .bjh-id {
            display: flex;
            align-items: center;
            height: 16px;
            font-size: 12px;
            line-height: 16px;
        }
    }

    .dls-icon {
        color: @dls-color-brand-7;
    }
}

.manage-bjh-userinfo-select {
    height: 56px;

    /* stylelint-disable-next-line selector-class-pattern */
    .one-select-selection .one-select-selection__rendered {
        line-height: 56px !important;
    }

    /* stylelint-disable-next-line selector-class-pattern */
    .one-select-selection-selected-value, .manage-bjh-userinfo-select-option {
        display: flex;
        height: 54px;

        .manage-bjh-userinfo-select-option-left {
            display: inline-flex;
            align-items: center;
        }
    }
}
