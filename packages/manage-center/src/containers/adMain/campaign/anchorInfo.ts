import {AnchorDataResponse} from 'app/api/adgroup/anchor';

class AnchorInfo {
    private rows: AnchorDataResponse['rows'];

    constructor() {
        this.rows = [];
    }
    clearInfo() {
        this.rows = [];
    }
    setInfo({
        rows
    }: {rows: any[]}) {
        this.rows = rows;
    }
    getInfo() {
        return {
            rows: this.rows
        };
    }
}

export const globalAnchorInfo = new AnchorInfo();

