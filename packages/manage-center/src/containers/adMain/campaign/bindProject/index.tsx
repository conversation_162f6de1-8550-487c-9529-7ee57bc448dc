import {useEffect, useState} from 'react';
import {useRequest} from '@huse/request';
import {IconBulbSolid} from 'dls-icons-react';
import {isArray, isString} from 'lodash-es';
import {OneLink} from 'commonLibs/SimpleLink';
import {CPQL, APP, STORE, NATIVE, isLiveSubMarket} from 'commonLibs/config/marketTarget';
import {useRouterFormatter} from 'commonLibs/route';
import manageCenter from 'app/routes';
import {Select, Popover, Button, Alert} from '@baidu/one-ui';
import {useKeyOrientedArray} from 'commonLibs/hooks/collection/array';
import useUserInfo from 'commonLibs/hooks/useUserInfo';
import {fetchProjectList} from 'app/containers/adMain/apis/campaign/api';
import {liftBudgetStatusConfig} from 'commonLibs/config/ocpc';
import ProjectDetail from 'app/containers/adMain/campaign/projectDetail';
import {useControl} from 'commonLibs/hooks/externalControl';
import './style.less';
import {isInQinggeIframe} from 'commonLibs/utils/isInIframe';
import {globalAnchorInfo} from '../anchorInfo';

interface ProjectValue {
    projectId?: number;
    projectInfo?: unknown;
}

interface Props {
    value: ProjectValue;
    onChange: (value: ProjectValue) => void;
    marketingTargetId: unknown;
}

const getKey = row => row.projectId;
const EmptyRows = [];

interface DisabledContentProps {
    isLiftBudgetOpen: boolean;
}

const getDisabledContent = ({isLiftBudgetOpen}: DisabledContentProps) => {
    const content = [];
    if (isLiftBudgetOpen) {
        content.push(
            <div>
                <span>不能选择正在起量中的项目</span>
            </div>
        );
    }
    return content;
};

const NEW_GAME_PROJECT_APPLICATION_TYPE = 2;

// 考虑到select内部已经在使用react渲染children，需要获取文字搜索
const getTextDefs = node => {
    let children = node.props.children;
    if (!children) {
        return '';
    }
    if (isString(children)) {
        return children;
    }
    children = isArray(children) ? children : [children];
    for (const child of children) {
        const childText = getTextDefs(child);
        if (childText) {
            return childText;
        }
    }
    return '';
};

export const JumpToProject = () => {
    const fetchProjectListUrl = useRouterFormatter({module: manageCenter, name: 'projects'})();
    const projectListUrl = isInQinggeIframe
        ? 'https://qingge.baidu.com/ad/manageCenter/projectList'
        : fetchProjectListUrl;
    return (
        <OneLink
            toUrl={projectListUrl}
            className="link-btn"
            name="找不到？新建项目"
            target="_blank"
            type="strong"
            size="medium"
        />
    );
};

export const bottomTipMap = {
    [CPQL]: '当前计划已加入项目，关联产品、出价、项目预算相关设置遵循项目',
    [STORE]: '当前计划已加入项目，出价、项目预算相关设置遵循项目',
    [APP]: '当前计划已加入项目，出价、项目预算相关设置遵循项目',
    [NATIVE]: '当前计划已加入项目，出价、项目预算相关设置遵循项目'
};

export function BindProjectTip({marketingTargetId}) {
    return (
        <div className='ad-main-campaign-bottom-tip'>
            <IconBulbSolid />{bottomTipMap[marketingTargetId]}
        </div>
    );
}

export default (props: Props) => {
    const {
        marketingTargetId,
        value,
        onChange
    } = props;
    const [projectDetail, setProjectDetail] = useState({});
    const {userId} = useUserInfo();
    const [ControlledProjectDetail, {open}] = useControl(ProjectDetail);
    const {
        data: rawRows = EmptyRows,
        pending
    } = useRequest(fetchProjectList, {marketingTargetId, userId});
    const [rows, {
        set,
        getItemByKey
    }] = useKeyOrientedArray(rawRows, {getKey});
    useEffect(() => {
        // fixme: 最好在api请求时就过滤，本次因为排期问题，接口没有支持按subMarketingTargetId过滤
        const anchorInfo = globalAnchorInfo.getInfo();
        if (isInQinggeIframe && marketingTargetId === CPQL && !!anchorInfo?.rows?.length) {
            set(rawRows);
        }
        else {
            set(rawRows.filter(row => !isLiveSubMarket(row.subMarketingTargetId)));
        }
    }, [marketingTargetId, rawRows]);

    useEffect(() => {
        if (rows.length && value.projectId) {
            onChange({
                projectId: value.projectId,
                projectInfo: getItemByKey(value.projectId)
            });
        }
    }, [rows, value.projectId]);

    const selectProps = {
        loading: pending,
        value: value?.projectId,
        allowClear: true,
        showSearch: true,
        filterOption: (input, option) => {
            const content = getTextDefs(option.props.children);
            return content.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        onChange: value => {
            onChange({
                projectId: value,
                projectInfo: getItemByKey(value)
            });
        },
        optionLabelProp: 'label',
        width: 300
    };

    const onClickDetail = (e, row) => {
        e.stopPropagation();
        setProjectDetail(row);
        open();
    };

    return (
        <>
            <div className='project-select'>
                <Select {...selectProps}>
                    {rows.map(row => {
                        const isLiftBudgetOpen = row.liftBudgetStatus === liftBudgetStatusConfig.open;
                        const disabled = isLiftBudgetOpen;
                        const disabledContent = getDisabledContent({isLiftBudgetOpen});
                        return (
                            <Select.Option
                                key={row.projectId}
                                value={row.projectId}
                                disabled={disabled}
                                label={
                                    <div className='project-text-container'>
                                        <span className='project-text-ellipsis'>{row.projectName}</span>
                                        {
                                            row?.projectApplicationType === NEW_GAME_PROJECT_APPLICATION_TYPE
                                                ? <span className='project-new-game'>新游首发</span>
                                                : null
                                        }
                                    </div>
                                }
                            >
                                <Popover
                                    content={disabled ? disabledContent : null}
                                >
                                    <div className='project-text'>
                                        <span className='project-text-ellipsis'>{row.projectName}</span>
                                        <Button type='text-strong' onClick={e => onClickDetail(e, row)}>详情</Button>
                                    </div>
                                </Popover>
                            </Select.Option>
                        );
                    })}
                </Select>
                <JumpToProject />
            </div>
            {
                value.projectInfo?.projectApplicationType === NEW_GAME_PROJECT_APPLICATION_TYPE && (
                    <Alert
                        type='info'
                        showIcon
                        content={'已加入“享有新游首发权益”的智投项目，退出项目或将项目删除后，计划、单元、创意将不再享有新游首发权益。'}
                        className='project-new-game-tip'
                    />
                )
            }
            <ControlledProjectDetail projectDetail={projectDetail} />
        </>
    );
};

