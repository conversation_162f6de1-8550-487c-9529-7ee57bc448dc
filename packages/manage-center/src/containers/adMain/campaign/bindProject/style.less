.project-text {
    display: flex;
    justify-content: space-between;
    &-ellipsis {
        width: @dls-padding-unit * 50;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
.project-text-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .project-new-game {
        padding: @dls-padding-unit * 0.5 @dls-padding-unit * 2;
        border-radius: @dls-border-radius-1;
        background: #fff8f2;
        color: #f27318;
        font-size: @dls-padding-unit * 3;
        line-height: @dls-padding-unit * 4;
    }
}

.project-select {
    .link-btn {
        margin-left: @dls-padding-unit * 3;
        font-size: @dls-padding-unit * 3;
        line-height: @dls-padding-unit * 4;
    }
}

.project-new-game-tip {
    margin-top: @dls-padding-unit * 4;
}
