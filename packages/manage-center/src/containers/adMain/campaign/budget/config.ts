/*
 * @file: budget config
 * @author: lian<PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2020-09-23 17:07:12
 */
export const budgetTypeConfig = {
    NO_LIMIT: 'noLimitBudget',
    DAILY_BUDGET: 'dailyBudget',
    SHARE_BUDGET: 'sharedBudget'
};

export const budgetTypeList = [budgetTypeConfig.NO_LIMIT, budgetTypeConfig.DAILY_BUDGET];

export const getBudgetTypeList = ({hasSharedBudget}: {hasSharedBudget: boolean}) => {
    if (hasSharedBudget) {
        return [...budgetTypeList, budgetTypeConfig.SHARE_BUDGET];
    }
    return budgetTypeList;
};

export const budgetTypeText = {
    [budgetTypeConfig.NO_LIMIT]: '不限',
    [budgetTypeConfig.DAILY_BUDGET]: '自定义',
    [budgetTypeConfig.SHARE_BUDGET]: '项目预算'
};

export const rangeBid = {
    min: 50,
    max: 10000000
};

// const validateBudgetAndBidFields = [
//     'campaignOcpcBid',
//     'campaignBudget'
// ];
// 出价与预算之间存在相互校验(基本逻辑：出价不得高于预算，预算不得低于出价)
// 目标转化出价计划校验【出价不高于预算】【预算不低于出价】
// export const validateFormBudgetAndBid = ({
//     validateFields,
//     budget,
//     budgetType,
//     campaignOcpcBid
// }) => {
//     if (
//         campaignOcpcBid
//     ) {
//         validateFields(validateBudgetAndBidFields);
//     }
// };