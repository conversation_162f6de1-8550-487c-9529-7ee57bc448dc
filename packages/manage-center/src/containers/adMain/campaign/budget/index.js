/*
 * @file: schedule
 * @author: lian<PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2020-09-23 16:54:02
 */

import {Radio} from '@baidu/one-ui';
import PropTypes from 'prop-types';
import Tip from 'commonLibs/Tips';
import sendMonitor from 'commonLibs/utils/sendHm';
import {isCVMaxCampaignBudgetUser} from 'commonLibs/utils/getFlag';
import {campaignOcpcBidTypeConfig} from 'commonLibs/config/ocpc';
import {ConfidenceKeyConfig} from 'commonLibs/components/customBudget/common/confidenceContent';
import CustomBudget from 'commonLibs/components/customBudget';
import {estimatedBudgetTypeConfig, satisfyShooTableLength} from 'commonLibs/components/customBudget/config';
import {getBudgetTypeList, budgetTypeText, budgetTypeConfig, rangeBid} from './config';
import './style.less';
import {UINumberInput} from '@baidu/react-formulator';
import {getTransTypeFromOptimize} from 'commonLibs/config/optimizeTransTarget';


const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;

const CampaignBudget = props => {
    const {
        value,
        onChange,
        campaignOcpcBid: campaignOcpcBidValue,
        campaignTransTypes = [],
        optimizeTransTarget,
        className,
        campaignOcpcBidType,
        bindProject
    } = props;
    const transTypeFromOptimize = getTransTypeFromOptimize(optimizeTransTarget);
    const {budgetType, budget, sharedBudgetId, sharedBudget} = value;
    const onBudgetTypeChange = e => {
        const budgetType = e.target.value;
        const newValue = {
            budgetType,
            budget,
            sharedBudgetId,
            sharedBudget
        };
        onChange(newValue);
        sendMonitor('click', {level: 'campaign', item: 'new_campaign_budget_type_change'});
    };
    const onHandleBudgetChange = v => {
        const newValue = {
            budgetType,
            budget: v,
            sharedBudgetId
        };
        onChange(newValue);
        sendMonitor('click',
            {level: 'campaign', item: 'new_campaign_budget_value_change', params: v});
    };

    const tipText = campaignOcpcBidValue != null
        ? <span style={{color: '#848b99'}}>为了保证您的转化效果，建议您每日预算至少≥{Math.max(500, +campaignOcpcBidValue * 3)}元</span>
        : <span style={{color: '#848b99'}}>为了保证您的推广效果，每日预算需≥50元</span>;

    const numberInputProps = {
        className: `budget-number-box ${className}`,
        placeholder: '',
        step: 1,
        suffix: '元',
        tipText,
        showErrorMessage: true,
        type: 'float',
        fixed: 2,
        value: budget,
        onChange: onHandleBudgetChange,
        ...rangeBid
    };

    const onHandleBudgetAndCampaignOcpcBidChange = ({budget, transPrice}) => {
        const newValue = {
            budgetType,
            budget,
            transPrice
        };
        onChange(newValue);
    };

    const customBudgetTableProps = {
        onChange: onHandleBudgetAndCampaignOcpcBidChange,
        value: budget,
        materialInfo: {
            transTypes: transTypeFromOptimize ?? campaignTransTypes
        },
        estimatedBudgetType: estimatedBudgetTypeConfig.campaign,
        getIsShowCustomBudgetTable: ({adviceEffect = [], confidence}) => {
            return (
                (
                    confidence === ConfidenceKeyConfig.account
                    || confidence === ConfidenceKeyConfig.subject
                )
                && adviceEffect.length >= satisfyShooTableLength
                && campaignOcpcBidType === campaignOcpcBidTypeConfig.cvMax
            );
        },
        bidRange: rangeBid
    };
    const budgetTypeList = getBudgetTypeList({
        hasSharedBudget: !!bindProject?.projectId && !!bindProject?.projectInfo?.sharedBudgetId
    });
    return (
        <div className="manage-editor-campaign-new-budget-container">
            <div className="budget-radio-group">
                <RadioGroup value={budgetType} onChange={onBudgetTypeChange} className="budget-radio-btn">
                    {budgetTypeList.map(type => (
                        <RadioButton
                            value={type}
                            key={type}
                            className="ad-main-radio-button"
                            disabled={
                                // 最大转化下预算【不限】和【共享预算】置灰
                                // 选择项目且项目使用共享预算 预算所有radio置灰(最大转化除外 不带入项目预算信息)
                                // 选择项目且项目不使用共享预算 【共享预算】置灰
                                (campaignOcpcBidType === campaignOcpcBidTypeConfig.cvMax
                                    && (type === budgetTypeConfig.NO_LIMIT || type === budgetTypeConfig.SHARE_BUDGET))
                                || (props.disabledBudget && campaignOcpcBidType !== campaignOcpcBidTypeConfig.cvMax)
                                || (type === budgetTypeConfig.SHARE_BUDGET && props.disabledSharedBudget)
                            }
                        >
                            {budgetTypeText[type]}
                            {type === budgetTypeConfig.NO_LIMIT && <Tip keyName="unlimitedBudget" />}
                            {type === budgetTypeConfig.SHARE_BUDGET && <Tip keyName="sharedBudget" />}
                        </RadioButton>
                    ))}
                </RadioGroup>
                <div className="budget-radio-tip">
                    预算范围内出现余额不足可能产生透支消费，欠款将在下次充值加款后自动扣除；系统为了尽量避免产生超出预算的消费，限制5分钟内仅支持修改1次预算
                </div>
            </div>
            {budgetType === budgetTypeConfig.DAILY_BUDGET && (
                <>
                    {
                        (isCVMaxCampaignBudgetUser() && campaignOcpcBidType === campaignOcpcBidTypeConfig.cvMax)
                            ? <CustomBudget {...customBudgetTableProps} />
                            : (
                                <div className="manage-editor-campaign-budget-container">
                                    <span className="daily-budget-label">日预算</span>
                                    <Tip keyName="dailyBudget" />
                                    <UINumberInput {...numberInputProps} />
                                </div>
                            )
                    }
                </>
            )}
        </div>
    );
};

CampaignBudget.propTypes = {
    value: PropTypes.object.isRequired,
    onChange: PropTypes.func.isRequired
};

export default CampaignBudget;
