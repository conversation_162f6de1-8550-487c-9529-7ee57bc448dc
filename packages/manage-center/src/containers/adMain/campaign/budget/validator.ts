import {optimizeTransTargetType} from 'commonLibs/config/optimizeTransTarget';
import {campaignOcpcBidTypeConfig} from 'commonLibs/config/ocpc';
import {budgetTypeConfig, rangeBid} from './config';

interface budgetValueValidateProps {
    budgetType: keyof typeof budgetTypeConfig;
    budgetValue: number;
    sharedBudgetId: number;
    campaignOcpcBid: number;
    sharedBudget: number;
    optimizeTransTarget: number;
    campaignOcpcBidType: number;
}

export const budgetValueValidate = ({
    budgetType,
    budgetValue,
    campaignOcpcBid,
    sharedBudget,
    optimizeTransTarget,
    campaignOcpcBidType
}: budgetValueValidateProps) => {
    if (budgetType === budgetTypeConfig.NO_LIMIT) {
        return '';
    }
    const effectiveBudget = budgetType === budgetTypeConfig.SHARE_BUDGET ? sharedBudget : budgetValue;
    if (
        campaignOcpcBid
        && optimizeTransTarget !== optimizeTransTargetType.click
        && campaignOcpcBidType !== campaignOcpcBidTypeConfig.cvMax
        && effectiveBudget
        && campaignOcpcBid > effectiveBudget
    ) {
        return `当前计划最低目标转化出价${campaignOcpcBid}元，预算不能低于出价`;
    }
    if (budgetType === budgetTypeConfig.DAILY_BUDGET) {
        if (!budgetValue || isNaN(budgetValue)) {
            return '请输入每日预算';
        }
        if (budgetValue < rangeBid.min) {
            return `为了保证您的推广效果，每日预算需≥${rangeBid.min}元`;
        }
        if (budgetValue > rangeBid.max) {
            return '日预算不能超过10000000元';
        }
    }
    return '';
};