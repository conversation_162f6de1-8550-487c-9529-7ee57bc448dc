/**
 * @file 点击计划出价
 * <AUTHOR>
 * @date 2021-12-29 17:05:05
 */

import {UINumberInput} from '@baidu/react-formulator';
import PropTypes from 'prop-types';
import sendMonitor from 'commonLibs/utils/sendHm';
import './style.less';

// 本地、爱采购增强模式暂不支持
const CampaignBid = props => {
    const {onChange, value, className, inputProps = {}} = props;
    const numberBoxProps = {
        className,
        width: 262,
        tailLabel: '元/点击',
        value,
        onChange: v => {
            onChange(v);
            sendMonitor('click',
                {level: 'campaign', item: 'new_campaign_campaignBid_change', params: v});
        },
        fixed: 2,
        ...inputProps
    };
    return (
        <div className="optimize-auto-bid-container">
            <UINumberInput {...numberBoxProps} />
        </div>
    );
};

CampaignBid.propTypes = {
    onChange: PropTypes.func.isRequired,
    value: PropTypes.number.isRequired,
    form: PropTypes.object.isRequired
};

export default CampaignBid;

