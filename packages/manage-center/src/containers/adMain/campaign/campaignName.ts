import {B2B, mtTextMap} from 'commonLibs/config/marketTarget';
import dayjs from 'dayjs';
import {substringInBytes} from 'commonLibs/utils/string';

function generateRandomCharacter(characters: string): string {
    return characters[Math.floor(Math.random() * characters.length)];
}

function generateRandomThreeCharacterString(characters: string): string {
    let result = '';
    for (let i = 0; i < 3; i++) {
        result += generateRandomCharacter(characters);
    }
    return result;
}

const characters = 'abcdefghijklmnopqrstuvwxyz0123456789'; // 可以包含的字符

const SupportNameMTConfig = [B2B]; // 名字提供【推广】的营销目标

export const initCampaignName = (prefix: string): string => {
    // 营销目标_计划+随机生成三位字符（数字/字母）_月_日_时间（计划创建）
    const randomCharacter = generateRandomThreeCharacterString(characters);
    const currentTime = dayjs().format('MMDD_HH:mm');
    return `${prefix}计划${randomCharacter}_${currentTime}`;
};