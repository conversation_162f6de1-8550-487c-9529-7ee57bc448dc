import IdType from 'commonLibs/config/idType';
import {getSingleErrorMessage} from 'commonLibs/utils/getErrorTextByCode';
import {getBackendError} from '../../apis/campaign/api';

export const validator = async value => {
    try {
        if (value) {
            await getBackendError({
                items: [{
                    name: value,
                    idType: IdType.PLAN_LEVEL
                }]
            });
            return;
        }
        return;
    } catch (e) {
        const code = e.errors?.[0]?.code;
        if (code) {
            return getSingleErrorMessage(e.errors[0]);
        }
        return;
    }
}