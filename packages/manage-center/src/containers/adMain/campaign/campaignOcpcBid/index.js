/**
 * @file transTypes
 * <AUTHOR>
 * @date 2021-12-31 15:38:47
 */
import {useEffect, useState, useCallback} from 'react';
import {NumberInput, Dialog} from '@baidu/one-ui';
import PropTypes from 'prop-types';
import sendMonitor from 'commonLibs/utils/sendHm';
import {request} from '@baidu/winds-ajax';
import {COMPREHENSIVE_CLUES, ORDER_SUCCESS_CT} from 'commonLibs/config/ocpc';
import RePayModal from './repayModal';
import {optimizeTransTargetType} from 'commonLibs/config/optimizeTransTarget';
import './style.less';


const CampaignOcpcBid = props => {
    const {
        value, onChange, campaignTransTypes: transTypes,
        className, optimizeTransTarget, inputProps = {}
    } = props;
    const [cpaRange, setCpaRange] = useState('');
    const hasTransTypes = transTypes.length > 0
        || optimizeTransTarget === optimizeTransTargetType.comprehensiveClue
        || optimizeTransTarget === optimizeTransTargetType.order;
    useEffect(() => {
        if (hasTransTypes) {
            let transList = [];
            if (optimizeTransTarget === optimizeTransTargetType.comprehensiveClue) {
                transList = [COMPREHENSIVE_CLUES];
            }
            else if (optimizeTransTarget === optimizeTransTargetType.order) {
                transList = [ORDER_SUCCESS_CT];// 订单提交成功
            }
            else {
                transList = transTypes;
            }
            request({
                path: 'rose/GET/FcOcpcService/getSuggestCPAs',
                params: {
                    transTypes: transList,
                    fields: ['tradeSuggestCpa', 'noThresholdSuggestCpa']
                }
            }).then(response => {
                const {tradeSuggestCpa, noThresholdSuggestCpa} = response || {};
                const cpa = noThresholdSuggestCpa || tradeSuggestCpa;
                setCpaRange((cpa && (+cpa).toFixed(2)) || '');
            });
        }
    }, [transTypes, optimizeTransTarget]);


    const numberBoxProps = {
        className,
        width: 264,
        suffix: '元/转化',
        value,
        onChange: e => {
            onChange(e.target.value);
            sendMonitor('click',
                {level: 'campaign', item: 'new_campaign_ocpc_bid_change', params: e.target.value});
        },
        fixed: 2,
        ...inputProps
    };
    const onChangePayRulerModal = useCallback(() => {
        Dialog.confirm({
            needCloseIcon: true,
            title: 'oCPC赔付规则',
            content: <RePayModal />,
            okCancel: false,
            wrapClassName: 'ocpc-pay-ruler-modal',
            width: 800
        });
    }, []);
    return (
        <div className="campaign-ocpc-bid-form-item">
            <div>
                <NumberInput {...numberBoxProps} />
                {cpaRange && hasTransTypes && (
                    <span className="cpa-range">
                        同行业/推广业务客户设置的目标转化成本:
                        <span className="cpa-range-money">{cpaRange}元</span>
                    </span>
                )}
            </div>
        </div>
    );
};

CampaignOcpcBid.propTypes = {
    value: PropTypes.object.isRequired,
    onChange: PropTypes.func.isRequired,
    form: PropTypes.object.isRequired
};

export default CampaignOcpcBid;
