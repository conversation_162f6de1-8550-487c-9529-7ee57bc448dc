/**
 * @file 零门槛超成本赔付规则弹窗
 * <AUTHOR>
 * @date 2021/01/05
 */
import './style.less';

export default () => {
    return (
        <div className="refund-ruler-container">
            <div className="refund-content">
                <p>
                    为保障[转化]出价计划在投放前期能得到充分的探索，平台将承担投放前期的成本波动风险，对投放成本超出预期的计划进行超成本赔付
                </p>
                <p>
                    返款规则如下：
                </p>
                <p className="title">1.赔付对象：</p>
                <p className="double-indent">
                    仅针对出价方式为[转化]的计划进行赔付，最大转化目前不在赔付范围内；
                </p>
                <p className="title">2.赔付条件：（以下条件缺一不可）</p>
                <p className="double-indent">1）超成本比例：转化成本超过目标转化出价的20%以上（举例：目标转化出价10元，转化成本≥12元）</p>
                <p className="double-indent">2）赔付时间范围，及返款到账时间</p>
                {/* <p className="double-indent">进入学习期后的第N日首次累计达到20个目标转化量，则有以下两种情况：</p> */}
                <div className="refund-no-threshold-table-wrapper">
                    <table className="refund-no-threshold-table">
                        <thead>
                            <tr>
                                <th />
                                <th>赔付时间范围</th>
                                <th>目标转化量要求</th>
                                <th>数据来源要求</th>
                                <th>返款到账时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>常规赔付</td>
                                <td>计划进入学习期那一刻起，及其之后的3个自然日</td>
                                <td>累计&gt;5个目标转化量</td>
                                <td>无限制</td>
                                <td>计划进入学习期的第6天</td>
                            </tr>
                            <tr>
                                <td>特殊赔付</td>
                                <td>计划进入学习期那一刻起，及其之后的N个自然日（3&lt;N&le;21）</td>
                                <td>累计≥20个目标转化量</td>
                                <td>仅限咨询工具授权、基木鱼、电话数据授权</td>
                                <td>计划进入学习期的第N+3天</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <p className="double-indent">
                    3）修改要求：赔付时间范围内，每日下调计划转化出价或每日修改定向（关键词、人群、地域、自动定向等）其中任意一个的次数≤2次；
                    不能修改出价方式、流量范围、数据来源和转化类型 ；不能删除计划或暂停投放
                </p>
                <p className="double-indent">4）该计划尚未进行过超成本赔付</p>
                <p className="double-indent">5）当系统判断「广告主无异常作弊等操作行为」时，才会给予赔付</p>
                <p className="double-indent">6）特殊赔付说明：数据来源必须为咨询工具授权、基木鱼、电话数据授权这三种类型，投放时加入其他类型数据来源，无法获得赔付</p>
                <p className="title">3.赔付金额计算规则：</p>
                <div className="refund-no-threshold-table-wrapper">
                    <table className="refund-no-threshold-table">
                        <thead>
                            <tr>
                                <th>优化目标</th>
                                <th>定义</th>
                                <th>预期消费</th>
                                <th>赔付金额</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>优化目标转化</td>
                                <td>设置目标转化，未设置深度目标转化及深度目标转化出价</td>
                                <td>（目标转化出价1 * 对应转化数）+（目标转化出价2 * 对应转化数）+ ...（目标转化出价n*对应转化数）</td>
                                <td>实际消费 - 预期消费</td>
                            </tr>
                            <tr>
                                <td>优化深度转化</td>
                                <td>①设置目标转化，同时设置深度目标转化<br />②设置目标转化，同时设置深度目标转化及深度目标转化出价</td>
                                <td>【深度转化状态】处于【仅优化目标转化】或【同时优化深度转化】状态时，保障目标转化成本<br />
                                    预期消费（保障目标转化成本）=（目标转化出价1 * 对应目标转化数）+（目标转化出价2 * 对应目标转化数）+ ...（目标转化出价n*对应目标转化数）；<br />
                                    【深度转化状态】处于【优化深度转化中】状态时，保障深度目标转化成本<br />
                                    预期消费（保障深度目标转化成本）=（深度目标转化出价1 * 对应深度目标转化数）+
                                    （深度目标转化出价2 * 对应深度目标转化数）+ ...（深度目标转化出价n*对应深度目标转化数）
                                </td>
                                <td>实际消费 - 预期消费（保障深度目标转化成本）- 预期消费（保障目标转化成本）</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <p className="title">4.异常作弊行为拒赔/处罚说明</p>
                <p className="double-indent">
                    广告主存在异常作弊行为，平台有权拒绝赔付并追回已赔付款项。同时根据作弊行为严重程度，平台会对广告主采取不同程度的处罚措施，
                    包括但不限于对计划进行低优先级投放、取消广告主账号或同主体下所有账号的赔付权利、封禁广告主账号及同主体账号等。
                </p>
                <p className="double-indent">1）平台有权单独作出异常作弊的判定结果，且无需向您披露具体的作弊判定规则</p>
                <p className="double-indent">
                    2）「oCPC超成本赔付」是平台给予广告主的一项福利政策，旨在帮助客户取得良好的投放效果。为保证后续广告的健康投放，建议您保持良好的投放习惯
                </p>
                <br />
                <p>关于返款计算规则和返款条件的最终解释权归百度推广平台所有；</p>
            </div>
        </div>
    );
};
