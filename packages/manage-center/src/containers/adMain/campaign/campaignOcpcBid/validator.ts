/**
 * @file 校验-ocpc出价
 * <AUTHOR>
 */
import {budgetTypeConfig} from 'app/containers/fcNew/campaign/budget/config';
import {optimizeTransTargetType} from 'commonLibs/config/optimizeTransTarget';
import {campaignOcpcBidTypeConfig} from 'commonLibs/config/ocpc';

export const validateOcpcBid = (
    value: number,
    budget: number,
    sharedBudget: number,
    budgetType: keyof typeof budgetTypeConfig,
    optimizeTransTarget: number,
    campaignOcpcBidType: number
) => {
    const effectiveBudget = budgetType === budgetTypeConfig.NO_LIMIT
        ? 0
        : budgetType === budgetTypeConfig.SHARE_BUDGET ? sharedBudget : budget;
    if (value < 0.1) {
        return '目标转化成本需≥0.1';
    }
    else if (value > 9999) {
        return '目标转化成本需小于9999';
    }
    else if (
        effectiveBudget
        && value > effectiveBudget
        && optimizeTransTarget !== optimizeTransTargetType.click
        && campaignOcpcBidType !== campaignOcpcBidTypeConfig.cvMax
    ) {
        return `当前计划可用日预算${effectiveBudget}元，出价不能高于预算`;
    }
    return '';
};
