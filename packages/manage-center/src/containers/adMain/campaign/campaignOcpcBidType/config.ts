import {campaignOcpcBidTypeConfig, ocpcBidTypeEnum} from 'commonLibs/config/ocpc';

export const campaignBidTypeOptionForStore = [
    {
        label: '目标转化成本',
        tip: '按照您的目标转化出价',
        value: campaignOcpcBidTypeConfig.oCPC,
        bidType: ocpcBidTypeEnum.OCPC
    },
    {
        label: '最大转化',
        tip: '系统自动进行转化出价',
        value: campaignOcpcBidTypeConfig.cvMax,
        bidType: ocpcBidTypeEnum.OCPC
    },
    {
        label: '点击出价',
        tip: '按照点击出价',
        value: campaignOcpcBidTypeConfig.CPC,
        bidType: ocpcBidTypeEnum.CPC
    }
] as const;

export const CampaignOcpcBidTypeOptions = [
    {
        label: '目标转化成本',
        tip: '按照您的目标转化出价',
        value: campaignOcpcBidTypeConfig.oCPC,
        bidType: ocpcBidTypeEnum.OCPC
    },
    {
        label: '最大转化',
        tip: '系统自动进行转化出价',
        value: campaignOcpcBidTypeConfig.cvMax,
        bidType: ocpcBidTypeEnum.OCPC
    },
    {
        label: '增强模式',
        tip: '系统自动微调点击出价',
        value: campaignOcpcBidTypeConfig.eCPC,
        bidType: ocpcBidTypeEnum.CPC
    },
    {
        label: '点击出价',
        tip: '按照点击出价',
        value: campaignOcpcBidTypeConfig.CPC,
        bidType: ocpcBidTypeEnum.CPC
    }
];