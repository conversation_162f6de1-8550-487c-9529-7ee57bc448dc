import SelectCard from 'commonLibs/components/selectCard';
import {campaignOcpcBidTypeConfig} from 'commonLibs/config/ocpc';
import {campaignBidTypeOptionForStore} from './config';
import './style.less';

export default props => {
    const {value, onChange, options = campaignBidTypeOptionForStore} = props;
    const selectCardProps = {
        value,
        options,
        onChange
    };
    return (
        <div>
            <SelectCard {...selectCardProps} />
            {
                value === campaignOcpcBidTypeConfig.cvMax && (
                    <div className='campaign-bidType-cvmax-tip'>
                        根据您的预算，为您探索尽可能多的转化量，转化成本可能在一定区间内波动
                    </div>
                )
            }
        </div>
    );
};