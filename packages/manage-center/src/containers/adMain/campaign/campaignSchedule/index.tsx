import CampaignSchedule from 'app/containers/fcNew/campaign/schedule/Main';
import {APP} from 'commonLibs/config/marketTarget';
import {useCampaignInfo} from '../context';

export const isShowBasic = ({
    isConversionCampaign,
    marketingTargetId
}: {isConversionCampaign: boolean, marketingTargetId: number}) => {
    return isConversionCampaign || marketingTargetId === APP;
};

export default props => {
    const {marketingTargetId} = useCampaignInfo();
    const {isConversionCampaign} = props;
    const showBasic = isShowBasic({isConversionCampaign, marketingTargetId});
    return <CampaignSchedule showBasic={showBasic} {...props} />;
};