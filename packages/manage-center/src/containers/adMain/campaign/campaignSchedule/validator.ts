import { isEmpty } from 'lodash-es';
import { getLengthInBytes } from 'commonLibs/utils/string';
import { schedueTypeConfig } from 'app/containers/fcNew/campaign/schedule/config';

export const max = 30;

export const validator = (value: {
    scheduleValue: unknown;
    checked: boolean;
    optType: string;
    optTemplateName: string;
    optTemplateId: number;
    type: number;
}): string | undefined => {
    const {
        scheduleValue,
        checked,
        optType,
        optTemplateName,
        optTemplateId,
        type
    } = value;

    if (type === schedueTypeConfig.all) {
        return;
    }

    if (isEmpty(scheduleValue)) {
        return '请至少选择一个时段';
    }

    if (checked) {
        if (optType === 'add') {
            const len = getLengthInBytes(optTemplateName);
            if (optTemplateName.trim() === '') {
                return '请输入时段模板名称';
            }
            if (len > max) {
                return '模板名称长度不超过30个字符';
            }
        } else if (optType === 'update' && !optTemplateId) {
            return '请选择时段模板';
        }
    }

    return;
};