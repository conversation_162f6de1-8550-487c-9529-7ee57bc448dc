import {Radio} from '@baidu/one-ui';
import {TransManagerModeType, TransManagerModeOptionConfig} from 'commonLibs/config/ocpc';
import {isLiveSubMarket} from 'commonLibs/config/marketTarget';

interface TransManagerModeProps {
    value: keyof typeof TransManagerModeType;
    onChange(value: keyof typeof TransManagerModeType): void;
    subMarketingTargetId: number;
    options?: typeof TransManagerModeOptionConfig;
}

const liveTransManageModeLimitList = [TransManagerModeType.unlimited, TransManagerModeType.cvSource];

function CampaignTransManagerMode({
    value,
    onChange,
    subMarketingTargetId,
    options = TransManagerModeOptionConfig
}: TransManagerModeProps) {
    const liveSubmarket = isLiveSubMarket(subMarketingTargetId);
    const optionsWithDisabled = options.map(item => {
        if (liveTransManageModeLimitList.includes(item.value) && liveSubmarket) {
            return {
                ...item,
                disabled: true
            };
        }
        return {
            ...item,
            disabled: false
        };
    });
    return (
        <Radio.Group
            value={value}
            onChange={e => onChange(e.target.value as keyof typeof TransManagerModeType)}
            type="strong"
        >
            {
                optionsWithDisabled.map(item => {
                    const {value, label, disabled} = item;
                    return (
                        <Radio.Button key={value} value={value} disabled={disabled}>
                            {label}
                        </Radio.Button>
                    );
                })
            }
        </Radio.Group>
    );
}

export default CampaignTransManagerMode;
