import {transAssetOptionsEnum, cvSourcesOptions} from 'commonLibs/config/ocpc';
import CampaignTransTypes from 'app/containers/fcNew/campaign/campaignTransTypes';
import SuspenseBoundary, {useResource} from 'commonLibs/suspenseBoundary';
import {
    optimizeTargetConfig
} from 'app/containers/campaigns/list/editor/optimizeTarget/config';
import {fetchUserTransTypeList} from '../../apis/campaign/api';
import {useCampaignInfo} from '../context';

const initalObject = {};
function CampaignTransTypesB2B(props) {
    const {marketingTargetId} = useCampaignInfo();
    const [
        {
            forSelect = [],
            availableCvSources = [],
            eligibleInfo = {},
            suggestTransTypes = [],
            normalizedTransAssets = {},
            availableTransTypes = [],
            assets
        } = initalObject
    ] = useResource(fetchUserTransTypeList, {marketingTargetId});
    const {assetsData = {}, assetsInfo = {}} = normalizedTransAssets;
    const campaignTransTypesProps = {
        forSelect,
        availableCvSources,
        eligibleInfo,
        suggestTransTypes,
        availableTransTypes,
        assetsData,
        assetsInfo,
        marketingTargetId,
        form: {
            getFieldsValue: () => {
                return {
                    // 爱采购默认转化
                    optimizeTarget: optimizeTargetConfig.conversion,
                    transAssets: {
                        assets,
                        option: transAssetOptionsEnum.BY_ASSET_TYPE
                    },
                    cvSources: {
                        cvSources: [],
                        option: cvSourcesOptions.UNLIMITED
                    }
                };
            },
            setFieldsValue: () => {
                // 兼容旧版，无数据源所以没有代码
            }
        },
        isShowComprehensiveClueInPlan: false
    };
    return <CampaignTransTypes {...campaignTransTypesProps} {...props} />;
};

export default function (props) {
    return (
        <SuspenseBoundary loading={{type: 'normal', tip: '', size: 'small'}}>
            <CampaignTransTypesB2B {...props} />
        </SuspenseBoundary>
    );
}
