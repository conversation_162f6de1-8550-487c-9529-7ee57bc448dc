import {cvSourcesOptions, TransManagerModeType} from 'commonLibs/config/ocpc';
import CampaignTransTypes from 'app/containers/fcNew/campaign/campaignTransTypes';
import SuspenseBoundary, {useResource} from 'commonLibs/suspenseBoundary';
import {
    optimizeTargetConfig
} from 'app/containers/campaigns/list/editor/optimizeTarget/config';
import {fetchUserTransTypeList} from '../../apis/campaign/api';
import {useCampaignInfo} from '../context';
import {getIsConversionCampaign} from '../util/common';

const initalObject = {};
const CampaignTransTypesStore = props => {
    const {marketingTargetId} = useCampaignInfo();
    const {campaignOcpcBidType, transAsset, cvSources, transManagerMode} = props;
    const [
        {
            forSelect = [],
            availableCvSources = [],
            eligibleInfo = {},
            suggestTransTypes = [],
            normalizedTransAssets = {},
            availableTransTypes = []
        } = initalObject
    ] = useResource(fetchUserTransTypeList, {marketingTargetId});
    const {assetsData = {}, assetsInfo = {}} = normalizedTransAssets;

    const campaignTransTypesProps = {
        forSelect,
        availableCvSources,
        eligibleInfo,
        suggestTransTypes,
        availableTransTypes,
        assetsData,
        assetsInfo,
        marketingTargetId,
        // transTypeList,
        form: {
            getFieldsValue: () => {
                return {
                    optimizeTarget: getIsConversionCampaign({
                        marketingTargetId,
                        campaignOcpcBidType
                    }) ? optimizeTargetConfig.conversion : optimizeTargetConfig.click,
                    transAssets: transAsset,
                    cvSources: transManagerMode === TransManagerModeType.cvSource ? {
                        cvSources: cvSources.source,
                        option: cvSources.option
                    } : {
                        cvSources: [],
                        option: cvSourcesOptions.UNLIMITED
                    },
                    transManagerMode
                };
            },
            setFieldsValue: () => {
                // 兼容旧版，无数据源所以没有代码
            }
        },
        isShowComprehensiveClueInPlan: false
    };
    return <CampaignTransTypes {...campaignTransTypesProps} {...props} />;
};
export default function (props) {
    return (
        <SuspenseBoundary loading={{type: 'normal', tip: '', size: 'small'}}>
            <CampaignTransTypesStore {...props} />
        </SuspenseBoundary>
    );
}