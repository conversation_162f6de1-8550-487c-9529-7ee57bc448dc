import {campaignOcpcBidTypeConfig, transAssetOptionsEnum, cvSourcesOptions} from 'commonLibs/config/ocpc';
import {isFcTransAssetsUser} from 'commonLibs/utils/getFlag';
import {isLiveSubMarket} from 'commonLibs/config/marketTarget';
import CampaignTransTypes from 'app/containers/fcNew/campaign/campaignTransTypes';
import SuspenseBoundary, {useResource} from 'commonLibs/suspenseBoundary';
import {
    optimizeTargetConfig
} from 'app/containers/campaigns/list/editor/optimizeTarget/config';
import {getIsConversionCampaign} from '../util/common';
import {fetchUserTransTypeList} from '../../apis/campaign/api';
import {useCampaignInfo} from '../context';

const initalObject = {};

export function isRequired({campaignOcpcBidType, subMarketingTargetId, transAsset, cvSources}) {
    const liveSubMarketTarget = isLiveSubMarket(subMarketingTargetId);
    if (campaignOcpcBidType !== campaignOcpcBidTypeConfig.eCPC || liveSubMarketTarget) {
        return true;
    }
    if (
        transAsset?.option === transAssetOptionsEnum.BY_ASSET_ID
        && transAsset?.assets.length
    ) {
        return true;
    }
    if (
        cvSources.option === cvSourcesOptions.FILTER
        && cvSources.source.length
    ) {
        return true;
    }
    return false;
}

function CampaignTransTypesCommon(props) {
    const {marketingTargetId} = useCampaignInfo();
    const {transAsset, campaignOcpcBidType, cvSources, transManagerMode} = props;
    const [
        {
            forSelect = [],
            availableCvSources = [],
            eligibleInfo = {},
            suggestTransTypes = [],
            normalizedTransAssets = {},
            availableTransTypes = [],
            mtAvailableTransTypes = {}
        } = initalObject
    ] = useResource(fetchUserTransTypeList, {marketingTargetId});
    const {assetsData = {}, assetsInfo = {}} = normalizedTransAssets;
    const campaignTransTypesProps = {
        forSelect,
        availableCvSources,
        eligibleInfo,
        suggestTransTypes,
        availableTransTypes,
        assetsData,
        assetsInfo,
        marketingTargetId,
        mtAvailableTransTypes,
        form: {
            getFieldsValue: () => {
                return {
                    optimizeTarget: getIsConversionCampaign({
                        marketingTargetId,
                        campaignOcpcBidType
                    }) ? optimizeTargetConfig.conversion : optimizeTargetConfig.click,
                    transAssets: transAsset,
                    cvSources: {
                        cvSources: cvSources.source,
                        option: cvSources.option
                    },
                    transManagerMode
                };
            },
            setFieldsValue: () => {
                // 兼容旧版，无数据源所以没有代码
            }
        },
        isShowComprehensiveClueInPlan: false
    };
    return <CampaignTransTypes {...campaignTransTypesProps} {...props} />;
};

export default function (props) {
    return (
        <SuspenseBoundary loading={{type: 'normal', tip: '', size: 'small'}}>
            <CampaignTransTypesCommon {...props} />
        </SuspenseBoundary>
    );
}
