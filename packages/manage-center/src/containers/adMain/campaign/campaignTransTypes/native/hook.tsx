/**
 * @file 原生互动营销目标-优化目标
 * <AUTHOR>
 * @date 2025-02-17
 * @description 获取更多目标列表，用于原生互动营销目标-优化目标下拉选择
*/

import {useMemo} from 'react';
import {Select} from '@baidu/one-ui';
import {intersection} from 'lodash-es';
import {getValidTransTypes} from 'commonLibs/TransEditor/util';
import {
    transType as TRANS_TYPE,
    ocpcTransOptionsConfig,
    ocpcTransAssembleText,
    OcpcTransAssembleEnum,
    onlyDeepTransTypes
} from 'commonLibs/config/ocpc';


// 获取可选目标转化
export function useAssembleOptions(transTypeList: number[]) {
    const assembleOptions = useMemo(() => {
        const options: Array<{assembleKey: number, label: string, options: number[]}> = [];
        Object.keys(ocpcTransOptionsConfig).forEach(key => {
            const matchTypes = intersection(transTypeList, ocpcTransOptionsConfig[key]);
            if (matchTypes.length) {
                options.push({
                    assembleKey: +key,
                    label: ocpcTransAssembleText[key],
                    options: matchTypes
                });
            }
        }, {});
        return options;
    }, [transTypeList]);
    return assembleOptions;
}


// 先复用qingge项目的逻辑
export function useTransOptions({
    subMarketingTargetId,
    transTypeList,
    selectedTransTypes
}: {
    subMarketingTargetId: number;
    transTypeList: number[];
    selectedTransTypes: number[];
}) {
    const assembleOptions = useAssembleOptions(transTypeList);

    return assembleOptions.map(item => {
        return (
            <Select.OptGroup key={item.assembleKey} label={item.label}>
                {
                    item.options.map(type => {
                        const exclusive = !ocpcTransOptionsConfig[OcpcTransAssembleEnum.mutipleClue].includes(type);
                        const validTransTypes = getValidTransTypes(selectedTransTypes);
                        const disabled = !validTransTypes.includes(type) || onlyDeepTransTypes.includes(type);
                        const optionProps = {
                            value: type,
                            exclusive,
                            disabled
                        };
                        return (
                            <Select.Option key={type} {...optionProps}>
                                {TRANS_TYPE[type]}
                            </Select.Option>
                        );
                    })
                }
            </Select.OptGroup>
        );
    });
}