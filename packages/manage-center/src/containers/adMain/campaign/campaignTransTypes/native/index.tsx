/**
 * @file 原生互动营销目标下的更多优化目标
 * <AUTHOR>
 * @date 2025-02-17
 * @description 原生互动营销目标-优化目标分为 综合线索、微信加粉成功 以及 更多目标
*/

import {Select} from '@baidu/one-ui';
import {useTransOptions} from './hook';
import {transTypeList} from './config';


export default function CampaignTransTypesNative(props) {
    const {value, onChange, subMarketingTargetId} = props;

    const newSelectProps = {
        mode: 'multiple',
        placeholder: '请选择优化目标',
        onChange,
        value,
        width: 400,
        defaultActiveFirstOption: false,
        filterOption: (input, option) => {
            const label = option.props.children;
            return label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        }
    };

    const assembleOptions = useTransOptions({
        subMarketingTargetId,
        transTypeList,
        selectedTransTypes: value
    });

    return (
        <div className="campaign-trans-type-form-item">
            <Select {...newSelectProps}>
                {assembleOptions}
            </Select>
        </div>
    );
};

