/**
 * @file: ai 编辑流程
 * @author: liuye11(<EMAIL>)
 * @Last Modified by: liuye11
 * @Last Modified time: 2023-07-11 12:21:48
 */

import {pick, findIndex} from 'lodash-es';
import {REGION_TYPE} from 'commonLibs/config/regionConfig';
import {parseJsonWithFallback} from 'commonLibs/utils/json';
import {schedueTypeConfig} from 'app/containers/fcNew/campaign/schedule/config';
import {
    getAllDaySelected,
    getFormateValuesFromBackend
} from 'app/containers/campaigns/list/editor/schedule/utils';
import {optimizeTransTargetType} from 'commonLibs/config/optimizeTransTarget';
import {budgetTypeConfig} from '../budget/config';
import DisplaySelectedPromotionScene from '../promotionScene/DisplaySelected';
import {getIsConversionCampaign} from '../util/common';
import {fields} from './B2B';

interface InitialDataParams {
    campaignInfo: Record<string, any>;
    aiCompositionCampaign: Record<string, any>;
}

function checkIsConversionCampaign(aiCampaignInfo: Record<string, any>) {
    const {marketingTargetId, campaignOcpcBidType, optimizationTarget} = aiCampaignInfo;

    return getIsConversionCampaign({
        marketingTargetId,
        campaignOcpcBidType,
        optimizeTransTarget: optimizationTarget
    });
}

function initSchedule(aiCampaignInfo: Record<string, any>) {
    let {schedulePriceFactors, schedule} = aiCampaignInfo;
    schedule = parseJsonWithFallback(schedule, []);

    if (!schedulePriceFactors || !schedule.length) {
        return {
            scheduleValue: getAllDaySelected(checkIsConversionCampaign(aiCampaignInfo)),
            type: schedueTypeConfig.all
        };
    }

    // 时段
    schedulePriceFactors = parseJsonWithFallback(schedulePriceFactors, []);
    const showBasic = checkIsConversionCampaign(aiCampaignInfo);
    return {
        scheduleValue: getFormateValuesFromBackend(schedulePriceFactors, showBasic),
        type: schedueTypeConfig.custom
    };
}

export function getAICampaignFormData({campaignInfo, aiCompositionCampaign}: InitialDataParams) {
    const {
        equipmentType, optimizationTarget, campaignTransTypes,
        budget, sharedBudgetId, sharedBudget
    } = aiCompositionCampaign;

    return {
        ...pick(
            campaignInfo,
            ['marketingTargetId', 'isShowComprehensiveClueInPlan', 'campaignTransTypes']
        ),
        ...pick(
            aiCompositionCampaign,
            [
                'campaignName', 'promotionScene', 'campaignBid', 'marketingTargetId',
                'campaignBidType', 'campaignOcpcBidType', 'campaignOcpcBid', 'campaignTransTypes'
            ]
        ),
        equipmentType: {
            equipmentType: equipmentType || campaignInfo.equipmentType.equipmentType
        },
        optimizeTransTarget: optimizationTarget,
        campaignTransTypes: optimizationTarget === optimizeTransTargetType.transType
            ? campaignTransTypes
            : campaignInfo.campaignTransTypes,
        campaignBudget: {
            budgetType: sharedBudgetId
                ? budgetTypeConfig.SHARE_BUDGET
                : budget ? budgetTypeConfig.DAILY_BUDGET : budgetTypeConfig.NO_LIMIT,
            budget: budget || '',
            sharedBudgetId,
            sharedBudget: sharedBudget || ''
        },
        campaignSchedule: {
            ...campaignInfo.campaignSchedule,
            ...initSchedule(aiCompositionCampaign)
        },
        campaignRegion: {
            ...campaignInfo.campaignRegion,
            ...(aiCompositionCampaign?.regionTarget?.length
                ? {
                    // 自定义计划地域
                    regionMainType: REGION_TYPE.CAMPAIGN_REGION,
                    regionTarget: aiCompositionCampaign.regionTarget
                }
                : {}
            )
        }
    };
}

const customAIFields = [
    {
        ...fields.promotionScene,
        use: [DisplaySelectedPromotionScene]
    }
];
export function getAICampaignFormConfig(formConfig: Record<string, any>) {
    return {
        ...formConfig,
        fields: formConfig.fields.map((fieldsGroup: Record<string, any>) => {
            return {
                ...fieldsGroup,
                fields: fieldsGroup.fields.map((fieldConfig: Record<string, any>) => {
                    const index = findIndex(
                        customAIFields, customAIField => customAIField.field === fieldConfig.field
                    );
                    return index !== -1 ? customAIFields[index] : fieldConfig;
                })
            };
        })
    };
}