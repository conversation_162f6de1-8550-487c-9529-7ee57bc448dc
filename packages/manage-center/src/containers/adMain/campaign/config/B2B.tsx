import queryString from 'query-string';
import {isEmpty} from 'lodash-es';
import {
    FLOW_SCOPE, cvSourcesOptions,
    campaignBidTypeConfig, COMPREHENSIVE_CLUES,
    campaignOcpcBidTypeConfig, transAssetOptionsEnum,
    CVSOURCES_UNLIMITED, getCvSourceLimitConfig
} from 'commonLibs/config/ocpc';
import {isFcTransAssetsUser} from 'commonLibs/utils/getFlag';
import {getComprehensiveClueInPlanTrans} from 'commonLibs/TransEditor/util';
import {B2B, mtTextMap} from 'commonLibs/config/marketTarget';
import {initialValue as scheduleInitialValue} from 'app/containers/campaigns/list/editor/schedule/config';
import {getAllDaySelected} from 'app/containers/campaigns/list/editor/schedule/utils';
import {getRegionInitialValue} from 'app/containers/fcNew/campaign/util';
import {schedueTypeConfig} from 'app/containers/fcNew/campaign/schedule/config';
import {
    campaignParamsPipeline,
    campaignPromotionParams,
    campaignClickPriceParams,
    campaignBudgetParams,
    campaignSettingParams,
    campaignRegionParams,
    campaignScheduleParams
} from '../util/generateParams';
import {promotionSceneType} from 'commonLibs/config/promotionScene';
import {optimizeTransTargetType} from 'commonLibs/config/optimizeTransTarget';
import {budgetTypeConfig} from '../budget/config';
import {initCampaignName} from '../campaignName';
import {getIsConversionCampaign} from '../util/common';
import OptimizeTransTarget from '../optimizeTransTarget';
import PromotionScene from '../promotionScene';
import CampaignTransTypes from '../campaignTransTypes/B2B';

export const fields = {
    promotionScene: {
        field: 'promotionScene',
        label: '推广场景',
        showRequiredMark: true,
        use: [PromotionScene]
    },
    optimizeTransTarget: {
        field: 'optimizeTransTarget',
        label: '优化目标',
        showRequiredMark: true,
        use: [OptimizeTransTarget],
        componentProps: ['isShowComprehensiveClueInPlan', 'isSetInitialAssetDataFromApi'],
        tip: 'optimizeTransTarget'
    },
    campaignTransTypes: {
        field: 'campaignTransTypes',
        use: [CampaignTransTypes],
        validators: [
            {
                validator: function (value) {
                    if (value.length === 0) {
                        return '请选择目标转化';
                    }
                    return;
                }
            }
        ],
        visible: formData => {
            return formData.optimizeTransTarget === optimizeTransTargetType.transType;
        }
    }
};

export function getInitialAssetsAndOptimizeTransTarget({
    assets = [], marketingTargetId, availableCvSources, normalizedTransAssets
}) {
    const assetId = assets[assets.length - 1];
    const transAssetType = normalizedTransAssets?.assetsData?.[assetId]?.assetType;
    // (小流量名单内)仅数据源选择【不限】且转化追踪内已有基木鱼、咨询工具授权、电话工具授权(一种即可)创建的转化追踪及 目标且计划营销目标为【网链、爱采购】时
    // 目标转化下拉列表内提供该目标【综合线索收集】；
    const isShowComprehensiveClueInPlan = getComprehensiveClueInPlanTrans(
        availableCvSources, cvSourcesOptions.UNLIMITED, marketingTargetId, transAssetType);
    const optimizeTransTarget = isShowComprehensiveClueInPlan
        ? optimizeTransTargetType.comprehensiveClue
        : optimizeTransTargetType.click;
    const isConversionCampaign = getIsConversionCampaign({marketingTargetId, optimizeTransTarget});
    return {
        campaignSchedule: {
            ...scheduleInitialValue,
            scheduleValue: getAllDaySelected(isConversionCampaign),
            type: schedueTypeConfig.all
        },
        optimizeTransTarget,
        isShowComprehensiveClueInPlan
    };
};

export function getInitialData(params) {
    const {assets = [], marketingTargetId} = params;
    const {pageType, b2bPageId} = queryString.parse(location.search);
    let initialAssetsAndOptimizeTransTarget = {
        optimizeTransTarget: undefined,
        isShowComprehensiveClueInPlan: false,
        campaignSchedule: {
            ...scheduleInitialValue,
            scheduleValue: getAllDaySelected(getIsConversionCampaign({
                marketingTargetId, optimizeTransTarget: undefined
            })),
            type: schedueTypeConfig.all
        }
    };
    if (!isEmpty(assets)) {
        initialAssetsAndOptimizeTransTarget = getInitialAssetsAndOptimizeTransTarget(params);
    }
    return {
        marketingTargetId,
        campaignName: initCampaignName(`${mtTextMap[B2B]}`),
        promotionScene: b2bPageId ? +pageType : promotionSceneType.store,
        isSetInitialAssetDataFromApi: true,
        ...initialAssetsAndOptimizeTransTarget,
        campaignBudget: {
            budgetType: budgetTypeConfig.NO_LIMIT,
            budget: '',
            sharedBudgetId: undefined,
            sharedBudget: ''
        },
        equipmentType: {
            equipmentType: FLOW_SCOPE.TOTAL
        },
        campaignTransTypes: [],
        campaignBid: '',
        campaignOcpcBid: '',
        campaignRegion: getRegionInitialValue({marketTarget: marketingTargetId})
    };
};

function campaignConversionPriceParams({
    values, marketingTargetId, assets
}, result) {
    const {
        optimizeTransTarget,
        campaignTransTypes,
        campaignOcpcBid
    } = values;
    const isOcpcCampaign = getIsConversionCampaign({
        marketingTargetId,
        ...values
    });
    if (!isOcpcCampaign) {
        return result;
    }
    result.campaignType.campaignDeviceBidStatus = false;
    result.campaignType.campaignBidType = campaignBidTypeConfig.oCPC;
    result.campaignType.campaignOcpcBidType = campaignOcpcBidTypeConfig.oCPC;
    result.campaignType.campaignTransTypes = campaignTransTypes;
    if (optimizeTransTarget === optimizeTransTargetType.comprehensiveClue) {
        result.campaignType.campaignTransTypes = [COMPREHENSIVE_CLUES];
    }
    if (isFcTransAssetsUser()) {
        // 处理默认资产
        result.campaignType.transAsset = transAssetOptionsEnum.BY_ASSET_ID;
        result.campaignType.transAssetId = assets[assets.length - 1];
    } else {
        // 根据后端要求，优化目标选择综合线索收集，数据来源传1000（不限）
        if (optimizeTransTarget === optimizeTransTargetType.comprehensiveClue) {
            result.campaignType.campaignCvSources = [CVSOURCES_UNLIMITED];
        } else {
            // 其他情况传固定5个的集合
            result.campaignType.campaignCvSources = getCvSourceLimitConfig(marketingTargetId, false);
        }
    }
    result.campaignType.campaignOcpcBid = +campaignOcpcBid;
    return result;
}

export const generateCampaignParams = campaignParamsPipeline(
    [
        campaignPromotionParams,
        campaignClickPriceParams,
        campaignConversionPriceParams,
        campaignBudgetParams,
        campaignSettingParams,
        campaignRegionParams,
        campaignScheduleParams
    ],
    {campaignType: {}}
);