import {
    FLOW_SCOPE, transAssetOptionsEnum, campaignOcpcBidTypeConfig, cvSourcesOptions,
    TransManagerModeType, ocpcBidTypeEnum
} from 'commonLibs/config/ocpc';
import {CPQL, mtTextMap, SUB_MARKET_TARGET_NORMAL} from 'commonLibs/config/marketTarget';
import AdType from 'commonLibs/config/adType';
import {isFcTransAndAssetFuheUser} from 'commonLibs/utils/getFlag';
import {initialValue as scheduleInitialValue} from 'app/containers/campaigns/list/editor/schedule/config';
import {getAllDaySelected} from 'app/containers/campaigns/list/editor/schedule/utils';
import {getRegionInitialValue} from 'app/containers/fcNew/campaign/util';
import {schedueTypeConfig} from 'app/containers/fcNew/campaign/schedule/config';
import {budgetTypeConfig} from '../budget/config';
import {initCampaignName} from '../campaignName';
import {CrowdType} from '../crowd/config';
import {
    campaignParamsPipeline,
    campaignClickPriceParams,
    campaignProjectIdParams,
    campaignConversionPriceParams,
    campaignBudgetParams,
    campaignSettingParams,
    campaignRegionParams,
    campaignScheduleParams,
    campaignCrowdParams,
    campaignSubMarketingTargetParams,
    campaignBusinessPointParams
} from '../util/generateParams';

export const fields = {};

export function getInitialData({marketingTargetId, projectIdFromQuery, bizId}) {
    return {
        marketingTargetId,
        campaignBidType: ocpcBidTypeEnum.OCPC,
        campaignOcpcBidType: campaignOcpcBidTypeConfig.oCPC,
        ...(isFcTransAndAssetFuheUser() ? {transManagerMode: TransManagerModeType.unlimited} : {}),
        transAsset: {
            assets: [],
            option: transAssetOptionsEnum.BY_ASSET_ID
        },
        cvSources: {
            option: cvSourcesOptions.UNLIMITED,
            source: []
        },
        tipInfo: {
            deletedCvSource: [], // 修改设备导致删除的数据源
            deletedTransTypes: [] // 修改数据源导致删除的目标转化
        },
        bindProject: projectIdFromQuery ? {projectId: +projectIdFromQuery} : {},
        campaignTransTypes: [],
        campaignBid: '',
        campaignOcpcBid: '',
        campaignBudget: {
            budgetType: budgetTypeConfig.NO_LIMIT,
            budget: '',
            sharedBudgetId: undefined,
            sharedBudget: ''
        },
        campaignRegion: getRegionInitialValue({marketTarget: marketingTargetId}),
        campaignSchedule: {
            ...scheduleInitialValue,
            scheduleValue: getAllDaySelected(true),
            type: schedueTypeConfig.all
        },
        equipmentType: {
            equipmentType: FLOW_SCOPE.TOTAL
        },
        crowd: {
            type: [CrowdType.UMLIMITED],
            orientCrowd: {},
            excludeCrowd: {}
        },
        campaignName: initCampaignName(mtTextMap[CPQL]),
        subMarketingTargetId: SUB_MARKET_TARGET_NORMAL,
        businessPoint: {
            path: Number(bizId) ? [{businessPointId: bizId}] : [],
            isBrand: Number(bizId) === 99,
            isExist: true
        }
    };
}

export function campaignAdTypeParams(_, result) {
    result.campaignType.adType = AdType.NORMAL;
    return result;
}

export const generateCampaignParams = campaignParamsPipeline(
    [
        campaignClickPriceParams,
        campaignConversionPriceParams,
        campaignProjectIdParams,
        campaignBudgetParams,
        campaignSettingParams,
        campaignRegionParams,
        campaignScheduleParams,
        campaignCrowdParams,
        campaignAdTypeParams,
        campaignSubMarketingTargetParams,
        campaignBusinessPointParams
    ],
    {campaignType: {}, crowdBindTypes: null}
);
