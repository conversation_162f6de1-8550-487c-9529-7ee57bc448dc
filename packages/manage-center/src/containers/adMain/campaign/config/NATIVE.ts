import {
    FLOW_SCOPE, transAssetOptionsEnum, campaignOcpcBidTypeConfig, cvSourcesOptions,
    TransManagerModeType, ocpcBidTypeEnum, COMPREHENSIVE_CLUES
} from 'commonLibs/config/ocpc';
import {NATIVE, mtTextMap, SUB_MARKET_TARGET_NOTE} from 'commonLibs/config/marketTarget';
import AdType from 'commonLibs/config/adType';
import {isFcTransAndAssetFuheUser} from 'commonLibs/utils/getFlag';
import {initialValue as scheduleInitialValue} from 'app/containers/campaigns/list/editor/schedule/config';
import {getAllDaySelected} from 'app/containers/campaigns/list/editor/schedule/utils';
import {getRegionInitialValue} from 'app/containers/fcNew/campaign/util';
import {schedueTypeConfig} from 'app/containers/fcNew/campaign/schedule/config';
import {budgetTypeConfig} from '../budget/config';
import {initCampaignName} from '../campaignName';
import OptimizeTransTarget, {transTargetMap} from '../optimizeTransTarget/native';
import NativeSubMarketTarget from '../subMarketTarget/native';
import CampaignTransTypes from '../campaignTransTypes/native/index';
import BJHUserInfo from '../BJHUserInfo';
import {CrowdType} from '../crowd/config';
import {
    campaignParamsPipeline,
    campaignClickPriceParams,
    campaignProjectIdParams,
    campaignConversionPriceParams,
    campaignBudgetParams,
    campaignSettingParams,
    campaignRegionParams,
    campaignScheduleParams,
    campaignCrowdParams
} from '../util/generateParams';

const isSupportTransType = campaignOcpcBidType => {
    return [
        campaignOcpcBidTypeConfig.cvMax,
        campaignOcpcBidTypeConfig.eCPC,
        campaignOcpcBidTypeConfig.oCPC
    ].includes(campaignOcpcBidType);
};

export const fields = {
    subMarketingTargetId: {
        field: 'subMarketingTargetId',
        label: '营销场景',
        use: [NativeSubMarketTarget],
        componentProps: ['bindProject']
    },
    optimizeTransTarget: {
        field: 'optimizeTransTarget',
        label: '优化目标',
        showRequiredMark: true,
        use: [OptimizeTransTarget],
        tip: 'optimizeTransTarget',
        visible: ({
            campaignOcpcBidType
        }) => {
            return isSupportTransType(campaignOcpcBidType);
        }
    },
    campaignTransTypes: {
        field: 'campaignTransTypes',
        use: [CampaignTransTypes],
        validators: [
            {
                validator: function (value) {
                    if (value.length === 0) {
                        return '请选择目标转化';
                    }
                    return;
                }
            }
        ],
        visible: ({
            campaignOcpcBidType,
            optimizeTransTarget
        }) => {
            return isSupportTransType(campaignOcpcBidType)
            && optimizeTransTarget === transTargetMap.MORE;
        }
    },
    bjhUserInfo: {
        field: 'bjhUserInfo',
        label: '关联百家号',
        showRequiredMark: true,
        use: [BJHUserInfo],
        validators: [
            {
                validator: function (value) {
                    if (!value) {
                        return '请选择推广身份';
                    }
                    return;
                }
            }
        ]
    }
};


export function getInitialData({marketingTargetId, projectIdFromQuery}) {
    return {
        marketingTargetId,
        campaignBidType: ocpcBidTypeEnum.OCPC,
        campaignOcpcBidType: campaignOcpcBidTypeConfig.oCPC,
        ...(isFcTransAndAssetFuheUser() ? {transManagerMode: TransManagerModeType.unlimited} : {}),
        transAsset: {
            assets: [],
            option: transAssetOptionsEnum.BY_ASSET_ID
        },
        cvSources: {
            option: cvSourcesOptions.UNLIMITED,
            source: []
        },
        tipInfo: {
            deletedCvSource: [], // 修改设备导致删除的数据源
            deletedTransTypes: [] // 修改数据源导致删除的目标转化
        },
        bindProject: projectIdFromQuery ? {projectId: +projectIdFromQuery} : {},
        campaignTransTypes: [],
        campaignBid: '',
        campaignOcpcBid: '',
        campaignBudget: {
            budgetType: budgetTypeConfig.NO_LIMIT,
            budget: '',
            sharedBudgetId: undefined,
            sharedBudget: ''
        },
        campaignRegion: getRegionInitialValue({marketTarget: marketingTargetId}),
        campaignSchedule: {
            ...scheduleInitialValue,
            scheduleValue: getAllDaySelected(true),
            type: schedueTypeConfig.all
        },
        equipmentType: {
            equipmentType: FLOW_SCOPE.TOTAL
        },
        crowd: {
            type: [CrowdType.UMLIMITED],
            orientCrowd: {},
            excludeCrowd: {}
        },
        campaignName: initCampaignName(mtTextMap[NATIVE]),
        subMarketingTargetId: SUB_MARKET_TARGET_NOTE,
        optimizeTransTarget: COMPREHENSIVE_CLUES
    };
};

export function campaignAdTypeParams(_, result) {
    result.campaignType.adType = AdType.NORMAL;
    return result;
};

export function campaignSubMarketingTargetParams({values}, result) {
    const {
        subMarketingTargetId
    } = values;

    result.campaignType.subMarketingTargetId = subMarketingTargetId;

    return result;
};

function campaignTransTypesParams({values}, result) {
    const {
        optimizeTransTarget,
        campaignOcpcBidType
    } = values;

    if (isSupportTransType(campaignOcpcBidType)
        && optimizeTransTarget !== transTargetMap.MORE) {
        result.campaignType.campaignTransTypes = [optimizeTransTarget];
    }

    return result;
};

function campaignBJHParams({values}, result) {
    const {
        bjhUserInfo
    } = values;
    result.campaignType.bjhUserInfo = bjhUserInfo;
    return result;
};

export const generateCampaignParams = campaignParamsPipeline(
    [
        campaignClickPriceParams,
        campaignConversionPriceParams,
        campaignProjectIdParams,
        campaignBudgetParams,
        campaignSettingParams,
        campaignRegionParams,
        campaignScheduleParams,
        campaignCrowdParams,
        campaignAdTypeParams,
        campaignSubMarketingTargetParams,
        campaignTransTypesParams,
        campaignBJHParams
    ],
    {campaignType: {}, crowdBindTypes: null}
);
