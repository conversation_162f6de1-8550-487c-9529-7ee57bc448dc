import queryString from 'query-string';
import {
    cvSourcesOptions, campaignOcpcBidTypeConfig,
    transAssetOptionsEnum, COMPREHENSIVE_CLUES, campaignBidTypeConfig,
    ORDER_SUCCESS_CT, CVSOURCES_UNLIMITED, assetTypeEnum,
    ocpcBidTypeEnum, TransManagerModeType, FLOW_SCOPE
} from 'commonLibs/config/ocpc';
import {getComprehensiveClueInPlanTrans} from 'commonLibs/TransEditor/util';
import {initialValue as scheduleInitialValue} from 'app/containers/campaigns/list/editor/schedule/config';
import {getAllDaySelected} from 'app/containers/campaigns/list/editor/schedule/utils';
import {getRegionInitialValue} from 'app/containers/fcNew/campaign/util';
import {schedueTypeConfig} from 'app/containers/fcNew/campaign/schedule/config';
import {promotionSceneType} from 'commonLibs/config/promotionScene';
import {optimizeTransTargetType, getOptimizeFromTransType,
    getAllOptimizeTransTargetOptions} from 'commonLibs/config/optimizeTransTarget';
import {getAvailableTransTypes, getMTStoreCvSource} from 'commonLibs/utils/getAssetAndTransTypes';
import {budgetTypeConfig} from '../budget/config';
import {initCampaignName} from '../campaignName';
import {getIsConversionCampaign} from '../util/common';
import {isFcTransAssetsUser, isCVMaxCampaignBudgetUser, isFcTransAndAssetFuheUser} from 'commonLibs/utils/getFlag';
import OptimizeTransTarget from '../optimizeTransTarget';
import PromotionScene from '../promotionScene';
import CampaignTransTypes from '../campaignTransTypes/STORE';
import {
    campaignParamsPipeline,
    campaignPromotionParams,
    campaignProjectIdParams,
    campaignClickPriceParams,
    campaignBudgetParams,
    campaignSettingParams,
    campaignRegionParams,
    campaignScheduleParams,
    campaignBusinessPointParams
} from '../util/generateParams';
import {mtTextMap, STORE} from 'commonLibs/config/marketTarget';
import ALL_ADTYPE from 'commonLibs/config/adType';

export function getInitialAssetsAndOptimizeTransTarget({
    marketingTargetId, normalizedTransAssets, availableCvSources, eligibleInfo, assets,
    cvSources, transManagerMode, transAsset, availableTransTypes: initAvailableTransTypes
}) {
    const assetId = assets[assets.length - 1];
    const transAssetType = normalizedTransAssets?.assetsData?.[assetId]?.assetType;
    // 新流程综合线索收集依赖于是否有基木鱼类型资产，因此在资产名单内，cvSourceOptions默认值为FILTER，否则为UNLIMITED
    const cvSourceOptions = isFcTransAndAssetFuheUser() ? (
        transManagerMode === TransManagerModeType.cvSource ? cvSourcesOptions.FILTER : cvSourcesOptions.UNLIMITED
    ) : (
        isFcTransAssetsUser() ? cvSourcesOptions.FILTER : cvSourcesOptions.UNLIMITED
    );
    const isShowComprehensiveClueInPlan = getComprehensiveClueInPlanTrans(
        availableCvSources, cvSourceOptions, marketingTargetId, transAssetType, transManagerMode
    );
    const availableTransTypes = getAvailableTransTypes({
        assets,
        normalizedTransAssets,
        eligibleInfo,
        marketingTargetId,
        transAsset,
        cvSources,
        transManagerMode,
        availableCvSources,
        isProjectLevel: false
    });
    const availabledOptimizeTransTarget = getAllOptimizeTransTargetOptions({
        isShowComprehensiveClueInPlan, availableTransTypes
    }).filter(({disabled}) => !disabled).map(({value}) => value);
    const optimizeTransTarget = availabledOptimizeTransTarget[0];
    if (isFcTransAndAssetFuheUser()) {
        return {
            ...(transManagerMode === TransManagerModeType.asset ? {
                transAsset: {
                    assets: [assetTypeEnum.JMY, +assetId],
                    option: transAssetOptionsEnum.BY_ASSET_ID
                }
            } : {}),
            optimizeTransTarget,
            isShowComprehensiveClueInPlan
        };
    }
    return {
        transAsset: {
            assets: [assetTypeEnum.JMY, +assetId],
            option: transAssetOptionsEnum.BY_ASSET_ID
        },
        optimizeTransTarget,
        isShowComprehensiveClueInPlan // todo 资产名单内一定是true,名单外需要再看下
    };
}
export function getInitialData({
    marketingTargetId, projectIdFromQuery, bizId
}) {
    // 推广场景目前只支持推店铺，可以不读取url参数，直接写死
    // const {pageType, storePageId} = queryString.parse(location.search);
    const defaultPromotionScene = promotionSceneType.store;
    return {
        marketingTargetId,
        campaignName: initCampaignName(mtTextMap[STORE]),
        // promotionScene: storePageId ? (isNil(pageType) ? defaultPromotionScene : +pageType) : defaultPromotionScene,
        promotionScene: defaultPromotionScene,
        campaignSchedule: {
            ...scheduleInitialValue,
            // 初始默认值一定是转化计划，这里为true
            scheduleValue: getAllDaySelected(true),
            type: schedueTypeConfig.all
        },
        ...(isFcTransAndAssetFuheUser() ? {transManagerMode: TransManagerModeType.unlimited} : {}),
        transAsset: {
            assets: [],
            option: transAssetOptionsEnum.BY_ASSET_ID
        },
        cvSources: {
            option: cvSourcesOptions.UNLIMITED,
            source: []
        },
        tipInfo: {
            deletedCvSource: [], // 修改设备导致删除的数据源
            deletedTransTypes: [] // 修改数据源导致删除的目标转化
        },
        bindProject: projectIdFromQuery ? {projectId: +projectIdFromQuery} : {},
        optimizeTransTarget: optimizeTransTargetType.transType,
        isShowComprehensiveClueInPlan: false,
        isSetInitialAssetDataFromApi: true,
        campaignBudget: {
            budgetType: budgetTypeConfig.NO_LIMIT,
            budget: '',
            sharedBudgetId: undefined,
            sharedBudget: ''
        },
        campaignTransTypes: [],
        campaignBid: {
            campaignBid: ''
        },
        campaignOcpcBid: '',
        campaignRegion: getRegionInitialValue({marketTarget: marketingTargetId}),
        campaignBidType: ocpcBidTypeEnum.OCPC,
        campaignOcpcBidType: campaignOcpcBidTypeConfig.oCPC,
        equipmentType: {
            equipmentType: FLOW_SCOPE.MOBILE
        },
        businessPoint: {
            path: Number(bizId) ? [{businessPointId: bizId}] : [],
            isBrand: Number(bizId) === 99,
            isExist: true
        }
    };
}

export const fields = {
    promotionScene: {
        field: 'promotionScene',
        label: '推广场景',
        showRequiredMark: true,
        use: [PromotionScene],
        visible: ({bindProject}) => !bindProject?.projectId
    },
    optimizeTransTarget: {
        field: 'optimizeTransTarget',
        label: '优化目标',
        showRequiredMark: true,
        use: [OptimizeTransTarget],
        componentProps: [
            'isSetInitialAssetDataFromApi', 'isShowComprehensiveClueInPlan',
            'transAsset', 'cvSources', 'transManagerMode'
        ],
        tip: 'optimizeTransTarget',
        visible: formData => formData.campaignOcpcBidType !== campaignOcpcBidTypeConfig.CPC
    },
    campaignTransTypes: {
        field: 'campaignTransTypes',
        use: [CampaignTransTypes],
        componentProps: ['campaignOcpcBidType', 'transAsset', 'cvSources', 'transManagerMode'],
        validators: [
            {
                validator: function (value) {
                    if (value.length === 0) {
                        return '请选择目标转化';
                    }
                    return;
                }
            }
        ],
        visible: formData => {
            return formData.optimizeTransTarget === optimizeTransTargetType.transType
                && formData.campaignOcpcBidType !== campaignOcpcBidTypeConfig.CPC;
        }
    }
};

export function campaignAdTypeParams(_, result) {
    result.campaignType.adType = ALL_ADTYPE.NEW_STORE;
    return result;
}

function campaignConversionPriceParams({
    values, marketingTargetId, eligibleInfo
}, result) {
    const {
        optimizeTransTarget,
        campaignTransTypes,
        campaignOcpcBid,
        campaignOcpcBidType,
        campaignBudget,
        transAsset,
        transManagerMode,
        cvSources
    } = values;
    const isOcpcCampaign = getIsConversionCampaign({
        marketingTargetId,
        ...values
    });
    if (!isOcpcCampaign) {
        return result;
    }
    result.campaignType.campaignDeviceBidStatus = false;
    result.campaignType.campaignBidType = campaignBidTypeConfig.oCPC;
    result.campaignType.campaignOcpcBidType = campaignOcpcBidType;
    result.campaignType.campaignTransTypes = campaignTransTypes;
    if (optimizeTransTarget === optimizeTransTargetType.comprehensiveClue) {
        result.campaignType.campaignTransTypes = [COMPREHENSIVE_CLUES];
    }
    if (optimizeTransTarget === optimizeTransTargetType.order) {
        result.campaignType.campaignTransTypes = [ORDER_SUCCESS_CT]; // 订单提交成功
    }
    if (transManagerMode !== undefined) {
        result.campaignType.transManagerMode = transManagerMode;
        if (transManagerMode === TransManagerModeType.unlimited) {
            result.campaignType.campaignCvSources = [CVSOURCES_UNLIMITED];
            result.campaignType.transAsset = transAssetOptionsEnum.UNLIMITED;
        }
        if (transManagerMode === TransManagerModeType.cvSource) {
            const {source} = cvSources;
            result.campaignType.campaignCvSources = source;
        }
        if (transManagerMode === TransManagerModeType.asset) {
            const {assets, option} = transAsset;
            result.campaignType.transAsset = option;
            const [, transAssetId] = assets;
            result.campaignType.transAssetId = transAssetId;
        }
    }
    else {
        if (isFcTransAssetsUser()) {
            const {assets, option} = transAsset;
            result.campaignType.transAsset = option;
            const [, transAssetId] = assets;
            result.campaignType.transAssetId = transAssetId;
        }
        else {
            if (
                optimizeTransTarget === optimizeTransTargetType.comprehensiveClue
                || getOptimizeFromTransType(campaignTransTypes) === optimizeTransTargetType.comprehensiveClue
            ) {
                result.campaignType.campaignCvSources = [CVSOURCES_UNLIMITED];
            }
            else {
                result.campaignType.campaignCvSources = getMTStoreCvSource({
                    marketingTargetId,
                    eligibleInfo,
                    campaignTransTypes,
                    isProjectLevel: false
                });
            }
        }
    }
    // 出价
    if (campaignOcpcBidType !== campaignOcpcBidTypeConfig.cvMax) {
        result.campaignType.campaignOcpcBid = +campaignOcpcBid;
    }
    else if (isCVMaxCampaignBudgetUser() && campaignBudget.transPrice) {
        result.campaignType.campaignOcpcBid = +campaignBudget.transPrice;
    }
    return result;
}


export const generateCampaignParams = campaignParamsPipeline(
    [
        campaignPromotionParams,
        campaignProjectIdParams,
        campaignClickPriceParams,
        campaignConversionPriceParams,
        campaignBudgetParams,
        campaignSettingParams,
        campaignRegionParams,
        campaignScheduleParams,
        campaignAdTypeParams,
        campaignBusinessPointParams
    ],
    {campaignType: {}}
);