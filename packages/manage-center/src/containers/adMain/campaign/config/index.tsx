import {uniq, without, get} from 'lodash-es';
import {
    B2B,
    STORE,
    CPQL,
    NATIVE,
    defaultSubMarketingTargetIdMap,
    SUB_MARKET_TARGET_NORMAL,
    SUB_MARKET_TARGET_LIVE
} from 'commonLibs/config/marketTarget';
import {budgetValueValidate} from '../budget/validator';
import {
    transAssetOptionsEnum, campaignOcpcBidTypeConfig, CVSOURCES_UNLIMITED, COMPREHENSIVE_CLUES,
    cvSourcesOptions, datasource, strategyOcpcBidTypeValueMapByKey,
    TransManagerModeOptionConfig, TransManagerModeType, ocpcBidTypeEnum, assetTypeEnum
} from 'commonLibs/config/ocpc';
import {isFcTransAssetsUser, isFcTransAndAssetFuheUser, isFcLiveUser} from 'commonLibs/utils/getFlag';
import {isCvsourceEquipmentTypeDisabled} from 'commonLibs/utils/ocpc';
import SelectCard from 'commonLibs/components/selectCard';
import {getIsConversionCampaign} from '../util/common';
import {validateOcpcBid as validateCampaignOcpcBid} from '../campaignOcpcBid/validator';
import CampaignOcpcBid from '../campaignOcpcBid';
import RegionEditor from '../regionEditor';
import {validator as regionValidator} from '../regionEditor/validator';
import CampaignSchedule from '../campaignSchedule';
import {validator as scheduleValidator} from '../campaignSchedule/validator';
import CampaignBudget from '../budget';
import EquipmentType from '../equipmentType';
import CampaignTransTypes, {isRequired as transTypesIsRequired} from '../campaignTransTypes/common';
import {getCampaignFields} from 'app/containers/adMain/config/formFields';
import CampaignBid from '../campaignBid';
import {validateCampaignBid} from '../campaignBid/validator';
import {optimizeTransTargetType} from 'commonLibs/config/optimizeTransTarget';
import MarketTarget from '../marketingTarget';
import BindProject, {BindProjectTip} from '../bindProject';
import SubMarketTarget from '../subMarketTarget';
import CvSources, {isRequired as cvSourcesIsRequired} from '../cvSources';
import TransAsset, {isRequired as transAssetIsRequired} from '../transAsset';
import CampaignTransManagerMode from '../campaignTransManageMode';
import {CampaignOcpcBidTypeOptions, campaignBidTypeOptionForStore} from '../campaignOcpcBidType/config';
import {CrowdType} from '../crowd/config';
import CrowdTypeSelect from '../crowd/checkbox';
import OrientCrowd from '../crowd/orientCrowd';
import ExcludeCrowd from '../crowd/excludeCrowd';
import {convertFieldToConfig, filterWatchByFields} from '../../utils';
import {budgetTypeConfig} from '../budget/config';
import {isInQinggeIframe} from 'commonLibs/utils/isInIframe';
import {globalAnchorInfo} from '../anchorInfo';
import {BusinessPoint, validatePathsLen} from 'commonLibs/businessPoint';

const baseFields = {
    marketingTargetId: {
        field: 'marketingTargetId',
        label: '',
        use: [MarketTarget]
    },
    campaignBidType: {
        field: 'campaignBidType',
        label: '出价方式',
        showRequiredMark: true,
        componentProps: ({bindProject}) => {
            // 当选择项目出价模式为点击或增强模式时，不允许使用转化出价方式
            const projectBidType = get(bindProject, 'projectInfo.bidType');
            return {
                options: [
                    {label: '点击', value: ocpcBidTypeEnum.CPC},
                    {
                        label: '转化', value: ocpcBidTypeEnum.OCPC,
                        disabled: projectBidType === ocpcBidTypeEnum.CPC
                    }
                ]
            };
        },
        use: ['RadioGroup']
    },
    campaignOcpcBidType: {
        field: 'campaignOcpcBidType',
        label: '出价模式',
        showRequiredMark: true,
        componentProps: ({campaignBidType, marketingTargetId}) => {
            const allOptions = marketingTargetId === STORE ? campaignBidTypeOptionForStore : CampaignOcpcBidTypeOptions;
            return {
                options: allOptions.filter(v => v.bidType === campaignBidType)
            };
        },
        use: [SelectCard]
    },
    transManagerMode: {
        field: 'transManagerMode',
        label: '转化管理模式',
        showRequiredMark: true,
        use: [CampaignTransManagerMode, {
            options: TransManagerModeOptionConfig
        }],
        visible: ({
            campaignOcpcBidType
        }) => {
            return isFcTransAndAssetFuheUser() && [
                campaignOcpcBidTypeConfig.cvMax,
                campaignOcpcBidTypeConfig.eCPC,
                campaignOcpcBidTypeConfig.oCPC
            ].includes(campaignOcpcBidType);
        },
        componentProps: ({subMarketingTargetId}) => ({subMarketingTargetId})
    },
    cvSources: {
        field: 'cvSources',
        label: '数据来源',
        use: [CvSources],
        componentProps: ['marketingTargetId', 'campaignOcpcBidType', 'equipmentType', 'tipInfo', 'transManagerMode'],
        showRequiredMark: cvSourcesIsRequired,
        validators: [
            {
                validator: function (value, formData) {
                    const {
                        source,
                        option
                    } = value;
                    if (
                        cvSourcesIsRequired(formData)
                        && option === cvSourcesOptions.FILTER
                        && !source.length
                    ) {
                        return '请选择数据来源';
                    }
                    return;
                }
            }
        ],
        visible: ({
            campaignOcpcBidType,
            transManagerMode
        }) => {
            return (
                (!isFcTransAndAssetFuheUser() && !isFcTransAssetsUser())
                || transManagerMode === TransManagerModeType.cvSource
            ) && [
                campaignOcpcBidTypeConfig.cvMax,
                campaignOcpcBidTypeConfig.eCPC,
                campaignOcpcBidTypeConfig.oCPC
            ].includes(campaignOcpcBidType);
        }
    },
    transAsset: {
        field: 'transAsset',
        label: '转化资产',
        use: [TransAsset],
        componentProps: ({
            marketingTargetId,
            subMarketingTargetId,
            campaignOcpcBidType,
            equipmentType,
            isSetInitialAssetDataFromApi,
            transManagerMode
        }) => {
            const isConversionCampaign = getIsConversionCampaign({
                marketingTargetId,
                campaignOcpcBidType
            });
            return {
                isConversionCampaign, equipmentType, marketingTargetId,
                campaignOcpcBidType, isSetInitialAssetDataFromApi,
                transManagerMode, subMarketingTargetId
            };
        },
        showRequiredMark: transAssetIsRequired,
        validators: [
            {
                validator: function (value, formData) {
                    const {
                        assets,
                        option
                    } = value;
                    if (
                        transAssetIsRequired(formData)
                        && option === transAssetOptionsEnum.BY_ASSET_ID
                        && !assets.length) {
                        return '请选择指定资产';
                    }
                    return;
                }
            }
        ],
        visible: ({
            campaignOcpcBidType,
            transManagerMode
        }) => {
            return (
                (!isFcTransAndAssetFuheUser() && isFcTransAssetsUser())
                || transManagerMode === TransManagerModeType.asset
            ) && [
                campaignOcpcBidTypeConfig.cvMax,
                campaignOcpcBidTypeConfig.eCPC,
                campaignOcpcBidTypeConfig.oCPC
            ].includes(campaignOcpcBidType);
        }
    },
    campaignTransTypes: {
        field: 'campaignTransTypes',
        label: '优化目标',
        use: [CampaignTransTypes],
        componentProps: [
            'campaignOcpcBidType', 'transAsset', 'cvSources',
            'transManagerMode', 'subMarketingTargetId'
        ],
        showRequiredMark: transTypesIsRequired,
        validators: [
            {
                validator: function (value, formData) {
                    if (
                        transTypesIsRequired(formData)
                        && !value.length
                    ) {
                        return '请选择优化目标';
                    }
                    return;
                }
            }
        ],
        visible: ({
            campaignOcpcBidType, marketingTargetId, optimizeTransTarget
        }) => {
            if (marketingTargetId === B2B) {
                return optimizeTransTarget === optimizeTransTargetType.transType;
            }
            return [
                campaignOcpcBidTypeConfig.cvMax,
                campaignOcpcBidTypeConfig.eCPC,
                campaignOcpcBidTypeConfig.oCPC
            ].includes(campaignOcpcBidType);
        }
    },
    campaignBid: {
        field: 'campaignBid',
        label: '出价',
        showRequiredMark: true, // 这里用rules没用因为自定义组件的值是对象
        validators: [
            {
                validator: function (value) {
                    const errors = [
                        validateCampaignBid(value)
                    ].filter(v => v);
                    if (errors.length > 0) {
                        return errors;
                    }
                    return;
                }
            }
        ],
        use: [CampaignBid],
        visible: ({
            marketingTargetId, campaignOcpcBidType, optimizeTransTarget
        }) => {
            return !getIsConversionCampaign({
                marketingTargetId,
                campaignOcpcBidType,
                optimizeTransTarget
            });
        }
    },
    campaignOcpcBid: {
        field: 'campaignOcpcBid',
        label: '目标转化出价',
        rules: [['required', '请填写目标转化出价']],
        validators: [
            {
                validator: function (value, formData) {
                    if (!value) {
                        return '请填写目标转化出价';
                    }
                    const {campaignBudget} = formData;
                    return validateCampaignOcpcBid(
                        +value,
                        +campaignBudget.budget,
                        +campaignBudget.sharedBudget,
                        campaignBudget.budgetType,
                    );
                }
            }
        ],
        use: [CampaignOcpcBid],
        visible: ({
            marketingTargetId, campaignOcpcBidType, optimizeTransTarget
        }) => {
            return getIsConversionCampaign({
                marketingTargetId,
                campaignOcpcBidType,
                optimizeTransTarget
            }) && campaignOcpcBidType !== campaignOcpcBidTypeConfig.cvMax;
        },
        componentProps: ['campaignTransTypes', 'campaignBudget', 'optimizeTransTarget']
    },
    campaignBudget: {
        field: 'campaignBudget',
        rules: [
            ['required']
        ],
        label: '预算',
        use: [CampaignBudget],
        componentProps: ({
            campaignOcpcBid,
            campaignTransTypes,
            campaignOcpcBidType,
            optimizeTransTarget,
            bindProject
        }) => {
            // 如果加入项目，不允许设置共享预算
            return {
                disabledSharedBudget: bindProject?.projectId && !bindProject?.projectInfo?.sharedBudget,
                disabledBudget: !!bindProject?.projectInfo?.sharedBudget,
                campaignOcpcBid,
                campaignTransTypes,
                campaignOcpcBidType,
                optimizeTransTarget,
                bindProject
            };
        },
        validators: [
            {
                validator: function (value, formData) {
                    if (!value) {
                        return;
                    }
                    const {campaignOcpcBid, optimizeTransTarget, campaignOcpcBidType} = formData;
                    const {budgetType, budget, sharedBudgetId, sharedBudget} = value;
                    return budgetValueValidate({
                        budgetType,
                        budgetValue: +budget,
                        sharedBudgetId,
                        campaignOcpcBid: +campaignOcpcBid,
                        sharedBudget: +sharedBudget,
                        optimizeTransTarget,
                        campaignOcpcBidType
                    });
                }
            }
        ]
    },
    equipmentType: {
        field: 'equipmentType',
        label: '设备',
        use: [EquipmentType],
        rules: [
            ['required']
        ],
        componentProps: ['subMarketingTargetId']
    },
    campaignRegion: {
        field: 'campaignRegion',
        label: '地域',
        use: [RegionEditor],
        rules: [
            ['required']
        ],
        componentProps: ({
            marketingTargetId,
            optimizeTransTarget,
            campaignOcpcBidType
        }) => {
            const isConversionCampaign = getIsConversionCampaign({
                marketingTargetId,
                optimizeTransTarget,
                campaignOcpcBidType
            });
            return {isConversionCampaign, optimizeTransTarget};
        },
        validators: [regionValidator]
    },
    bindProject: {
        field: 'bindProject',
        label: '加入项目',
        tip: 'bindProject',
        use: [BindProject],
        componentProps: ['marketingTargetId', 'promotionScene']
    },
    bindProjectTip: {
        field: 'bindProjectTip',
        label: null,
        use: [BindProjectTip],
        componentProps: ['marketingTargetId'],
        visible: formData => formData?.bindProject?.projectId
    },
    subMarketingTargetId: {
        field: 'subMarketingTargetId',
        label: '营销场景',
        use: [SubMarketTarget],
        componentProps: ['bindProject'],
        visible: () => {
            const anchorInfo = globalAnchorInfo.getInfo();
            return isFcLiveUser() && !!anchorInfo?.rows?.length && isInQinggeIframe;
        }
    },
    campaignSchedule: {
        field: 'campaignSchedule',
        label: '时段',
        use: [CampaignSchedule],
        rules: [
            ['required']
        ],
        componentProps: ({
            marketingTargetId,
            optimizeTransTarget,
            campaignOcpcBidType
        }) => {
            const isConversionCampaign = getIsConversionCampaign({
                marketingTargetId,
                optimizeTransTarget,
                campaignOcpcBidType
            });
            return {isConversionCampaign, optimizeTransTarget};
        },
        validators: [scheduleValidator]
    },
    crowdType: {
        field: 'crowd.type',
        label: '人群',
        rules: [
            ['required']
        ],
        use: [CrowdTypeSelect]
    },
    orientCrowd: {
        field: 'crowd.orientCrowd',
        label: '',
        use: [OrientCrowd],
        componentProps: ({
            marketingTargetId,
            optimizeTransTarget,
            campaignOcpcBidType
        }) => {
            const isConversionCampaign = getIsConversionCampaign({
                marketingTargetId,
                optimizeTransTarget,
                campaignOcpcBidType
            });
            return {isConversionCampaign};
        },
        visible: ({
            crowd
        }) => {
            return crowd.type.includes(CrowdType.ORIENT_CROWD);
        }
    },
    excludeCrowd: {
        field: 'crowd.excludeCrowd',
        label: '',
        use: [ExcludeCrowd],
        componentProps: ['marketingTargetId', 'campaignOcpcBidType'],
        visible: ({
            crowd
        }) => {
            return crowd.type.includes(CrowdType.EXCLUDE_CROWD);
        }
    },
    campaignName: {
        field: 'campaignName',
        label: '计划名称',
        use: ['Input', {width: 400, minLen: 1, maxLen: 100}],
        rules: [
            ['required'],
            ['textRange', [1, 100]]
        ]
    },
    businessPoint: {
        field: 'businessPoint',
        label: '推广业务',
        use: [BusinessPoint],
        validators: [
            {
                validator: function (value, formData) {
                    const errors = [
                        validatePathsLen(value, 2)
                    ].filter(v => v);
                    if (errors.length > 0) {
                        return errors;
                    }
                    return '';
                }
            }
        ]
    }
};

const settingsGroup = {
    group: 'settingsGroup',
    use: ['ExpandableGroup', {title: ['展开', '收起']}]
};

const baseGroups = {
    settingsGroup
};

export const remoteConfigLoaders = {
    [B2B]: () => import('./B2B'),
    [STORE]: () => import('./STORE'),
    [CPQL]: () => import('./CPQL'),
    [NATIVE]: () => import('./NATIVE')
};

const getBaseWatch = () => ({
    'bindProject': (value = {}, formData) => {
        const {projectInfo = {}, projectId} = value;
        const {marketingTargetId} = formData;
        const {
            ocpcBidType, transAssetInfo = {}, transTypes = [], ocpcBid, promotionScene, transAsset,
            cvSources = [], sharedBudget, sharedBudgetId,
            transManagerMode, transTypeMultiBidStatus, bidType
        } = projectInfo;
        // 选择了绑定的项目，项目层级的出价模式、资产、优化目标、数据来源默认填充到计划层级
        if (projectId) {
            // 项目是销售线索 && 增强模式 && 资产类型为不限 带入资产及目标转化信息
            const isEcpcUnlimitedTransProject = marketingTargetId === CPQL
                    && ocpcBidType === strategyOcpcBidTypeValueMapByKey.ENHANCE_MODE
                    && transAsset === transAssetOptionsEnum.UNLIMITED;
            // 当选择项目出价模式为目标转化成本、最大转化时，按项目设置填充到计划不变
            // 当选择项目出价模式为增强模式时，按项目设置填充到计划，本地推广出价模式选择点击出价, 销售线索选择增强模式
            // 当选择项目出价模式为目标ROI时，出价模式选择目标转化成本
            formData.campaignBidType = bidType;
            if (ocpcBidType === strategyOcpcBidTypeValueMapByKey.ENHANCE_MODE && marketingTargetId === STORE) {
                formData.campaignOcpcBidType = campaignOcpcBidTypeConfig.CPC;
            }
            else if (ocpcBidType === strategyOcpcBidTypeValueMapByKey.ENHANCE_MODE && marketingTargetId === CPQL) {
                formData.campaignOcpcBidType = campaignOcpcBidTypeConfig.eCPC;
            }
            else if (ocpcBidType === strategyOcpcBidTypeValueMapByKey.TARGET_ROI) {
                formData.campaignOcpcBidType = campaignOcpcBidTypeConfig.oCPC;
            }
            else {
                formData.campaignOcpcBidType = ocpcBidType;
            }
            if (transManagerMode !== undefined) {
                formData.transManagerMode = transManagerMode;
            }
            // 设置资产
            if (transAssetInfo.transAssetId) {
                formData.transAsset = {
                    assets: [transAssetInfo.assetType, transAssetInfo.transAssetId],
                    option: transAssetOptionsEnum.BY_ASSET_ID
                };
            }
            if (isEcpcUnlimitedTransProject) {
                formData.transAsset = {
                    assets: [-1],
                    option: transAssetOptionsEnum.UNLIMITED
                };
            }
            // 设置优化目标
            if (transTypes.length && (transAssetInfo?.transAssetId || isEcpcUnlimitedTransProject)) {
                formData.optimizeTransTarget = optimizeTransTargetType.transType;
                formData.campaignTransTypes = transTypes;
            }
            // 设置数据来源
            if (cvSources?.length) {
                if (cvSources.includes(CVSOURCES_UNLIMITED)) {
                    formData.cvSources = {
                        option: cvSourcesOptions.UNLIMITED,
                        source: []
                    };
                }
                else {
                    formData.cvSources = {
                        option: cvSourcesOptions.FILTER,
                        source: cvSources
                    };
                    formData.campaignTransTypes = transTypes;
                }
            }
            // 不限时需要设置项目的 优化目标。但是本地推广的计划不代入，因为本地推广的计划层级不支持综合线索收集转化目标
            if (transManagerMode === TransManagerModeType.unlimited) {
                if (marketingTargetId === STORE && transTypes?.includes(COMPREHENSIVE_CLUES)) {
                    formData.optimizeTransTarget = optimizeTransTargetType.transType;
                    formData.campaignTransTypes = [];
                }
                else {
                    formData.campaignTransTypes = transTypes;
                }
            }
            // 非多目标差异化出价的项目，将项目出价设置到计划出价
            if (ocpcBid && !transTypeMultiBidStatus) {
                formData.campaignOcpcBid = ocpcBid;
            }
            // 绑定了项目, 如果项目使用共享预算，计划层级展示项目的共享预算且不能修改（最大转化除外，最大转化不带入项目预算相关信息)
            // 如果项目未使用共享预算，则计划层级的共享预算置灰，仅可选择不限/自定义
            if (sharedBudgetId && ocpcBidType !== strategyOcpcBidTypeValueMapByKey.SCALE_MODE) {
                formData.campaignBudget = {
                    budgetType: budgetTypeConfig.SHARE_BUDGET,
                    budget: '',
                    sharedBudgetId,
                    sharedBudget
                };
            }
            if (!sharedBudgetId && ocpcBidType !== strategyOcpcBidTypeValueMapByKey.SCALE_MODE) {
                formData.campaignBudget = {
                    budgetType: budgetTypeConfig.NO_LIMIT,
                    budget: '',
                    sharedBudgetId: undefined,
                    sharedBudget: ''
                };
            }
            // 最大转化仅可选自定义预算
            if (ocpcBidType === strategyOcpcBidTypeValueMapByKey.SCALE_MODE) {
                formData.campaignBudget = {
                    budgetType: budgetTypeConfig.DAILY_BUDGET,
                    budget: '',
                    sharedBudgetId: undefined,
                    sharedBudget: ''
                };
            }
            if (promotionScene && marketingTargetId === STORE) {
                formData.promotionScene = promotionScene;
            }
            // 已【加入项目】：营销场景与【加入项目】的保持一致且不可修改
            // 注：原生互动下，笔记可加入摘要项目，原来营销场景不用更改
            if ([CPQL].includes(marketingTargetId)) {
                formData.subMarketingTargetId = projectInfo?.subMarketingTargetId || SUB_MARKET_TARGET_NORMAL;
            }
        }
        else {
            // 清空项目的时候如果当前在共享预算，需要将预算切换为不限
            if (formData.campaignBudget?.budgetType === budgetTypeConfig.SHARE_BUDGET) {
                formData.campaignBudget = {
                    budgetType: budgetTypeConfig.NO_LIMIT,
                    budget: '',
                    sharedBudgetId: undefined,
                    sharedBudget: ''
                };
            }
            if (formData.transAsset?.assets?.[0] === assetTypeEnum.LIVE) { // 取消项目，如果之前是直播的资产，则重置到不限，清空优化目标
                formData.transManagerMode = TransManagerModeType.unlimited;
                formData.transAsset = {
                    assets: [],
                    option: transAssetOptionsEnum.UNLIMITED
                };
                formData.campaignTransTypes = [];
            }
            // 取消项目时，重置营销场景
            formData.subMarketingTargetId = defaultSubMarketingTargetIdMap[marketingTargetId]
                || SUB_MARKET_TARGET_NORMAL;
        }
    },
    'campaignBidType': (value, formData) => {
        const allOptions = formData.marketingTargetId === STORE
            ? campaignBidTypeOptionForStore : CampaignOcpcBidTypeOptions;
        formData.campaignOcpcBidType = allOptions.find(v => v.bidType === value).value;
    },
    campaignOcpcBidType(value, formData) {
        if (
            getIsConversionCampaign({marketingTargetId: formData.marketingTargetId, campaignOcpcBidType: value})
            && formData.transAsset.option === transAssetOptionsEnum.UNLIMITED
        ) {
            formData.transAsset = {
                option: transAssetOptionsEnum.BY_ASSET_ID,
                assets: []
            };
            formData.campaignTransTypes = [];
        }
        const {campaignBudget} = formData;
        // 最大转化不支持预算选择不限和共享预算，切换到自定义
        if (
            value === campaignOcpcBidTypeConfig.cvMax
        ) {
            formData.campaignBudget = {
                ...campaignBudget,
                budgetType: budgetTypeConfig.DAILY_BUDGET
            };
        }
        // 已选共享预算的项目&&出价模式不是放量时 需要将项目的共享预算带入
        if (!!formData.bindProject?.projectInfo?.sharedBudget && value !== campaignOcpcBidTypeConfig.cvMax) {
            formData.campaignBudget = {
                budgetType: budgetTypeConfig.SHARE_BUDGET,
                budget: '',
                sharedBudgetId: formData.bindProject?.projectInfo?.sharedBudgetId,
                sharedBudget: formData.bindProject?.projectInfo?.sharedBudget
            };
        }
    },
    transManagerMode(value, formData) {
        if (value === TransManagerModeType.unlimited) {
            formData.cvSources = {
                option: cvSourcesOptions.UNLIMITED,
                source: []
            };
            formData.transAsset = {
                option: transAssetOptionsEnum.UNLIMITED,
                assets: []
            };
        }
        if (value === TransManagerModeType.cvSource) {
            formData.optimizeTransTarget = formData.marketingTargetId === STORE
                ? optimizeTransTargetType.transType : undefined;
            formData.campaignTransTypes = [];
            formData.cvSources = {
                option: cvSourcesOptions.FILTER,
                source: []
            };
            formData.transAsset = {
                option: transAssetOptionsEnum.UNLIMITED,
                assets: []
            };
        }
        if (value === TransManagerModeType.asset) {
            formData.optimizeTransTarget = undefined;
            formData.cvSources = {
                option: cvSourcesOptions.UNLIMITED,
                source: []
            };
            formData.transAsset = {
                option: transAssetOptionsEnum.BY_ASSET_ID,
                assets: []
            };
        }
    },
    cvSources(value, formData) {
        const {option, source, eligibleInfo = {}} = value;
        const {campaignTransTypes} = formData;
        if (option === cvSourcesOptions.FILTER) {
            // 新数据源对应的备选转化类型（包括目标转化和深度转化）的集合
            const transTypesAfterDeleteCvSource = source.reduce((memo, item) => {
                return uniq(memo.concat(eligibleInfo[item] || []));
            }, []);
            // 删除某个数据源后，要删除已选转化类型（包括目标转化和深度转化）中只在该删除数据源中出现的转化类型
            // 即要删除已选转化类型中不在transTypesAfterDeleteCvSource里面的转化类型
            const selectedTransTypesAfterDeleteCvSource
                = campaignTransTypes.filter(itm => transTypesAfterDeleteCvSource.includes(+itm));
            // 要删除的目标转化类型
            const deletedTransTypesByCvSource = campaignTransTypes
                .filter(itm => !transTypesAfterDeleteCvSource.includes(+itm));
            if (deletedTransTypesByCvSource.length) {
                formData.campaignTransTypes = selectedTransTypesAfterDeleteCvSource;
                formData.tipInfo.deletedTransTypes = deletedTransTypesByCvSource;
            }
            else {
                if (formData.tipInfo.deletedTransTypes.length) {
                    formData.tipInfo.deletedTransTypes = [];
                }
            }
        }
    },
    transAsset(value, formData) {
        if (value.option === transAssetOptionsEnum.BY_ASSET_ID) {
            formData.campaignTransTypes = [];
        }
    },
    equipmentType(value, formData) {
        const {cvSources, marketingTargetId} = formData;
        if (cvSources.option === cvSourcesOptions.FILTER) {
            const disabledSelectedCvsources = cvSources.source.filter(sourceId => {
                const cvSourceItem = datasource.find(v => v[0] === sourceId);
                return isCvsourceEquipmentTypeDisabled({
                    equipmentType: value.equipmentType,
                    cvSourceItem,
                    marketingTargetId
                }).disabled;
            });
            if (disabledSelectedCvsources.length > 0) {
                const newCvsources = without(cvSources.source, ...disabledSelectedCvsources);
                formData.cvSources = {
                    ...cvSources,
                    source: newCvsources
                };
                formData.tipInfo.deletedCvSource = disabledSelectedCvsources;
            }
            else {
                if (formData.tipInfo.deletedCvSource.length) {
                    formData.tipInfo.deletedCvSource = [];
                }
            }
        }
    },
    promotionScene(value, formData) {
        formData.bindProject = {};
    },
    subMarketingTargetId(value, formData) {
        if (value === SUB_MARKET_TARGET_LIVE) {
            formData.transManagerMode = TransManagerModeType.asset;
            formData.cvSources = {
                option: cvSourcesOptions.UNLIMITED,
                source: []
            };
            formData.transAsset = {
                option: transAssetOptionsEnum.BY_ASSET_ID,
                assets: []
            };
        }
        if (value === SUB_MARKET_TARGET_NORMAL) {
            formData.transManagerMode = TransManagerModeType.unlimited;
            formData.transAsset = {
                assets: [],
                option: transAssetOptionsEnum.UNLIMITED
            };
        }
        formData.campaignTransTypes = [];
    }
});

export async function initialCampaignForm({
    marketingTargetId,
    assets,
    availableCvSources,
    normalizedTransAssets,
    eligibleInfo,
    projectIdFromQuery,
    bizId
}: {marketingTargetId: number}
) {
    const campaignFieldsConfig = getCampaignFields({marketingTargetId}).config;
    let remoteConfig = {};
    try {
        remoteConfig = await remoteConfigLoaders[marketingTargetId]();
    }
    catch (e) {
        console.error(`(marketingTargetId: ${marketingTargetId})是否存在配置文件`);
    }
    const {getInitialData, watch: remoteWatch} = remoteConfig;
    const fields = campaignFieldsConfig.map(({group, title, desc, fields, visible, groupProps = {}}) => {
        return {
            group,
            visible,
            use: ['Group', {title, desc, ...groupProps}],
            fields: fields.map(field => convertFieldToConfig({
                field,
                baseFields,
                baseGroups,
                remoteConfig
            }))
        };
    });
    const watch = filterWatchByFields({
        watch: {
            ...getBaseWatch(),
            ...remoteWatch
        },
        fields
    });

    const initialData = getInitialData({
        assets, marketingTargetId, availableCvSources,
        normalizedTransAssets, eligibleInfo, projectIdFromQuery, bizId
    });
    return {
        config: {
            fields,
            watch
        },
        initialData
    };
}

export async function initialAssetsAndOptimizeTransTarget({
    marketingTargetId,
    assets,
    availableCvSources,
    normalizedTransAssets,
    eligibleInfo,
    cvSources,
    transManagerMode,
    transAsset,
    availableTransTypes
}: {marketingTargetId: number}
) {
    let remoteConfig = {};
    try {
        remoteConfig = await remoteConfigLoaders[marketingTargetId]();
    }
    catch (e) {
        console.error(`(marketingTargetId: ${marketingTargetId})是否存在配置文件`);
    }
    const {getInitialAssetsAndOptimizeTransTarget} = remoteConfig;

    if (getInitialAssetsAndOptimizeTransTarget) {
        return getInitialAssetsAndOptimizeTransTarget({
            marketingTargetId, normalizedTransAssets, availableCvSources, eligibleInfo, assets,
            cvSources, transManagerMode, transAsset, availableTransTypes
        });
    }
    return {};
}
