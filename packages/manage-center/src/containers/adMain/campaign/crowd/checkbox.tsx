import {Checkbox} from '@baidu/one-ui';
import {CrowdTypeOptions, CrowdTypeText} from './config';

export default (props: {
    value: number[];
    onChange: (value: number[]) => void;
}) => {
    const {value, onChange} = props;
    const checkboxProps = {
        value,
        onChange
    };
    return (
        <Checkbox.Group {...checkboxProps}>
            {
                CrowdTypeOptions.map(({exclusive, options}) => (
                    options.map(option => (
                        <Checkbox.Button
                            key={option}
                            value={option}
                            exclusive={exclusive}
                        >
                            {CrowdTypeText[option]}
                        </Checkbox.Button>
                    ))
                ))
            }
        </Checkbox.Group>
    );
};