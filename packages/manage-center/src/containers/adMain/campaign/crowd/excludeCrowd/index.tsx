import {useCallback, useState} from 'react';
import {useRequest} from '@huse/request';
import {Transfer} from '@baidu/one-ui';
import {useParams} from 'commonLibs/route';
import {MAX_EXCLUDE_CROWD_COUNT} from 'commonLibs/config/campaign';
import {fetchCrowdList} from 'app/containers/adMain/apis/campaign/api';
import {CrowdType, CrowdTypeText} from '../config';
import SelectedItem from '../selectRender/selectedItem';
import {UnSelectedTitleExclude} from '../selectRender/unSelectedTitle';
import UnSelectedItem from '../selectRender/unSelectedItem';
import {SelectedTitleExclude} from '../selectRender/selectedTitle';
import CandidateFooterRender from '../footer';

export default (props: {
    value: object;
    onChange: (value: object) => void;
}) => {
    const {value, onChange} = props;
    const [search, setSearch] = useState('');
    const userId = useParams().userId;
    const {data, pending} = useRequest(fetchCrowdList, {
        userId, corwdType: CrowdType.EXCLUDE_CROWD, search
    });
    const {map: crowdMap = {}} = data || {};
    const handleSelect = useCallback(selectedCrowdsList => {
        const selectedCrowdsObj = selectedCrowdsList.reduce((result, crowdId) => {
            if (value[crowdId]) {
                result[crowdId] = value[crowdId];
            } else {
                result[crowdId] = {
                    ...crowdMap[crowdId],
                    crowdId: crowdId
                };
            }
            return result;
        }, {});
        onChange(selectedCrowdsObj);
    }, [onChange, value, crowdMap]);

    const handleSearch = useCallback(e => {
        setSearch((e.target.value && e.target.value.trim()) || '');
    }, [setSearch]);

    const transferProps = {
        treeName: CrowdTypeText[CrowdType.ORIENT_CROWD],
        loading: pending,
        loadingText: '加载中，请稍候...',
        showCandidateNum: true,
        showSelectedNum: true,
        allDataMap: data?.map,
        candidateList: data?.list,
        selectedList: Object.keys(value),
        maxSelectedNum: MAX_EXCLUDE_CROWD_COUNT,
        showSearchBox: true,
        handleSearch,
        handleSelect,
        handleSelectAll: handleSelect,
        handleDelete: handleSelect,
        handleDeleteAll: handleSelect,
        CandidateTitleRender: UnSelectedTitleExclude,
        CandidateItem: UnSelectedItem,
        candidateTreeStyle: {
            width: '330px'
        },
        SelectedTitleRender: SelectedTitleExclude,
        SelectedItem,
        selectedItemProps: {
            changeBidValue: () => {},
            crowdMap: value,
            isNeedCrowdType: true,
            allCrowdDataMap: data?.map
        },
        selectedTreeStyle: {
            width: '350px'
        },
        showCandidateFooter: true,
        CandidateFooterRender: <CandidateFooterRender
            orientCandidateList={data?.list}
            isOrientCrowds
        />
    };
    return <Transfer {...transferProps} />;
};