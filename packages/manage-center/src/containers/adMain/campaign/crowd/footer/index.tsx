import {Link} from '@baidu/one-ui';
import {useRouterFormatter, useParams} from 'commonLibs/route';
import './style.less';

export default ({orientCandidateList, isOrientCrowds}) => {
    const userId = useParams().userId;
    const formatUrl = useRouterFormatter({name: 'newCrowd'});
    const createCrowdUrl = formatUrl({
        userId,
        effectPageType: isOrientCrowds ? 'premiumcrowds' : 'excludecrowds'
    });
    const linkProps = {
        type: 'strong',
        target: '_blank',
        toUrl: createCrowdUrl
    };
    return orientCandidateList && !orientCandidateList.length
        ? (
            <div className="crowd-setting-no-list-tip">
                暂无人群数据，可前往
                <Link {...linkProps}>新建</Link>
            </div>
        )
        : '';
};