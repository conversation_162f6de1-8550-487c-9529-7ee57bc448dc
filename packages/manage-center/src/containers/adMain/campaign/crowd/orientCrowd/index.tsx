import {useCallback, useState} from 'react';
import {useRequest} from '@huse/request';
import {Transfer} from '@baidu/one-ui';
import {useParams} from 'commonLibs/route';
import {MAX_ORIENT_CROWD_COUNT} from 'commonLibs/config/campaign';
import {DEFAULT_NEW_BID_VALUE} from 'app/containers/crowds/config/common';
import {fetchCrowdList} from 'app/containers/adMain/apis/campaign/api';
import {getIsConversionCampaign} from '../../util/common';
import {CrowdType, CrowdTypeText} from '../config';
import SelectedItem from '../selectRender/selectedItem';
import {UnSelectedTitleOrient} from '../selectRender/unSelectedTitle';
import UnSelectedItem from '../selectRender/unSelectedItem';
import {SelectedTitleOrient} from '../selectRender/selectedTitle';
import CandidateFooterRender from '../footer';

export default (props: {
    value: object;
    onChange: (value: object) => void;
    isConversionCampaign: boolean;
}) => {
    const {value, onChange, isConversionCampaign} = props;
    const [search, setSearch] = useState('');
    const userId = useParams().userId;
    const {data, pending} = useRequest(fetchCrowdList, {
        userId, corwdType: CrowdType.ORIENT_CROWD, search
    });
    const {map: crowdMap = {}} = data || {};
    const handleSelect = useCallback(selectedCrowdsList => {
        const selectedCrowdsObj = selectedCrowdsList.reduce((result, crowdId) => {
            if (value[crowdId]) {
                result[crowdId] = value[crowdId];
            } else {
                result[crowdId] = {
                    ...crowdMap[crowdId],
                    crowdId: crowdId,
                    bidValue: DEFAULT_NEW_BID_VALUE
                };
            }
            return result;
        }, {});
        onChange(selectedCrowdsObj);
    }, [onChange, value, crowdMap]);

    const handleSearch = useCallback(e => {
        setSearch((e.target.value && e.target.value.trim()) || '');
    }, [setSearch]);

    const changeBidValue = useCallback((crowdId, e) => {
        const selectedCrowdObj = {
            ...value,
            [crowdId]: {
                ...value[crowdId],
                bidValue: e.target.value
            }
        };
        onChange(selectedCrowdObj);
    }, [value, onChange]);

    const transferProps = {
        treeName: CrowdTypeText[CrowdType.ORIENT_CROWD],
        loading: pending,
        loadingText: '加载中，请稍候...',
        showCandidateNum: true,
        showSelectedNum: true,
        allDataMap: data?.map,
        candidateList: data?.list,
        selectedList: Object.keys(value),
        maxSelectedNum: MAX_ORIENT_CROWD_COUNT,
        showSearchBox: true,
        handleSearch,
        handleSelect,
        handleSelectAll: handleSelect,
        handleDelete: handleSelect,
        handleDeleteAll: handleSelect,
        CandidateTitleRender: UnSelectedTitleOrient,
        CandidateItem: UnSelectedItem,
        candidateTreeStyle: {
            width: '400px'
        },
        SelectedTitleRender: SelectedTitleOrient,
        SelectedItem,
        selectedItemProps: {
            changeBidValue,
            crowdMap: value,
            isNeedCrowdType: true,
            allCrowdDataMap: data?.map,
            isConversionCampaign
        },
        isConversionCampaign,
        selectedTreeStyle: {
            width: '500px'
        },
        showCandidateFooter: true,
        CandidateFooterRender: <CandidateFooterRender
            orientCandidateList={data?.list}
            isOrientCrowds
        />
    };
    return <Transfer {...transferProps} />;
};