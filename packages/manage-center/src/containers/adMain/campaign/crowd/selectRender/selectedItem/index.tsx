/**
 * @file 人群 - 穿梭框右侧 - 已选中的人群
 * <AUTHOR>
 * @date 2020/07/09
 */
import {partial} from 'lodash-es';
import {NumberInput} from '@baidu/one-ui';
import classNames from 'classnames';
import {
    EFFECT_TYPE_CONFIG,
    EFFECT_TYPE_LIST_TEXT
} from 'app/containers/crowds/config/orientMethod';
import {CrowdStatusKey, CrowdStatus} from 'app/containers/fcNew/campaign/crowd/config';
import {DEFAULT_NEW_BID_VALUE} from 'app/containers/crowds/config/common';
import {getRangeBid} from 'app/containers/crowds/editor/util';
import './style.less';

const step = 0.01;

export default function (props) {
    const {
        allCrowdDataMap = {},
        title = {},
        isConversionCampaign = false,
        crowdMap,
        changeBidValue
    } = props;
    const crowdName = props.crowdName || title.crowdName;
    const crowdId = props.crowdId || title.crowdId;
    const effectType = allCrowdDataMap && allCrowdDataMap[crowdId] && allCrowdDataMap[crowdId].effectType;
    const audienceStatus = allCrowdDataMap && allCrowdDataMap[crowdId] && allCrowdDataMap[crowdId].audienceStatus;

    const {
        bidValue = DEFAULT_NEW_BID_VALUE
    } = crowdMap[crowdId] || {};
    const {min, max} = getRangeBid();
    const bidValueErrorMessage = (bidValue > max || bidValue < min) ? `范围：${min}~${max},若未填写，默认系数为1.1` : '';
    const numberBoxProps = {
        step,
        type: 'float',
        fixed: 2,
        width: 90,
        errorMessage: bidValueErrorMessage,
        location: 'layer',
        min,
        max,
        value: bidValue,
        onChange: partial(changeBidValue, crowdId),
        key: `crowd-setting-number-box-${crowdId}`
    };

    const bidValueClassName = classNames({
        'bid-value-normal': true,
        'bid-value-error': !!bidValueErrorMessage
    });

    const numberBoxContainerProps = {
        className: classNames({
            'bid-detail': true,
            'seleted-item': true
        })
    };
    return (
        <div className="crowd-setting-selected-item-detail" key={`crowd-setting-selected-item-${crowdId}`}>
            <span title={crowdName} className="right-column1 seleted-item">{crowdName}</span>
            <span className="right-column2 seleted-item">
                {CrowdStatus[audienceStatus] || CrowdStatus[CrowdStatusKey.generating]}
            </span>
            {
                effectType === EFFECT_TYPE_CONFIG.exclude
                    ? null
                    : (
                        <span className="seleted-item throw-way">
                            {
                                effectType === EFFECT_TYPE_CONFIG.orient
                                    ? EFFECT_TYPE_LIST_TEXT[EFFECT_TYPE_CONFIG.orient]
                                    : EFFECT_TYPE_LIST_TEXT[EFFECT_TYPE_CONFIG.premium]
                            }
                        </span>
                    )
            }
            {
                effectType === EFFECT_TYPE_CONFIG.exclude || (isConversionCampaign
                    && effectType === EFFECT_TYPE_CONFIG.orient)
                    ? null
                    : (
                        <span {...numberBoxContainerProps}>
                            <NumberInput {...numberBoxProps} />
                            <div className={bidValueClassName} title={bidValue}>{bidValue}</div>
                        </span>
                    )
            }
        </div>
    );
}

