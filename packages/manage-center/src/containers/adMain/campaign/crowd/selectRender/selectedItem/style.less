.crowd-setting-bid-detail-focus() {
    .bid-value-normal {
        display: none;
    }
    .number-for-form, .new-fc-one-numberbox-wrapper {
        display: inline-block !important;
    }
    .one-number-input-wrapper {
        display: inline-block !important;
        .btn-container {
            opacity: 1;
        }
    }
}
.crowd-setting-selected-item-detail {
    width: 100%;
    height: @dls-height-unit * 10;
    display: flex;
    .right-column1 {
        width: @dls-height-unit * 38;
        overflow: hidden;
        display: inline-block;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
    }
    .right-column2 {
        width: @dls-height-unit * 21;
    }
    .throw-way {
        width: @dls-height-unit * 23;
    }
    .bid-detail {
        height: 36px;
        .new-fc-one-numberbox-wrapper {
            display: none !important;
            .new-fc-one-icon {
                width: 20px !important;
            }
        }
        .number-for-form {
            display: none !important;
        }
        .one-number-input-wrapper {
            display: none !important;
        }
        &:hover {
            .crowd-setting-bid-detail-focus;
        }
    }
    .bid-detail-focus {
        .crowd-setting-bid-detail-focus;
    }
    .bid-value-normal {
        display: inline-block;
        width: 65px;
        text-align: right;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        height: 32px;
    }
}