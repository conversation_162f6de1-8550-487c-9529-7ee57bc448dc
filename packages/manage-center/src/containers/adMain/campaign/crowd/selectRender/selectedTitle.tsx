import Tip from 'commonLibs/Tips';
import './style.less';

export const SelectedTitleOrient = props => {
    const {isConversionCampaign, selectedNum, maxSelectedNum} = props;
    return (
        <div className="crowd-setting-selected-list-title">
            <span className="title right-title1">已选定向人群({selectedNum}/{maxSelectedNum})</span>
            <span className="title right-title2">状态</span>
            <span className="title right-title4">
                <span>投放方式</span>
                <Tip keyName="crowdSettingThrowWay" />
            </span>
            {
                !isConversionCampaign ? (
                    <span className="title right-title3">
                        <span>出价系数</span>
                        <Tip keyName="crowdSettingNewBid" />
                    </span>
                ) : null
            }
        </div>
    );
};

export const SelectedTitleExclude = ({selectedNum, maxSelectedNum}) => (
    <div className="crowd-setting-selected-list-title">
        <span className="title right-title1">已选排除人群({selectedNum}/{maxSelectedNum})</span>
        <span className="title right-title2">状态</span>
    </div>
);