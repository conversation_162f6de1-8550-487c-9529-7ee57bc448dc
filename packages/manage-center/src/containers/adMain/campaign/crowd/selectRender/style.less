.crowd-setting-unselected-list-title {
    display: inline-block;
    span {
        overflow: hidden;
        display: inline-block;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
        .tip-anchor {
            font-size: @dls-font-size-0;
            color: rgba(0, 0, 0, 0.65);
            margin-left: @dls-padding-unit;
            vertical-align: middle;
        }
    }
    .left-title1 {
        width: @dls-height-unit * 38;
        margin-right: @dls-padding-unit * 7;
    }
    .left-title2 {
        width: @dls-height-unit * 12;
    }
    .left-title3 {
        width: @dls-height-unit * 21;
        margin-left: @dls-padding-unit * 9;
    }
}
.unselected-item-detail {
    span {
        overflow: hidden;
        display: inline-block;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
    }
    .left-column1 {
        width: @dls-height-unit * 34;
        margin-right: @dls-padding-unit * 7;
    }
    .left-column2 {
        width: @dls-height-unit * 12;
    }
    .left-column3 {
        margin-left: @dls-padding-unit * 9;
    }
}
.crowd-setting-selected-list-title {
    display: inline-block;
    .title {
        display: inline-block;
        .fenice-icon {
            font-size: @dls-font-size-0;
            color: rgba(0, 0, 0, 0.65);
            margin-left: @dls-padding-unit;
        }
    }
    .right-title1 {
        width: @dls-height-unit * 40;
    }
    .right-title2 {
        width: @dls-height-unit * 18;
        text-indent: 10px;
    }
    .right-title3 {
        width: @dls-height-unit * 25;
        text-indent: 14px;
    }
    .right-title4 {
        width: @dls-height-unit * 25;
        text-indent: 18px;
    }
}