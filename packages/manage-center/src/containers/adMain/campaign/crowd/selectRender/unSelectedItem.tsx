import {CrowdStatusKey, CrowdStatus} from 'app/containers/fcNew/campaign/crowd/config';
import {EFFECT_TYPE_CONFIG, EFFECT_TYPE_LIST_TEXT} from 'app/containers/crowds/config/orientMethod';
import './style.less';

export default item => {
    const {crowdName, audienceStatus, effectType} = item;
    return (
        <div className="unselected-item-detail">
            <span title={crowdName} className="left-column1">{crowdName}</span>
            <span className="left-column2">
                {CrowdStatus[audienceStatus] || CrowdStatus[CrowdStatusKey.generating]}
            </span>
            {
                effectType === EFFECT_TYPE_CONFIG.exclude
                    ? null
                    : (
                        <span className="left-column3">
                            {
                                effectType === EFFECT_TYPE_CONFIG.orient
                                    ? EFFECT_TYPE_LIST_TEXT[EFFECT_TYPE_CONFIG.orient]
                                    : EFFECT_TYPE_LIST_TEXT[EFFECT_TYPE_CONFIG.premium]
                            }
                        </span>
                    )
            }
        </div>
    );
};