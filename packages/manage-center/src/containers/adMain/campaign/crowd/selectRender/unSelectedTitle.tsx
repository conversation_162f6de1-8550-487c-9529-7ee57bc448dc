import Tip from 'commonLibs/Tips';
import './style.less';

export const UnSelectedTitleOrient = ({unSelectedNum}) => (
    <div className="crowd-setting-unselected-list-title">
        <span className="left-title1">可选定向人群({unSelectedNum})</span>
        <span className="left-title2">状态</span>
        <span className="left-title3">
            <span>投放方式</span>
            <Tip keyName="crowdSettingThrowWay" />
        </span>
    </div>
);

export const UnSelectedTitleExclude = ({unSelectedNum}) => (
    <div className="crowd-setting-unselected-list-title">
        <span className="left-title1">可选排除人群({unSelectedNum})</span>
        <span className="left-title4">状态</span>
    </div>
);