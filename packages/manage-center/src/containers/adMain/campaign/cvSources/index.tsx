import {ReactNode} from 'react';
import {Radio, Select, Link} from '@baidu/one-ui';
import {IconExclamationCircle} from 'dls-icons-react';
import {useParams} from 'commonLibs/route';
import {isFcOcpcBaiduAppTransTypeUser} from 'commonLibs/utils/getFlag';
import {datasourceLabel, cvSourcesOptions, cvSourceTranstypeDisabledReason,
    datasource, transType as TRANS_TYPE_CONFIG, campaignOcpcBidTypeConfig, TransManagerModeType
} from 'commonLibs/config/ocpc';
import {isCvsourceEquipmentTypeDisabled, isCvsourceTargetDisabled} from 'commonLibs/utils/ocpc';
import {trackPrefix} from 'commonLibs/config/route';
import SuspenseBoundary, {useResource} from 'commonLibs/suspenseBoundary';
import {fetchUserTransTypeList} from '../../apis/campaign/api';
import './style.less';

// 定义常量 爱番番
const AIFANFAN = 9;

export function isRequired({campaignOcpcBidType}) {
    return campaignOcpcBidType !== campaignOcpcBidTypeConfig.eCPC;
}

function CvSources({
    value, onChange,
    equipmentType, marketingTargetId, shopType,
    tipInfo, transManagerMode
}) {
    const userId = useParams().userId;
    const {option, source} = value;
    const {
        deletedCvSource,
        deletedTransTypes
    } = tipInfo;
    const [{availableCvSources, eligibleInfo} = {}] = useResource(fetchUserTransTypeList, {marketingTargetId});

    const onCvSourcesTypeChange = e => {
        const option = e.target.value;
        onChange({
            ...value,
            option,
            eligibleInfo,
            source: []
        });
    };

    const radioProps = {
        value: option,
        onChange: onCvSourcesTypeChange,
        className: 'cv-sources-radio'
    };

    const cvSourcesSelectProps = {
        width: 400,
        mode: 'multiple',
        value: source,
        onChange: selectedSource => {
            onChange({
                ...value,
                eligibleInfo,
                source: selectedSource,
                option
            });
        },
        className: 'cvSources-select'
    };

    return (
        <>
            {
                transManagerMode !== TransManagerModeType.cvSource && (
                    <Radio.Group {...radioProps}>
                        <Radio.Button
                            value={cvSourcesOptions.UNLIMITED}
                            key={cvSourcesOptions.UNLIMITED}
                        >
                            不限
                        </Radio.Button>
                        <Radio.Button
                            value={cvSourcesOptions.FILTER}
                            key={cvSourcesOptions.FILTER}
                        >
                            筛选
                        </Radio.Button>
                    </Radio.Group>
                )
            }
            {
                option === cvSourcesOptions.FILTER && (
                    <Select {...cvSourcesSelectProps}>
                        {
                            // CPD-245953 增量不支持投放爱番番数据源
                            datasource.map(optionItem => {
                                const [item, name] = optionItem;
                                if (name === 'BAIDU' && !isFcOcpcBaiduAppTransTypeUser()) {
                                    return;
                                }
                                let disabledReason: ReactNode = '';
                                const noTransTypeDisabled = !availableCvSources.includes(item);
                                const {
                                    disabled: equipmentTypeDisabled,
                                    reason: equipmentTypeDisabledReason
                                } = isCvsourceEquipmentTypeDisabled({
                                    equipmentType: equipmentType.equipmentType,
                                    cvSourceItem: optionItem,
                                    marketingTargetId
                                });
                                const {
                                    disabled: targetTypeDisabled,
                                    reason: targetTypeDisabledReason
                                } = isCvsourceTargetDisabled({
                                    marketingTargetId,
                                    shopType,
                                    cvSourceItem: optionItem
                                });
                                // 按以下优先级展示数据源不可用的原因：转化追踪、推广设备、营销目标
                                if (noTransTypeDisabled) {
                                    const disabledInfo = cvSourceTranstypeDisabledReason[item];
                                    // 内部页面转化追踪列表页
                                    const trackUrl = `${trackPrefix}/user/${userId}`;
                                    const textLinkProps = {
                                        type: 'strong',
                                        size: 'small',
                                        target: '_blank',
                                        toUrl: disabledInfo.url ? disabledInfo.url.replace('userId', userId) : trackUrl
                                    };
                                    disabledReason = (
                                        <>
                                            <span>{disabledInfo.text}</span>
                                            {
                                                (disabledInfo.url || disabledInfo.innerUrl)
                                                    && (
                                                        <Link {...textLinkProps}>
                                                            {disabledInfo.linkText || '前去设置'}
                                                        </Link>
                                                    )
                                            }
                                        </>
                                    );
                                } else if (equipmentTypeDisabled) {
                                    disabledReason = equipmentTypeDisabledReason;
                                } else if (targetTypeDisabled) {
                                    disabledReason = targetTypeDisabledReason;
                                }
                                const disabled = noTransTypeDisabled
                                    || equipmentTypeDisabled
                                    || targetTypeDisabled;
                                const optionProps = {
                                    value: item,
                                    disabled,
                                    className: 'cv-source-checkbox'
                                };
                                const checkBoxProps = {
                                    label: datasourceLabel[item],
                                    value: item,
                                    source,
                                    key: `text-${item}`,
                                    disabled,
                                    disabledReason
                                };
                                return (
                                    <Select.Option key={`option-${item}`} {...optionProps} style={item === AIFANFAN ? {display: 'none'} : {}}>
                                        <Select.CheckboxText {...checkBoxProps} />
                                    </Select.Option>
                                );
                            })
                        }
                    </Select>
                )
            }
            <div className="show-delete-cvsource-tip">
                {
                    deletedCvSource.length
                        ? (
                            <div className="delete-tip">
                                <IconExclamationCircle className="icon" />
                                因修改推广设备，数据来源
                                <span>
                                    {
                                        deletedCvSource.map(v => datasourceLabel[v]).join('、')
                                    }
                                </span>
                                将取消选择。
                            </div>
                        )
                        : null
                }
            </div>
            <div className="show-delete-cvsource-tip">
                {
                    deletedTransTypes.length
                        ? (
                            <div className="delete-tip">
                                <IconExclamationCircle className="icon" />
                                因数据来源变更，优化目标
                                <span>
                                    {
                                        deletedTransTypes.map(i => TRANS_TYPE_CONFIG[i]).join('、')
                                    }
                                </span>
                                将取消选择。
                            </div>
                        )
                        : null
                }
            </div>
        </>
    );
};

export default function (props) {
    return (
        <SuspenseBoundary loading={{type: 'normal', tip: '', size: 'small'}}>
            <CvSources {...props} />
        </SuspenseBoundary>
    );
};