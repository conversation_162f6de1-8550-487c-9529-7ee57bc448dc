import {CPQL, STORE, B2B} from 'commonLibs/config/marketTarget';
import {useResource} from 'commonLibs/suspenseBoundary';
import {fetchUserTransTypeList, CVsourceAndTransTypeInfo} from '../apis/campaign/api';
import {fetchAnchorList} from 'app/api/adgroup/anchor';
import {globalAnchorInfo} from './anchorInfo';
import {useParams, useURLQuery} from 'commonLibs/route';

const initObject: Partial<CVsourceAndTransTypeInfo> = {
    normalizedTransAssets: {},
    availableCvSources: []
};

interface Props {
    marketingTargetId: number;
    initialFormFunc: (...args: any[]) => any;
}
export function useFormInitiation({marketingTargetId, initialFormFunc}: Props) {
    const [
        {normalizedTransAssets, availableCvSources, eligibleInfo, suggestTransTypes, assets} = initObject
    ] = useResource(fetchUserTransTypeList, {
        marketingTargetId,
        ...([CPQL, STORE, B2B].includes(marketingTargetId) ? {ignored: true} : {})
    });
    const [{projectId: projectIdFromQuery}] = useURLQuery() as [{projectId?: string}];
    const {bizId} = useParams();
    const [anchorInfo] = useResource(fetchAnchorList, {needAnchorWithoutLiveUrl: true});
    globalAnchorInfo.setInfo({rows: anchorInfo?.rows || []});

    // @ts-ignore
    const [{config, initialData}] = useResource(
        initialFormFunc,
        {
            marketingTargetId,
            assets,
            availableCvSources,
            normalizedTransAssets,
            eligibleInfo,
            projectIdFromQuery,
            bizId
        }
    );

    return [{
        assets,
        suggestTransTypes,
        normalizedTransAssets,
        eligibleInfo,
        formConfig: config,
        initialData
    }];
}
