import {useState, useMemo, useImperativeHandle, forwardRef, useRef, useCallback} from 'react';
import {Link} from '@baidu/one-ui';
import {ProcessSteps, Title} from '@baidu/one-ui-pro';
import {useQuickForm, createReactiveData, useFieldWatch, useFormErrors} from '@baidu/react-formulator';
import SuspenseBoundary, {CacheProvider} from 'commonLibs/suspenseBoundary';
import {isStoreConstructionUser} from 'commonLibs/utils/getFlag';
import {useRecordByComponent} from 'commonLibs/hooks/record';
import {useInitialMarketingTargetId} from 'commonLibs/hooks/useLinkToNew';
import {useFormulatorLogMethods} from 'commonLibs/hooks/funnelLog';
import {B2B, STORE, mtTextMap, CPQL, isLiveSubMarket} from 'commonLibs/config/marketTarget';
import flags from 'commonLibs/utils/flags';
import {useDraft} from 'commonLibs/hooks/draft';
import {useReplaceIframeText} from 'commonLibs/hooks/replace';
import useUserInfo from 'commonLibs/hooks/useUserInfo';
import {getAdMainProcessSteps} from 'app/containers/adMain/config/formFields';
import {CVsourceAndTransTypeInfo} from '../apis/campaign/api';
import MarketingTargetId from './marketingTarget';
import SmartComposeEntry from './smartCompseEntry';
import {initialCampaignForm} from './config';
import Save from './save';
import {CampaignInfoProvider} from './context';
import {useFormInitiation} from './hooks';
import {isMtSupportProgramCreative} from 'commonLibs/config/marketTarget';

import './style.less';
import {isInQinggeIframe} from 'commonLibs/utils/isInIframe';
import {FLOW_SCOPE} from 'commonLibs/config/ocpc';

export {bottomTipMap} from './bindProject';

interface CampaignProps extends Partial<Pick<CVsourceAndTransTypeInfo, keyof CVsourceAndTransTypeInfo>> {
    marketingTargetId: number;
    verificationData: any;
};

const CampaignForm = forwardRef<{getFormStatusChanged: () => boolean}, CampaignProps>(({
    marketingTargetId,
    verificationData
}, ref) => {
    const [Form, {getFieldError, setFieldsValue}] = useQuickForm();

    const [{
        formConfig,
        initialData
    }] = useFormInitiation({marketingTargetId, initialFormFunc: initialCampaignForm});
    const [reactiveData] = useState(() => createReactiveData(initialData));
    // 目前只需要用到所有fileds的数据，先写个简易版的getFieldsValue吧
    const getFieldsValue_ = useCallback(() => {
        return reactiveData.$toRaw();
    }, [reactiveData]);
    const {
        logOnSubmitClick,
        logOnSubmitSuccess,
        logOnSubmitFail,
        logOnCancelClick,
        logOnAreaClick
    } = useFormulatorLogMethods({
        formData: reactiveData,
        source: 'campaignNewForm',
        shouldSendChangeLog: true,
        config: {
            commonTrackParams: {
                marketingtargetid: marketingTargetId
            }
        },
        getFieldError
    });

    const formStatus = useRef({changed: false});
    const {markChanged, markCompleted, blockAndSaveDraft, getFormStatusChanged} = useDraft({
        form: {setFieldsValue, getFieldsValue: getFieldsValue_},
        level: 'campaign',
        conditions: {marketingTargetId: marketingTargetId},
        tpl: {
            applyDraftTipTpl: `系统检测到您在当前营销目标(${mtTextMap[marketingTargetId]})下有未完成的计划草稿，您要继续编辑草稿吗?`,
            saveDraftTipTpl: `您当前新建的"${mtTextMap[marketingTargetId]}"计划还未完成，保存草稿方便您下次进入时继续编辑，确认保存吗？`
        }
    });
    useFieldWatch(reactiveData, '*', (changedFields: string[]) => {
        if (
            changedFields.includes('subMarketingTargetId')
            && isLiveSubMarket(reactiveData.subMarketingTargetId)
            && marketingTargetId === CPQL
            && isInQinggeIframe
        ) {
            setFieldsValue({
                equipmentType: {
                    equipmentType: FLOW_SCOPE.MOBILE
                }
            });
        }
        // 地域组件初始化时会onChange，此时不计算为form的变化
        if (changedFields.some(field => !field.startsWith('campaignRegion'))) {
            formStatus.current.changed = true;
            markChanged();
        }
    });
    useImperativeHandle(ref, () => ({
        getFormStatusChanged
    }));
    const addCampaignCompleted = () => {
        markCompleted();
    };



    return (
        <div className="ad-main-campaign-form-container" onClick={logOnAreaClick}>
            <CampaignInfoProvider value={{marketingTargetId}}>
                <Form
                    config={formConfig}
                    data={reactiveData}
                    className="use-rf-preset-form-ui use-vertical"
                    verificationData={verificationData}
                >
                    <Save
                        logOnSubmitClick={logOnSubmitClick}
                        logOnSubmitSuccess={logOnSubmitSuccess}
                        logOnSubmitFail={logOnSubmitFail}
                        logOnCancelClick={logOnCancelClick}
                        addCampaignCompleted={addCampaignCompleted}
                        getFormStatusChanged={getFormStatusChanged}
                        blockAndSaveDraft={blockAndSaveDraft}
                    />
                </Form>
            </CampaignInfoProvider>
        </div>
    );
});

const CampaignNewProcess = () => {
    const {userId} = useUserInfo();
    const intialMarketingTargetId = useInitialMarketingTargetId();
    const [marketingTargetId, marketOnChange] = useState(intialMarketingTargetId);
    useRecordByComponent({tag: 'create_campaign'});
    const ref = useRef({getFormStatusChanged: () => false});
    const getFormStatusChanged = () => {
        return ref.current.getFormStatusChanged();
    };
    const [verificationData] = useState(() => createReactiveData({}));
    // 只在爱采购展示智能创编入口
    const enableSmartCompose = flags.getFlag('fc_new_acg_plan_user_aigc') && marketingTargetId === B2B;
    // 本地店铺推广显示跳转搬家工具的提示链接
    const isShowStoreMoveTip = marketingTargetId === STORE;
    const domRef = useRef<Element>(null);
    useReplaceIframeText({dom: domRef.current || document.body});
    return (
        <div className="ad-main-new-campaign-page" ref={domRef}>
            <Steps marketingTargetId={marketingTargetId} verificationData={verificationData} />
            <div className="ad-main-new-campaign-page-container" id="ad-main-new-campaign-page">
                <Title label="新建推广计划" className="campaign-name-title" />
                <MarketingTargetId
                    value={marketingTargetId}
                    onChange={marketOnChange as () => void}
                    getFormStatusChanged={getFormStatusChanged}
                />
                {enableSmartCompose && (
                    <SmartComposeEntry />
                )}
                {
                    isShowStoreMoveTip && (
                        <div className='store-move-tip-area'>
                            当前支持{isStoreConstructionUser() ? '原本地推广计划切换为' : '批量搭建'}新版本地店铺推广计划，新计划支持优选落地页等功能，帮助您提升投放效果。
                            <Link
                                type='strong'
                                target='_blank'
                                toUrl={`/fc/toolscenter/account/localAdsConstruct/new/user/${userId}?mt=2`}
                            >
                                立即查看
                            </Link>
                        </div>
                    )
                }
                <SuspenseBoundary loading={{tip: '正在初始化配置'}}>
                    <CampaignForm
                        key={marketingTargetId}
                        marketingTargetId={marketingTargetId}
                        ref={ref}
                        verificationData={verificationData}
                    />
                </SuspenseBoundary>
            </div>
        </div>
    );
};

export default function () {
    return (
        <CacheProvider>
            <CampaignNewProcess />
        </CacheProvider>
    );
};

function Steps({marketingTargetId, verificationData}: CampaignProps) {
    const errorsMap = useFormErrors(verificationData);
    const steps = useMemo(() => getAdMainProcessSteps({
        marketingTargetId,
        v2: isMtSupportProgramCreative({marketingTargetId})
    }), [marketingTargetId]);
    const stepsProps = {
        type: 'container',
        current: 0,
        dataSource: steps,
        errorsMap,
        className: 'ad-main-new-campaign-page-steps'
    };
    return (
        <div className="ad-main-new-campaign-page-steps__wrapper">
            <ProcessSteps {...stepsProps} />
        </div>
    );
}
