/**
 * @file: 营销目标展示器(仅展示已经选中的项目，不可点击)
 * @author: liuye11(<EMAIL>)
 * @Last Modified by: liuye11
 * @Last Modified time: 2023-06-14 20:05:25
 */
import {useMemo} from 'react';
import Main from './main';
import Title from './title';

const DisplaySelectedMarketTarget = (props: {value: number}) => {
    const {value} = props;
    // 只展示选中的值
    const marketArray = useMemo(() => {
        return [value];
    }, [value]);

    const selectProps = {
        ...props,
        disabled: true,
        marketArray
    };
    return (
        <div className="market-target-area" id="market-target-area">
            <Title />
            <Main {...selectProps} />
        </div>
    );
};

export default DisplaySelectedMarketTarget;
