/**
 * @file 营销目标选择器
 * <AUTHOR>
 * @date 2023-04-21 16:22:21
 */
import {useMemo} from 'react';
import {useSelector} from 'react-redux';
import {getMarketArray, isValidMtId} from 'commonLibs/utils/getMTArray';
import {useParams} from 'commonLibs/route';
import Main from './main';
import Title from './title';
import {MarketTargetProps} from './interface';

const MarketTarget = (props: MarketTargetProps) => {
    const {value, onChange, getFormStatusChanged} = props;

    const {mtId}  = useParams();
    const mtArray = useSelector(getMarketArray);
    const marketArray = useMemo(() => {
        return isValidMtId(mtId) ? [+mtId] : mtArray;
    }, [mtArray, mtId]);

    const selectProps = {
        value,
        onChange,
        marketArray,
        getFormStatusChanged
    };
    return (
        <div className="market-target-area" id="market-target-area">
            <Title />
            <Main {...selectProps} />
        </div>
    );
};

export default MarketTarget;
