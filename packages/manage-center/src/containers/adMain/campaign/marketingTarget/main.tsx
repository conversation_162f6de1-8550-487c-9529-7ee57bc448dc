/*
 * @file: index
 * @Author: <EMAIL>
 * @Date: 2022-07-25 12:56:55
 */
/**
 * @file 营销目标选择器
 * <AUTHOR>
 * @date 2020/09/29
 */
import {Dialog} from '@baidu/one-ui';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import {isFunction} from 'lodash-es';
import sendMonitor from 'commonLibs/utils/sendHm';
import {useRouterRedirect} from 'commonLibs/route';
import {mtTextMap, B2B, CPQL, getNewMarketingTargetIdList} from 'commonLibs/config/marketTarget';
import newWord from 'app/resource/svg/new.svg';
import {marketingTargetId} from './config';
import {MarketTargetMainPageProps} from './interface';
import {isDraftUser} from 'commonLibs/utils/getFlag';
import './style.less';

const MarketTarget = (props: MarketTargetMainPageProps) => {
    const {value, onChange, marketArray, getFormStatusChanged, disabled} = props;
    const jumpAdMainCampaignUrl = useRouterRedirect('@newProcessCampaign', {inheritParams: true, inheritQuery: true});
    const jumpFcNewCampaignUrl = useRouterRedirect('@newCampaign', {inheritParams: true, inheritQuery: true});
    return (
        <div className="fc-manage-ad-main-market-select-main">
            {
                marketArray.map((id: number) => {
                    const {label, icon, desc} = marketingTargetId[id as keyof typeof mtTextMap];
                    const itemClass = classnames({
                        'market-target-item': true,
                        'market-target-item-disabled': disabled,
                        'market-target-item-selected': value === id
                    });
                    return (
                        <div
                            key={id}
                            className={itemClass}
                            onClick={() => {
                                if (disabled) {
                                    return;
                                }
                                // 新老营销目标切换
                                if (
                                    (
                                        getNewMarketingTargetIdList().includes(id)
                                    )
                                    && !getNewMarketingTargetIdList().includes(value)) {
                                    jumpAdMainCampaignUrl({}, {marketingTargetId: id});
                                }
                                else if (!getNewMarketingTargetIdList().includes(id)
                                    && (
                                        getNewMarketingTargetIdList().includes(value)
                                    )) {
                                    const formStatusChanged = isFunction(getFormStatusChanged)
                                        ? getFormStatusChanged() : false;
                                    // 新版跳老版，校验是否表单有变动再出弹窗
                                    if (formStatusChanged && !isDraftUser()) {
                                        Dialog.confirm({
                                            title: '温馨提示',
                                            content: '切换其他营销目标后，当前填写的内容将被清空，请确认是否切换？',
                                            onOk: () => {
                                                jumpFcNewCampaignUrl({}, {marketingTargetId: id});
                                            }
                                        });
                                    }
                                    else {
                                        jumpFcNewCampaignUrl({}, {marketingTargetId: id});
                                    }
                                }
                                else {
                                    onChange(id);
                                }
                                sendMonitor('new', {
                                    level: 'market-target', params: value, item: 'switch_from_market_target'
                                });
                                sendMonitor('new', {
                                    level: 'market-target', params: id, item: 'switch_to_market_target'
                                });
                            }}
                        >
                            <img src={icon} alt="" />
                            <div className="market-target-item-label">{label}</div>
                            <div className="market-target-item-content">{desc}</div>
                            {[B2B, CPQL].includes(id) && <img src={newWord} className="new-word"></img>}
                        </div>
                    );
                })
            }
        </div>
    );
};

MarketTarget.propTypes = {
    marketArray: PropTypes.array.isRequired,
    onChange: PropTypes.func.isRequired,
    value: PropTypes.string.isRequired
};

export default MarketTarget;
