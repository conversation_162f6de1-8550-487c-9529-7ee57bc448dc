.fc-manage-ad-main-market-select-main {
  display: flex;
  flex-wrap: wrap;
  gap: @dls-padding-unit * 4;
  background-color: #fff;
  .market-target-item {
    width: 192px;
    height: 136px;
    box-sizing: border-box;
    display: block;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    background-color: @dls-color-gray-1;
    padding: @dls-padding-unit * 4;
    border: 1px solid @dls-color-gray-1;
    position: relative;
    cursor: pointer;
    &:hover {
      border: 1px solid #1758e5;
    }
    img {
      width: @dls-height-unit * 10;
    }
    .market-target-item-label {
      font-size: 14px;
      color: @dls-color-gray-9;
      font-weight: 500;
      margin-top: 9px;
      line-height: @dls-height-unit * 5;
    }
    .market-target-item-content {
      font-size: @dls-font-size-0;
      line-height: @dls-height-unit * 4;
      color: @dls-color-gray-7;
      margin-top: @dls-padding-unit;
    }
    .new-word {
      position: absolute;
      top: @dls-height-unit * 2;
      right: @dls-height-unit * 2;
      width: @dls-height-unit * 4;
    }
    &-selected {
      background-color: #ebf3ff;
      border: 1px solid #1758e5;
    }
    &-disabled {
      cursor: not-allowed;
      &:hover {
        border-color: @dls-color-gray-1;
      }
      &.market-target-item-selected {
        &:hover {
          border-color: #1758e5;
        }
      }
    }
  }
}
.market-target-area, .market-settings-area {
  padding: @dls-padding-unit * 6;
  background: #fff;
  border-radius: 6px;
  margin-bottom: @dls-padding-unit * 6;
  .title {
    display: flex;
    align-items: center;
    margin-bottom: @dls-padding-unit * 6;
    .label {
      font-size: 18px;
      font-weight: 500;
      color: @dls-color-gray-9;
    }
    .info {
      font-size: 14px;
      margin-left: @dls-padding-unit * 4;
      color: @dls-color-gray-7;
    }
  }
}
