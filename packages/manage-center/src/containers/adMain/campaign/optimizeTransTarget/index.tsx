import {useEffect} from 'react';
import SuspenseBoundary, {useResource, CacheProvider} from 'commonLibs/suspenseBoundary';
import {isEmpty} from 'lodash-es';
import SelectCard from 'commonLibs/components/selectCard';
import {getAvailableTransTypes} from 'commonLibs/utils/getAssetAndTransTypes';
import {useOptimizeTransTargetOptions} from 'commonLibs/config/optimizeTransTarget';
import {fetchUserTransTypeList} from '../../apis/campaign/api';
import {useCampaignInfo} from '../context';
import {initialAssetsAndOptimizeTransTarget} from 'app/containers/adMain/campaign/config';
import './style.less';

const initObject = {
    availableTransTypes: []
};

function OptimizeTransTarget(props: {value: number, onChange: () => void, isShowComprehensiveClueInPlan: boolean}) {
    const {
        value, onChange, isSetInitialAssetDataFromApi, isShowComprehensiveClueInPlan, transAsset = {},
        cvSources, transManagerMode
    } = props;
    const {marketingTargetId} = useCampaignInfo();
    const {assets: assetsFromProps} = transAsset;
    const [
        {
            normalizedTransAssets, availableCvSources,
            eligibleInfo, assets: assetsFromApi,
            availableTransTypes: initAvailableTransTypes
        } = initObject
    ] = useResource(fetchUserTransTypeList, {marketingTargetId});
    const assets = isEmpty(assetsFromProps) ? assetsFromApi : assetsFromProps;
    // 根据是否在转化资产名单区分availableTransTypes
    const availableTransTypes = getAvailableTransTypes({
        assets,
        normalizedTransAssets,
        eligibleInfo,
        marketingTargetId,
        cvSources,
        transManagerMode,
        transAsset,
        availableCvSources,
        isProjectLevel: false
    });
    const [initialData] = useResource(
        initialAssetsAndOptimizeTransTarget,
        {
            marketingTargetId, assets, availableCvSources, normalizedTransAssets, eligibleInfo,
            cvSources, transManagerMode, transAsset, availableTransTypes: initAvailableTransTypes
        }
    );
    const isShowComprehensiveClue = isShowComprehensiveClueInPlan || initialData.isShowComprehensiveClueInPlan;
    const selectCardProps = {
        value,
        options: useOptimizeTransTargetOptions({
            marketingTargetId,
            isShowComprehensiveClueInPlan: isShowComprehensiveClue,
            availableTransTypes
        }),
        onChange
    };
    // 如果isSetInitialAssetDataFromApi为true且没有选中值，根据api返回值设置初始值
    useEffect(() => {
        if (isSetInitialAssetDataFromApi && !value && initialData.optimizeTransTarget) {
            onChange(initialData.optimizeTransTarget);
        }
    }, [initialData.optimizeTransTarget]);
    return (
        <div>
            <SelectCard {...selectCardProps} />
        </div>
    );
};

export default function (props) {
    return (
        <CacheProvider>
            <SuspenseBoundary loading={{type: 'normal', tip: '', size: 'small'}}>
                <OptimizeTransTarget {...props} />
            </SuspenseBoundary>
        </CacheProvider>

    );
}
