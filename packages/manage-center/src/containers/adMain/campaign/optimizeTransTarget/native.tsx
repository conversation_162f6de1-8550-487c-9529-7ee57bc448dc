/**
 * @file 原生互动营销目标下的优化目标
 * <AUTHOR>
 * @date 2025-02-17
*/


import SelectCard from 'commonLibs/components/selectCard';
import {transType, COMPREHENSIVE_CLUES} from 'commonLibs/config/ocpc';
import './style.less';
import {HoverTip} from 'commonLibs/Tips';


const MORE = 'MORE';
export const transTargetMap = {
    COMPREHENSIVE_CLUES: COMPREHENSIVE_CLUES,
    MORE: 'MORE'
};
export const OPTIMIZE_TRANS_TARGETS = [
    COMPREHENSIVE_CLUES,
    MORE
];

const optimizeTransTargetLabelMap = {
    [COMPREHENSIVE_CLUES]: transType[COMPREHENSIVE_CLUES],
    [MORE]: '更多目标'
};

const optimizeTransTargetTipMap = {
    [COMPREHENSIVE_CLUES]: '使用原生推广身份，寻找对您的产品、服务感兴趣的客户',
    [MORE]: '查看更多优化目标'
};


export const optimizeTransTargetOptions = OPTIMIZE_TRANS_TARGETS.map(key => ({
    label: optimizeTransTargetLabelMap[key],
    tip: optimizeTransTargetTipMap[key],
    hoverTip: optimizeTransTargetTipMap[key],
    value: key
}));

export default function OptimizeTransTarget(props: {value: number, onChange: () => void}) {
    const {
        value,
        onChange
    } = props;

    const selectCardProps = {
        value,
        onChange,
        options: optimizeTransTargetOptions
    };

    return (
        <div>
            <SelectCard {...selectCardProps} />
        </div>
    );
};

