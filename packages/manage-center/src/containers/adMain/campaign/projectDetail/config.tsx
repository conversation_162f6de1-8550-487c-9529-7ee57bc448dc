import {STORE} from 'commonLibs/config/marketTarget';
import {
    transType,
    bidType,
    transAssetOptionsEnum,
    PROJECT_MODE_TYPE,
    getAssetTypeLabelByType,
    TransManagerModeOptionConfig,
    TransManagerModeType
} from 'commonLibs/config/ocpc';
import {TransTypeMultiBidEditor} from 'assetsCenter/__ocpc__/components';
import AiMaxSvg from 'app/resource/img/ai_max_without_border.svg';
import {promotionSceneValues} from 'app/containers/smartCompose/modules/smartForm/config';

export const projectDetailFields = [
    {
        group: 'promotionSceneInfo',
        use: ['Group', {
            title: '推广设置'
        }],
        fields: [
            {
                field: 'promotionScene',
                label: '推广设置',
                use: ['RadioGroup', {
                    options: [
                        {label: '推店铺', value: promotionSceneValues.shop},
                        {label: '推商品', value: promotionSceneValues.product}
                    ]
                }],
                componentProps: {
                    disabled: true
                }
            }
        ],
        visible: ({marketingTargetId}) => marketingTargetId === STORE
    },
    {
        group: 'bid',
        use: ['Group', {
            title: '优化目标与项目预期'
        }],
        fields: [
            {
                field: 'ocpcBidType',
                label: '出价模式',
                use: ['RadioGroup', {
                    disabled: true,
                    options: [
                        {label: '点击出价', value: bidType.CPC},
                        {label: '增强模式', value: bidType.ECPC},
                        {label: '目标转化成本', value: bidType.CPA},
                        {label: '最大转化', value: bidType.CVMAX},
                        {label: '目标ROI', value: bidType.ROI}
                    ]
                }]
            },
            {
                field: 'transManagerMode',
                label: '转化管理模式',
                use: ['RadioGroup', {disabled: true, options: TransManagerModeOptionConfig}]
            },
            {
                field: 'transAsset',
                label: '转化资产',
                use: ['Select', {disabled: true}],
                componentProps: ({transAssetInfo = {}, assetType}) => {
                    const options = [
                        {label: '不限', value: transAssetOptionsEnum.UNLIMITED},
                        {
                            label: getAssetTypeLabelByType(+assetType[0]) || '-',
                            value: transAssetOptionsEnum.BY_ASSET_TYPE
                        },
                        {label: transAssetInfo.transName, value: transAssetOptionsEnum.BY_ASSET_ID}
                    ];
                    return {options};
                },
                visible: formData => formData.TransManagerModeType === TransManagerModeType.asset
            },
            {
                field: 'transTypes',
                label: '优化目标',
                use: ['Select', {
                    mode: 'multiple'
                }],
                componentProps: ({transTypes}) => {
                    const options = transTypes.map(item => ({label: transType[item], value: item}));
                    return {
                        disabled: true,
                        options
                    };
                },
                visible: ({transTypes}) => !!transTypes.length
            },
            {
                field: 'ocpcBid',
                label: '目标转化出价',
                use: ['Input'],
                componentProps: {
                    disabled: true
                },
                visible: ({ocpcBidType}) => ocpcBidType === bidType.CPA
            },
            {
                field: 'transTypesOcpcBidRatio',
                label: '分目标精细化出价',
                use: [TransTypeMultiBidEditor],
                componentProps: ({transTypesOcpcBidRatio, transTypes, ocpcBid}) => ({
                    disabled: true,
                    width: 442,
                    onlyShowTable: true,
                    ocpcBid,
                    transData: {
                        newSelectedTransTypes: transTypes,
                        transTypesOcpcBidRatio,
                        ocpcBid
                    }
                }),
                visible: ({transTypeMultiBidStatus}) => !!transTypeMultiBidStatus
            },
            {
                field: 'targetRoiRatio',
                label: '目标ROI系数',
                use: ['NumberInput'],
                componentProps: {
                    disabled: true
                },
                visible: ({targetRoiRatio, ocpcBidType}) => !!targetRoiRatio && ocpcBidType === bidType.ROI
            },
            {
                field: 'deepTransTypeStatus',
                label: '深度优化方式',
                use: ['Input'],
                componentProps: {
                    disabled: true
                },
                visible: ({deepTransTypeStatus}) => !!deepTransTypeStatus
            },
            {
                field: 'assistTransTypes',
                label: '深度优化目标',
                use: ['Select', {
                    mode: 'multiple'
                }],
                componentProps: ({assistTransTypes = []}) => {
                    const options = assistTransTypes.map(item => ({label: transType[item], value: item}));
                    return {
                        disabled: true,
                        options
                    };
                },
                visible: ({assistTransTypes = []}) => !!assistTransTypes.length
            },
            {
                field: 'ocpcDeepBid',
                label: '深度目标转化出价',
                use: ['Input'],
                componentProps: {
                    disabled: true
                },
                visible: ({ocpcDeepBid}) => !!ocpcDeepBid
            }
        ]
    },
    {
        group: 'budget',
        use: ['Group', {
            title: '项目预算'
        }],
        fields: [
            {
                field: 'sharedBudgetRadio',
                label: '项目预算',
                use: ['RadioGroup', {
                    options: [
                        {label: '使用', value: true},
                        {label: '不使用', value: false}
                    ]
                }],
                componentProps: ({sharedBudget}) => ({
                    value: !!sharedBudget,
                    disabled: true
                })
            },
            {
                field: 'sharedBudget',
                label: '日预算',
                use: ['Input'],
                componentProps: {
                    disabled: true
                },
                visible: ({sharedBudget}) => !!sharedBudget
            },
            {
                field: 'sharedBudgetName',
                label: '项目预算名称',
                use: ['Input'],
                componentProps: {
                    disabled: true
                },
                visible: ({sharedBudget}) => !!sharedBudget
            }
        ]
    },
    {
        group: 'projectModeTypeGroup',
        use: ['Group', {
            title: '投放模式'
        }],
        fields: [
            {
                field: 'projectModeType',
                label: '投放模式',
                use: ['RadioGroup', {
                    disabled: true,
                    className: 'aix-max-put-way-switch',
                    options: [
                        {label: '自定义', value: PROJECT_MODE_TYPE.custom},
                        {
                            label: (
                                <div className="put-way-awitch-radio-item">
                                    <img src={AiMaxSvg} alt="AIMax" />
                                </div>
                            ),
                            value: PROJECT_MODE_TYPE.ai
                        }
                    ]
                }]
            }
        ]
    }
];