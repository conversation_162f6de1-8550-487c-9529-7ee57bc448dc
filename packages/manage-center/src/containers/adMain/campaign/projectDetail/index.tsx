import {useImperativeHandle, forwardRef} from 'react';
import {Drawer} from '@baidu/one-ui';
import {useBoolean} from 'huse';
import {useQuickForm} from '@baidu/react-formulator';
import {
    deepTypeStatOptionsWithReason
} from 'commonLibs/config/ocpc';
import {formatOcpcBidRatioResponse} from 'commonLibs/components/ocpcBidRatio';
import {projectDetailFields} from './config';
import './style.less';


const drawerStyle = {background: '#F2F7FF', padding: '16px'};

interface ProjectDetailProps {
    projectDetail: object;
};


const ProjectDetail = ({projectDetail}: ProjectDetailProps, ref) => {

    const [Form] = useQuickForm();
    const config = {fields: projectDetailFields};
    const [productDetailVisible, {on: open, off: close}] = useBoolean(false);
    useImperativeHandle(ref, () => ({open, close}));
    const projectDetailData = {
        ...projectDetail,
        sharedBudgetName: `${projectDetail.projectName}-项目预算`,
        sharedBudgetRadio: !!projectDetail.sharedBudget,
        deepTransTypeStatus: deepTypeStatOptionsWithReason[
            projectDetail.deepTransTypeStatus as keyof typeof deepTypeStatOptionsWithReason],
        ...formatOcpcBidRatioResponse(projectDetail)
    };
    return (
        <Drawer
            className="project-drawer"
            title={`『${projectDetail.projectName}』的更多设置`}
            visible={productDetailVisible}
            placement='right'
            destroyOnClose
            closeDrawerByClickBody
            onClose={close}
            mask={false}
            drawerStyle={drawerStyle}
            width={800}
        >
            <Form
                config={config}
                data={projectDetailData}
                className="use-rf-preset-form-ui use-label-top"
            />
        </Drawer>
    );
};

export default forwardRef(ProjectDetail);