/**
 * @file: 选择展示器（选项禁用）
 * @author: liuye11(<EMAIL>)
 * @Last Modified by: liuye11
 * @Last Modified time: 2023-07-05 13:49:59
 */

import {useMemo} from 'react';
import {noop} from 'lodash-es';
import SelectCard from 'commonLibs/components/selectCard';
import {getPromotionSceneOptions} from 'commonLibs/config/promotionScene';
import {useCampaignInfo} from '../context';

export default ({value}: {value: number}) => {
    const {marketingTargetId} = useCampaignInfo();
    const promotionOptions = useMemo(
        () => getPromotionSceneOptions(marketingTargetId).map(option => ({...option, disabled: true})),
        [value, marketingTargetId]
    );
    const selectCardProps = {
        value,
        options: promotionOptions,
        onChange: noop
    };
    return (
        <div className="promotion-scene-container">
            <SelectCard {...selectCardProps} />
        </div>
    );
};