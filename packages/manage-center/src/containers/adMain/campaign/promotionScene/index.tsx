import SelectCard from 'commonLibs/components/selectCard';
import {getPromotionSceneOptions} from 'commonLibs/config/promotionScene';
import {useCampaignInfo} from '../context';

export default (props: {value: number, onChange: () => void}) => {
    const {value, onChange} = props;
    const {marketingTargetId} = useCampaignInfo();
    const selectCardProps = {
        value,
        options: getPromotionSceneOptions(marketingTargetId),
        onChange
    };
    return (
        <div className="promotion-scene-container">
            <SelectCard {...selectCardProps} />
        </div>
    );
};