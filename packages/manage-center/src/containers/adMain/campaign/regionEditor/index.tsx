import RegionEditor from 'commonLibs/region';
import {STORE, COMMODITY} from 'commonLibs/config/marketTarget';
import {useParams} from 'commonLibs/route';
import {useCampaignInfo} from '../context';
import ALL_ADTYPE from 'commonLibs/config/adType';

export default (props: {
    value: number;
    onChange: () => void;
    isConversionCampaign: boolean;
}) => {
    const {value, onChange, isConversionCampaign} = props;
    const {userId} = useParams();
    const {marketingTargetId} = useCampaignInfo();
    const regionProps = {
        form: {getFieldValue: () => value},
        isSmartAd: isConversionCampaign,
        initialCurrentTabKey: localStorage.getItem(`storageRegionTabKey_${userId}`),
        isStoreProgram: marketingTargetId === STORE,
        adType: ALL_ADTYPE.NEW_STORE,
        isShantouAd: marketingTargetId === COMMODITY,
        validateRegion: onChange,
        storePageInfos: [],
        // todo: region marketingTargetId
        marketingTargetId,
        value
    };
    return <RegionEditor {...regionProps} />;
};