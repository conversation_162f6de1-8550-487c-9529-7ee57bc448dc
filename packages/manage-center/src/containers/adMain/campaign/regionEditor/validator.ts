import {getIsConflict} from 'commonLibs/utils/handleRegion';
import {
    STORE_REGION_TYPE_CONFIG
} from 'commonLibs/config/regionConfig';
import {regionValidate} from 'app/containers/campaigns/list/editor/region/config';

export const validator = value => {
    if (!value) {
        return '请选择地域';
    }
    const {
        regionMainType,
        regionTarget = [],
        regionMap = {},
        regionType,
        areaType,
        regionArea = [],
        allowRegion = [],
        geoLocationStatus
    } = value;
    if (regionType === STORE_REGION_TYPE_CONFIG.REGION && allowRegion.length > 0
        && getIsConflict(allowRegion, regionTarget)) {
        return;
    }
    const errors = [
        regionValidate({regionMainType, regionTarget, regionMap, regionType, areaType, regionArea, geoLocationStatus})
    ].filter(v => v);
    if (errors.length > 0) {
        return errors;
    }
    return;
};