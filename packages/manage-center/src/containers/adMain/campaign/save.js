import {useRouterRedirect} from 'commonLibs/route';
import {forEach, some, values as valuesOf, concat} from 'lodash-es';
import {Dialog, Loading} from '@baidu/one-ui';
import {useLocalStorage} from '@huse/local-storage';
import {Boundary, useResource} from 'commonLibs/suspenseBoundary';
import {sendScreenRecord} from 'commonLibs/hooks/record';
import SaveFooter from 'commonLibs/SaveFooter';
import {useFormContext} from '@baidu/react-formulator';
import {cvSourcesOptions, transType, datasourceLabel, campaignOcpcBidTypeConfig} from 'commonLibs/config/ocpc';
import {isCvSourceError} from 'commonLibs/utils/ocpc';
import {useEfficiencyPPMonitor} from 'commonLibs/hooks/monitor';
import {getSingleErrorMessage} from 'commonLibs/utils/getErrorTextByCode';
import {isFcTransAssetsUser} from 'commonLibs/utils/getFlag';
import {FC_LAST_USE_MARKET_STORAGE_KEY} from 'commonLibs/config/campaign';
import {useUserInfo} from 'commonLibs/context/sharedInfo';
import CvSourceErrorContent from 'commonLibs/cvSourceErrorContent';
import {
    addNewCampaign
} from 'app/containers/fcNew/actions/campaign/crowd/api';
import {useCampaignInfo} from './context';
import {
    campaignParamsPipeline,
    campaignClickPriceParams,
    campaignConversionPriceParams,
    campaignBudgetParams,
    campaignSettingParams,
    campaignRegionParams,
    campaignScheduleParams,
    campaignCrowdParams
} from './util/generateParams';
import {remoteConfigLoaders} from './config';
import {getEfficiencyBidTypeByCampaignInfo} from '../utils';
import {getDeletedCvSource} from '../../fcNew/campaign/Main';
import {isMtSupportProgramCreative} from 'commonLibs/config/marketTarget';
import {fetchUserTransTypeList} from '../apis/campaign/api';
import {
    featureNameMap,
    motionTypeMap, sendBaseUniversalMonitor, universalMonitorKeyMap} from 'commonLibs/config/monitor-pro';
import {useBackToCampaignList} from 'app/containers/fcNew/common/backUrl';
import {isInQinggeIframe} from 'commonLibs/utils/isInIframe';
import {PostMessageEventType, postMessageFunc, PostMessageScene} from 'commonLibs/utils/postMessage';


export const defaultGenerateCampaignParams = campaignParamsPipeline(
    [
        campaignClickPriceParams,
        campaignConversionPriceParams,
        campaignBudgetParams,
        campaignSettingParams,
        campaignRegionParams,
        campaignScheduleParams,
        campaignCrowdParams
    ],
    {campaignType: {}, crowdBindTypes: null}
);


export async function submitWithCvSourceConfirm({userId, formData, handleSubmit, eligibleInfo}) {
    const {
        campaignTransTypes = [],
        cvSources = {},
        campaignOcpcBidType,
        transManagerMode
    } = formData;
    const {
        option, source
    } = cvSources;
    const cvSourceError = isCvSourceError(transManagerMode, source, campaignTransTypes);
    if (cvSourceError) {
        Dialog.confirm({
            title: '温馨提示',
            content: (
                <CvSourceErrorContent
                    level="计划"
                    isEdit={false}
                    transTypes={campaignTransTypes}
                    userId={userId}
                />
            ),
            buttonPosition: 'left',
            needCloseIcon: true,
            footer: []
        });
        return;
    }
    // 是否为增强模式自动优选
    const isEcpcCamapign = campaignOcpcBidType === campaignOcpcBidTypeConfig.eCPC && !campaignTransTypes.length;
    if (!isFcTransAssetsUser() && option === cvSourcesOptions.FILTER && !isEcpcCamapign) {
        const deletedCvSources = getDeletedCvSource(
            campaignTransTypes.map(item => +item),
            source,
            eligibleInfo
        );
        if (deletedCvSources.length) {
            Dialog.confirm({
                title: '温馨提示',
                content: (
                    <span>
                        根据您选择的目标转化：{
                            campaignTransTypes.map(item => transType[+item]).join('、')
                        }，已自动为您删除不支持该转化目标的数据回传方式：{
                            deletedCvSources.map(item => datasourceLabel[+item]).join('、')
                        }
                    </span>
                ),
                buttonPosition: 'left',
                needCloseIcon: true,
                width: 536,
                onOk: async () => {
                    await handleSubmit(deletedCvSources);
                }
            });
        }
        else {
            await handleSubmit();
        }
    }
    else {
        await handleSubmit();
    }
};


function Save({
    logOnSubmitClick, logOnSubmitSuccess, logOnSubmitFail, logOnCancelClick,
    addCampaignCompleted, blockAndSaveDraft, getFormStatusChanged
}) {
    const {setFieldsError, validateFields, formData} = useFormContext();
    const {marketingTargetId} = useCampaignInfo();
    const {userId} = useUserInfo();
    const clickToNewAdgroup = useRouterRedirect('@newProcessAdgroupV1', {inheritParams: true, inheritQuery: true});
    const clickToNewAdgroupAndCreative = useRouterRedirect(
        '@newProcessAdgroupV2', {inheritParams: true, inheritQuery: true});
    const [
        {normalizedTransAssets, suggestTransTypes, eligibleInfo, assets}
    ] = useResource(fetchUserTransTypeList, {marketingTargetId});
    const reportMonitor = useEfficiencyPPMonitor('new_campaign', 'ad_main_new');
    const [_mtIdLastUsed, setMtIdInStorage] = useLocalStorage(FC_LAST_USE_MARKET_STORAGE_KEY, marketingTargetId);

    const handleSubmit = async (deletedCvSources = []) => {
        logOnSubmitClick();
        const values = await validateFields();
        let data;
        try {
            const {
                generateCampaignParams = defaultGenerateCampaignParams
            } = await remoteConfigLoaders[marketingTargetId]();
            const params = generateCampaignParams({
                values,
                normalizedTransAssets,
                assets,
                marketingTargetId,
                suggestTransTypes,
                deletedCvSources,
                eligibleInfo
            });

            data = await addNewCampaign({
                item: {
                    ...params
                }
            });
            addCampaignCompleted();
            setMtIdInStorage(marketingTargetId);
            logOnSubmitSuccess();
            const campaignId = data?.campaignType?.campaignId;
            const adType = data?.campaignType?.adType;
            reportMonitor({
                count: 1,
                marketingTargetId,
                bid_type: getEfficiencyBidTypeByCampaignInfo(values)
            });
            if (isInQinggeIframe) {
                postMessageFunc({
                    scene: PostMessageScene.ADD_CAMPAIGN,
                    eventType: PostMessageEventType.ON_SAVE_SUCCESS
                });
            }
            if (isMtSupportProgramCreative({marketingTargetId, adType})) {
                clickToNewAdgroupAndCreative({campaignId});
            }
            else {
                clickToNewAdgroup({campaignId});
            }
        }
        catch (error) {
            const {errMap, otherError, _originError} = error || {};
            const alertError = [];
            const allErrors = [];
            const errorMap = {};
            sendScreenRecord('campaign_save_error');
            if (!some(valuesOf(errMap), errors => errors.length)) {
                const {code} = otherError;
                const errorMessage = getSingleErrorMessage(otherError);
                alertError.push({
                    code,
                    message: errorMessage
                });
                if (logOnSubmitFail) {
                    logOnSubmitFail(_originError);
                }
                // throw {errorMap, alertError: alertError};
            }
            else {
                const {
                    campaignErrors = [], // 计划字段错误
                    crowdBindErrors = [], // 绑定人群错误
                    crowdErrors = [], // 人群错误
                    cycTemplateErrors = [] // 时段模板错误
                } = _originError.errors[0] || {};
                if (logOnSubmitFail) {
                    logOnSubmitFail({
                        ..._originError,
                        errors: concat(
                            campaignErrors,
                            crowdBindErrors,
                            crowdErrors,
                            cycTemplateErrors
                        )
                    });
                }
            }
            forEach(valuesOf(errMap), errors => {
                handleErrorMapAndAlertErrors({
                    values,
                    errors,
                    alertError,
                    allErrors,
                    errorMap
                });
            });
            setFieldsError(errorMap);
            if (alertError.length) {
                Dialog.alert({
                    title: '温馨提示',
                    content: alertError.map(e => e.message).join('；')
                });
            }
            throw {errorMap, alertError: alertError};
        }
    };

    const goBackToCampaign = useBackToCampaignList({blockAndSaveDraft, getFormStatusChanged});

    const handleCancel = () => {
        reportMonitor({
            count: 1,
            completed: 0,
            marketingTargetId,
            bid_type: getEfficiencyBidTypeByCampaignInfo(formData)
        });
        logOnCancelClick();
        goBackToCampaign();
    };

    const onSave = async () => {
        sendBaseUniversalMonitor({
            [universalMonitorKeyMap.featureName]: featureNameMap['保存并新建单元'],
            [universalMonitorKeyMap.motionType]: motionTypeMap['点击']
        });
        return await submitWithCvSourceConfirm({userId, formData, handleSubmit, eligibleInfo});
    };

    const saveFooterProps = {
        onSave,
        onCancel: handleCancel,
        isNewStyle: true,
        saveType: 'primary',
        cancelType: 'normal',
        size: 'medium',
        saveLabel: '保存并新建单元'
    };
    return <SaveFooter {...saveFooterProps} />;
};

export default function (props) {
    return (
        <div className="ad-main-campaign-footer">
            <Boundary pendingFallback={<Loading />}>
                <Save {...props} />
            </Boundary>
        </div>
    );
}

function handleErrorMapAndAlertErrors({values = {}, errors = [], alertError, allErrors, errorMap}) {
    const errorMap_ = errors.reduce((memo, err) => {
        const {field, code} = err;
        const errorMessage = getSingleErrorMessage(err);
        // 资产表单项可能隐藏了，所以把错误信息也放到alertError中
        if (field && !['transAsset'].includes(field) && values[field] != null) {
            memo[field] = [errorMessage];
            allErrors.push({
                code,
                message: errorMessage
            });
        }
        else {
            alertError.push({code, message: errorMessage});
            allErrors.push({code, message: errorMessage});
        }
        return memo;
    }, errorMap);
    return {
        errorMap: errorMap_,
        alertError,
        allErrors
    };
};
