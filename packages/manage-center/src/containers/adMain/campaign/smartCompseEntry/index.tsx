/**
 * @file          智能创编入口
 * <AUTHOR> <<EMAIL>>
 * @date          2023-06-20 17:39
 * @lastModified  2023-06-20 17:39
 */

import {useCallback, useState} from 'react';
import {Button, Dialog} from '@baidu/one-ui';
import {useRequest} from '@huse/request';
import {request} from '@baidu/winds-ajax';
import {IconCompassSolid, IconChevronRightCircleSolid} from 'dls-icons-react';
import {useRouterRedirect} from 'commonLibs/route';
import AiLoadingPrimary from 'app/resource/svg/ai-loading-primary.svg';
import AiGuideBg from 'app/resource/svg/ai-guide.svg';
import {useReportType} from '../../../campaigns/hooks/useTableParams';
import useUserInfo from 'commonLibs/hooks/useUserInfo';
import {USER_LEVEL} from 'commonLibs/config/idType';
import {MarsproReportToken} from 'commonLibs/types/report/base';
import sendMonitor from 'commonLibs/utils/sendHm';
import {B2B} from 'commonLibs/config/marketTarget';

import './style.less';

function fetchCampainList(params: any) {
    return request({
        path: 'puppet/GET/MaterialQueryFunction/getMaterialCampaignList',
        params
    });
}

const tip = '使用智能创编模式，系统将应用生成式AI能力，结合您的账户情况生成账户结构，省时省力，帮助您快速实现投放目标，助力业务增长。';

export default function SmartComposeEntry() {
    const directToSmartCompose = useRouterRedirect('@smartCompose', {inheritParams: true, appendBackUrl: true});
    const [isDialogVisible, setIsDialogVisible] = useState(true);
    const {userId} = useUserInfo();

    const enterSmartCompose = useCallback(() => {
        sendMonitor('click', {level: 'campaign', item: 'enter-aicompose'});
        directToSmartCompose();
    }, [directToSmartCompose]);

    const {data: campainListData} = useRequest(fetchCampainList, {
        token: MarsproReportToken.material,
        reportType: useReportType(),
        userId,
        ids: [userId],
        idType: USER_LEVEL,
        limit: [0, 1],
        fields: ['campaignId'],
        fieldFilters: [
            {field: 'marketingTargetId', op: 'in', values: [B2B]}
        ]
    });
    // 给没有爱采购推广计划的新户弹出一个弹窗进行强提示
    const hasDialog = campainListData?.totalRowCount === 0;

    const dialogConfirm = useCallback(() => {
        enterSmartCompose();
        setIsDialogVisible(false);
    }, [enterSmartCompose, setIsDialogVisible]);

    const dialogCancel = useCallback(() => {
        setIsDialogVisible(false);
    }, [setIsDialogVisible]);

    return (
        <div className="ad-smart-compose-area">
            <div className="smart-compose-texts">
                <span className="area-title">
                    智能创编模式
                </span>
                <span className="area-desc">
                    {tip}
                </span>
            </div>
            <Button type="strong" onClick={enterSmartCompose}>
                <div className="compose-button-content">
                    <img src={AiLoadingPrimary} alt="ai-svg" />
                    开始智能创编
                </div>
            </Button>

            {hasDialog && (
                <Dialog
                    width={380}
                    needCloseIcon={false}
                    contentClassName="smart-compose-guide-dialog"
                    footer={[]}
                    visible={isDialogVisible}
                >
                    <div className="smart-compose-guide-dialog-content ai-theme-styles">
                        <img src={AiGuideBg} className="compose-dialog-background" />
                        <div className="content-area">
                            <h3 className="content-title">
                                <IconCompassSolid />
                                智能创编模式
                            </h3>
                            <div className="content-text">
                                {tip}
                            </div>
                            <div className="content-operations">
                                <Button
                                    type="text"
                                    className="operation-button"
                                    onClick={dialogCancel}
                                >
                                    暂不尝试
                                </Button>
                                <Button
                                    type="text-strong"
                                    className="operation-button"
                                    onClick={dialogConfirm}
                                >
                                    开始智能创编
                                    <IconChevronRightCircleSolid />
                                </Button>
                            </div>
                        </div>
                    </div>
                </Dialog>
            )}
        </div>
    );
}