@import '../../../smartCompose/aiTheme.less';

.ad-smart-compose-area {
    margin-bottom: @dls-padding-unit * 6;
    padding: 0 @dls-padding-unit * 6;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: @dls-border-radius-2;
    background-image: url('app/resource/svg/ai-bg.svg');
    background-size: cover;
    background-repeat: no-repeat;

    .smart-compose-texts {
        display: flex;
        align-items: center;
    }

    .area-title {
        min-width: 6em;
        margin-right: @dls-padding-unit * 2;
        font-size: 18px;
        font-weight: @dls-font-weight-2;
        color: @dls-color-gray-9;
    }

    .compose-button-content {
        display: flex;
        align-items: center;

        img {
            margin-right: @dls-padding-unit * 2;
        }
    }
}
.smart-compose-guide-dialog {
    border-radius: 16px;
}
.smart-compose-guide-dialog-content {
    font-size: @dls-font-size-1;

    .compose-dialog-background {
        width: 100%;
        display: block;
        position: absolute;
        top: 0;
        left: 0;
    }
    .content-area {
        margin-top: 100px;
        text-align: center;
    }
    .content-title {
        margin-top: @dls-padding-unit * 8;
        margin-bottom: @dls-padding-unit * 3;
        font-size: 18px;
        color: @ai-color-7;
        svg {
            margin-right: @dls-padding-unit;
        }
    }
    .content-text {
        line-height: 1.5;
    }
    .content-operations {
        margin: @dls-padding-unit * 6 0 @dls-padding-unit * 7;
        display: flex;
        justify-content: space-around;
    }
}