import {Boundary} from 'react-suspense-boundary';
import {Loading, Radio, RadioProps} from '@baidu/one-ui';
import {
    FC_SUB_MARKET_TARGET,
    fcSubMarketTargetOptionsList
} from 'commonLibs/config/marketTarget';
import {Base} from 'commonLibs/types/base';

const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;

interface Props {
    bindProject: Record<string, any>;
    value: Base.ValueOf<typeof FC_SUB_MARKET_TARGET>;
    onChange(value: Base.ValueOf<typeof FC_SUB_MARKET_TARGET>): void;
}

function SubMarketTarget(props: Props) {
    const {value, onChange, bindProject = {}} = props;
    const {projectId, projectInfo} = bindProject;
    const radioProps: RadioProps = {
        value,
        type: 'strong',
        disabled: projectId,
        className: 'sub-market-target-radio',
        onChange: (e: any) => onChange(e.target.value)
    };
    return (
        <RadioGroup {...radioProps}>
            {fcSubMarketTargetOptionsList.map(item => {
                const {value, label} = item;
                return (
                    <RadioButton key={value} value={value}>
                        {label}
                    </RadioButton>
                );
            })}
        </RadioGroup>
    );
}

export default function SubMarketTargetWithBoundary(props: Props) {
    return (
        <Boundary
            pendingFallback={<Loading />}
        >
            <SubMarketTarget {...props} />
        </Boundary>
    );
}
