import {useMemo, useEffect} from 'react';
import {Radio, Cascader} from '@baidu/one-ui';
import {isEmpty} from 'lodash-es';
import {IconExternalLink} from 'dls-icons-react';
import {useParams} from 'commonLibs/route';
import {getFcAssetFlag} from 'commonLibs/utils/getFlag';
import {
    campaignTransAssetOptions, transAssetOptionsEnum, campaignOcpcBidTypeConfig,
    TransManagerModeType
} from 'commonLibs/config/ocpc';
import {APP, isLiveSubMarket} from 'commonLibs/config/marketTarget';
import {OneLink} from 'commonLibs/SimpleLink';
import {trackPrefix, eventAssetPrefix} from 'commonLibs/config/route';
import {UNLIMITED_ID, transformTransAssetsDatasource, getTransAssetsDatasource} from 'commonLibs/TransEditor/util';
import SuspenseBoundary, {useResource} from 'commonLibs/suspenseBoundary';
import {initialAssetsAndOptimizeTransTarget} from 'app/containers/adMain/campaign/config';
import {fetchUserTransTypeList} from '../../apis/campaign/api';
import './style.less';

export function isRequired({campaignOcpcBidType, subMarketingTargetId}) {
    const liveSubMarketTarget = isLiveSubMarket(subMarketingTargetId);
    return campaignOcpcBidType !== campaignOcpcBidTypeConfig.eCPC || liveSubMarketTarget;
}

function TransAsset({
    value, onChange,
    equipmentType,
    marketingTargetId,
    subMarketingTargetId,
    className,
    isConversionCampaign,
    campaignOcpcBidType,
    isSetInitialAssetDataFromApi,
    transManagerMode,
    cvSources
}) {
    const userId = useParams().userId;
    const {assets, option} = value;
    const [
        {normalizedTransAssets, assets: availableAssetList, availableCvSources, eligibleInfo, availableTransTypes} = {}
    ] = useResource(fetchUserTransTypeList, {marketingTargetId});

    const onAssetOptionTypeChange = e => {
        const option = e.target.value;
        if (option === transAssetOptionsEnum.UNLIMITED) {
            onChange({option, assets: [UNLIMITED_ID]});
        }
        else {
            onChange({option, assets: []});
        }
    };
    const onTransAssetChange = value => {
        onChange({assets: value, option});
    };
    const transAssetsDatasource = getTransAssetsDatasource({
        assetsInfo: normalizedTransAssets.assetsInfo,
        isConversionOcpcCampaign: campaignOcpcBidType === campaignOcpcBidTypeConfig.oCPC
    });
    const transformedTransAssetsDatasource = useMemo(() => {
        return transformTransAssetsDatasource({
            equipmentType,
            marketTarget: marketingTargetId, // 营销目标
            subMarketTarget: subMarketingTargetId,
            availableAssetList,
            transAssetsDatasource,
            ...normalizedTransAssets
        });
    }, [
        equipmentType,
        marketingTargetId,
        subMarketingTargetId,
        normalizedTransAssets,
        availableAssetList,
        transAssetsDatasource
    ]);
    // 跳转到转化追踪列表页
    const url = `${getFcAssetFlag() ? eventAssetPrefix : trackPrefix}/user/${userId}`;
    const [{
        transAsset: initialTransAsset
    }] = useResource(
        initialAssetsAndOptimizeTransTarget,
        {
            marketingTargetId, assets: availableAssetList, availableCvSources, normalizedTransAssets, eligibleInfo,
            cvSources, transManagerMode, transAsset: value, availableTransTypes
        }
    );
    // 如果isSetInitialAssetDataFromApi为true且没有选中值，根据api返回值设置初始值
    useEffect(() => {
        if (isSetInitialAssetDataFromApi && isEmpty(assets) && !isEmpty(initialTransAsset)) {
            onChange(initialTransAsset);
        }
    }, [initialTransAsset]);
    return (
        <>
            {
                (!isConversionCampaign && transManagerMode !== TransManagerModeType.asset) && (
                    <Radio.Group value={option} onChange={onAssetOptionTypeChange} className="trans-assets-radio">
                        {campaignTransAssetOptions.map(({value, label}) => (
                            <Radio.Button
                                value={value}
                                key={value}
                                disabled={value === transAssetOptionsEnum.UNLIMITED && marketingTargetId === APP}
                            >
                                {label}
                            </Radio.Button>
                        ))}
                    </Radio.Group>
                )
            }
            {
                option === transAssetOptionsEnum.BY_ASSET_ID && (
                    <div className="trans-asset-container">
                        <Cascader
                            className={className}
                            value={assets}
                            options={transformedTransAssetsDatasource}
                            onChange={onTransAssetChange}
                            placeholder="请选择或搜索转化资产"
                            width={400}
                            showSearch
                            allowClear
                        />
                        <OneLink
                            toUrl={url}
                            className="link-btn"
                            name={<span>新建转化资产 <IconExternalLink /></span>}
                            target="_blank"
                            type="strong"
                            size="midium"
                        />
                    </div>
                )
            }
        </>
    );
};

export default function (props) {
    return (
        <SuspenseBoundary loading={{type: 'normal', tip: '', size: 'small'}}>
            <TransAsset {...props} />
        </SuspenseBoundary>
    );
}