import {campaignOcpcBidTypeConfig} from 'commonLibs/config/ocpc';
import {B2B, mtTextMap} from 'commonLibs/config/marketTarget';
import {optimizeTransTargetType} from 'commonLibs/config/optimizeTransTarget';

export const getIsConversionCampaign = (props: {
    marketingTargetId: keyof typeof mtTextMap;
    optimizeTransTarget?: typeof optimizeTransTargetType[keyof typeof optimizeTransTargetType];
    campaignOcpcBidType?: typeof campaignOcpcBidTypeConfig[keyof typeof campaignOcpcBidTypeConfig];
}): boolean => {
    const {marketingTargetId, optimizeTransTarget, campaignOcpcBidType} = props;
    switch (marketingTargetId) {
        case B2B:
            return optimizeTransTarget !== optimizeTransTargetType.click;
        default:
            return [campaignOcpcBidTypeConfig.oCPC, campaignOcpcBidTypeConfig.cvMax].includes(campaignOcpcBidType);
    }
};
