import {values as getValues, isEmpty, cloneDeep} from 'lodash-es';
import {
    getBackendDataFromUI,
    getSaveToAssetsData
} from 'app/containers/campaigns/list/editor/schedule/utils';
import {
    getEquipmentTypeInitValue
} from 'app/containers/fcNew/campaign/util';
import {
    APP,
    COMMODITY,
    CPQL,
    isLiveSubMarket
} from 'commonLibs/config/marketTarget';
import {getIsShowGeographicLocation} from 'commonLibs/utils/handleRegion';
import queryString from 'query-string';
import {
    REGION_TYPE,
    STORE_REGION_TYPE_CONFIG,
    STORE_AREA_TYPE_CONFIG
} from 'commonLibs/config/regionConfig';
import {
    campaignOcpcBidTypeConfig,
    campaignBidTypeConfig,
    transAssetOptionsEnum,
    CVSOURCES_UNLIMITED,
    cvSourcesOptions,
    TransManagerModeType,
    FLOW_SCOPE
} from 'commonLibs/config/ocpc';
import {budgetTypeEnum, budgetTypeConfig} from 'app/containers/campaigns/list/editor/budget/config';
import {isCVMaxCampaignBudgetUser, isFcTransAssetsUser, isStoreNewProcessUser} from 'commonLibs/utils/getFlag';
import {
    schedueTypeConfig
} from 'app/containers/fcNew/campaign/schedule/config';
import {DEFAULT_NEW_BID_VALUE, DEFAULT_ORIENT_BID_VALUE} from 'app/containers/crowds/config/common';
import {getIsConversionCampaign} from './common';
import {CrowdType} from '../crowd/config';
import {isInQinggeIframe} from 'commonLibs/utils/isInIframe';
import {globalAnchorInfo} from '../anchorInfo';

export const queryArg = ['adviceId', 'adviceOptType'];

export function campaignParamsPipeline(generators, initial) {
    return function (params) {
        return generators.reduce(
            (result, generator) => generator(params, result),
            cloneDeep(initial)
        );
    };
}

export function campaignProjectIdParams({values}, result) {
    const {
        bindProject: projectIdInfo
    } = values;
    // 加入项目
    if (projectIdInfo?.projectId) {
        const {projectId} =  projectIdInfo;
        result.campaignType.projectId = projectId;
    }
    return result;
};

export function campaignSubMarketingTargetParams({values}, result) {
    const {
        subMarketingTargetId,
        marketingTargetId
    } = values;
    const anchorInfo = globalAnchorInfo.getInfo();
    if (isInQinggeIframe && marketingTargetId === CPQL && !!anchorInfo?.rows?.length) {
        result.campaignType.subMarketingTargetId = subMarketingTargetId;
    }
    return result;
};

export function campaignBusinessPointParams({values}, result) {
    const {
        businessPoint
    } = values;
    const {path = []} = businessPoint || {};
    const {businessPointId, businessPointName} = path[path.length - 1] || {};
    // 业务点
    if (result.campaignType) {
        result.campaignType.businessPointId = businessPointId || 0;
        result.campaignType.businessPointName = businessPointName;
    }
    return result;
};

export function campaignPromotionParams({values}, result) {
    const {
        promotionScene
    } = values;
    // 推广场景
    result.campaignType.promotionScene = promotionScene;
    return result;
};

function handleTransInfo(result, {
    transAsset,
    normalizedTransAssets,
    cvSources,
    deletedCvSources,
    transManagerMode
}) {
    if (transManagerMode !== undefined) {
        result.campaignType.transManagerMode = transManagerMode;
        if (transManagerMode === TransManagerModeType.unlimited) {
            result.campaignType.campaignCvSources = [CVSOURCES_UNLIMITED];
            result.campaignType.transAsset = transAssetOptionsEnum.UNLIMITED;
        }
        if (transManagerMode === TransManagerModeType.cvSource) {
            const {source} = cvSources;
            result.campaignType.campaignCvSources = source.filter(item => !deletedCvSources.includes(item));
        }
        if (transManagerMode === TransManagerModeType.asset) {
            const {assets, option} = transAsset;
            result.campaignType.transAsset = option;
            const [, transAssetId] = assets;
            result.campaignType.campaignCvSources = normalizedTransAssets.assetsData[transAssetId]?.availableCvSources;
            result.campaignType.transAssetId = transAssetId;
        }
    }
    else {
        if (isFcTransAssetsUser()) {
            const {assets, option} = transAsset;
            result.campaignType.transAsset = option;
            if (option === transAssetOptionsEnum.UNLIMITED) {
                result.campaignType.campaignCvSources = [CVSOURCES_UNLIMITED];
            }
            else {
                const [, transAssetId] = assets;
                /* eslint-disable max-len */
                result.campaignType.campaignCvSources = normalizedTransAssets.assetsData[transAssetId]?.availableCvSources;
                result.campaignType.transAssetId = transAssetId;
            }
        }
        else {
            const {option, source} = cvSources;
            if (option === cvSourcesOptions.FILTER) {
                result.campaignType.campaignCvSources = source.filter(item => !deletedCvSources.includes(item));
            }
            else {
                result.campaignType.campaignCvSources = [CVSOURCES_UNLIMITED];
            }
        }
    }
}

export function campaignClickPriceParams({
    values, normalizedTransAssets, marketingTargetId, deletedCvSources
}, result) {
    const isOcpcCampaign = getIsConversionCampaign({
        marketingTargetId,
        ...values
    });
    if (isOcpcCampaign) {
        return result;
    }
    const {
        campaignBid,
        campaignTransTypes,
        suggestTransTypes,
        transAsset,
        campaignOcpcBidType,
        cvSources,
        transManagerMode
    } = values;
    // 点击计划处理
    result.campaignType.campaignOcpcBidType = campaignOcpcBidType || campaignOcpcBidTypeConfig.CPC;
    result.campaignType.campaignBidType = campaignBidTypeConfig.cpc;
    result.campaignType.campaignBid = +campaignBid;
    // 增量模式计划
    if (campaignOcpcBidType === campaignOcpcBidTypeConfig.eCPC) {
        // 自动优选逻辑，前端默认赋campaignAutoOptimizationStatus、suggestType填充至transType
        if (!campaignTransTypes.length) {
            result.campaignType.campaignAutoOptimizationStatus = true;
            result.campaignType.campaignTransTypes = suggestTransTypes;
        }
        else {
            result.campaignType.campaignAutoOptimizationStatus = false;
            result.campaignType.campaignTransTypes = campaignTransTypes;
        }
        handleTransInfo(result, {
            transAsset,
            normalizedTransAssets,
            cvSources,
            deletedCvSources,
            transManagerMode
        });
    }
    return result;
}

export function campaignConversionPriceParams({
    values, normalizedTransAssets, marketingTargetId, deletedCvSources
}, result) {
    const isOcpcCampaign = getIsConversionCampaign({
        marketingTargetId,
        ...values
    });
    if (!isOcpcCampaign) {
        return result;
    }
    const {
        campaignBudget = {},
        campaignTransTypes,
        campaignOcpcBid,
        transAsset,
        campaignOcpcBidType,
        cvSources,
        transManagerMode
    } = values;
    result.campaignType.campaignOcpcBidType = campaignOcpcBidType;
    result.campaignType.campaignBidType = campaignBidTypeConfig.oCPC;
    result.campaignType.campaignTransTypes = campaignTransTypes;
    // 出价
    if (campaignOcpcBidType !== campaignOcpcBidTypeConfig.cvMax) {
        result.campaignType.campaignOcpcBid = +campaignOcpcBid;
    }
    else if (isCVMaxCampaignBudgetUser() && campaignBudget.transPrice) {
        result.campaignType.campaignOcpcBid = +campaignBudget.transPrice;
    }
    handleTransInfo(result, {
        transAsset,
        normalizedTransAssets,
        cvSources,
        deletedCvSources,
        transManagerMode
    });
    return result;
}

export function campaignBudgetParams({values}, result) {
    const {
        campaignBudget = {}
    } = values;

    const {budgetType, budget, sharedBudgetId} = campaignBudget;
    if (budgetType === budgetTypeConfig.NO_LIMIT) {
        result.campaignType.budget = budgetTypeEnum[budgetTypeConfig.NO_LIMIT];
    }
    else if (budgetType === budgetTypeConfig.DAILY_BUDGET) {
        result.campaignType.budget = budget * 1;
    }
    else if (budgetType === budgetTypeConfig.SHARE_BUDGET) {
        result.campaignType.sharedBudgetId = sharedBudgetId;
    }
    return result;
}

export function campaignSettingParams({values, marketingTargetId}, result) {
    const {
        campaignName,
        shopType,
        equipmentType = {},
        subMarketingTargetId
    } = values;
    result.campaignType.marketingTargetId = marketingTargetId;
    result.campaignType.businessPointId = 0;
    result.campaignType.campaignDeviceBidStatus = false;
    // 计划名称
    result.campaignType.campaignName = campaignName;
    // 设备相关
    if (marketingTargetId !== COMMODITY) {
        result.campaignType.equipmentType = getEquipmentTypeInitValue(marketingTargetId, shopType);
    }
    if (!isEmpty(equipmentType)) {
        const {
            equipmentType: equipmentTypeValue
        } = equipmentType;
        result.campaignType.equipmentType = equipmentTypeValue;
    }
    // 直播间-设备兜底
    if (
        isLiveSubMarket(subMarketingTargetId)
        && marketingTargetId === CPQL
        && isInQinggeIframe
        && result.campaignType.equipmentType
    ) {
        result.campaignType.equipmentType = FLOW_SCOPE.MOBILE;
    }
    // 优化中心带上参数queryArg：['adviceId', 'adviceOptType']
    const locationSearch = queryString.parse(window.location.search);
    queryArg.map(item => {
        if (locationSearch[item]) {
            result.campaignType[item] = locationSearch[item];
        }
    });
    return result;
};

export function campaignRegionParams({values, marketingTargetId}, result) {
    const {
        campaignRegion = {}
    } = values;
    const {
        regionMainType,
        regionTarget,
        regionPriceFactor,
        areaType,
        regionArea,
        regionStore,
        regionType,
        geoLocationStatus,
        storeDistance
    } = campaignRegion;

    if (getIsShowGeographicLocation()) {
        result.campaignType.geoLocationStatus = geoLocationStatus;
    }
    result.campaignType.regionTarget = regionTarget;
    if (regionMainType === REGION_TYPE.ACCOUNT_REGION) {
        result.campaignType.regionTarget = [];
    }
    if (regionMainType === REGION_TYPE.CAMPAIGN_REGION
        && marketingTargetId !== APP
        && regionType === STORE_REGION_TYPE_CONFIG.REGION
    ) {
        result.campaignType.regionPriceFactor = regionPriceFactor;
    }
    // 门店地域选择商圈、门店周边时装载数据
    if (
        (regionMainType === REGION_TYPE.CAMPAIGN_REGION || isStoreNewProcessUser())
        && regionType === STORE_REGION_TYPE_CONFIG.AREA
    ) {
        result.campaignType.regionType = regionType;
        // 当选择商圈 / 门店周边地域时，需要清空 省市 regionTarget 信息。
        result.campaignType.regionTarget = [];

        if (areaType === STORE_AREA_TYPE_CONFIG.BUSINESS) {
            result.campaignType.regionArea = regionArea;
            if (!isStoreNewProcessUser()) {
                result.campaignType.regionStore = [];
            }
        }
        else if (areaType === STORE_AREA_TYPE_CONFIG.STORE) {
            if (isStoreNewProcessUser()) {
                result.campaignType.storeDistance = storeDistance;
            }
            else {
                result.campaignType.regionStore = regionStore;
            }
            result.campaignType.regionArea = [];
        }
    }
    return result;
};

export function campaignScheduleParams({values, marketingTargetId}, result) {
    const {
        campaignSchedule = {}
    } = values;
    const isOcpcCampaign = getIsConversionCampaign({
        marketingTargetId,
        ...values
    });
    // 时段
    const {
        scheduleValue,
        type,
        ...others
    } = campaignSchedule;
    const isSelectAll = type === schedueTypeConfig.all;
    const showBasic = isOcpcCampaign || marketingTargetId === APP;
    const backendSchedule = getBackendDataFromUI(scheduleValue, showBasic);
    const {schedule, schedulePriceFactors} = backendSchedule;
    result.campaignType.schedule = isSelectAll ? [] : schedule;
    if (marketingTargetId !== APP) {
        result.campaignType.schedulePriceFactors = schedulePriceFactors;
    }
    // 时段模板
    const cycTemplateType = type === schedueTypeConfig.all ? null : getSaveToAssetsData(others, backendSchedule);
    if (cycTemplateType) {
        result = {
            ...result,
            ...cycTemplateType
        };
    }
    return result;
}

export function campaignCrowdParams({values, marketingTargetId}, result) {
    const isOcpcCampaign = getIsConversionCampaign({
        marketingTargetId,
        ...values
    });
    const {
        crowd
    } = values;
    const {type, orientCrowd, excludeCrowd} = crowd;
    if (type.includes(CrowdType.UMLIMITED)) {
        return result;
    }
    const crowdList = [];
    if (type.includes(CrowdType.ORIENT_CROWD)) {
        // 定向人群
        getValues(orientCrowd).map(crowd => {
            const {strategyId = 0, mcId = 0, crowdId} = crowd;
            // TODO 这边的逻辑很怪，转化计划在页面让用户填了，结果这边还是用默认值
            const crowdPriceRatio = isOcpcCampaign
                ? DEFAULT_ORIENT_BID_VALUE : crowd.bidValue || DEFAULT_NEW_BID_VALUE;
            if (crowdId < 0) {
                crowdList.push({
                    crowdPriceRatio,
                    strategyId,
                    mcId
                });
            }
            else {
                crowdList.push({
                    crowdId,
                    crowdPriceRatio
                });
            }
        });
    }
    if (type.includes(CrowdType.EXCLUDE_CROWD)) {
        // 排除人群
        getValues(excludeCrowd).map(crowd => {
            const {strategyId = 0, mcId = 0, crowdId} = crowd;
            if (crowdId < 0) {
                crowdList.push({
                    strategyId,
                    mcId
                });
            }
            else {
                crowdList.push({
                    crowdId
                });
            }
        });
    }
    result.crowdBindTypes = crowdList;
    return result;
}
