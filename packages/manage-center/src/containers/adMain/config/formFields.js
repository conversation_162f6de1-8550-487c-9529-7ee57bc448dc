/**
 * @file steps
 * <AUTHOR>
 */
import {isObject} from 'lodash-es';
import {
    isUnitAgentUrlUser,
    isFcTransAndAssetFuheUser,
    isFcLiveUser,
    isAdgroupProductRequiredUser
} from 'commonLibs/utils/getFlag';
import {
    B2B,
    STORE,
    CPQL,
    WEB,
    APP,
    NATIVE,
    isLiveSubMarket,
    isBjhSubMarket
} from 'commonLibs/config/marketTarget';
import Tip from 'commonLibs/Tips';
import {isInQinggeIframe} from 'commonLibs/utils/isInIframe';
import {getDefaultProductCategoryType} from 'commonLibs/components/selectCard/product/config';
import {TipWhyRequiredProduct} from 'commonLibs/components/TipWhyRequiredProduct';
import {globalAnchorInfo} from '../campaign/anchorInfo';

export const CREATIVE_STEPS = {
    CREATIVE_CONTENT: 'creative-content',
    CREATIVE_URL: 'creative-url',
    CREATIVE_IMAGE: 'creative-image'
};
export const ADGROUP_STEPS = {
    ADGROUP_BASE: 'adgroup-setting-base',
    ADGROUP_ORIENT: 'adgroup-setting-orient',
    ADGROUP_NAME: 'adgroup-setting-name'
};

const bindProjectTipGroupConfig = {
    group: 'bindProjectTip',
    title: '',
    fields: ['bindProjectTip'],
    groupProps: {
        className: 'campaign-bind-project-tip',
        style: {
            margin: 0,
            padding: 0,
            backgroundColor: 'transparent'
        }
    },
    visible: formData => formData.bindProject?.projectId
};

// -------------------分营销目标的计划流程字段----------------------
// ---爱采购---
export const B2BCampaignFields = createFormFieldsProxy([
    {
        group: 'promote',
        title: '推广设置',
        fields: [
            'promotionScene'
        ]
    },
    {
        group: 'bidAndBudget',
        title: '出价与预算',
        fields: [
            'optimizeTransTarget',
            'campaignTransTypes',
            'campaignBid',
            'campaignOcpcBid',
            'campaignBudget'
        ]
    },
    {
        group: 'settings',
        title: <span>计划设置<Tip keyName="rfqAgreement" /></span>,
        fields: [
            'equipmentType',
            'campaignSchedule',
            'campaignRegion',
            'campaignName'
        ]
    }
]);
// ---本地推广---
export const STORECampaignFields = createFormFieldsProxy([
    {
        group: 'project',
        title: '加入项目',
        desc: '加入项目后，项目层级设置的出价模式、转化资产、优化目标、出价将会优先生效，若该计划与项目解绑，则计划层级的设置生效。',
        fields: [
            'bindProject'
        ]
    },
    bindProjectTipGroupConfig,
    {
        group: 'promote',
        title: '推广设置',
        fields: [
            'promotionScene',
            'businessPoint'
        ]
    },
    {
        group: 'bidAndBudget',
        title: '出价与预算',
        fields: [
            'campaignBidType',
            'campaignOcpcBidType',
            'transManagerMode',
            'transAsset',
            ...(isFcTransAndAssetFuheUser() ? ['cvSources'] : []),
            'optimizeTransTarget',
            'campaignTransTypes',
            'campaignBid',
            'campaignOcpcBid',
            'campaignBudget'
        ]
    },
    {
        group: 'setting',
        title: <span>计划设置<Tip keyName="rfqAgreement" /></span>,
        fields: [
            'campaignSchedule',
            'campaignRegion',
            'campaignName'
        ]
    }
]);
export const CPQLCampaignFields = createFormFieldsProxy([
    {
        group: 'project',
        title: '加入项目',
        desc: '加入项目后，项目层级设置的出价模式、转化资产、优化目标、出价将会优先生效，若该计划与项目解绑，则计划层级的设置生效。',
        fields: [
            'bindProject'
        ]
    },
    bindProjectTipGroupConfig,
    {
        group: 'promote',
        title: '推广设置',
        fields: [
            'businessPoint'
        ]
    },
    {
        group: 'subMarketingTargetId',
        title: '营销场景',
        fields: [
            'subMarketingTargetId'
        ],
        visible: () => {
            const anchorInfo = globalAnchorInfo.getInfo();
            return isFcLiveUser() && !!anchorInfo?.rows?.length && isInQinggeIframe;
        }
    },
    {
        group: 'bidAndBudget',
        title: '出价与预算',
        fields: [
            'campaignBidType',
            'campaignOcpcBidType',
            'transManagerMode',
            'cvSources',
            'transAsset',
            'campaignTransTypes',
            'campaignBid',
            'campaignOcpcBid',
            'campaignBudget'
        ]
    },
    {
        group: 'settings',
        title: <span>计划设置<Tip keyName="rfqAgreement" /></span>,
        fields: [
            'campaignSchedule',
            'campaignRegion',
            {group: 'settingsGroup', fields: ['equipmentType', 'crowdType', 'orientCrowd', 'excludeCrowd']},
            'campaignName'
        ]
    }
]);

// 原生互动
export const NATIVECampaignFields = createFormFieldsProxy([
    {
        group: 'project',
        title: '加入项目',
        desc: '加入项目后，项目层级设置的出价模式、转化资产、优化目标、出价将会优先生效，若该计划与项目解绑，则计划层级的设置生效。',
        fields: [
            'bindProject'
        ]
    },
    bindProjectTipGroupConfig,
    {
        group: 'subMarketingTargetId',
        title: '营销场景',
        fields: [
            'subMarketingTargetId'
        ],
        visible: () => {
            return isInQinggeIframe;
        }
    },
    {
        group: 'bjhUserInfo',
        title: '推广身份',
        fields: [
            'bjhUserInfo'
        ]
    },
    {
        group: 'bidAndBudget',
        title: '出价与预算',
        fields: [
            'campaignBidType',
            'campaignOcpcBidType',
            'cvSources',
            'optimizeTransTarget',
            'campaignTransTypes',
            'campaignBid',
            'campaignOcpcBid',
            'campaignBudget'
        ]
    },
    {
        group: 'settings',
        title: <span>计划设置<Tip keyName="rfqAgreement" /></span>,
        fields: [
            'campaignSchedule',
            'campaignRegion',
            {
                group: 'settingsGroup',
                fields: [
                    'equipmentType', 'crowdType', 'orientCrowd', 'excludeCrowd'
                ]
            },
            'campaignName'
        ]
    }
]);

export function getCampaignFields({marketingTargetId}) {
    switch (marketingTargetId) {
        case B2B:
            return B2BCampaignFields;
        case CPQL:
            return CPQLCampaignFields;
        case STORE:
            return STORECampaignFields;
        case NATIVE:
            return NATIVECampaignFields;
        default:
            return B2BCampaignFields;
    }
}


// ---------------------分营销目标的单元流程字段----------------------
// ---爱采购---
export const B2BAdgroupFields = createFormFieldsProxy([
    {
        group: 'url',
        title: '落地页信息',
        fields: ['mobileFinalUrl', 'pcFinalUrl']
    },
    {
        group: 'orient',
        title: '定向设置',
        fields: ['adgroupAutoTargetingStatus', 'keywords']
    },
    {
        group: 'brand',
        title: '品牌信息',
        fields: ['brandName']
    },
    {
        group: 'settings',
        title: '单元设置',
        fields: ['adgroupName']
    }
]);
// ---本地推广---
export const STOREAdgroupFields = createFormFieldsProxy([
    {
        group: 'orient',
        title: '定向设置',
        fields: ['adgroupAutoTargetingStatus', 'keywords']
    },
    {
        group: 'pageSetting',
        title: '推广对象',
        fields: ['storeInfo',
            {group: 'settingsGroup', fields: ['storeMobileUrl', 'storeMobileUrlList']}
        ]
    },
    {
        group: 'brand',
        title: '品牌信息',
        fields: ['brandName']
    },
    {
        group: 'settings',
        title: '单元设置',
        fields: ['adgroupName']
    }
]);

const getAdgroupAgentUrlSettingFields = () => (isUnitAgentUrlUser() ? ['adgroupUrlType', 'projectAgentUrl',
    'adgroupAgentUrl', 'adgroupAgentParam', 'useAgentUrl'] : []);

// ---销售线索---
const getCPQLAdgroupFields = ({subMarketingTargetId}) => {
    const defaultProductCategoryType = getDefaultProductCategoryType();
    return createFormFieldsProxy([
        ...(isLiveSubMarket(subMarketingTargetId) && isInQinggeIframe ? [
            {
                group: 'relativeAnchor',
                title: '关联主播',
                fields: ['relativeAnchor']
            }
        ] : []),
        ...(isLiveSubMarket(subMarketingTargetId) ? [] : [
            {
                group: 'url',
                title: '落地页信息',
                fields: [
                    ...getAdgroupAgentUrlSettingFields(),
                    'pcFinalUrl',
                    {group: 'pcUrlGroup', fields: ['pcTrackParam', 'pcTrackTemplate']},
                    'mobileFinalUrl',
                    'mobileFinalUrlPreview',
                    {group: 'mobileUrlGroup', fields: ['mobileTrackParam', 'mobileTrackTemplate']}
                ]
            }
        ]),
        ...(defaultProductCategoryType ? [
            {
                group: 'product',
                title: '投放产品',
                slots: {
                    before: isAdgroupProductRequiredUser() ? TipWhyRequiredProduct : null
                },
                fields: ['newAdgroupStructuredCategoryType', 'product', 'autoProductContent']
            }
        ] : []),
        {
            group: 'orient',
            title: '定向设置',
            fields: ['adgroupAutoTargetingStatus', 'keywords']
        },
        {
            group: 'brand',
            title: '品牌信息',
            fields: ['brandName']
        },
        {
            group: 'settings',
            title: '单元设置',
            fields: ['adgroupPrice', 'adgroupName']
        }
    ]);
};

// ---销售线索 落地页类型 ---
export const getCPQLAdgroupUrlFields = () => createFormFieldsProxy([
    {
        group: 'url',
        title: '落地页信息',
        fields: [
            ...getAdgroupAgentUrlSettingFields(),
            'pcFinalUrl',
            {group: 'pcUrlGroup', fields: ['pcTrackParam', 'pcTrackTemplate']},
            'mobileFinalUrl',
            'mobileFinalUrlPreview',
            {group: 'mobileUrlGroup', fields: ['mobileTrackParam', 'mobileTrackTemplate']}
        ]
    }
]);

// ---网站链接---
export const WEBAdgroupFields = createFormFieldsProxy([
    {
        group: 'url',
        title: '落地页信息',
        fields: [
            ...getAdgroupAgentUrlSettingFields(),
            'pcFinalUrl',
            {group: 'pcUrlGroup', fields: ['pcTrackParam', 'pcTrackTemplate', 'pcUnitUrlPreview']},
            'mobileFinalUrl',
            {group: 'mobileUrlGroup', fields: ['mobileTrackParam', 'mobileTrackTemplate', 'mobileUnitUrlPreview']}
        ]
    },
    {
        group: 'orient',
        title: '定向设置',
        fields: ['adgroupAutoTargetingStatus', 'keywords']
    },
    {
        group: 'brand',
        title: '品牌信息',
        fields: [
            'brandName'
        ]
    },
    {
        group: 'adgroupNameSettings',
        title: '单元设置',
        fields: ['adgroupPrice', 'adgroupName']
    }
]);

// ---应用推广---
export const APPAdgroupFields = createFormFieldsProxy([
    {
        group: 'appSettings',
        title: '应用设置',
        fields: [
            'osType', 'appSelect', 'appShopDirectStatus'
        ]
    },
    {
        group: 'url',
        title: '落地页信息',
        fields: [
            'pcFinalUrl',
            {group: 'pcUrlGroup', fields: ['pcTrackParam', 'pcTrackTemplate']},
            'mobileFinalUrl',
            'mobileFinalUrlPreview',
            {group: 'mobileUrlGroup', fields: ['mobileTrackParam', 'mobileTrackTemplate']}
        ]
    },
    {
        group: 'orient',
        title: '定向设置',
        fields: ['adgroupAutoTargetingStatus', 'keywords']
    },
    {
        group: 'adgroupNameSettings',
        title: '单元设置',
        fields: ['adgroupPrice', 'adgroupName']
    }
]);

// ---原生互动---
export const getNATIVEAdgroupFields = ({subMarketingTargetId}) => createFormFieldsProxy([
    ...(
        isBjhSubMarket(subMarketingTargetId) ? [
            {
                group: 'bjhContentInfos',
                title: '推广内容',
                fields: ['bjhContent']
            },
            {
                group: 'orient',
                title: '定向设置',
                fields: ['keywords']
            }
        ] : []
    ),
    {
        group: 'adgroupNameSettings',
        title: '单元设置',
        fields: ['adgroupName']
    }
]);
export function getAdgroupFields({marketingTargetId, subMarketingTargetId}) {
    switch (marketingTargetId) {
        case B2B:
            return B2BAdgroupFields;
        case STORE:
            return STOREAdgroupFields;
        case CPQL:
            return getCPQLAdgroupFields({subMarketingTargetId});
        case WEB:
            return WEBAdgroupFields;
        case APP:
            return APPAdgroupFields;
        case NATIVE:
            return getNATIVEAdgroupFields({subMarketingTargetId});
        default:
            return B2BAdgroupFields;
    }
}

// 分营销目标的单元流程字段
// ---爱采购---
export const B2BCreativeFields = createFormFieldsProxy([
    {
        group: 'creativeText',
        title: '创意文案',
        fields: []
    },
    {
        group: 'creativeSegment',
        title: '创意素材',
        fields: []
    }
]);
export const CPQLCreativeFields = createFormFieldsProxy([
    {
        group: 'creativeText',
        title: '创意文案',
        fields: [
            {
                arrayField: 'creativeTexts',
                fields: [
                    'title', 'description1', 'description2',
                    {
                        group: 'urlGroup',
                        fields: [
                            'pcFinalUrl',
                            {group: 'pcUrlGroup', fields: ['pcTrackParam', 'pcTrackTemplate']},
                            'mobileFinalUrl',
                            {group: 'mobileUrlGroup', fields: ['mobileTrackParam', 'mobileTrackTemplate']},
                            'miniProgramUrl'
                        ]
                    }
                ]
            }
        ]
    },
    {
        group: 'creativeSegment',
        title: '创意素材',
        fields: ['segmentBinds']
    }
]);

export const WEBCreativeFields = createFormFieldsProxy([
    {
        group: 'creativeText',
        title: '创意文案',
        fields: [
            {
                arrayField: 'creativeTexts',
                fields: [
                    'title', 'description1', 'description2',
                    {
                        group: 'urlGroup',
                        fields: [
                            'pcFinalUrl',
                            {group: 'pcUrlGroup', fields: ['pcTrackParam', 'pcTrackTemplate', 'pcUnitUrlPreview']},
                            'mobileFinalUrl',
                            {
                                group: 'mobileUrlGroup',
                                fields: ['mobileTrackParam', 'mobileTrackTemplate', 'mobileUnitUrlPreview']
                            },
                            'miniProgramUrl'
                        ]
                    }
                ]
            }
        ]
    },
    {
        group: 'creativeSegment',
        title: '创意素材',
        fields: ['segmentBinds']
    }
]);

export const APPCreativeFields = createFormFieldsProxy([
    {
        group: 'creativeText',
        title: '创意文案',
        fields: [
            {
                arrayField: 'creativeTexts',
                fields: [
                    'title', 'description1', 'description2',
                    {
                        group: 'urlGroup',
                        fields: [
                            'pcFinalUrl',
                            {group: 'pcUrlGroup', fields: ['pcTrackParam', 'pcTrackTemplate']},
                            'mobileFinalUrl',
                            {group: 'mobileUrlGroup', fields: ['mobileTrackParam', 'mobileTrackTemplate']},
                            'miniProgramUrl'
                        ]
                    }
                ]
            }
        ]
    },
    {
        group: 'creativeSegment',
        title: '创意素材',
        fields: ['segmentBinds']
    }
]);

export const STORECreativeFields = createFormFieldsProxy([
    {
        group: 'creativeText',
        title: '创意文案',
        fields: [
            {
                arrayField: 'creativeTexts',
                fields: [
                    'title', 'description1', 'description2'
                ]
            }
        ]
    },
    {
        group: 'creativeSegment',
        title: '创意素材',
        fields: ['segmentBinds']
    }
]);

export const programCreativeFields = createFormFieldsProxy([
    {
        group: 'creatives',
        title: '创意设置',
        fields: [
            'title', 'desc', 'segmentBinds'
        ]
    }
]);
export function getCreativeFields({marketingTargetId, adType}) {
    if (marketingTargetId === B2B) {
        return B2BCreativeFields;
    }
    if (marketingTargetId === CPQL) {
        return CPQLCreativeFields;
    }
    if (marketingTargetId === STORE) {
        return STORECreativeFields;
    }
    if (marketingTargetId === WEB) {
        return WEBCreativeFields;
    }
    if (marketingTargetId === APP) {
        return APPCreativeFields;
    }
    return [];
}


export const getAdMainProcessSteps = ({marketingTargetId, v2 = false, subMarketingTargetId}) => {
    if (v2) {
        return [{
            title: '推广计划',
            children: [
                {title: '营销目标', id: 'market-target-area'},
                ...getCampaignFields({marketingTargetId}).steps
            ],
            id: 'ad-main-new-campaign-page',
            getContainer: () => window
        }, {
            title: '推广单元',
            children: [
                ...getAdgroupFields({marketingTargetId, subMarketingTargetId}).steps,
                ...programCreativeFields.steps
                    .map(({title, id, list}) => ({title, id, list: list.map(field => `${field}$`)}))
            ],
            id: 'ad-main-new-adgroup-page',
            getContainer: () => window
        }];
    }
    return [{
        title: '推广计划',
        children: [
            {title: '营销目标', id: 'market-target-area'},
            ...getCampaignFields({marketingTargetId}).steps
        ],
        id: 'ad-main-new-campaign-page',
        getContainer: () => window
    }, {
        title: '推广单元',
        children: getAdgroupFields({marketingTargetId, subMarketingTargetId}).steps,
        id: 'ad-main-new-adgroup-page',
        getContainer: () => window
    }, {
        title: '推广创意',
        children: getCreativeFields({marketingTargetId}).steps,
        getContainer: () => window
    }];
};

const getFieldsArrFromNestingGroup = fields => {
    return fields.reduce((arr, field) => {
        return arr.concat(isObject(field) ? getFieldsArrFromNestingGroup(field.fields) : field);
    }, []);
};

function createFormFieldsProxy(config) {
    return new Proxy(
        {
            $getFieldsByGroup(groupName) {
                return config.find(({group}) => group === groupName)?.fields || [];
            },
            $getGroupByField(field) {
                return config.find(({fields}) => fields.includes(field))?.group;
            }
        },
        {
            get(target, property) {
                if (target[property]) {
                    return target[property];
                }
                if (property === 'config') {
                    return config;
                }
                if (property === 'allFields') {
                    return config.reduce((arr, {fields}) => {
                        const fieldsArr = getFieldsArrFromNestingGroup(fields);
                        arr.push(...fieldsArr);
                        return arr;
                    }, []);
                }
                if (property === 'steps') {
                    return config.map(({group, title, fields}) => {
                        const fieldsArr = getFieldsArrFromNestingGroup(fields);
                        return {
                            title,
                            id: `${group}`,
                            list: fieldsArr
                        };
                    });
                }
            }
        }
    );
}
