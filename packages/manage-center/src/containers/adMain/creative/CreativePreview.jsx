import {useMemo} from 'react';
import CreativePreview from 'app/containers/fcNew/creative/preview';
import BjhPreview from 'app/containers/fcNew/creative/preview/bjhPreview';
import {NATIVE} from 'commonLibs/config/marketTarget';

export function LiveCreativePreview({
    formData: creativeInfo = {},
    marketingTargetId,
    creativeType,
    imageTypes,
    displayUrl = '',
    portraitPicUrl = '',
    adgroups
}) {
    const {segments = [], videos = []} = creativeInfo.segmentBinds || {};
    const adgroupIds = adgroups?.map(item => item.adgroupId) || [];
    const previewInfoFromProps = useMemo(
        () => ({creativeInfo, bindInfo: {segments, videos}}),
        [creativeInfo, segments, videos]
    );
    if (marketingTargetId === NATIVE) {
        return (
            <BjhPreview
                previewInfoFromProps={previewInfoFromProps}
                adgroups={adgroups}
            />
        );
    }
    return (
        <CreativePreview
            isNewProcess
            creativeType={creativeType}
            marketingTargetId={marketingTargetId}
            previewInfoFromProps={previewInfoFromProps}
            imageTypes={imageTypes}
            adgroupIds={adgroupIds}
            displayUrl={displayUrl}
            portraitPicUrl={portraitPicUrl}
        />
    );
}