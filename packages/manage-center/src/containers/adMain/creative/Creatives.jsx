import {forwardRef, useCallback, useMemo, memo, useEffect, useState} from 'react';
import classNames from 'classnames';
import {pick, noop, isEqual, cloneDeep, partial} from 'lodash-es';
import {useFormDefinition, useFieldValue} from '@baidu/react-formulator';
import {giveMeShortcuts} from 'commonLibs/utils/handleOptions';
import {useSlot} from 'commonLibs/hooks/slot';
import {ProgrammaticCreative} from './programmaticCreative';
import {CustomizeCreative} from './customizeCreative';
import {creativeTypeEnum} from 'commonLibs/config/creativeText';
import {MAX_PROGRAM_COUNT} from './programmaticCreative/config';
import {getProgrammaticCreativeAcceptFn, getCustomizeCreativeAcceptFn} from './recommend';
import {useCreativeSharedInfo} from './context';
import {LiveCreativePreview} from './CreativePreview';
import {LiveRichDegreeAlert} from './RichDegree';
import './style.less';
import {CreativeHorizontalGroup} from './components/creativeGroup';
import {AdgroupSwitch} from './fields/adgroupSwitch';
import {ToggleCreativeButton} from './fields/toggleCreativeButton';
import {useBotContextInfo} from 'commonLibs/context/botInfo';
import {isInQinggeIframe} from 'commonLibs/utils/isInIframe';
import {NATIVE} from 'commonLibs/config/marketTarget';
import {isAutoCreativeUser} from 'commonLibs/utils/getFlag';

const [creativeTypeOptions] = giveMeShortcuts([
    {value: creativeTypeEnum.programmatic, label: '自适应'},
    {value: creativeTypeEnum.customize, label: '自定义'}
]);


const getCreativeConfig = ({marketingTargetId}) => ({
    fields: [
        {
            group: 'creative-toggle-group',
            fields: [
                {
                    field: 'isShowCreativeForm',
                    label: null,
                    use: [ToggleCreativeButton, {
                        titles: ['展开创意设置', '收起稍后填写'],
                        confirmTip: '收起后提交将无法保存已填写的创意内容，如需保存可展开后操作'
                    }],
                    componentProps: () => ({marketingTargetId})
                }
            ],
            use: [CreativeHorizontalGroup, {
                className: 'ad-main-toggle-creative-group'
            }],
            visible: formData => formData.isInAdgroupProcess && marketingTargetId !== NATIVE
        },
        {
            group: 'creative-group',
            fields: [
                {
                    field: 'creativeType',
                    name: '生成方式',
                    label: null,
                    use: ['RadioGroup', {options: creativeTypeOptions}],
                    visible: () => marketingTargetId !== NATIVE
                },
                {
                    group: 'creativeGroup',
                    fields: [
                        {
                            field: 'creativeTextOptimizationStatus',
                            label: null,
                            use: [AdgroupSwitch, {
                                field: 'creativeTextOptimizationStatus'
                            }],
                            visible: function (formData) {
                                return !formData.isMultiAdgroup;
                            }
                        },
                        {
                            field: 'segmentRecommendStatus',
                            label: null,
                            use: [AdgroupSwitch, {
                                field: 'segmentRecommendStatus',
                                style: {
                                    marginLeft: 12
                                }
                            }],
                            visible: function (formData) {
                                return !formData.isMultiAdgroup;
                            }
                        },
                        {
                            field: 'videoAutoOptimizationStatus',
                            label: null,
                            use: [AdgroupSwitch, {
                                field: 'videoAutoOptimizationStatus',
                                style: {
                                    marginLeft: 12
                                }
                            }],
                            visible: function (formData) {
                                return !formData.isMultiAdgroup && isAutoCreativeUser() && isInQinggeIframe;
                            }
                        }
                    ],
                    use: [CreativeHorizontalGroup, {
                        className: 'ad-main-inline-group'
                    }],
                    // (在轻舸的GUI创编创意 & 单独新建场景) || 笔记营销目标下  不展示这个
                    visible: formData => !(
                        (isInQinggeIframe && !formData.isInAdgroupProcess)
                        || marketingTargetId === NATIVE
                    )
                }
            ],
            use: [CreativeHorizontalGroup, {
                className: 'ad-main-space-between-group'
            }],
            visible: formData => formData.isShowCreativeForm
        }
    ]
});

const hideStyle = {display: 'none'};
const MAX_CUSTOMIZE_CREATIVE_COUNT = 20;
export const CreativesWithAichat = memo(forwardRef(CreativesWithAichat_));
function CreativesWithAichat_({
    initialCreativeData,
    onCreativeDataChange = noop,
    programmaticCreativeInstance, mountProgrammaticCreativeInstance,
    customizeCreativeInstance, mountCustomizeCreativeInstance,
    slotProps = {},
    alertKey = '',
    isShowPreviewOnRightSection,
    isShowPreviewOnMainSection,
    displayUrl,
    portraitPicUrl
}) {
    const {campaignInfo, $trigger, logOnAreaClick = noop} = useCreativeSharedInfo();
    const {
        marketingTargetId
    } = campaignInfo;
    const {visible: botVisible} = useBotContextInfo();
    const config = useMemo(() => getCreativeConfig({marketingTargetId}), [marketingTargetId]);
    const {Form: CreativeFormContainer, formData} = useFormDefinition(config, initialCreativeData);
    const [creativeType] = useFieldValue(formData, 'creativeType');
    const [isShowCreativeForm] = useFieldValue(formData, 'isShowCreativeForm');
    const [segmentRecommendStatus] = useFieldValue(formData, 'segmentRecommendStatus');

    const onAcceptProgrammaticCreatives = useMemo(
        () => getProgrammaticCreativeAcceptFn(programmaticCreativeInstance, {maxCount: MAX_PROGRAM_COUNT}),
        [programmaticCreativeInstance]
    );
    const onAcceptCustomizeCreatives = useMemo(
        () => getCustomizeCreativeAcceptFn(
            {$trigger, customizeCreativeInstance},
            {maxCount: MAX_CUSTOMIZE_CREATIVE_COUNT}
        ),
        [customizeCreativeInstance]
    );
    const Slot = useSlot(slotProps);

    const adminProgramCreativeTip = classNames({
        'ad-main-creative-bottom-tip': true,
        'ad-main-creative-bottom-tip-hide': !isShowCreativeForm
    });

    useEffect(
        () => {
            onCreativeDataChange({creativeType, isShowCreativeForm});
        },
        [creativeType, isShowCreativeForm]
    );

    const [livePreviewFormData, setLivePreviewFormData] = useState({
        customize: {},
        programmatic: {}
    });
    const onUpdate = useCallback((ideaType, newData) => {
        setLivePreviewFormData(pre => {
            if (isEqual(pre[ideaType], newData)) {
                return pre;
            }
            return {...pre, [ideaType]: cloneDeep(newData)};
        });
    }, [setLivePreviewFormData]);

    return (
        <div className="ad-main-creatives-form-wrapper">
            <div style={(isShowCreativeForm && !isInQinggeIframe) ? {} : hideStyle}>
                <LiveRichDegreeAlert
                    formData={creativeType ? livePreviewFormData.programmatic : livePreviewFormData.customize}
                    creativeType={creativeType}
                    recommendCreativeStatus={segmentRecommendStatus}
                    {...pick(slotProps, ['imageTypes'])}
                />
            </div>
            <Slot
                name="titleAlert"
                alertKey={alertKey}
                style={slotProps.style ?? {margin: '0 20px'}}
            />
            <div
                className={
                    `ad-main-creatives-form-container ${isShowPreviewOnMainSection ? 'creative-preview-block' : ''}`
                }
            >
                <CreativeFormContainer
                    className="use-rf-preset-form-ui ad-main-creative-form"
                    style={{flex: 1, marginRight: 24, width: 0}}
                    onClick={logOnAreaClick}
                >
                    {
                        marketingTargetId === NATIVE && (
                            <div className="ad-main-creative-form-tip">
                                系统根据对用户搜索个性化需求的分析和理解，基于您的百家号内容进行动态优化。
                            </div>
                        )
                    }
                    <div style={creativeType === creativeTypeEnum.programmatic && isShowCreativeForm ? {} : hideStyle}>
                        <ProgrammaticCreative
                            mountInstance={mountProgrammaticCreativeInstance}
                            creativeCommonFormData={formData}
                            watchedFields={['programTitles', 'programDescs', 'segmentBinds']}
                            onWatchedFieldsChange={partial(onUpdate, 'programmatic')}
                        />
                    </div>
                    {
                        marketingTargetId !== NATIVE && (
                            <div
                                style={
                                    creativeType === creativeTypeEnum.customize && isShowCreativeForm ? {} : hideStyle
                                }
                            >
                                <CustomizeCreative
                                    mountInstance={mountCustomizeCreativeInstance}
                                    creativeCommonFormData={formData}
                                    watchedFields={['creativeTexts', 'tabIndex', 'segmentBinds']}
                                    onWatchedFieldsChange={partial(onUpdate, 'customize')}
                                    displayUrl={displayUrl}
                                    portraitPicUrl={portraitPicUrl}
                                />
                            </div>
                        )
                    }
                    <div className={adminProgramCreativeTip}>
                        请复核您添加和使用的内容，确保符合《中华人民共和国广告法》等法律法规， 不侵犯他人合法权益。若您的内容出现违法违规情形， 百度发现后将进行断链、下线等处理，并报相关监管部门。
                    </div>
                </CreativeFormContainer>
                {
                    isShowCreativeForm ? (
                        <div>
                            {botVisible ? null : (
                                <Slot
                                    name="recommend"
                                    onAccept={
                                        creativeType === creativeTypeEnum.programmatic
                                            ? onAcceptProgrammaticCreatives
                                            : onAcceptCustomizeCreatives
                                    }
                                />
                            )}
                            {
                                (isShowPreviewOnRightSection || isShowPreviewOnMainSection) ? (
                                    <div className="ad-main-creative-side-container">
                                        <LiveCreativePreview
                                            formData={
                                                creativeType
                                                    ? livePreviewFormData.programmatic
                                                    : livePreviewFormData.customize
                                            }
                                            marketingTargetId={marketingTargetId}
                                            creativeType={creativeType}
                                            {...pick(
                                                slotProps, ['imageTypes', 'adgroupIds', 'displayUrl', 'portraitPicUrl']
                                            )}
                                        />
                                    </div>
                                ) : null
                            }
                        </div>
                    ) : null
                }
            </div>
        </div>
    );
}


