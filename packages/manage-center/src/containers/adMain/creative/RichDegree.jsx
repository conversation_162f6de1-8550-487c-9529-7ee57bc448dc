import {useMemo} from 'react';
import {cloneDeep} from 'lodash-es';
import {getRichDegreeValue} from 'app/containers/fcNew/creative/utils';
import {BaseRichDegree, RichProgress} from 'app/containers/fcNew/creative/richDegree';
import {liquidfill} from 'app/containers/fcNew/creative/richDegree/config';
import {creativeTypeEnum} from 'commonLibs/config/creativeText';

export function LiveRichDegree({formData: copywritingInfo = {}, creativeType, imageTypes, recommendCreativeStatus}) {
    const {segments = [], videos = []} = copywritingInfo.segmentBinds || {};
    const bindInfo = useMemo(() => ({segments, videos}), [segments, videos]);
    const option = useMemo(
        () => {
            const clone = cloneDeep(liquidfill);
            const value = getRichDegreeValue({
                isNewProcess: true,
                data: {
                    recommendCreativeStatus,
                    bindInfo
                },
                imageTypes,
                copywritingInfo,
                creativeType
            });
            clone.series[0].data = [value, value];
            return clone;
        },
        [copywritingInfo, creativeType, imageTypes, recommendCreativeStatus]
    );
    return <BaseRichDegree option={option} />;
}

export function LiveRichDegreeAlert({formData: copywritingInfo = {}, creativeType,
    imageTypes, recommendCreativeStatus}) {
    const {segments = [], videos = []} = copywritingInfo.segmentBinds || {};
    const bindInfo = useMemo(() => ({segments, videos}), [segments, videos]);

    const richProgressPros = {
        creativeType,
        isNewProcess: creativeType === creativeTypeEnum.programmatic,
        data: {
            creatives: copywritingInfo.creativeTexts || [],
            bindInfo,
            recommendCreativeStatus
        },
        imageTypes,
        form: {},
        activeCreativeId: null
    };

    return <RichProgress {...richProgressPros} />;
}