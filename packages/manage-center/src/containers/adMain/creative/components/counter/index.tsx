/*
 * @file: 计数
 * <AUTHOR>
 * @date Mon Jul 03 2023
 */

import classNames from 'classnames';
import {isVideoRestructure} from 'commonLibs/utils/getFlag';
import './style.less';

interface CounterProps {
    count: number;
    max: number;
    className?: string;
    style?: React.CSSProperties;
}
export function Counter(props: CounterProps) {
    const {count, max, className, style} = props;
    const cls = classNames('ad-main-counter', className);
    return (
        <span className={cls} style={style}>
            <span className="ad-main-counter-count">{count}</span>
            <span className="ad-main-counter-divider">/</span>
            <span className="ad-main-counter-max">{max}</span>
        </span>
    );
}

interface CounterForSegmentsProps {
    videoCount: number;
    picCount: number;
    className?: string;
    style?: React.CSSProperties;
    isShowVideo?: boolean;
}
export function CounterForSegments(props: CounterForSegmentsProps) {
    const {videoCount, picCount, className, style, isShowVideo = true} = props;
    const cls = classNames('ad-main-counter', className);

    return (
        <span className={cls} style={style}>
            <span className="ad-main-counter-title">图片</span>
            <span className="ad-main-counter-count">{picCount}</span>
            {
                isVideoRestructure() && isShowVideo && (
                    <>
                        <span className="ad-main-counter-divider">/</span>
                        <span className="ad-main-counter-title">视频</span>
                        <span className="ad-main-counter-count">{videoCount}</span>
                    </>
                )
            }
        </span>
    );
}
