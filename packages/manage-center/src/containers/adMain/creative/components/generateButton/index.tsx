/*
 * @file: 生成更多文案、图片、视频按钮
 * <AUTHOR>
 * @date 2024-4-9
 */
import {Button} from '@baidu/one-ui';
import {IconAI} from '@baidu/light-ai-react';
import {isAigcImageUser, isAigcVideoUser} from 'commonLibs/utils/getFlag';
import {onSuggestClick} from 'app/containers/fcNew/creative/separateNewWithAiChat/utils/aichat';


export function TextGenerateButton(props) {
    const buttonText = props?.buttonText || '生成更多文案';
    return (
        <Button
            type="text-strong"
            onClick={() => onSuggestClick({content: buttonText})}
            icon={<IconAI />}
        >
            {buttonText}
        </Button>
    );
}

export function ImageGenerateButton() {
    return isAigcImageUser() ? (
        <Button
            type="text-strong"
            onClick={() => onSuggestClick({content: '生成更多图片'})}
            icon={<IconAI />}
        >
            生成更多图片
        </Button>
    ) : null;
}

export function VideoGenerateButton(props) {
    return isAigcVideoUser() ? (
        <Button
            type="text-strong"
            onClick={props?.onClick}
            icon={<IconAI />}
        >
            生成更多视频
        </Button>
    ) : null;
}

