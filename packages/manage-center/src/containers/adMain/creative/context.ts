import {createContext, useContext} from 'react';

const FormInstanceContext = createContext({});
export const FormInstanceProvider = FormInstanceContext.Provider;
export const useFormInstance = () => useContext(FormInstanceContext);

const CreativeSharedInfo = createContext({});
export const CreativeSharedInfoProvider = CreativeSharedInfo.Provider;
export const useCreativeSharedInfo = () => useContext(CreativeSharedInfo);
