import {creativeParamsPipeline, creativeTypeParams} from '../../utils/generateParams';

export const creativeValueToAdd = {
    title: '',
    description1: '',
    description2: '',
    pcFinalUrl: '',
    pcTrackParam: '',
    pcTrackTemplate: '',
    mobileFinalUrl: '',
    mobileTrackParam: '',
    mobileTrackTemplate: ''
};

export const initialData = {
    creativeTexts: [creativeValueToAdd],
    tabIndex: 0, // 用于判断当前是哪个创意文案tab
    segmentBinds: {
        segments: [],
        videos: []
    }
};

export const fields = {};

export const generateCreativeParams = creativeParamsPipeline([creativeTypeParams], {creativeType: {}});

