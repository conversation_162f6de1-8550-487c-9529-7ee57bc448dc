import {useState, memo, useCallback, useMemo} from 'react';
import {IconExclamationCircle} from 'dls-icons-react';
import {useFieldValue, useFormContext, useFieldWatch, UIInput} from '@baidu/react-formulator';
import {getCreativeFields} from 'app/containers/adMain/config/formFields';
import MARKET_TARGET, {CPQL, isLiveSubMarket, STORE} from 'commonLibs/config/marketTarget';
import {
    DESTINATION_URL_MAX_LENGTH_IN_BYTES,
    TEMPLATE_REPLACE_URL_REG,
    TEMPLATE_REPLACE_ALL_URL_REG,
    AGENT_URL_REG,
    TEMPLATE_URL_MAP
} from 'app/components/tableEditor/url/config';
import {FLOW_SCOPE} from 'commonLibs/config/ocpc';
import CreativeImport from 'app/containers/fcNew/creative/creativeImport';
import {INPUT_MAX as DESC_MAX_LEN, INPUT_MIN as DESC_MIN_LEN} from '../../../../fcNew/creative/desc/config';
import {INPUT_MAX as TITLE_MAX_LENGTH, INPUT_MIN as TITLE_MIN_LENGTH} from '../../../../fcNew/creative/title/config';
import {convertFieldToConfig} from '../../../utils';
import {FinalUrl} from '../fields/finalUrl';
import {MiniProgramUrl} from '../fields/miniProgramUrl';
import {FieldArrayTabInput} from 'commonLibs/components/formulatorRender/FieldArrayTabInput';
import {useInputMaxWidth} from '../../hooks/useInputMaxWidth';
import {CreativeSegments} from '../../fields/segments';
import {validateSegments} from '../../fields/segments/validator';
import UnionUrlPreview from 'app/components/tableEditor/url/preview';
import {MaterialConfig} from 'app/containers/fcNew/creative/creativeImage/config';
import {Counter, CounterForSegments} from '../../components/counter';
import {useCreativeSharedInfo, useFormInstance} from '../../context';
import {CreativeTextInput} from '../../fields/creativeInput';
import {isEmpty, partial} from 'lodash-es';
import {creativeInputValidator, onPreCheckValidator} from '../../fields/creativeInput/validator';
import {validateMiniProgramUrl} from 'app/components/miniProgramUrl/validator';
import {
    getIsPcFinalUrlRequired,
    getIsMobileFinalUrlRequired,
    getAdgroupFinalUrlIsExist
} from 'app/containers/fcNew/creative/url/validator';
import {TextGenerateButton} from 'app/containers/adMain/creative/components/generateButton';
import {isInQinggeIframe} from 'commonLibs/utils/isInIframe';
import {isIdeaUrlCleanWhite} from 'commonLibs/utils/getFlag';


const MAX_CREATIVE_TEXT_COUNT = 20;

const isShowFinalUrl = ({isSeparate, adgroupList, campaignInfo, isPC}) => {
    const {marketingTargetId, equipmentType, subMarketingTargetId} = campaignInfo;
    const getFinalUrlFunc = isPC ? getIsPcFinalUrlRequired : getIsMobileFinalUrlRequired;
    const isShowFinalUrlInSeparate = adgroupList.some(adgroup => {
        return getFinalUrlFunc({
            ...adgroup
        });
    });

    const isShowFinalUrl = isSeparate
        ? isShowFinalUrlInSeparate
        : getFinalUrlFunc({marketingTargetId, equipmentType, subMarketingTargetId});

    return isShowFinalUrl;
};

const title = {
    field: 'title',
    label: '创意标题',
    showRequiredMark: true,
    validators: [
        {
            validator: partial(creativeInputValidator, partial.placeholder, TITLE_MAX_LENGTH, true)
        },
        {
            validator: partial(onPreCheckValidator, 'title', partial.placeholder, TITLE_MAX_LENGTH, true),
            triggers: ['onBlur', '@final']
        }
    ],
    use: [CreativeTextInput, {
        maxLen: TITLE_MAX_LENGTH,
        minLen: TITLE_MIN_LENGTH
    }]
};

const description1 = props => ({
    field: 'description1',
    label: '创意描述第一行',
    showRequiredMark: true,
    validators: [
        {
            validator: partial(creativeInputValidator, partial.placeholder, DESC_MAX_LEN, true)
        },
        {
            validator: partial(onPreCheckValidator, 'description1', partial.placeholder, DESC_MAX_LEN, true),
            triggers: ['onBlur', '@final']
        }
    ],
    use: [CreativeTextInput, {
        maxLen: DESC_MAX_LEN,
        minLen: DESC_MIN_LEN,
        hideRegionWildCard: ![MARKET_TARGET.CPQL, MARKET_TARGET.STORE].includes(props.campaignInfo.marketingTargetId)
    }]
});

const description2 = props => ({
    field: 'description2',
    label: '创意描述第二行',
    validators: [
        {
            validator: partial(creativeInputValidator, partial.placeholder, DESC_MAX_LEN, false)
        },
        {
            validator: partial(onPreCheckValidator, 'description2', partial.placeholder, DESC_MAX_LEN, false),
            triggers: ['onBlur', '@final']
        }
    ],
    use: [CreativeTextInput, {
        maxLen: DESC_MAX_LEN,
        minLen: DESC_MIN_LEN,
        hideRegionWildCard: ![MARKET_TARGET.CPQL, MARKET_TARGET.STORE].includes(props.campaignInfo.marketingTargetId)
    }]
});

const pcUnitUrlPreview = ({
    isSeparate,
    adgroupList = [],
    campaignInfo
}) => ({
    field: 'pcUnitUrlPreview',
    label: '计算机网址预览',
    use: [
        UnionUrlPreview,
        {
            urlField: 'pcFinalUrl',
            trackParamsField: 'pcTrackParam',
            trackTemplateField: 'pcTrackTemplate'
        }
    ],
    componentProps: formData => ({
        materiaData: {
            pcFinalUrl: formData?.$arrayItemData?.pcFinalUrl,
            pcTrackParam: formData?.$arrayItemData?.pcTrackParam,
            pcTrackTemplate: formData?.$arrayItemData?.pcTrackTemplate
        }
    }),
    visible: () => isIdeaUrlCleanWhite() && isShowFinalUrl({isSeparate, adgroupList, campaignInfo, isPC: true})
});

const pcFinalUrl = ({
    isCheckAll,
    isDisabledAgentReg,
    isSeparate,
    adgroupList = [],
    campaignInfo
}) => {
    const [isPcExist, isMobileExist] = getAdgroupFinalUrlIsExist(adgroupList);
    const isPcFinalUrlRequiredInSeparate = isPcExist ? false
        : (isCheckAll || adgroupList.every(adgroup => {
            return getIsPcFinalUrlRequired({
                adgroupPcFinalUrl: isPcExist,
                ...adgroup
            });
        }));
    const isPcFinalUrlRequired = isSeparate ? isPcFinalUrlRequiredInSeparate : false;

    return {
        field: 'pcFinalUrl',
        label: '计算机最终访问网址',
        tip: 'finalUrl',
        rules: [
            ...(isPcFinalUrlRequired ? [['required']] : []),
            ...(isDisabledAgentReg ? [
                ['not:match', AGENT_URL_REG, '落地页链接不支持填写商家智能体，若想进行智能体投放，可通过将当前创意所在单元的「广告链接类型」修改为商家智能体']
            ] : []),
            ['legacyIsURL'],
            ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]]
        ],
        use: [FinalUrl, {fieldName: 'pcFinalUrl', arrayField: 'creativeTexts', isHideAgentUrl: isDisabledAgentReg}],
        visible: () => isIdeaUrlCleanWhite() && isShowFinalUrl({isSeparate, adgroupList, campaignInfo, isPC: true})
    };
};

const pcTrackParam = {
    field: 'pcTrackParam',
    label: '监控后缀',
    tip: 'trackParam',
    rules: [
        ['optional'],
        ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]]
    ],
    use: [
        InputWithMaxWidth,
        {
            field: 'pcTrackParam',
            placeholder: '示例：?keyword={keywordid}',
            maxLen: DESTINATION_URL_MAX_LENGTH_IN_BYTES
        }
    ],
    visible: () => isIdeaUrlCleanWhite()
};

const pcTrackTemplate = {
    field: 'pcTrackTemplate',
    label: '追踪模板',
    rules: [
        ['optional'],
        ['legacyIsURL'],
        ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]],
        ['match', TEMPLATE_REPLACE_URL_REG, `追踪模板中必须有且仅有一个${TEMPLATE_URL_MAP.encode}通配符`],
        ['not:match', TEMPLATE_REPLACE_ALL_URL_REG, `追踪模板中必须有且仅有一个${TEMPLATE_URL_MAP.encode}通配符`]
    ],
    use: [
        InputWithMaxWidth,
        {
            field: 'pcTrackTemplate',
            placeholder: '示例：https://www.trackingtemplate.com?url={lpurl_encode}',
            maxLen: DESTINATION_URL_MAX_LENGTH_IN_BYTES
        }
    ],
    visible: () => isIdeaUrlCleanWhite()
};
const mobileUnitUrlPreview = ({
    isSeparate,
    adgroupList = [],
    campaignInfo
}) => ({
    field: 'mobileUnitUrlPreview',
    label: '移动网址预览',
    use: [
        UnionUrlPreview,
        {
            urlField: 'mobileFinalUrl',
            trackParamsField: 'mobileTrackParam',
            trackTemplateField: 'mobileTrackTemplate'
        }
    ],
    componentProps: formData => ({
        materiaData: {
            mobileFinalUrl: formData?.$arrayItemData?.mobileFinalUrl,
            mobileTrackParam: formData?.$arrayItemData?.mobileTrackParam,
            mobileTrackTemplate: formData?.$arrayItemData?.mobileTrackTemplate
        }
    }),
    visible: () => isIdeaUrlCleanWhite() && isShowFinalUrl({isSeparate, adgroupList, campaignInfo, isPC: false})
});
const mobileFinalUrl = ({
    campaignInfo,
    isDisabledAgentReg,
    isCheckAll,
    isSeparate,
    adgroupList = []
}) => {
    const [isPcExist, isMobileExist] = getAdgroupFinalUrlIsExist(adgroupList);

    const isMobileFinalUrlRequiredInSeparate = isMobileExist ? false
        : (isCheckAll || adgroupList.every(adgroup => {
            return getIsMobileFinalUrlRequired({
                adgroupMobileFinalUrl: isMobileExist,
                ...adgroup
            });
        }));

    const isMobileFinalUrlRequired = isSeparate ? isMobileFinalUrlRequiredInSeparate : false;

    return {
        field: 'mobileFinalUrl',
        label: '移动最终访问网址',
        tip: 'finalUrl',
        rules: [
            ...(isMobileFinalUrlRequired ? [['required']] : []),
            ...(isDisabledAgentReg ? [
                ['not:match', AGENT_URL_REG, '落地页链接不支持填写商家智能体，若想进行智能体投放，可通过将当前创意所在单元的「广告链接类型」修改为商家智能体']
            ] : []),
            ['legacyIsURL'],
            ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]]
        ],
        use: [FinalUrl, {
            fieldName: 'mobileFinalUrl', arrayField: 'creativeTexts', isHideAgentUrl: isDisabledAgentReg,
            splitUrlStyle: {
                position: 'absolute',
                top: -2,
                left: 134
            }
        }],
        visible: () => isIdeaUrlCleanWhite() && isShowFinalUrl({isSeparate, adgroupList, campaignInfo, isPC: false})
    };
};

const mobileTrackParam = {
    field: 'mobileTrackParam',
    label: '监控后缀',
    tip: 'trackParam',
    rules: [
        ['optional'],
        ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]]
    ],
    use: [
        InputWithMaxWidth,
        {
            field: 'mobileTrackParam',
            placeholder: '示例：?keyword={keywordid}',
            maxLen: DESTINATION_URL_MAX_LENGTH_IN_BYTES
        }
    ],
    visible: () => isIdeaUrlCleanWhite()
};

const mobileTrackTemplate = {
    field: 'mobileTrackTemplate',
    label: '追踪模板',
    rules: [
        ['optional'],
        ['legacyIsURL'],
        ['textRange', [0, DESTINATION_URL_MAX_LENGTH_IN_BYTES]],
        ['match', TEMPLATE_REPLACE_URL_REG, `追踪模板中必须有且仅有一个${TEMPLATE_URL_MAP.encode}通配符`],
        ['not:match', TEMPLATE_REPLACE_ALL_URL_REG, `追踪模板中必须有且仅有一个${TEMPLATE_URL_MAP.encode}通配符`]
    ],
    use: [
        InputWithMaxWidth,
        {
            field: 'mobileTrackTemplate',
            placeholder: '示例：https://www.trackingtemplate.com?url={lpurl_encode}',
            maxLen: DESTINATION_URL_MAX_LENGTH_IN_BYTES
        }
    ],
    visible: () => isIdeaUrlCleanWhite()
};


const miniProgramUrl = ({campaignInfo}) => {
    return {
        field: 'miniProgramUrl',
        validators: partial(validateMiniProgramUrl, partial.placeholder),
        use: [MiniProgramUrl],
        visible: () => isIdeaUrlCleanWhite() && campaignInfo.equipmentType !== FLOW_SCOPE.PC
    };
};

const creativeTexts = props => ({
    field: 'creativeTexts',
    label: null,
    name: '创意文案',
    use: [CreativeTabInput, {
        marketingTargetId: props.campaignInfo.marketingTargetId,
        displayUrl: props.displayUrl,
        portraitPicUrl: props.portraitPicUrl,
        valueToAdd: {
            title: '',
            miniProgramUrl: ''
        }, // todo 可能跟营销目标有关
        min: 1,
        max: MAX_CREATIVE_TEXT_COUNT,
        hideGenerateButton: props.isEdit || isInQinggeIframe
    }]
});

const customSegments = ({
    adgroupInfo,
    campaignInfo,
    creativeCommonFormData,
    isEdit
}) => {
    const {
        adgroups,
        accountVideoSegmentCount,
        adgroupsBindVideoSegmentCount
    } = adgroupInfo;
    return {
        field: 'segmentBinds',
        label: null,
        name: '创意素材',
        use: [CreativeSegmentsWithBorder, {
            adgroups,
            accountVideoSegmentCount,
            adgroupsBindVideoSegmentCount,
            campaignInfo,
            segmentMaterials: [MaterialConfig.image, MaterialConfig.video],
            hideGenerateButton: isEdit
        }],
        componentProps: formData => {
            return {
                recommend: {
                    title: formData.creativeTexts[formData.tabIndex]?.title || ''
                }
            };
        },
        validators: value => {
            const segmentRecommendStatus = creativeCommonFormData.segmentRecommendStatus;
            return validateSegments(value, segmentRecommendStatus);
        }
    };
};

const baseFields = {
    title,
    description1,
    description2,
    pcFinalUrl,
    pcTrackParam,
    pcTrackTemplate,
    mobileFinalUrl,
    mobileTrackParam,
    mobileTrackTemplate,
    mobileUnitUrlPreview,
    pcUnitUrlPreview,
    miniProgramUrl,
    creativeTexts,
    segmentBinds: customSegments
};

const urlGroup = ({campaignInfo}) => ({
    group: 'urlGroup',
    use: ['ExpandableGroup', {title: ['展开落地页配置', '收起落地页配置']}],
    componentProps: formData => {
        return {
            // 这里先加 ?. 把， 在删除时会是undefined，不影响功能但会throw error 待套件修复
            defaultExpand: !!formData.$arrayItemData?.pcFinalUrl
                || !!formData.$arrayItemData?.mobileFinalUrl
                || !!formData.$arrayItemData?.miniProgramUrl
        };
    },
    visible: () => isIdeaUrlCleanWhite() && !isLiveSubMarket(campaignInfo.subMarketingTargetId)
});
const expandTitle = [
    <span key='title'><span style={{color: '#848B99', marginRight: 4}}>监控后缀</span>展开</span>,
    '收起'
];

const pcUrlGroup = ({isSeparate, adgroupList, campaignInfo}) => {
    return {
        group: 'pcUrlGroup',
        use: ['ExpandableGroup', {title: expandTitle}],
        componentProps: formData => {
            return {
                defaultExpand: !!formData.$arrayItemData?.pcTrackParam
                    || !!formData.$arrayItemData?.pcTrackTemplate
            };
        },
        visible: () => isIdeaUrlCleanWhite() && isShowFinalUrl({isSeparate, adgroupList, campaignInfo, isPC: true})
    };
};

const mobileUrlGroup = ({isSeparate, adgroupList, campaignInfo}) => {
    return {
        group: 'mobileUrlGroup',
        use: ['ExpandableGroup', {title: expandTitle}],
        componentProps: formData => {
            return {
                defaultExpand: !!formData.$arrayItemData?.mobileTrackParam
                    || !!formData.$arrayItemData?.mobileTrackTemplate
            };
        },
        visible: () => isIdeaUrlCleanWhite() && isShowFinalUrl({isSeparate, adgroupList, campaignInfo, isPC: false})
    };
};

const baseGroups = {
    urlGroup,
    pcUrlGroup,
    mobileUrlGroup
};

export const remoteConfigLoaders = {
    [MARKET_TARGET.CPQL]: () => import('./CPQL'),
    [MARKET_TARGET.STORE]: () => import('./STORE'),
    [MARKET_TARGET.WEB]: () => import('./WEB'),
    [MARKET_TARGET.APP]: () => import('./APP'),
    [MARKET_TARGET.NATIVE]: () => import('./NATIVE')
};

export async function initialCustomizeCreativeForm({
    initialData,
    isEdit,
    campaignInfo,
    adgroupInfo,
    creativeCommonFormData,
    isDisabledAgentReg,
    portraitPicUrl, displayUrl
}) {
    const {
        adType,
        marketingTargetId
    } = campaignInfo;
    const customizeCreativeFields = getCreativeFields({marketingTargetId, adType});
    const fieldsConfig = customizeCreativeFields.config;
    let remoteConfig = {};
    try {
        remoteConfig = await remoteConfigLoaders[marketingTargetId]();
    }
    catch (e) {
        console.error(`(marketingTargetId: ${marketingTargetId})是否存在配置文件`);
    }
    const fields = fieldsConfig.map(
        field => convertFieldToConfig({field, baseFields, remoteConfig, baseGroups, props: {
            isEdit,
            campaignInfo,
            isDisabledAgentReg,
            adgroupInfo,
            creativeCommonFormData,
            isCheckAll: adgroupInfo.isCheckAll,
            isSeparate: !!adgroupInfo.adgroups.length,
            adgroupList: adgroupInfo.adgroups,
            portraitPicUrl, displayUrl
        }})
    );
    return {
        config: {
            fields
        },
        initialData: initialData ?? remoteConfig.initialData,
        creativeValueToAdd: remoteConfig.creativeValueToAdd
    };
}

function CreativeTabInputTitle({hideGenerateButton} = {hideGenerateButton: false}) {
    const {formData} = useFormContext();
    const [creativeTexts] = useFieldValue(formData, 'creativeTexts');
    return (
        <div className="ad-main-form-creative-box-title ad-main-form-creative-box-title-for-creatives">
            <div className="ad-main-form-creative-box-title-left">
                <span className="ad-main-form-creative-box-title-text">创意文案</span>
                <Counter count={creativeTexts.length} max={MAX_CREATIVE_TEXT_COUNT} />
            </div>
            {hideGenerateButton ? null : (
                <div className="ad-main-form-creative-box-title-right">
                    <TextGenerateButton />
                </div>
            )}
        </div>
    );
}
function CreativeTabTitle({idx, arrayField}) {
    const {verificationResults} = useFormContext();
    const [hasErrors, setHasErrors] = useState(false);
    useFieldWatch(verificationResults, `^${arrayField}.`, () => {
        const errorsMap = verificationResults.$toRaw();
        const hasErrors = Object.entries(errorsMap).some(([key, value]) => {
            const hasError = value && Object.values(value).some(i => i && i.length);
            return key.startsWith(`${arrayField}.${idx}.`) && hasError;
        });
        setHasErrors(hasErrors);
    }, {
        duplicate: true
    });

    return (
        <div className="tab-title">
            <span>创意文案{idx + 1}</span>
            {hasErrors && <IconExclamationCircle className="error-tip" />}
        </div>
    );
}

function CreativeImportAnchor(props) {
    const {idx, componentProps: {onUpdateCreative, marketingTargetId} = {}} = props;
    const importFormData = useCallback(
        formValues => {
            if (!isEmpty(formValues)) {
                onUpdateCreative(idx, formValues);
            }
        },
        [idx, onUpdateCreative]
    );
    const importProps = {
        ...(props.componentProps || {}), importFormData
    };

    return [CPQL, STORE].includes(marketingTargetId) ? null : <CreativeImport {...importProps} />;
}

const creativeTabSlotsProps = {
    slots: {
        tabTitle: memo(CreativeTabTitle),
        tabContentItemAnchor: memo(CreativeImportAnchor)
    }
};
function CreativeTabInput(props) {
    const {arrayFieldMethods: {update}, marketingTargetId, displayUrl, portraitPicUrl} = props;
    const width = useInputMaxWidth();
    const {formData} = useFormContext();
    const {$on} = useCreativeSharedInfo();
    const {creativeValueToAdd} = useFormInstance();
    const [tabIndex, setTabIndex] = useFieldValue(formData, 'tabIndex');
    const p = {
        ...props,
        style: {width: width + 32}, // 这里因为父元素没有padding，所以得加上padding
        tabIndex,
        setTabIndex,
        valueToAdd: creativeValueToAdd
    };
    const onAddCustomizeCreative = valueToAdd => {
        const {arrayFieldMethods, value} = props;
        arrayFieldMethods.push(...valueToAdd);
        setTabIndex(value.length);
    };
    if ($on) {
        $on('addCustomizeCreative', onAddCustomizeCreative);
        $on('changeCustomizeCreativeTabIndex', setTabIndex);
    }
    const slotProps = useMemo(
        () => ({
            ...creativeTabSlotsProps,
            componentProps: {
                marketingTargetId,
                displayUrl,
                portraitPicUrl,
                onUpdateCreative: update
            }
        }),
        [marketingTargetId, displayUrl, portraitPicUrl, update]
    );
    return (
        <div className="ad-main-form-creative-box ad-main-customize-creative-group">
            <CreativeTabInputTitle hideGenerateButton={props.hideGenerateButton} />
            <FieldArrayTabInput {...p} slotProps={slotProps} />
        </div>
    );
}

function CreativeSegmentsTitle() {
    const {formData} = useFormContext();
    const [segmentBinds] = useFieldValue(formData, 'segmentBinds');
    const videoCount = segmentBinds.videos.length;
    const picCount = segmentBinds.segments.length;
    return (
        <div className="ad-main-form-creative-box-title">
            <span className="ad-main-form-creative-box-title-text">创意素材</span>
            <CounterForSegments videoCount={videoCount} picCount={picCount} />
        </div>
    );
}
function CreativeSegmentsWithBorder(props) {
    return (
        <div className="ad-main-form-creative-custom-segments">
            <CreativeSegmentsTitle />
            <CreativeSegments {...props} />
        </div>
    );
}


function InputWithMaxWidth(...props) {
    const width = useInputMaxWidth();
    return <UIInput {...props} width={width} />;
}
