import {useCallback} from 'react';
import CreativeFinalUrl from 'app/containers/fcNew/creative/url/finalUrl';
import {useCreativeSharedInfo, useFormInstance} from '../../context';
import {reduce} from 'lodash-es';
import {useInputMaxWidth} from '../../hooks/useInputMaxWidth';

export function FinalUrl({
    isHideAgentUrl, value, onChange, fieldName, className,
    arrayItemIndex, arrayField, splitUrlStyle
}) {
    const {campaignInfo} = useCreativeSharedInfo();
    const {
        marketingTargetId,
        shopType
    } = campaignInfo;
    const {setFieldsValue, getFieldError, setFieldsError} = useFormInstance();
    const width = useInputMaxWidth();

    const setFields = useCallback(
        function (fieldsMap) {
            const {valueFieldsMap, errorFieldsMap} = reduce(
                fieldsMap,
                function (result, field, _fieldName) {
                    const fieldName = `${arrayField}.${arrayItemIndex}.${_fieldName}`;
                    const {value, errors} = field;
                    result.valueFieldsMap[fieldName] = value;
                    if (errors) {
                        result.errorFieldsMap[fieldName] = errors[0].message;
                    }
                    return result;
                },
                {valueFieldsMap: {}, errorFieldsMap: {}}
            );
            setFieldsValue(valueFieldsMap);
            setFieldsError(errorFieldsMap);
        },
        [setFieldsValue, setFieldsError, arrayItemIndex, arrayField]
    );
    const _getFieldError = useCallback(function (field) {
        const fieldName = `${arrayField}.${arrayItemIndex}.${field}`;
        return getFieldError(fieldName);
    }, [arrayItemIndex, arrayField, getFieldError]);

    return (
        <CreativeFinalUrl
            form={{setFieldsValue, getFieldError: _getFieldError, setFields}}
            value={value}
            onChange={onChange}
            field={fieldName}
            isHideAgentUrl={isHideAgentUrl}
            marketingTargetId={marketingTargetId}
            shopType={shopType}
            inputWidth={width - 118}
            classNameForInput={className}
            splitUrlStyle={splitUrlStyle}
        />
    );
}
