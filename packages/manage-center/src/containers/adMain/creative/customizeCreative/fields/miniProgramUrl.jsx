import {Checkbox} from '@baidu/one-ui';
import UrlEditor from 'app/components/miniProgramUrl';
import {useCreativeSharedInfo} from '../../context';
import {getIsJimuyuShop} from 'app/components/miniProgramUrl/util';
import {useState} from 'react';

export function MiniProgramUrl({value, onChange, className}) {
    const [checked, setChecked] = useState(!!value);
    const {campaignInfo} = useCreativeSharedInfo();
    const {
        marketingTargetId,
        shopType
    } = campaignInfo;
    const isJimuyuShop = getIsJimuyuShop({marketingTargetId, shopType});
    // 区别直接输入和选择已有两种情况
    const onUrlChange = e => onChange(typeof e === 'string' ? e : e.value);
    const onCheckBoxChange = () => {
        setChecked(!checked);
        if (checked) {
            onChange('');
        }
    };
    return (
        <>
            <Checkbox checked={checked} onChange={onCheckBoxChange} style={{marginBottom: 8}}>百度小程序</Checkbox>
            {
                checked && (
                    <UrlEditor
                        value={value}
                        onChange={onUrlChange}
                        isJimuyuShop={isJimuyuShop}
                        inputWidth={466}
                        className={className}
                    />
                )
            }
        </>
    );
}