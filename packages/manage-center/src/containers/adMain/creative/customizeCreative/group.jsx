import {forwardRef, useMemo, memo} from 'react';
import {nanoid} from 'nanoid';
import {Tabs} from '@baidu/one-ui';
import {useFormErrors} from '@baidu/react-formulator';
import {IconExclamationCircle} from 'dls-icons-react';
import {isEmpty, omitBy, isUndefined, partial} from 'lodash-es';
import {Boundary} from 'commonLibs/magicSuspenseBoundary';
import {creativeTypeEnum} from 'commonLibs/config/creativeText';
import {CustomizeCreative} from './index';

export const CustomizeCreativeGroup = memo(forwardRef(CustomizeCreativeGroup_));
function CustomizeCreativeGroup_({
    creatives,
    mountInstance,
    currentCreative,
    onAdd,
    onDelete,
    changeTabKey,
    maxCount
}) {
    return (
        <Tabs
            className="ad-main-new-adgroup-page-tabs"
            activeKey={currentCreative}
            onChange={changeTabKey}
            onBeforeDelete={onDelete}
            showAdd={creatives.length < maxCount}
            addButtonText="添加"
            onAdd={onAdd}
        >
            {
                creatives.map((cf, index) => {
                    const {key, initialData} = cf;
                    const {verificationResults} = creatives.find(i => i.key === key) || {};
                    const tabNode = verificationResults && (
                        <TabTitle index={index + 1} verificationResults={verificationResults} />
                    );
                    return (
                        <Tabs.TabPane key={key} closable={creatives.length > 1} tab={tabNode}>
                            <CustomizeCreativeOnKey
                                _key={key}
                                initialData={initialData}
                                verificationResults={verificationResults}
                                mountInstance={mountInstance}
                            />
                        </Tabs.TabPane>
                    );
                })
            }
        </Tabs>
    );
};

function TabTitle({verificationResults, index}) {
    const error = useFormErrors(verificationResults);
    return (
        <span>
            创意文案{index}
            {!isEmpty(omitBy(error, isUndefined)) && <IconExclamationCircle className="error-tip" />}
        </span>
    );
}

function CustomizeCreativeOnKey({
    _key,
    mountInstance,
    verificationResults,
    ...props
}) {
    const methods = useMemo(
        () => ({mountInstance: partial(mountInstance, _key)}),
        [mountInstance, _key]
    );
    return (
        <Boundary>
            <CustomizeCreative
                reactiveVerificationResults={verificationResults}
                {...methods}
                {...props}
            />
        </Boundary>
    );
}

export function createInitialCreative({initialData} = {}) {
    return {
        key: nanoid(),
        type: creativeTypeEnum.customize,
        initialData
    };
}
export function initialCreatives() {
    return [createInitialCreative()];
}
export function getKey(data) {
    return data.key;
}
