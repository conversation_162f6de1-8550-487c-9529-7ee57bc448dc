import {useCallback, forwardRef, useMemo, useEffect, memo} from 'react';
import {noop, reduce} from 'lodash-es';
import {useResource} from 'commonLibs/magicSuspenseBoundary';
import {Debug, useFieldWatch, useFormDefinition} from '@baidu/react-formulator';
import {initialCustomizeCreativeForm} from './config';
import {FormInstanceProvider, useCreativeSharedInfo} from '../context';

export const CustomizeCreative = memo(forwardRef(CustomizeCreative_));

function CustomizeCreative_({mountInstance, initialData: initialData_,
    creativeCommonFormData, onWatchedFieldsChange = noop, watchedFields = [],
    displayUrl, portraitPicUrl
}) {
    const {campaignInfo, adgroupInfo, isDisabledAgentReg} = useCreativeSharedInfo();
    const [{config, initialData, creativeValueToAdd}] = useResource(
        initialCustomizeCreativeForm,
        {
            campaignInfo, initialData: initialData_,
            adgroupInfo, creativeCommonFormData,
            isDisabledAgentReg,
            portraitPicUrl, displayUrl
        }
    );

    const [{Form, formData, FormInstance}] = useCreativeForm(config, initialData, {mountInstance, creativeValueToAdd});
    const getFieldsValue_ = () => {
        return formData.$toRaw();
    };
    // changedFields可能是['x.0.x']
    useFieldWatch(formData, '*', changedFields => {
        const hasChange = changedFields.some(field => {
            return watchedFields.find(watchedField => field.startsWith(watchedField));
        });
        if (hasChange) {
            onWatchedFieldsChange(getFieldsValue_());
        }
    });
    return (
        <FormInstance>
            <Form className="use-rf-preset-form-ui use-label-top">
                {/* <Debug /> */}
            </Form>
        </FormInstance>
    );
}

function useCreativeForm(config, initialData, {mountInstance, creativeValueToAdd}) {
    const {
        Form,
        formData,
        validateFields,
        setFieldsError,
        setFieldsValue,
        getFieldError,
        verification: {
            results: verificationResults
        },
        getConfigByField,
        scrollToField
    } = useFormDefinition(config, initialData);

    const __legacySetFields = useCallback(
        function (fieldsMap) {
            const {valueFieldsMap, errorFieldsMap} = reduce(
                fieldsMap,
                function (result, field, fieldName) {
                    const {value, errors} = field;
                    result.valueFieldsMap[fieldName] = value;
                    if (errors) {
                        result.errorFieldsMap[fieldName] = errors[0].message;
                    }
                    return result;
                },
                {valueFieldsMap: {}, errorFieldsMap: {}}
            );
            setFieldsValue(valueFieldsMap);
            setFieldsError(errorFieldsMap);
        },
        [setFieldsValue, setFieldsError]
    );

    const getFieldValue = useCallback(
        function (field) {
            return formData.$get(field);
        },
        [formData]
    );

    const methods = useMemo(
        () => ({
            setFieldsValue,
            getFieldError,
            __legacySetFields,
            setFieldsError,
            getFieldValue,
            validateFields,
            getConfigByField,
            scrollToField
        }),
        [
            setFieldsValue, getFieldError, __legacySetFields, setFieldsError, getFieldValue, validateFields,
            getConfigByField, scrollToField
        ]
    );
    const contextValue = useMemo(() => ({...methods, creativeValueToAdd}), [methods, creativeValueToAdd]);

    useEffect(
        () => {
            if (mountInstance) {
                mountInstance({formData, verificationResults, methods});
            }
        },
        [formData, verificationResults, methods]
    );

    const FormInstance_ = useCallback(
        function ({children}) {
            return (
                <FormInstanceProvider value={contextValue}>
                    {children}
                </FormInstanceProvider>
            );
        },
        [contextValue]
    );
    return [
        {Form, FormInstance: FormInstance_, formData, verificationResults},
        {validateFields, setFieldsError, setFieldsValue, getFieldError, __legacySetFields, getFieldValue}
    ];
}
