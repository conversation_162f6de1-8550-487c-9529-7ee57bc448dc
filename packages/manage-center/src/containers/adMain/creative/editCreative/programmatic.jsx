/*
 * @file: 编辑创意-程序化
 * <AUTHOR>
 * @date Tue Jul 11 2023
 */

import {useMemo, useState} from 'react';
import {pick, isEmpty} from 'lodash-es';
import {Layout, Dialog} from '@baidu/one-ui';
import {useParams, useHistory} from 'commonLibs/route';
import {ProcessSteps} from '@baidu/one-ui-pro';
import SuspenseBoundary, {useResource, CacheProvider} from 'commonLibs/suspenseBoundary';
import {useFormDefinition, useFieldValue} from '@baidu/react-formulator';
import {giveMeShortcuts} from 'commonLibs/utils/handleOptions';
import SaveFooter from 'commonLibs/SaveFooter';
import {createError} from 'commonLibs/utils/materialList/error';
import {useBotContextInfo} from 'commonLibs/context/botInfo';
import {getSingleErrorMessage} from 'commonLibs/utils/getErrorMessage';
import {MODE as RecommendCreativesSelectMode} from 'commonLibs/hooks/collection/array';
import {stepConfig} from 'app/containers/fcNew/creative/separateNew/config';
import {toPithySteps} from 'app/containers/fcNew/creative/utils';
import {creativeTypeEnum} from 'commonLibs/config/creativeText';
import {CreativeTextStatus} from 'commonLibs/config/status/creative';
import TopAlert from 'app/containers/fcNew/creative/topAlert';
import {getCPQLAlertKey} from 'app/containers/fcNew/creative/topAlert/config';
import {fetchEditCreativeInfo, modCreative} from 'app/containers/fcNew/apis/creative';
import {getImageTypes} from 'app/containers/fcNew/creative/utils/image';
import {usePinpaiInfo} from 'app/containers/fcNew/creative/hooks';
import {formatCreativeErrors, validateSegmentsError} from 'app/containers/fcNew/creative/utils/error';
import {getEditSegments, getVideoSegmentParams} from 'app/containers/fcNew/creative/editCreative/util';
import {RecommendCreatives, getProgrammaticCreativeAcceptFn} from '../recommend';
import {useCreativeSharedInfo, CreativeSharedInfoProvider} from '../context';
import {generateCreativeParams, MAX_PROGRAM_COUNT, mapProgramFieldToInnerField} from '../programmaticCreative/config';
import {ProgrammaticCreative} from '../programmaticCreative';
import {LiveCreativePreview} from '../CreativePreview';
import {LiveRichDegree} from '../RichDegree';
import {CreativeHorizontalGroup} from '../components/creativeGroup';
import {AdgroupSwitch} from '../fields/adgroupSwitch';
import './style.less';
import '../style.less';
import {useAdgroupBindAppInfo} from 'app/hooks/useAdgroupBindAppInfo';

const {Sidebar, Content, Header, Footer} = Layout;

const [creativeTypeOptions] = giveMeShortcuts([
    {value: creativeTypeEnum.programmatic, label: '自适应'},
    {value: creativeTypeEnum.customize, label: '自定义'}
]);

const config = {
    fields: [
        {
            group: 'creativeGroup',
            fields: [
                {
                    field: 'creativeType',
                    name: '生成方式',
                    label: null,
                    use: ['RadioGroup', {options: creativeTypeOptions, disabled: true}]
                },
                {
                    group: 'creativeGroup',
                    fields: [
                        {
                            field: 'creativeTextOptimizationStatus',
                            label: null,
                            use: [AdgroupSwitch, {
                                field: 'creativeTextOptimizationStatus'
                            }]
                        },
                        {
                            field: 'segmentRecommendStatus',
                            label: null,
                            use: [AdgroupSwitch, {
                                field: 'segmentRecommendStatus',
                                style: {
                                    marginLeft: 12
                                }
                            }]
                        }
                    ],
                    use: [CreativeHorizontalGroup, {
                        className: 'ad-main-inline-group'
                    }]
                }
            ],
            use: [CreativeHorizontalGroup, {
                className: 'ad-main-space-between-group'
            }]
        }
    ]
};

function Creatives({
    initialCreativeData,
    initialProgrammaticCreative,
    programmaticCreativeInstance,
    mountProgrammaticCreativeInstance,
    slotProps,
    isEdit
}) {
    const {marketingTargetId} = useCreativeSharedInfo();
    const {Form: CreativeFormContainer, formData} = useFormDefinition(config, initialCreativeData);
    const [creativeType] = useFieldValue(formData, 'creativeType');
    const [segmentRecommendStatus] = useFieldValue(formData, 'segmentRecommendStatus');

    const onAcceptProgrammaticCreatives = useMemo(
        () => getProgrammaticCreativeAcceptFn(programmaticCreativeInstance, {maxCount: MAX_PROGRAM_COUNT}),
        [programmaticCreativeInstance]
    );

    const [liveFormData, setLivePreviewFormData] = useState(initialProgrammaticCreative);

    return (
        <div className="ad-main-edit-creative-form-container">
            <CreativeFormContainer
                className="use-rf-preset-form-ui ad-main-edit-creative-form ad-main-creative-form"
            >
                <ProgrammaticCreative
                    mountInstance={mountProgrammaticCreativeInstance}
                    creativeCommonFormData={formData}
                    initialData={initialProgrammaticCreative}
                    watchedFields={['programTitles', 'programDescs', 'segmentBinds']}
                    onWatchedFieldsChange={setLivePreviewFormData}
                    isEdit={isEdit}
                />
            </CreativeFormContainer>
            <div>
                <RecommendCreatives
                    mode={RecommendCreativesSelectMode.SINGLE}
                    onAccept={onAcceptProgrammaticCreatives}
                    {...slotProps}
                />
                <div className="ad-main-creative-side-container">
                    <LiveRichDegree
                        formData={liveFormData}
                        creativeType={creativeType}
                        recommendCreativeStatus={segmentRecommendStatus}
                        {...pick(slotProps, ['imageTypes'])}
                    />
                    <LiveCreativePreview
                        formData={liveFormData}
                        marketingTargetId={marketingTargetId}
                        creativeType={creativeType}
                        {...pick(slotProps, ['imageTypes', 'adgroupIds', 'displayUrl', 'portraitPicUrl'])}
                    />
                </div>
            </div>
        </div>
    );
}

function EditCreative({adgroupId, campaignId, creativeId}) {
    const history = useHistory();
    const [data = []] = useResource(fetchEditCreativeInfo, {campaignId, adgroupId, creativeId});
    const [campaignInfo, adgroups, creativeInfo, accountVideoCount, adgroupVideoCount] = data;
    const {formValues, imageSegments, pinpaiSegments, videoSegments} = creativeInfo;
    const {visible: botVisible} = useBotContextInfo();
    const adgroupInfo = adgroups[0];
    const {marketingTargetId, campaignName} = campaignInfo;
    const {imageTypes, adgroupIds} = useMemo(
        () => ({imageTypes: getImageTypes(adgroups), adgroupIds: adgroups.map(adgroup => adgroup.adgroupId)}),
        [adgroups]
    );
    const {displayUrl, portraitPicUrl} = usePinpaiInfo({adgroupId, segments: pinpaiSegments, marketingTargetId});
    const slotProps = useMemo(
        () => ({imageTypes, adgroupIds, displayUrl, portraitPicUrl}),
        [imageTypes, adgroupIds, displayUrl, portraitPicUrl]
    );
    const [
        programmaticCreativeInstance,
        mountProgrammaticCreativeInstance
    ] = useState({type: creativeTypeEnum.programmatic});
    const {
        programTitleAuditMap,
        programDescAuditMap
    } = useMemo(
        () => formatTextAuditInfo(formValues), [formValues]
    );

    const initialProgrammaticCreative = {
        // eslint-disable-next-line max-len
        programTitles: ((formValues.shadowProgramTitles || formValues.programTitles) instanceof Array) ? (formValues.shadowProgramTitles || formValues.programTitles).map(title => ({title})) : [],
        // eslint-disable-next-line max-len
        programDescs: ((formValues.shadowProgramDescriptions || formValues.programDescriptions) instanceof Array) ? (formValues.shadowProgramDescriptions || formValues.programDescriptions).map(desc => ({desc})) : [],
        programTitleAuditMap,
        programDescAuditMap,
        segmentBinds: {
            segments: imageSegments,
            videos: videoSegments
        }
    };
    const initialCreativeData = {
        creativeType: creativeTypeEnum.programmatic,
        creativeTextOptimizationStatus: adgroupInfo.creativeTextOptimizationStatus,
        segmentRecommendStatus: adgroupInfo.segmentRecommendStatus,
        isInAdgroupProcess: false,
        isShowCreativeForm: true
    };
    const {data: adgroupBindInfo, pending} = useAdgroupBindAppInfo(adgroupInfo);
    const alertKey = getCPQLAlertKey({
        marketingTargetId: adgroupInfo?.marketingTargetId,
        structuredContentIds: adgroupInfo.structuredContentIds,
        adgroupInfo: adgroupBindInfo?.[0]
    });
    const onSave = async () => {
        const values = await programmaticCreativeInstance.methods.validateFields({scrollToError: false});
        const params = await generateParams(
            {campaignId, adgroupId, creativeId},
            {initialSegments: imageSegments, initialVideos: videoSegments, ...campaignInfo, ...adgroupInfo},
            {values}
        );

        try {
            await modCreative({item: params});
            history.goBack();
        }
        catch (errors) {
            const {creativeErrors, status, creativeErrorsMap, ...segmentBindError} = formatCreativeErrors(errors);
            const formattedCretiveTypeError = formatCretiveTypeError(
                {
                    creativeErrors,
                    status
                },
                {setFieldsError: programmaticCreativeInstance.methods.setFieldsError}
            );
            const segmentErrorToAlert = validateSegmentsError(segmentBindError);
            const alertError = [...formattedCretiveTypeError.toAlert, ...segmentErrorToAlert];

            if (formattedCretiveTypeError._normalized?.errors.length) {
                formattedCretiveTypeError.fillFormErrors();
            }

            if (alertError.length) {
                Dialog.alert({
                    title: '编辑创意失败',
                    content: alertError.map(err => (<div key={err.code}>{err.message}</div>))
                });
            }
            else if (
                creativeErrors.length === 0
                && Object.keys(segmentBindError).every(key => isEmpty(segmentBindError[key]))
            ) {
                Dialog.alert({title: '编辑创意失败', content: getSingleErrorMessage(errors?.errors[0])});
            }
        }
    };
    const contextValue = useMemo(
        () => ({
            marketingTargetId,
            campaignInfo,
            adgroupInfo: {
                adgroups,
                accountVideoSegmentCount: accountVideoCount - adgroupVideoCount,
                adgroupsBindVideoSegmentCount: adgroupVideoCount,
                catalogId: adgroups[0]?.structuredContentIdStrs,
                productCategoryType: adgroups[0]?.productCategoryType
            }
        }),
        [campaignInfo, adgroups, accountVideoCount, adgroupVideoCount, marketingTargetId]
    );
    const stepsProps = {
        current: 1,
        ...(
            botVisible ? toPithySteps(stepConfig) : stepConfig
        )
    };
    const sidebarStyle = botVisible ? {width: '80px'} : {};
    return (
        <Layout className="ad-main-edit-creative">
            <Sidebar sticky style={sidebarStyle}>
                <ProcessSteps {...stepsProps} />
            </Sidebar>
            <Layout>
                <Header sticky>
                    <div className="creative-adgroup-tip">
                        所属计划 “计划{campaignName}”，单元“单元{adgroupInfo.adgroupName}”
                    </div>
                </Header>
                <Content>
                    {!pending && <TopAlert alertKey={alertKey} />}
                    <CreativeSharedInfoProvider value={contextValue}>
                        <Creatives
                            initialCreativeData={initialCreativeData}
                            programmaticCreativeInstance={programmaticCreativeInstance}
                            mountProgrammaticCreativeInstance={mountProgrammaticCreativeInstance}
                            initialProgrammaticCreative={initialProgrammaticCreative}
                            slotProps={slotProps}
                            isEdit
                        />
                    </CreativeSharedInfoProvider>
                </Content>
                <Footer sticky className="ad-main-edit-creative-footer">
                    <SaveFooter saveLabel="修改创意" onSave={onSave} onCancel={() => history.goBack()} />
                </Footer>
            </Layout>
        </Layout>
    );
}

export default function () {
    const {adgroupId, campaignId, creativeId} = useParams();
    return (
        <CacheProvider>
            <SuspenseBoundary>
                <EditCreative
                    campaignId={campaignId}
                    adgroupId={adgroupId}
                    creativeId={creativeId}
                />
            </SuspenseBoundary>
        </CacheProvider>
    );
}

async function generateParams(
    {campaignId, adgroupId, creativeId},
    {initialSegments, initialVideos, ...info},
    {values}
) {
    const {creativeType} = generateCreativeParams(info, {...values, creativeId});
    const {
        campaign2AdgroupTypes,
        addAdgroupSegmentBinds,
        deleteSegmentBinds,
        updateSegments,
        updateSegmentBinds
    } = getEditSegments({campaignId, adgroupId, segments: [values.segmentBinds.segments, initialSegments]});
    const {
        addAdgroupVideoBinds,
        deleteVideoBinds,
        updateVideos
    } = getVideoSegmentParams({videoSegments: [values.segmentBinds.videos, initialVideos], campaign2AdgroupTypes});
    return {
        campaign2AdgroupTypes,
        creativeType,
        addAdgroupSegmentBinds: addAdgroupSegmentBinds.concat(addAdgroupVideoBinds),
        deleteSegmentBindInfos: deleteSegmentBinds.concat(deleteVideoBinds),
        updateSegments: updateSegments.concat(updateVideos),
        updateSegmentBinds
    };
}

function formatCretiveTypeError({creativeErrors, status}, {setFieldsError}) {
    const formattedCretiveTypeError = createError({errors: creativeErrors, status});
    const creativeTypeMessagesToAlert = [];
    const errorFieldsMap = {};
    creativeErrors.forEach(error => {
        const {field, code, position} = error;
        const message = getSingleErrorMessage(error);
        if (mapProgramFieldToInnerField[field] && position != null) {
            errorFieldsMap[`${field}.${position}.${mapProgramFieldToInnerField[field]}`] = message;
        }
        else {
            errorFieldsMap[field] = message;
        }
        creativeTypeMessagesToAlert.push({code, message: `自适应创意文案: ${message}`});
    });
    formattedCretiveTypeError.errorFieldsMap = errorFieldsMap;
    formattedCretiveTypeError.fillFormErrors = () => setFieldsError(errorFieldsMap);
    formattedCretiveTypeError.toAlert = creativeTypeMessagesToAlert;
    return formattedCretiveTypeError;
}

// 目前只有程序化加了，自定义没有
function formatTextAuditInfo(formValues) {
    const {
        programDescriptionAuditInfos,
        programTitleAuditInfos,
        shadowProgramDescriptionAuditInfos,
        shadowProgramTitleAuditInfos
    } = formValues;

    const programTitleAuditMap = {};
    const programDescAuditMap = {};

    (shadowProgramTitleAuditInfos || programTitleAuditInfos || []).forEach(({
        reason, content, auditStatus
    }) => {
        if (auditStatus === CreativeTextStatus.REJECT) {
            programTitleAuditMap[content] = reason;
        }
    });
    (shadowProgramDescriptionAuditInfos || programDescriptionAuditInfos || []).forEach(({
        content, reason, auditStatus
    }) => {
        if (auditStatus === CreativeTextStatus.REJECT) {
            programDescAuditMap[content] = reason;
        }
    });
    return {
        programTitleAuditMap,
        programDescAuditMap
    };
}
