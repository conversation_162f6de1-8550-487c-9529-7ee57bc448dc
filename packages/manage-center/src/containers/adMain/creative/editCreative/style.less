.ad-main-edit-creative {
    .creative-adgroup-tip {
        background-color: #fff;
        border-radius: 6px;
        margin-bottom: 20px;
        color: #848b99;
        font-size: 14px;
        padding: 20px 0 16px 32px;
    }
    .creative-top-alert {
        margin: 16px 20px;
    }
    &-form-container {
        display: flex;
        justify-content: space-between;
        margin: 16px 20px;
        background: #fff;
        padding: 20px;
        .recommend-creatives + .ad-main-creative-side-container {
            margin-top: 20px;
        }
    }
    &-form {
        flex: 1;
        margin-right: 24px;
        width: 0;
    }
    .ad-main-edit-creative-footer {
        background: #fff;
        display: flex;
        align-items: center;
        padding: 0 20px;
        margin: 0 20px;
    }

    .wood-spring-app-preview {
        margin-left: 0;
        margin-top: 0;

        .wood-spring-app-preview-container {
            padding: 0;
        }
    }
}