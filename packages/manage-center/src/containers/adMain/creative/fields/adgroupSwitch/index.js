/*
 * @file: 单元层级的开关
 * <AUTHOR>
 * @date Mon Jul 10 2023
 */

import {useCallback} from 'react';
import {useActionPending} from 'huse';
import {Switch, Popover} from '@baidu/one-ui';
import Tip from 'commonLibs/Tips';
import {updateSegmentsRecommendStatus} from 'app/containers/fcNew/apis/creative';
import {displayError, createError} from 'commonLibs/utils/materialList/error';
import {productCategoryTypeEnum} from 'commonLibs/config/enumEntry';
import {cancelConfirm, isShowCancelConfirm} from 'app/containers/adgroups/utils/cancelConfirm';
import {useCreativeSharedInfo} from '../../context';
import './style.less';

const fieldMap = {
    'segmentRecommendStatus': {
        text: '自动图片优化',
        tipKey: 'recommendCreativeStatus'
    },
    'creativeTextOptimizationStatus': {
        text: '自动文案优化',
        tipKey: 'creativeTextOptimizationStatus'
    },
    'videoAutoOptimizationStatus': {
        text: '自动视频优化',
        tipKey: 'videoAutoOptimizationStatus'
    }
};

export function AdgroupSwitch(props) {
    const {
        value,
        onChange: _onChange,
        field,
        style
    } = props;
    const {adgroupInfo} = useCreativeSharedInfo();
    const {
        adgroups,
        catalogId,
        productCategoryType
    } = adgroupInfo;
    const tourismSelectedProduct = catalogId?.length > 0 && productCategoryType === productCategoryTypeEnum.LXT;

    const [updateStatus, pendingCount] = useActionPending(updateSegmentsRecommendStatus);

    const onChange = useCallback(
        async function (value) {
            const onOk = async () => {
                if (adgroups.length === 1) {
                    try {
                        await updateStatus(adgroups[0].adgroupId, value, field);
                    }
                    catch (err) {
                        displayError(createError(err));
                        return; // * 这里需要return 接口失败不应该更新状态
                    }
                }
                _onChange(value);
            };

            if (isShowCancelConfirm(productCategoryType, catalogId)
                && !value
            ) {
                return cancelConfirm({
                    contentLabel: fieldMap[field].text,
                    onOk
                });
            }
            onOk();
        },
        [_onChange, field, adgroups, updateStatus, catalogId]
    );


    const switchProps = {
        checked: value,
        onChange,
        loading: !!pendingCount,
        disabled: tourismSelectedProduct
    };

    return (
        <div className="ad-main-adgroup-switch" style={style}>
            <Popover><Switch {...switchProps} /></Popover>
            <span className="ad-main-adgroup-switch-text">{fieldMap[field].text}</span>
            <Tip keyName={fieldMap[field].tipKey} />
        </div>
    );
}
