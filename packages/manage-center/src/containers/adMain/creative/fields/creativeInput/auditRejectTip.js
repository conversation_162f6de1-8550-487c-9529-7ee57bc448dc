import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>} from '@baidu/one-ui';
import {IconInfoCircleSolid} from 'dls-icons-react';
import {useBoolean} from 'huse';
import './style.less';


export const AuditRejectTip = ({rejectReason, onHideClick}) => {

    const [visible, {toggle}] = useBoolean(false);

    return (
        <Popover
            title="审核不通过"
            visible={visible}
            onVisibleChange={toggle}
            content={(
                <div className='creative-input-audit'>
                    <div className='creative-input-audit-reason'>
                        不通过原因：{rejectReason}
                    </div>
                </div>
            )}
            placement='topRight'
        >
            <IconInfoCircleSolid className='creative-input-icon' />
        </Popover>
    );
};
