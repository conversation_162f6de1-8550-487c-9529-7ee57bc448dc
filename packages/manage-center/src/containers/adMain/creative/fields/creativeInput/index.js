import {setCaretPosition, getCaretPosition} from 'app/containers/fcNew/creative/title/util';
import {useState, useEffect, useRef} from 'react';
import {Input, Button, TextArea} from '@baidu/one-ui';
import {
    filterArr,
    REGION_WILDCARD_PREFIX,
    provinceReg,
    PROVINCE_WILDCARD_PREFIX
} from 'app/containers/fcNew/creative/title/config';
import {formatCHtoENCharts} from 'commonLibs/config/irregularChar';
import {CharWarningInfo} from 'commonLibs/components/charWarningInfo';
import {AuditRejectTip} from './auditRejectTip';
import Tip from 'commonLibs/Tips';
import './style.less';
import {useInputMaxWidth} from 'app/containers/adMain/creative/hooks/useInputMaxWidth';
import {getCreativeIrregularCharUser} from 'commonLibs/utils/getFlag';

const CreativeInputWildcard = ({value, onAddKeywordWildcard, onAddRegionWildcard, regionWildcard}) => {
    return (
        <div className="input-wildcard">
            <div>插入通配符</div>
            <Button onClick={onAddKeywordWildcard} type="text-strong">
                <div className="btn-with-tip">
                    <span className="keyword-wildcard">
                        + 关键词
                        <Tip keyName='keywordWildcard' />
                    </span>
                </div>
            </Button>
            {
                regionWildcard && (
                    <Button
                        disabled={value && value.indexOf(REGION_WILDCARD_PREFIX) >= 0}
                        onClick={onAddRegionWildcard}
                        type="text-strong"
                    >
                        <div className="btn-with-tip">
                            <span className="keyword-wildcard">
                                + 地域
                                <Tip keyName='zoneWildcard' />
                            </span>
                        </div>
                    </Button>
                )
            }
        </div>
    );
};


export const CreativeTextInput = props => {
    const {
        value = '', onChange, hideRegionWildCard = false,
        maxLen, minLen, auditMap, onBlur, isTextArea = false, className,
        reducedWidth = 0
    } = props;
    const ref = useRef();

    // 根据初始化的文案去获取错误原因，这样就不需要去判断index了， 用index可能不准
    const [auditRejectDesc] = useState(auditMap?.[value] || '');
    const [hasWrittenCHChar, setHasWrittenCHChar] = useState(false);
    // 用于在插入通配符的时候重新定位光标
    const [caretPosition, changeCaretPosition] = useState(0);

    // 是否编辑过title 编辑过才展示字符替换的warning
    const [isFocused, setIsFocused] = useState(false);

    const inputMaxWidth = useInputMaxWidth();

    useEffect(() => {
        if (caretPosition !== 0) {
            const inputEle = ref.current;
            setCaretPosition(inputEle, caretPosition);
        }
    }, [caretPosition]);

    const onChangeTitleInput = e => {
        setHasWrittenCHChar(false);
        let value = e.value;
        if (provinceReg.test(value)) { // 省份前置
            value = PROVINCE_WILDCARD_PREFIX + value.replace(provinceReg, '');
        }
        setIsFocused(true);
        const formatedEnValue = formatCHtoENCharts(value);
        const newValue = getCreativeIrregularCharUser() ? formatedEnValue : value;
        if (newValue !== value) {
            setHasWrittenCHChar(true);
        }
        onChange(newValue);
    };

    const onAddKeywordWildcard = () => {
        const inputEle = ref.current;

        const result = `${value.substring(0, inputEle.selectionStart)}{${value.substring(
            inputEle.selectionStart,
            inputEle.selectionEnd
        )}}${value.substring(inputEle.selectionEnd)}`;
        onChange(result);
        changeCaretPosition(getCaretPosition(inputEle) + 1);
    };
    const onAddRegionWildcard = () => {
        const result = REGION_WILDCARD_PREFIX + value;
        onChange(result);
        changeCaretPosition(REGION_WILDCARD_PREFIX.length - 1);
    };

    // 这里-oneui textarea不支持 width 100%，先用js实现自适应的宽度吧

    const commonProps = {
        value,
        width: inputMaxWidth - reducedWidth, // 减去删除按钮的宽度
        placeholder: `${minLen}-${maxLen}个字符`,
        onChange: onChangeTitleInput,
        filterArray: filterArr,
        showErrorMessage: false,
        onBlur,
        maxLen,
        minLen
    };

    const inputProps = {
        ...commonProps,
        inputRef: ref,
        className: `creative-title-input ${className}`,
        suffix: auditRejectDesc ? <AuditRejectTip rejectReason={auditRejectDesc} /> : null
    };
    const textAreaProps = {
        ...commonProps,
        containerRef: el => {
            ref.current = el.textAreaRef;
        },
        maxRows: 2,
        minRows: 1,
        className: `creative-title-textArea ${className}`
    };
    return (
        <div>
            <div className="creative-title-input-container">
                {
                    isTextArea
                        ? <TextArea {...textAreaProps} />
                        : <Input {...inputProps} />
                }
                <CreativeInputWildcard
                    value={value}
                    onAddRegionWildcard={onAddRegionWildcard}
                    onAddKeywordWildcard={onAddKeywordWildcard}
                    regionWildcard={!isTextArea && !hideRegionWildCard}
                />
            </div>
            {isFocused && hasWrittenCHChar && <CharWarningInfo value={value} />}
        </div>
    );
};
