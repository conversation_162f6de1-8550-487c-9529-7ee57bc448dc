.creative-input-icon {
    color: #f27318;
}
.creative-input-audit {
    width: @dls-height-unit * 92;
    &-btns {
        display: flex;
        justify-content: flex-end;
        margin-top: @dls-padding-unit * 3;
        button {
            margin-right: @dls-padding-unit * 2;
        }
    }
    &-reason {
        word-wrap: break-word;
    }
}

.creative-title-input-container {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    .input-wildcard {
        margin-top: @dls-padding-unit * 2;
        display: flex;
        align-items: center;
        button {
            margin-left: @dls-padding-unit * 4;
        }
        .btn-with-tip {
            display: flex;
        }
        .keyword-wildcard {
            display: flex;
            align-items: center;
        }
    }
}
