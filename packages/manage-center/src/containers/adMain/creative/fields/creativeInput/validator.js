import {
    filterArr,
    INPUT_MIN,
    provinceReg,
    monthReg,
    dateReg,
    areaReg,
    MAX_BRACE_NUM
} from 'app/containers/fcNew/creative/title/config';
import {identity} from 'lodash-es';
import {getLengthInBytes} from 'commonLibs/utils/string';
import {testBrace} from 'app/containers/fcNew/creative/title/validator';
import {getCreativePreCheck} from 'app/containers/fcNew/apis/creative';
import {getSingleErrorMessage} from 'commonLibs/utils/getErrorTextByCode';
import {creativeTypeEnum} from 'commonLibs/config/creativeText';

// 创意文案校验规则
export const creativeInputValidator = (value = '', INPUT_MAX, isRequired) => {
    const localeRegexp = /.+{投放地域}/;
    if (value.startsWith(' ') || value.endsWith(' ')) {
        return '不能以空格作为开头或结尾';
    }
    if (localeRegexp.test(value)) {
        return '地域通配符仅支持在创意标题前插入1次';
    }
    if ([provinceReg, monthReg, dateReg].some(reg => reg.test(value.replace(reg, '')))) {
        return '标题中创意通配符重复，请修改';
    }
    if (testBrace(value)) {
        return `您连续输入了${MAX_BRACE_NUM}个大括号，请删除一个。`;
    }
    const emptyKeywordWildcardRegexp = /\{\}/;
    if (emptyKeywordWildcardRegexp.test(value)) {
        return '创意默认关键词不能为空';
    }
    const inputLen = getLengthInBytes(value, filterArr);
    if (inputLen > INPUT_MAX) {
        return `已超出${inputLen - INPUT_MAX}个字符`;
    }
    if (!!inputLen && inputLen < INPUT_MIN) {
        return `还需输入${INPUT_MIN - inputLen}个字符`;
    }
    if (isRequired && inputLen === 0) {
        return '请输入创意文案';
    }
    const wildcard = [provinceReg, monthReg, dateReg, areaReg].map(reg => reg.test(value)).filter(identity);
    if (wildcard.length > 2) {
        return '标题中最多支持使用2个创意通配符，请修改';
    }
    if (provinceReg.test(value) && areaReg.test(value)) {
        return '标题中最多支持使用1个地区类通配符，请修改';
    }
};

export const onPreCheckValidator = (field, value, INPUT_MAX, isRequired = true,
    creativeType = creativeTypeEnum.customize) => {
    const formatError = creativeInputValidator(value, INPUT_MAX, isRequired);
    if (formatError) {
        return formatError;
    }

    if (value) {
        return getCreativePreCheck({field, value}, creativeType).then(() => {
            return '';
        }).catch(err => {
            const error = err.errors && err.errors[0];
            return getSingleErrorMessage(error);
        });
    }
};
