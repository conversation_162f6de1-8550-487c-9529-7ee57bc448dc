/*
 * @file: 创意素材： 图片和视频
 * <AUTHOR>
 * @date Fri Jun 30 2023
 */

import UploadImage from 'app/containers/fcNew/creative/creativeImage/upload/UploadImage';
import {ReactNode} from 'react';
import {getImageTypes} from 'app/containers/fcNew/creative/utils/image';
import {ImageTypes} from 'commonLibs/config/image/imageType';
import {useFormContext} from '@baidu/react-formulator';
import {useInputMaxWidth} from '../../hooks/useInputMaxWidth';
import {CounterForSegments} from '../../components/counter';
import './style.less';
import {useOpenDigitalHumanComposer} from 'app/hooks/useAichatCreative';
import {isInQinggeIframe} from 'commonLibs/utils/isInIframe';


interface CreativeSegmentsValue {
    segments: any[];
    videos: any[];
}

interface CreativeSegmentsProps {
    value: CreativeSegmentsValue;
    onChange: (value: any) => void;
    /**
     * 输入框的错误信息
     * 表单套件会根据validator和rule把errors传入，不用自己写
     */
    errors: ReactNode[];
    /**
     * 单元的信息，目前只用到了单元id
     * 用于获取创意图片的图片主题
     * 在单元创意同时新建的流程中，由于单元还没有创建，所以这里可以不传或者传空数组
     * @default []
     */
    adgroups?: Array<{
        adgroupId: number;
        marketingTargetId: number;
        shopType: number;
        adType: number;
        equipmentType: number;
        campaignBidType: number;
    }>;
    /**
     * title值为创意标题，用于推荐图片
     */
    recommend: {
        title: string;
    };
    /**
     * 是否必填
     * 由单元信息决定是否必填
     * 单元层级是否开启自动图片 不是必填
     * 单元层级已有其他图片， 不是必填
     * 其他情况为必填
     */
    isRequired: boolean;
    /**
     * 账户绑定的视频素材数量
     * 用于判断最大可上传视频数量
     */
    accountVideoSegmentCount: number;
    /**
     * 单元绑定的视频素材数量
     * 用于判断最大可上传视频数量
     * 在单元创意同时新建的流程中，由于单元还没有创建，所以这里可以不传或者传0
     * @default 0
     */
    adgroupsBindVideoSegmentCount: number;

    /**
     * 组件支持的素材类型
     */
    segmentMaterials: Array<'image' | 'video'>;
    /**
     * 计划信息
     */
    campaignInfo: {
        marketingTargetId: number;
        shopType: number;
        adType: number;
        equipmentType: number;
        campaignBidType: number;
    };
    // 隐藏“生成更多图片”、“生成更多视频”按钮
    hideGenerateButton?: boolean;
}

export function CreativeSegments({
    value,
    onChange,
    errors,
    adgroups = [],
    recommend,
    accountVideoSegmentCount,
    adgroupsBindVideoSegmentCount = 0,
    segmentMaterials,
    campaignInfo,
    hideGenerateButton
}: CreativeSegmentsProps) {
    const {formData} = useFormContext();
    const inputMaxWidth = useInputMaxWidth();
    const {
        marketingTargetId, shopType, adType, equipmentType, campaignBidType
    } = campaignInfo;

    // 当adgroups.length 为空时， 说明是在单元创意同时新建的流程中，此时拿不到单元信息，所以这里传入计划信息
    // 不为空时，则需要传入单元信息，因为单元可能是在不同计划下的，所以不能直接用计划信息替代
    const imageTypes = getImageTypes(
        adgroups.length
            ? adgroups
            : [
                {marketingTargetId, shopType, adType, equipmentType, campaignBidType}
            ],
    ) as ImageTypes[];

    const {getDigitalHumanVideo} = useOpenDigitalHumanComposer(formData);

    const uploadImageProps = {
        value,
        onChange,
        imageTypes,
        adgroups,
        recommend,
        accountVideoSegmentCount,
        adgroupsBindVideoSegmentCount,
        segmentMaterials,
        // * 创意素材的错误信息， 注意这个不是图片审核拒绝的错误信息
        // * 这个在表单校验时，如果有错误，会把子项中图片主题为空的图片主题背景色标红
        imageError: errors as string[],
        isInNewProcess: true,
        // isRequired 这里暂时不传isRequired了。组件内部用于展示必填星号，但新版组件不需要展示
        getDigitalHumanVideo,
        showCreativeGenerateButton: !hideGenerateButton && !isInQinggeIframe
    };

    return (
        <div className="ad-main-creative-segments" style={{width: inputMaxWidth}}>
            <UploadImage {...uploadImageProps} />
        </div>
    );
}


export function CreativeSegmentsWithTitle(props: CreativeSegmentsProps) {
    const {segments = [], videos = []} = props.value;
    return (
        <div className="ad-main-creative-segments-with-title">
            <div className="ad-main-creative-segments-title">
                <span className="ad-main-creative-segments-title-text">创意素材</span>
                <CounterForSegments
                    videoCount={videos.length}
                    picCount={segments.length}
                />
            </div>
        </div>
    );
}
