/*
 * @file: 创意素材校验规则
 * <AUTHOR>
 * @date Mon Jul 03 2023
 */

import {isVideoRestructure} from 'commonLibs/utils/getFlag';

export function validateSegments(value, segmentRecommendStatus) {
    const {
        segments = [],
        videos = []
    } = value;
    const errors = [];
    // 名单内优化开关关闭时，视频和图片至少要填一个；名单外只校验图片
    if (isVideoRestructure() && !segmentRecommendStatus && segments.length === 0 && videos.length === 0) {
        errors.push('图片/视频至少上传1个');
    }
    else if (!isVideoRestructure() && !segmentRecommendStatus && segments.length === 0) {
        errors.push('图片至少上传1张');
    }
    if (segments.length >= 1 && segments.some(segment => !segment.desc)) {
        errors.push('图片主题必填');
    }
    // 如果是数字人视频(v.isDigitalHumanVideo)，则不校验这些内容
    if (videos.length >= 1 && videos.some(v => !v.videoUrl && !v.isDigitalHumanVideo)) {
        errors.push('视频必填');
    }
    if (videos.length >= 1 && videos.some(v => !v.splendidVideoInfo?.videoUrl && !v.isDigitalHumanVideo)) {
        errors.push('精彩片段视频必填');
    }
    if (videos.length >= 1 && videos.some(v => (!v.images || v.images?.length === 0) && !v.isDigitalHumanVideo)) {
        errors.push('视频封面必填');
    }
    if (errors.length) {
        return errors;
    }
}
