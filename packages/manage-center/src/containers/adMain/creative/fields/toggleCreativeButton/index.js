/*
 * @file: 展开收起创意
 * <AUTHOR>
 * @date Mon Jul 10 2023
 */
import {useCallback} from 'react';
import {Button, Popover} from '@baidu/one-ui';
import {IconChevronDown, IconChevronUp} from 'dls-icons-react';
import sendMonitor from 'commonLibs/utils/sendHm';

export function ToggleCreativeButton({
    value, onChange, titles = ['展开', '收起'],
    confirmTip, marketingTargetId
}) {

    const [expandTitle, collapseTitle] = titles;
    const tip = value && confirmTip || null; // 展开时才有tip

    // 展开时才有tip
    const onClick = useCallback(
        () => {
            sendMonitor('funnel_statistic', {
                source: 'AdgroupAndCreativeForm',
                target: 'toggleCreative',
                marketingtargetid: marketingTargetId,
                value: value ? '收起' : '展开'
            });
            onChange(!value);
        },
        [value, onChange, marketingTargetId]
    );

    return (
        <div className="ad-main-toggle-creative-button">
            <Popover content={tip}>
                <Button
                    className="ad-main-toggle-creative-button-control-btn"
                    type="text-strong"
                    onClick={onClick}
                >
                    <div className="ad-main-toggle-creative-button-control-btn-inner">
                        {
                            value
                                ? (
                                    <>
                                        <span className="ad-main-toggle-creative-button-control-btn-title">
                                            {collapseTitle}
                                        </span>
                                        <IconChevronUp
                                            className="ad-main-toggle-creative-button-control-btn-icon"
                                        />
                                    </>

                                )
                                : (
                                    <>
                                        <span className="ad-main-toggle-creative-button-control-btn-title">
                                            {expandTitle}
                                        </span>
                                        <IconChevronDown
                                            className="ad-main-toggle-creative-button-control-btn-icon"
                                        />
                                    </>
                                )
                        }
                    </div>
                </Button>
            </Popover>
        </div>
    );
}
