/*
 * @file: 获取输入框的自适应的最大宽度
 * <AUTHOR>
 * @date Wed Jun 28 2023
 */

import {useElementSize} from 'commonLibs/hooks/size';

export function useInputMaxWidth(): number {
    const ele = document.querySelector('.one-tabs-tab-pane-is-active .ad-main-creative-form')
        || document.querySelector('.ad-main-creative-form');
    const {clientWidth = 910} = useElementSize(ele as HTMLElement) || {};
    // 这里-oneui textarea不支持 width 100%，先用js实现自适应的宽度吧
    const maxWidth = clientWidth - 32; // 减去两个padding
    return maxWidth;
}

