/*
 * @file: 串联新建-用于单元+创意组合新建
 * <AUTHOR>
 * @date Tue Jul 11 2023
 */

import {useMemo} from 'react';
import {partial} from 'lodash-es';
import {UIFormGroup} from '@baidu/react-formulator';
import AppType from 'commonLibs/config/osType';
import {useBotContextInfo} from 'commonLibs/context/botInfo';
import TopAlert from 'app/containers/fcNew/creative/topAlert';
import {getCPQLAlertKey} from 'app/containers/fcNew/creative/topAlert/config';
import {getImageTypes} from 'app/containers/fcNew/creative/utils/image';
import {CreativeSharedInfoProvider} from '../context';
import {CreativesWithAichat} from '../Creatives';

const adgroupInfoForNew = {
    adgroups: [],
    accountVideoSegmentCount: 0,
    adgroupsBindVideoSegmentCount: 0
};
const slots = {
    // recommend: RecommendCreatives,
    titleAlert: TopAlert
};

export function CreativesInAdgroup({
    adgroupKey,
    currentAdgroupInfo,
    adgroupTypeEmitter,
    reactiveCreativeInfo,
    mountProgrammaticCreativeInstance,
    mountCustomizeCreativeInstance,
    campaignInfo,
    isDisabledAgentReg,
    osType = AppType.ANDROID.value,
    ...props
}) {
    const {visible: botVisible} = useBotContextInfo();
    // 屏幕过大时，右侧预览区显示在右侧
    const isShowPreviewOnRightSection = document.body.clientWidth >= 1920 || !botVisible;
    const methods = useMemo(
        () => ({
            mountProgrammaticCreativeInstance: partial(mountProgrammaticCreativeInstance, adgroupKey),
            mountCustomizeCreativeInstance: partial(mountCustomizeCreativeInstance, adgroupKey)
        }),
        [adgroupKey, mountProgrammaticCreativeInstance, mountCustomizeCreativeInstance]
    );
    const imageTypes = useMemo(() => getImageTypes([campaignInfo]), [campaignInfo]);
    const slotProps = useMemo(() => ({imageTypes, slots, style: {margin: '12px 0'}}), [imageTypes]);
    // 创意推荐展开收起
    const alertKey = getCPQLAlertKey({
        marketingTargetId: campaignInfo.marketingTargetId,
        isProcessNew: true,
        adgroupInfo: {platform: osType}
    });
    const contextValue = useMemo(
        () => ({
            campaignInfo,
            adgroupInfo: {
                ...adgroupInfoForNew,
                catalogId: currentAdgroupInfo?.catalogId,
                bjhContent: currentAdgroupInfo?.bjhContent,
                productCategoryType: currentAdgroupInfo?.productCategoryType
            },
            isDisabledAgentReg,
            $on: adgroupTypeEmitter.on.bind(adgroupTypeEmitter),
            $trigger: adgroupTypeEmitter.triggerSync.bind(adgroupTypeEmitter)
        }),
        [campaignInfo, currentAdgroupInfo?.catalogId, currentAdgroupInfo?.bjhContent,
            isDisabledAgentReg, adgroupTypeEmitter]
    );
    return (
        <div className="ad-main-create-in-adgroup">
            <UIFormGroup config={{group: 'creatives'}} title="创意设置">
                <CreativeSharedInfoProvider value={contextValue}>
                    <CreativesWithAichat
                        initialCreativeData={reactiveCreativeInfo}
                        {...methods}
                        {...props}
                        slotProps={slotProps}
                        alertKey={alertKey}
                        isShowPreviewOnRightSection={isShowPreviewOnRightSection}
                    />
                </CreativeSharedInfoProvider>
            </UIFormGroup>
        </div>
    );
}
