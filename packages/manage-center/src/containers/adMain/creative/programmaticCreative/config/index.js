import {memo} from 'react';
import {Popover} from '@baidu/one-ui';
import {CreativeTextInput} from 'app/containers/adMain/creative/fields/creativeInput';
import {INPUT_MAX as DESC_MAX_LEN, INPUT_MIN as DESC_MIN_LEN} from '../../../../fcNew/creative/desc/config';
import {INPUT_MAX as TITLE_MAX_LENGTH, INPUT_MIN as TITLE_MIN_LENGTH} from '../../../../fcNew/creative/title/config';
import {FieldArrayInput} from 'commonLibs/components/formulatorRender/FieldArrayInput';
import {TabsGroup} from 'commonLibs/components/formulatorRender/TabsGroup';
import {convertFieldToConfig} from 'app/containers/adMain/utils';
import {MaterialConfig} from 'app/containers/fcNew/creative/creativeImage/config';
import {IconExclamationTriangleSolid} from 'dls-icons-react';
import {useFieldValue, useFormContext} from '@baidu/react-formulator';
import {creativeInputValidator, onPreCheckValidator} from '../../fields/creativeInput/validator';
import {partial} from 'lodash-es';
import {CreativeSegments} from '../../fields/segments';
import {validateSegments} from '../../fields/segments/validator';
import {creativeParamsPipeline, creativeTypeParams, creativeIdParams} from '../../utils/generateParams';
import {Counter, CounterForSegments} from '../../components/counter';
import {creativeTypeEnum} from 'commonLibs/config/creativeText';
import {
    TextGenerateButton
} from 'app/containers/adMain/creative/components/generateButton';
import {NATIVE} from 'commonLibs/config/marketTarget';
import {isInQinggeIframe} from 'commonLibs/utils/isInIframe';


const MIN_PROGRAM_COUNT = 1;
export const MAX_PROGRAM_COUNT = 6;
const REDUCE_WIDTH = 36;
const PROGRAM_DESC_MAX_LEN = 160;

export const mapProgramFieldToInnerField = {
    programTitles: 'title',
    programDescs: 'desc'
};


function GenerateButtonAnchor(props) {
    return (
        <div className="ad-main-form-creative-box-title-for-block">
            <TextGenerateButton {...props} />
        </div>
    );
}

const titleSlotProps = {
    slots: {
        fieldContentItemAnchor: memo(GenerateButtonAnchor)
    }
};
const programTitles = props => ({
    field: 'programTitles',
    label: null,
    name: '创意标题',
    use: [FieldArrayInput, {
        valueToAdd: {title: ''},
        min: MIN_PROGRAM_COUNT,
        max: MAX_PROGRAM_COUNT,
        slotProps: props.hideGenerateButton ? {
            slots: {}
        } : {
            ...titleSlotProps,
            buttonText: '生成更多标题'
        }
    }],
    fieldArrayConfig: [{
        field: 'title',
        label: null,
        name: '创意标题',
        validators: [
            {
                validator: partial(creativeInputValidator, partial.placeholder, TITLE_MAX_LENGTH, true)
            },
            {
                validator: partial(onPreCheckValidator, 'title', partial.placeholder,
                    TITLE_MAX_LENGTH, true, creativeTypeEnum.programmatic),
                triggers: ['onBlur', '@final']
            }
        ],
        use: [CreativeTextInput, {
            maxLen: TITLE_MAX_LENGTH,
            minLen: TITLE_MIN_LENGTH,
            reducedWidth: REDUCE_WIDTH
        }],
        componentProps: formData => {
            return {auditMap: formData.programTitleAuditMap};
        }
    }]
});

const programDescs = props => ({
    field: 'programDescs',
    label: null,
    name: '创意描述',
    use: [FieldArrayInput, {
        valueToAdd: {desc: ''},
        min: MIN_PROGRAM_COUNT,
        max: MAX_PROGRAM_COUNT,
        slotProps: props.hideGenerateButton ? {
            slots: {}
        } : {
            ...titleSlotProps,
            buttonText: '生成更多描述'
        }
    }],
    fieldArrayConfig: [{
        field: 'desc',
        label: null,
        name: '创意描述',
        validators: [
            {
                validator: partial(creativeInputValidator, partial.placeholder, PROGRAM_DESC_MAX_LEN, true)
            },
            {
                validator: partial(
                    onPreCheckValidator, 'description1', partial.placeholder,
                    PROGRAM_DESC_MAX_LEN, true, creativeTypeEnum.programmatic
                ),
                triggers: ['onBlur', '@final']
            }
        ],
        use: [CreativeTextInput, {
            maxLen: PROGRAM_DESC_MAX_LEN,
            minLen: DESC_MIN_LEN,
            reducedWidth: REDUCE_WIDTH
        }],
        componentProps: formData => {
            return {auditMap: formData.programDescAuditMap};
        }
    }]
});


const programSegments = ({
    adgroupInfo,
    campaignInfo,
    creativeCommonFormData,
    hideGenerateButton
}) => {
    const {
        adgroups,
        accountVideoSegmentCount,
        adgroupsBindVideoSegmentCount
    } = adgroupInfo;
    const {marketingTargetId} = campaignInfo;
    const isShowVideo = marketingTargetId !== NATIVE;
    return {
        field: 'segmentBinds',
        label: null,
        name: '创意素材',
        use: [CreativeSegments, {
            adgroups,
            accountVideoSegmentCount,
            adgroupsBindVideoSegmentCount,
            campaignInfo,
            segmentMaterials: [MaterialConfig.image, ...(isShowVideo ? [MaterialConfig.video] : [])],
            hideGenerateButton
        }],
        componentProps: formData => {
            return {
                recommend: {
                    title: formData.programTitles.find(i => i.title)?.title // 找到第一个不为空的
                }
            };
        },
        validators: value => {
            const segmentRecommendStatus = creativeCommonFormData.segmentRecommendStatus;
            return validateSegments(value, segmentRecommendStatus);
        }
    };
};

const baseFields = {
    programTitles,
    programDescs,
    segmentBinds: programSegments
};


const creativeSegmentsGroup = ({
    campaignInfo
}) => {
    const {marketingTargetId} = campaignInfo;
    const isShowVideo = marketingTargetId !== NATIVE;
    return {
        group: 'creativeSegmentsGroup',
        use: [TabsGroup, {
            minHeight: 304,
            TitleRenderer,
            titleProps: {isShowVideo}
        }]
    };
};

const baseGroups = {
    creativeSegmentsGroup
};

export const remoteConfigLoaders = {};
export const generateCreativeParams = creativeParamsPipeline(
    [creativeTypeParams, creativeIdParams],
    {creativeType: {}}
);

export async function initialProgrammaticCreativeForm({
    campaignInfo,
    adgroupInfo,
    creativeCommonFormData,
    initialData,
    isEdit
}) {
    const {marketingTargetId, adType} = campaignInfo;

    // TODO 目前看是固定的，不确定后面程序化的字段是否要和营销目标挂钩
    const fieldsConfig = [{
        group: 'creativeSegmentsGroup',
        fields: [
            'programTitles', 'programDescs', 'segmentBinds'
        ],
        use: [TabsGroup]
    }];
    let remoteConfig = {};
    try {
        const loader = remoteConfigLoaders[marketingTargetId];
        if (loader) {
            remoteConfig = await loader();
        }
    }
    catch (e) {
        console.error(`(marketingTargetId: ${marketingTargetId})是否存在配置文件`);
    }

    const fields = fieldsConfig.map(
        field => convertFieldToConfig({field, baseFields, remoteConfig, baseGroups, props: {
            campaignInfo,
            adgroupInfo,
            creativeCommonFormData,
            hideGenerateButton: (isEdit || isInQinggeIframe)
        }})
    );

    return {
        config: {
            fields
        },
        initialData: initialData ?? {
            programTitles: [{title: ''}],
            programDescs: [{desc: ''}],
            segmentBinds: {
                segments: [],
                videos: []
            }
        }
    };
}


const titleNameMap = {
    programTitles: '创意标题',
    programDescs: '创意描述',
    segmentBinds: '创意素材'
};

function TitleRenderer({field, errorsMapByField, isShowVideo}) {
    const name = titleNameMap[field];
    const {errors} = errorsMapByField[field] || {};
    const errorIcon = errors?.length ? (
        <Popover
            content={
                errors.map((i, idx) => (
                    // eslint-disable-next-line react/no-array-index-key
                    <div key={idx}>{i}</div>
                ))
            }
        >
            <IconExclamationTriangleSolid className="programmatic-segments-title-error-icon" />
        </Popover>
    ) : null;

    return (
        <div className="programmatic-segments-title">
            {name}
            <FieldCounter field={field} isShowVideo={isShowVideo} />
            {errorIcon}
        </div>
    );
}


function FieldCounter({field, isShowVideo}) {

    const {formData} = useFormContext();
    const [value] = useFieldValue(formData, field);

    if (field === 'segmentBinds') {
        const {segments = [], videos = []} = value;
        return (
            <CounterForSegments
                className="programmatic-segments-counter"
                picCount={segments.length}
                videoCount={videos.length}
                isShowVideo={isShowVideo}
            />
        );
    }

    return (
        <Counter
            className="programmatic-segments-counter"
            count={value.length}
            max={MAX_PROGRAM_COUNT}
        />
    );
}
