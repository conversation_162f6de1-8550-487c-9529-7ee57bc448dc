import {forwardRef, useEffect, useMemo, memo} from 'react';
import {useResource} from 'commonLibs/magicSuspenseBoundary';
import {noop} from 'lodash-es';
import {useFieldWatch, useFormDefinition} from '@baidu/react-formulator';
import {initialProgrammaticCreativeForm} from './config';
import {useCreativeSharedInfo} from '../context';

export const ProgrammaticCreative = memo(forwardRef(ProgrammaticCreative_));
function ProgrammaticCreative_({mountInstance, creativeCommonFormData, initialData: initialData_,
    watchedFields = [], onWatchedFieldsChange = noop, isEdit
}) {
    const {campaignInfo, adgroupInfo} = useCreativeSharedInfo();
    const [{config, initialData}] = useResource(
        initialProgrammaticCreativeForm,
        {campaignInfo, adgroupInfo, creativeCommonFormData, initialData: initialData_, isEdit}
    );
    const {
        Form,
        formData,
        validateFields,
        setFieldsError,
        setFieldsValue,
        getFieldError,
        verification: {
            results: verificationResults
        },
        getConfigByField,
        scrollToField
    } = useFormDefinition(config, initialData);

    const methods = useMemo(
        () => ({validateFields, setFieldsError, setFieldsValue, getFieldError, getConfigByField, scrollToField}),
        [validateFields, setFieldsError, setFieldsValue, getFieldError, getConfigByField, scrollToField]
    );
    useEffect(
        () => {
            if (mountInstance) {
                mountInstance({formData, verificationResults, methods});
            }
        },
        [formData, verificationResults, methods]
    );

    const getFieldsValue_ = () => {
        return formData.$toRaw();
    };
    // changedFields可能是['programTitles.0.title']
    useFieldWatch(formData, '*', changedFields => {
        const hasChange = changedFields.some(field => {
            return watchedFields.find(watchedField => field.startsWith(watchedField));
        });
        if (hasChange) {
            onWatchedFieldsChange(getFieldsValue_());
        }
    });

    return (
        <Form className="use-rf-preset-form-ui">
            {/* <Debug /> */}
        </Form>
    );
}
