import {useCallback, useMemo} from 'react';
import {useBoolean} from 'huse';
import {partial, compact} from 'lodash-es';
import sendMonitor from 'commonLibs/utils/sendHm';
import {Button, Checkbox, Toast} from '@baidu/one-ui';
import {useResource, withBoundary} from 'commonLibs/magicSuspenseBoundary';
import {CREATIVE_IMAGE_MAP} from 'commonLibs/components/creativePreview/previewMap';
import {imageTypesEnum} from 'commonLibs/config/image/imageType';
import {BindSource} from 'commonLibs/config/segment';
import {useSelectableArray, MODE as RecommendCreativesSelectMode} from 'commonLibs/hooks/collection/array';
import {fetchRecommendCreatives} from 'app/containers/fcNew/apis/creative';
import {getImageSign} from 'commonLibs/utils/handleCCSDK';
import {getImageAddFrom} from 'commonLibs/utils/getAddFrom';
import YiyanLoading from 'app/resource/svg/yiyan_loading.svg';
import BotIcon from 'app/resource/svg/bot.svg';
import './style.less';

const getKey = record => record.recommendId;
const MobilePicture = CREATIVE_IMAGE_MAP[imageTypesEnum.BASE_CREATIVE_WISE];
function RecommendCreatives_({
    mode = RecommendCreativesSelectMode.MULTIPLE,
    hasCheckAllAdgroup,
    campaignIds,
    imageTypes,
    adgroupIds,
    displayUrl,
    portraitPicUrl,
    onAccept
}) {
    const [
        {creativeList: creativeList_, pictures}
    ] = useResource(fetchRecommendCreatives, {isCheckAll: hasCheckAllAdgroup, campaignIds, imageTypes, adgroupIds});
    const [visible, {toggle}] = useBoolean(false);
    const [
        creativeList,
        {isCheckAll, indeterminate, selectedCount},
        {onSelect, onSelectAll, onResetSelect}
    ] = useSelectableArray(creativeList_, {getKey, mode});

    const segments = useMemo(
        () => pictures.map((picture, index) => {
            const {picUrl, desc, rawPicUrl, segmentType, source} = picture;
            const bindId = index + 1;
            return {
                source,
                picUrl,
                desc: (desc && desc.split('^')) || [],
                rawPicUrl,
                segmentType,
                bindSource: BindSource.USER,
                bindId,
                addfrom: getImageAddFrom(),
                segmentId: bindId
            };
        }),
        [pictures]
    );

    const acceptCreatives = useCallback(
        async () => {
            const creatives = creativeList.filter(item => item.selected);
            await onAccept(creatives, segments);
            onResetSelect();
        },
        [creativeList, segments, onAccept, onResetSelect]
    );

    if (!creativeList.length) {
        return null;
    }

    sendMonitor('click', {
        level: 'creative',
        item: '创意智能生成-展现',
        params: adgroupIds
    });

    return (
        <div className="recommend-creatives">
            <Header
                mode={mode}
                visible={visible}
                toggleVisible={toggle}
                totalCount={creativeList.length}
                isCheckAll={isCheckAll}
                isCheckPartial={indeterminate}
                onSelectAll={onSelectAll}
            />
            {
                visible && (
                    <Content
                        creativeList={creativeList}
                        onSelect={onSelect}
                        displayUrl={displayUrl}
                        portraitPicUrl={portraitPicUrl}
                        selectedCount={selectedCount}
                        acceptCreatives={acceptCreatives}
                    />
                )
            }
        </div>
    );
}

function Header({mode, visible, toggleVisible, totalCount, isCheckAll, isCheckPartial, onSelectAll}) {
    return (
        <div className="header">
            <div>
                <img src={BotIcon} />
                <span>创意智能生成</span>
            </div>
            <div>
                {
                    mode === RecommendCreativesSelectMode.MULTIPLE && visible && (
                        <Checkbox
                            checked={isCheckAll}
                            indeterminate={isCheckPartial}
                            onChange={e => onSelectAll(e.target.checked)}
                        >
                            全选（{totalCount}）
                        </Checkbox>
                    )
                }
                {!visible && <span className="count">已生成{totalCount}条</span>}
                <Button size="small" type="text-strong" onClick={toggleVisible}>
                    {!visible ? '查看 >' : '收起'}
                </Button>
            </div>
        </div>
    );
}

function Content({creativeList, onSelect, displayUrl, portraitPicUrl, selectedCount, acceptCreatives}) {
    return (
        <div className="content">
            {creativeList.map(({selected, recommendId, ...data}) => {
                return (
                    <div
                        key={recommendId}
                        className={`card ${selected ? 'selected' : ''}`}
                        onClick={partial(onSelect, recommendId)}
                    >
                        <MobilePicture {...data} displayUrl={displayUrl} portraitPicUrl={portraitPicUrl} />
                    </div>
                );
            })}
            {
                selectedCount > 0 && (
                    <div className="accept-area">
                        <Button className="accept-btn" type="strong" onClick={acceptCreatives}>
                            <div className="accept-btn-text">
                                <img src={YiyanLoading} />
                                采纳创意（{selectedCount}）
                            </div>
                        </Button>
                    </div>
                )
            }
        </div>
    );
}

export const RecommendCreatives = withBoundary({
    pendingFallback() {
        return <div />;
    },
    renderError() {
        return <div />;
    }
})(RecommendCreatives_);

export const getProgrammaticCreativeAcceptFn = (programmaticCreativeInstance, {maxCount}) => (creatives, segments) => {
    const {formData, methods: {setFieldsValue}} = programmaticCreativeInstance;
    const {programTitles, programDescs, segmentBinds} = formData.$toRaw();
    // 创意推荐填充时，覆盖标题描述为空的
    const pureProgramTitles = programTitles.filter(item => !!item.title);
    const pureProgramDescs = programDescs.filter(item => !!item.desc);
    const {segments: originSegments} = segmentBinds;
    const titleQuota = maxCount - pureProgramTitles.length;
    const descQuota = maxCount - pureProgramDescs.length;
    const recommendCount = creatives.length;
    if (titleQuota <= 0 && descQuota <= 0) {
        Toast.error({content: '添加失败，创意标题和创意描述数量已达上限，请删除后再次添加。'});
        return;
    }
    if (titleQuota >= recommendCount && descQuota >= recommendCount) {
        setFieldsValue({
            programTitles: pureProgramTitles.concat(creatives.map(({title}) => ({title}))),
            programDescs: pureProgramDescs.concat(creatives.map(({description1}) => ({desc: description1})))
        });
        Toast.success({content: '成功添加当前创意文案与配图'});
    }
    else {
        setFieldsValue({
            programTitles: pureProgramTitles.concat(creatives.slice(0, titleQuota).map(({title}) => ({title}))),
            programDescs: pureProgramDescs.concat(
                creatives.slice(0, descQuota).map(({description1}) => ({desc: description1})))
        });
        Toast.warning({
            content: '部分添加成功，'
                + `${compact([
                    titleQuota < recommendCount ? '创意标题' : '',
                    descQuota < recommendCount ? '创意描述' : ''
                ]).join('和')}`
                + '数量已达上限，请删除后再次添加。'
        });
    }
    const segmentsToAdd = segments.filter(
        segment => !originSegments.some(origin => getImageSign(origin.picUrl) === getImageSign(segment.picUrl))
    );
    // 图片为推荐共用，需要对图片做去重处理
    if (segmentsToAdd.length) {
        setFieldsValue({segmentBinds: {...segmentBinds, segments: originSegments.concat(segmentsToAdd)}});
    }
    if (segmentsToAdd.length !== segments.length) {
        Toast.warning({content: '已为您去掉重复添加的图片', duration: 3});
    }
};

export const getCustomizeCreativeAcceptFn = ({$trigger, customizeCreativeInstance}, {maxCount}) => creatives => {
    const quota = maxCount - customizeCreativeInstance.formData.$get('creativeTexts').length;
    if (quota <= 0) {
        Toast.error({content: `添加失败，该单元的创意文案最多添加${maxCount}个，目前已达到上限，如需要新增，请删除创意文案后再添加。`});
        return;
    }
    else if (quota >= creatives.length) {
        $trigger('addCustomizeCreative', creatives);
        Toast.success({content: '成功添加当前创意文案与配图'});
    }
    else {
        $trigger('addCustomizeCreative', creatives.slice(0, quota));
        Toast.warning({content: `部分添加成功，该单元的创意文案最多添加${maxCount}个，目前已达到上限，如需要新增，请删除创意文案后再添加。`});
    }
};
