.recommend-creatives {
    background: linear-gradient(180deg, #f6faff 0%, rgba(242, 247, 255, 0.60) 100%);
    margin-bottom: 16px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    padding-bottom: 12px;
    .header {
        padding: 8px;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        background-image: url("app/resource/svg/recommond_creative_bg1.svg");
        display: flex;
        justify-content: space-between;
        font-weight: 500;
        font-size: 14px;
        color: @dls-color-gray-9;
        img {
            margin-right: 4px;
        }
        & > div {
            display: flex;
            align-items: center;
        }
        .count {
            color: #999;
            font-size: 12px;
            padding: 0 12px;
        }
    }
    .content {
        padding: 0 12px;
        max-height: 428px;
        overflow-y: auto;
        width: 308px;
        .card {
            position: relative;
            margin: 12px 0;
            border: 1px solid transparent;
            cursor: pointer;
            border-radius: 4px;
            &:hover {
                border: 1px solid #0054e6;
            }
            .brave-the-winds-common-libs-creative-preview-mobile {
                width: 272px;
            }
        }
        .card.selected {
            border: 1px solid #0054e6;
        }
        .card.selected::before {
            content: "";
            z-index: 1;
            position: absolute;
            top: 2px;
            left: 2px;
            width: 0;
            height: 0;
            border-top: 4px solid #0054e6;
            border-right: 4px solid transparent;
            border-bottom: 4px solid transparent;
            border-left: 4px solid #0054e6;
        }
    }
    .accept-area {
        position: sticky;
        bottom: 0;
        background: linear-gradient(0deg, #f6faff 0%, rgba(247, 250, 255, 0.00) 100%);
        padding: 12px 24px 16px;
        .accept-btn {
            width: 100%;
            &-text {
                display: flex;
                align-items: center;
                img {
                    margin-right: 4px;
                }
            }
        }
    }
}