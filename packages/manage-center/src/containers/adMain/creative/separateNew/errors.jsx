import {useState, useMemo, useCallback, useEffect, useImperativeHandle, forwardRef} from 'react';
import {isEmpty, partial} from 'lodash-es';
import {useBoolean, useForceUpdate} from 'huse';
import {Popover, Button} from '@baidu/one-ui';
import {IconExclamationTriangleSolid} from 'dls-icons-react';
import {createReactiveData, useFormErrors} from '@baidu/react-formulator';
import {creativeTypeEnum} from 'commonLibs/config/creativeText';

export function useLiveList(reactiveList) {
    const [list, setList] = useState(reactiveList.map(reactiveData => reactiveData.$toRaw()));
    const forceUpdate = useForceUpdate();
    const onUpdate = useCallback((index, value, field, fullData) => {
        setList(list => list.slice(0, index).concat(fullData.$toRaw()).concat(list.slice(index + 1)));
        forceUpdate();
    }, [setList, forceUpdate]);
    useEffect(
        () => {
            setList(reactiveList.map(reactiveData => reactiveData.$toRaw()));
            reactiveList.map((reactiveData, index) => reactiveData.$watch('*', partial(onUpdate, index)));
        },
        [reactiveList, onUpdate]
    );
    return list;
}

function ErrorTipContent({errors, type, methods, $trigger}) {
    const onAnchorClick = (field, tabIndex) => {
        if (type === creativeTypeEnum.customize && tabIndex != null) {
            $trigger('changeCustomizeCreativeTabIndex', tabIndex);
        }
        const {scrollToField} = methods;
        scrollToField(field);
    };
    return (
        <div className='creative-new-error-tip-content'>
            <div className='title'>存在以下错误，请修改后提交</div>
            {
                Object.keys(errors).map(key => {
                    const [field, index] = key.split('.');
                    const {getConfigByField} = methods;
                    const {name: fieldName} = getConfigByField(field);
                    return (
                        <div key={key}>
                            <Button type="text-aux" size="medium" onClick={partial(onAnchorClick, key, index)}>
                                <span>{fieldName}{index != null && Number(index) + 1}: </span>
                                <span>{errors[key]}</span>
                            </Button>
                        </div>
                    );
                })
            }
        </div>
    );
}

export function transformErrors(rawResults) {
    return Object.entries(rawResults).reduce((acc, [field, triggerMap]) => {
        if (!triggerMap) {
            return acc;
        }
        const errors = Object
            .values(triggerMap).flat(2)
            // 根据ignorePending移除pending的结果， 并去重
            .reduce((errs, i) => {
                if ((i instanceof Promise) || errs.includes(i) || !i) {
                    return errs;
                }
                return [...errs, i];
            }, []);
        if (errors.length) {
            acc[field] = errors;
        }
        return acc;
    }, {});
}

export const ErrorsTip = forwardRef(ErrorsTip_);

function ErrorsTip_({creativeFormInstance, $trigger}, ref) {
    const {verificationResults} = creativeFormInstance;
    const defaultReactiveData = useMemo(() => createReactiveData({}), []);
    const errors = useFormErrors(verificationResults || defaultReactiveData);

    const [visible, {toggle, on}] = useBoolean();
    useImperativeHandle(ref, () => ({showErrorTip: on}));

    if (isEmpty(errors)) {
        return null;
    }
    return (
        <Popover
            visible={visible}
            placement="topRight"
            content={
                <ErrorTipContent errors={errors} $trigger={$trigger} {...creativeFormInstance} />
            }
            trigger="click"
            onVisibleChange={toggle}
            getPopupContainer={node => node.parentNode}
        >
            <div className="errors-tip">
                <IconExclamationTriangleSolid />
                <span>创意存在错误，请修改后提交</span>
            </div>
        </Popover>
    );
}
