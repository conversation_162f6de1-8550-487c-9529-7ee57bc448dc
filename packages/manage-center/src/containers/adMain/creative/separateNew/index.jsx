/*
 * @file: 单独新建创意-用于在一个或多个单元下新建
 * <AUTHOR>
 * @date Tue Jul 11 2023
 */

import {useMemo, useState, useCallback, useEffect, useRef} from 'react';
import {groupBy, isEmpty, pick, isNil, get} from 'lodash-es';
import {v4 as uuidv4} from 'uuid';
import {IconEdit, IconEye} from 'dls-icons-react';
import {Layout, Dialog, Button} from '@baidu/one-ui';
import {ProcessSteps} from '@baidu/one-ui-pro';
import SaveFooter from 'commonLibs/SaveFooter';
import {isCannotUseAgentUrlRegUser} from 'commonLibs/utils/getFlag';
import {isAgentUrlType} from 'commonLibs/components/commonEntry';
import idType from 'commonLibs/config/idType';
import {promisifyConfirm} from 'commonLibs/hooks/dialog';
import {useControl} from 'commonLibs/hooks/externalControl';
import {useRouterFormatter, useRouterGoBack, useURLQuery} from 'commonLibs/route';
import {createReactiveData} from '@baidu/react-formulator';
import {creativeTypeEnum} from 'commonLibs/config/creativeText';
import {useEventListener} from 'commonLibs/hooks/materialList/events';
import {useAccountInfo} from 'commonLibs/context/sharedInfo';
import {displayPartialError, createError} from 'commonLibs/utils/materialList/error';
import {getSingleErrorMessage} from 'commonLibs/utils/getErrorMessage';
import {sendCreativeCopilotMonitor, sendHelperMonitor, sendNewHelperMonitorPro} from 'commonLibs/utils/copilotMonitor';
import {advicesTypeMap} from 'commonLibs/config/monitor-pro/atom/advices';
import {newHelperTemplate} from 'commonLibs/config/monitor-pro/template/new-helper-template';
import {
    actionTypeMap,
    featureNameMap,
    sendNewDiagnosisCheckMonitor,
    universalMonitorKeyMap
} from 'commonLibs/config/monitor-pro';
import {stepConfig} from 'app/containers/fcNew/creative/separateNew/config';
import TopAlert from 'app/containers/fcNew/creative/topAlert';
import {getCPQLAlertKey} from 'app/containers/fcNew/creative/topAlert/config';
import {getImageTypes} from 'app/containers/fcNew/creative/utils/image';
import {submitCreativesAndVideos} from 'app/containers/fcNew/apis/creative';
import {formatCreativeErrors, validateSegmentsError} from 'app/containers/fcNew/creative/utils/error';
import {generateAdgroupSegmentBinds} from 'app/containers/fcNew/creative/processNew/util';
import {getAddVideoParams} from 'app/containers/fcNew/creative/separateNew/util';
import {CreativesWithAichat} from '../Creatives';
import {remoteConfigLoaders} from '../customizeCreative/config';
import {mapProgramFieldToInnerField} from '../programmaticCreative/config';
import {CreativeSharedInfoProvider} from '../context';
import {ErrorsTip} from './errors';
import './style.less';
import {ConfirmContent, getColumns} from 'commonLibs/editorPanel/batchErrorConfirm';
import {useBotContextInfo} from 'commonLibs/context/botInfo';
import {toPithySteps} from 'app/containers/fcNew/creative/utils';
import {getNewProcessCreativeData} from '../utils/aichatCreative';
import {useInitAichatCreativeV2, useOpenDigitalHumanComposer} from 'app/hooks/useAichatCreative';
import {helperMonitorDeepSource} from '../../../../containers/project/fcNewHelper/config';
import sendMonitor, {
    CreativeRecommendTarget, sendCreativeRecommendMonitor, PagePathType
} from 'commonLibs/utils/sendHm';
import {adviceAdoptSource} from 'commonApps/optimizeCenter/fcDetailCards/config';
import {tongjiMaxNum} from 'app/containers/fcNew/creative/config';
import {useAdgroupBindAppInfo} from 'app/hooks/useAdgroupBindAppInfo';
import {isInQinggeIframe} from 'commonLibs/utils/isInIframe';
import {postMessageFunc, PostMessageEventType, PostMessageScene} from 'commonLibs/utils/postMessage';
import {useFormulatorLogMethods} from 'commonLibs/hooks/funnelLog';

const {Sidebar, Content, Header, Footer} = Layout;

const slots = {
    // recommend: RecommendCreatives,
    titleAlert: TopAlert
};

const newHelperAddFrom = 20;
const newHelperAdviceTemplateMap = {
    '文案': newHelperTemplate[advicesTypeMap.textNewIdea],
    '图片': newHelperTemplate[advicesTypeMap.imageNewIdea]
};
const newDiagosisSourceList = [adviceAdoptSource.consumeReduce, adviceAdoptSource.overCharge];
const newDiagosisTab = {
    [adviceAdoptSource.consumeReduce]: '消费突降',
    [adviceAdoptSource.overCharge]: '超成本'
};

export const SeparateNewCreative = ({
    levelInfo: {
        marketingTargetId,
        isCheckAll,
        adgroupList,
        campaignList,
        campaignMap,
        isAdgroupLevel = false
    },
    extraInfo: {displayUrl = '', portraitPicUrl = ''} = {},
    prevStep,
    accountVideoSegmentCount = 0,
    adgroupsBindVideoSegmentCount = 0,
    isMultiAdgroup = false
}) => {
    const [query] = useURLQuery();
    const {registerDomain} = useAccountInfo();
    const operationId = useRef(uuidv4());
    const [isPreview, setIsPreview] = useState(false); // 创意预览是否展示
    const {visible: botVisible} = useBotContextInfo();
    // 屏幕过大时，右侧预览区显示在右侧
    const isShowPreviewOnRightSection = document.body.clientWidth >= 1920 || !botVisible;
    const headerSummary = useMemo(() => {
        // 这里需要遍历出计划因为在非全选是campianIds是空的
        const campaigns = adgroupList.reduce((total, adgroup) => {
            if (total.indexOf((adgroup?.campaignId)) > -1) {
                return total;
            }
            return [...total, +adgroup.campaignId];
        }, []);
        const firstUnit = adgroupList[0];
        const firstCampaignId = campaigns.find(id => id === firstUnit.campaignId);
        const summary = !isCheckAll && firstUnit
            ? `已选择${campaignMap[firstCampaignId].campaignName}等${campaigns?.length}个计划；
        ${firstUnit.adgroupName}等${adgroupList?.length || 0}个单元`
            : `已选择全部${campaigns?.length || 0}个计划下的全部单元`;
        return summary;
    }, [adgroupList, campaignMap, isCheckAll]);

    const {data: adgroupBindInfo} = useAdgroupBindAppInfo(adgroupList);
    // 创意推荐展开收起
    const alertKey = getCPQLAlertKey({
        marketingTargetId: adgroupList?.[0]?.marketingTargetId,
        structuredContentIds: adgroupList[0].structuredContentIds,
        isProcessNew: true,
        isShowProgramCreativeTip: true,
        adgroupInfo: adgroupBindInfo?.[0]
    });
    const {imageTypes, adgroupIds} = useMemo(
        () => ({
            imageTypes: getImageTypes(adgroupList),
            adgroupIds: adgroupList.map(adgroup => adgroup.adgroupId)
        }),
        [adgroupList]
    );
    const campaignIds = useMemo(
        () => campaignList.map(campaign => campaign.campaignId),
        [campaignList]
    );
    const slotProps = useMemo(
        () => ({
            imageTypes,
            adgroupIds,
            campaignIds,
            hasCheckAllAdgroup: isCheckAll,
            displayUrl,
            portraitPicUrl,
            slots
        }),
        [imageTypes, adgroupIds, campaignIds, isCheckAll, displayUrl, portraitPicUrl]
    );

    const [
        {programmaticCreativeInstance, customizeCreativeInstance},
        {mountProgrammaticCreativeInstance, mountCustomizeCreativeInstance}
    ] = useCreativeInstances();
    const reactiveCreativeInfo = useMemo(
        () => createReactiveData({
            creativeType: creativeTypeEnum.programmatic,
            isMultiAdgroup,
            segmentRecommendStatus: !isMultiAdgroup && adgroupList[0].segmentRecommendStatus,
            creativeTextOptimizationStatus: !isMultiAdgroup && adgroupList[0].creativeTextOptimizationStatus,
            isInAdgroupProcess: false,
            isShowCreativeForm: true
        }),
        [adgroupList[0]]
    );
    const [creativeType, setCreativeType] = useState(creativeTypeEnum.programmatic);
    const creativeFormInstance = useMemo(
        () => (
            creativeType === creativeTypeEnum.programmatic ? programmaticCreativeInstance : customizeCreativeInstance
        ),
        [creativeType, programmaticCreativeInstance, customizeCreativeInstance]
    );

    const getCreativeFormInstance = useCallback(
        () => {
            return creativeType === creativeTypeEnum.programmatic
                ? programmaticCreativeInstance : customizeCreativeInstance;
        },
        [creativeType, programmaticCreativeInstance, customizeCreativeInstance]
    );

    const defaultCreativeList = useRouterFormatter('@dashboard/creatives')();
    const backToManageList = useRouterGoBack(defaultCreativeList);
    const [CreativeErrorsTip, {showErrorTip}] = useControl(ErrorsTip);

    const {
        logOnSubmitClick,
        logOnSubmitSuccess,
        logOnCancelClick,
        logOnAreaClick
    } = useFormulatorLogMethods({
        shouldSendChangeLog: true,
        formData: getCreativeFormInstance().formData,
        source: 'SperateCreativeForm',
        config: {
            commonTrackParams: {
                marketingtargetid: marketingTargetId
            }
        },
        getFieldError: getCreativeFormInstance().methods?.getFieldError
    });

    const onSave = async () => {
        logOnSubmitClick();
        const creativeFormInstance = getCreativeFormInstance();
        showErrorTip();
        const {
            creativeTexts,
            segmentBinds,
            programTitles,
            programDescs
        } = await creativeFormInstance.methods.validateFields();
        const creativeTypeAndSegmentBindParams = await generateParams(
            {isCheckAll, adgroupList, campaignIds},
            {registerDomain, ...adgroupList[0]},
            {
                creativeTypeValues: creativeTexts ? creativeTexts : [{programTitles, programDescs}],
                segments: segmentBinds.segments,
                videos: segmentBinds.videos
            }
        );
        const videoSegmentBindParams = getAddVideoParams({
            adgroupList,
            activeAdgroupItem: {bindInfo: {videos: segmentBinds.videos}},
            campaignIds,
            isCheckAll
        });
        const label = isEmpty(programTitles) ? '自定义创意' : '自适应创意';
        sendMonitor('click', {
            level: 'creative',
            item: 'submit-creative',
            marketingtargetid: marketingTargetId,
            label
        });
        if (
            !isEmpty(get(creativeTypeAndSegmentBindParams, 'recommendVideos'))
            || !isEmpty(get(creativeTypeAndSegmentBindParams, 'recommendImageMap'))
            || !isEmpty(get(creativeTypeAndSegmentBindParams, 'recommendTextIds'))
        ) {
            sendCreativeCopilotMonitor({
                value: '创意素材采纳并提交',
                // eslint-disable-next-line max-len
                ideaType: isEmpty(programTitles) ? creativeTypeEnum.customize : creativeTypeEnum.programmatic
            });
        }
        try {
            await submitCreativesAndVideos(creativeTypeAndSegmentBindParams, videoSegmentBindParams);
            logOnSubmitSuccess();
            sendCreativeRecommendMonitor({
                target: CreativeRecommendTarget.submit_creative_recommend,
                id: operationId.current,
                source: PagePathType.createCreative,
                totaltime: creativeTypeAndSegmentBindParams?.recommendTextIds?.length || 0,
                count: Object.keys(creativeTypeAndSegmentBindParams?.recommendImageMap || {})?.length || 0,
                onecount: creativeTypeAndSegmentBindParams?.recommendVideos?.length || 0
            });
            if (query?.from === 'newHelper') {
                const newHelperAdviceTemplate = newHelperAdviceTemplateMap[query?.type] || [];
                sendHelperMonitor({
                    label: '项目诊断-潜力提升', value: '竞争胜出率', item: `点击竞争力-${query?.type}`,
                    deepSource: helperMonitorDeepSource, level: '质量分析（新建创意）', info: '深度'
                });
                sendNewHelperMonitorPro({
                    ...newHelperAdviceTemplate, actionType: actionTypeMap.adviceAccept,
                    info: '深度', account: query?.account, deepSource: helperMonitorDeepSource,
                    projectId: query?.projectId
                });
            }
            if (newDiagosisSourceList.includes(+query?.source)) {
                sendNewDiagnosisCheckMonitor({
                    [universalMonitorKeyMap.featureName]: featureNameMap['新场景化诊断能力建议'],
                    [universalMonitorKeyMap.actionType]: actionTypeMap.adviceAccept,
                    [universalMonitorKeyMap.adviceType]: '新建创意',
                    [universalMonitorKeyMap.projectId]: +query?.projectId,
                    [universalMonitorKeyMap.tabName]: newDiagosisTab[+query.source],
                    [universalMonitorKeyMap.deepSource]: +query.source
                });
            }
            if (isInQinggeIframe) {
                postMessageFunc({
                    scene: PostMessageScene.ADD_CREATIVE,
                    eventType: PostMessageEventType.ON_SAVE_SUCCESS
                });
                return;
            }
            backToManageList();
        }
        catch ([errors, videoError]) {
            const {creativeErrors, status, creativeErrorsMap, ...segmentBindError} = formatCreativeErrors(errors);
            const alertError = [];
            // 处理创意文案报错
            const formattedCretiveTypeError = formatCretiveTypeError(
                {creativeErrors, status},
                {creativeType, creativeFormInstance},
                {adgroupList}
            );
            if (formattedCretiveTypeError._normalized?.errors.length) {
                formattedCretiveTypeError.fillFormErrors();
            }
            alertError.push(...formattedCretiveTypeError.toAlert);

            const segmentErrorToAlert = validateSegmentsError(segmentBindError);
            const formattedVideoError = formatVideoError(videoError);
            alertError.push(...segmentErrorToAlert, ...formattedVideoError.toAlert);

            if (formattedVideoError._normalized?.type === 'partial') {
                displayPartialError(formattedVideoError);
            }
            else if (alertError.length || formattedCretiveTypeError.creativeIncompatibleError.length) {
                const errorSource = (formattedCretiveTypeError.creativeIncompatibleError || [])
                    .map(({adgroupName, adgroupId, message}) => ({
                        id: adgroupId,
                        materialName: adgroupName,
                        errorMessage: message
                    }));

                const tableProps = {
                    errorSource,
                    materialName: '创意',
                    optName: '新建',
                    getColumns: () => getColumns({materialName: '单元'}),
                    resp: formattedCretiveTypeError._normalized
                };

                const content = (
                    <div>
                        <ConfirmContent {...tableProps} />
                        {
                            alertError.map(err => (<div key={err.code}>{err.message}</div>))
                        }
                    </div>
                );
                sendMonitor('new', {
                    level: 'creative',
                    resp: 'fail',
                    type: 'new',
                    label,
                    item: errorSource.map(item => item.errorMessage).join('-').slice(0, tongjiMaxNum) || ''
                });

                Dialog.alert({
                    title: '新建创意失败',
                    content,
                    width: 'auto',
                    onOk() {
                        if (
                            // 类型不匹配时，返回创意列表
                            // todo 把视频的部分错误也放到这里
                            formattedCretiveTypeError._normalized.status === 1
                                || formattedCretiveTypeError.isAllIncompatible
                        ) {
                            if (isInQinggeIframe) {
                                postMessageFunc({
                                    scene: PostMessageScene.ADD_CREATIVE,
                                    eventType: PostMessageEventType.ON_CANCEL
                                });
                                return;
                            }
                            backToManageList();
                        }
                    }
                });
            }
            else if (
                creativeErrors.length === 0
                && Object.keys(segmentBindError).every(key => isEmpty(segmentBindError[key]))
            ) {
                Dialog.alert({title: '新建创意失败', content: getSingleErrorMessage(errors?.errors[0])});
            }
        }
    };

    const onCancel = async () => {
        logOnCancelClick();
        const remain = await promisifyConfirm({
            title: '提示',
            content: '当前还有未保存的创意，关闭将直接放弃已填写内容',
            okText: '留在此页',
            cancelText: '关闭此页'
        });
        if (!remain) {
            if (isInQinggeIframe) {
                postMessageFunc({
                    scene: PostMessageScene.ADD_CREATIVE,
                    eventType: PostMessageEventType.ON_CANCEL
                });
                return;
            }
            backToManageList();
        }
    };
    const [on, , , triggerSync] = useEventListener();
    const $on = useCallback((eventName, callback) => on(eventName, callback, {singleCallback: true}), []);
    const $trigger = useCallback((...args) => triggerSync(...args)[0], []);

    const isDisabledAgentReg = useMemo(
        () => !isMultiAdgroup && isCannotUseAgentUrlRegUser() && isAgentUrlType(adgroupList[0]?.adgroupUrlType),
        [adgroupList, isMultiAdgroup]
    ); // 落地页链接是否禁选商家智能体类型Url
    const contextValue = useMemo(
        () => ({
            isDisabledAgentReg,
            campaignInfo: adgroupList[0],
            adgroupInfo: {
                adgroups: adgroupList,
                accountVideoSegmentCount,
                adgroupsBindVideoSegmentCount,
                isCheckAll,
                catalogId: adgroupList[0]?.structuredContentIdStrs,
                productCategoryType: adgroupList[0]?.productCategoryType
            },
            $on,
            $trigger,
            logOnAreaClick
        }),
        [isDisabledAgentReg, adgroupList, accountVideoSegmentCount, $on, $trigger,
            adgroupsBindVideoSegmentCount, isCheckAll, logOnAreaClick]
    );

    const stepsProps = {
        current: 1,
        ...(
            botVisible ? toPithySteps(stepConfig) : stepConfig
        )
    };

    // 添加数字人视频
    const {
        getDigitalHumanVideo,
        onAddDigitalHumanVideo
    } = useOpenDigitalHumanComposer(creativeFormInstance?.formData);

    const onAichatSubmit = useCallback(
        data => {
            const {creativeType: ideaType} = data;

            const currentReactiveFormData = ideaType === creativeTypeEnum.programmatic
                ? programmaticCreativeInstance?.formData
                : customizeCreativeInstance?.formData;

            const newFormData = getNewProcessCreativeData({
                submitData: data,
                originData: currentReactiveFormData,
                currentCreative: (customizeCreativeInstance?.formData.creativeTexts || [])[
                    customizeCreativeInstance?.formData.tabIndex
                ]
            });

            const {segmentBinds} = newFormData;
            currentReactiveFormData.segmentBinds = segmentBinds;
            if (ideaType === creativeTypeEnum.customize) {
                const {tabIndex, creativeTexts} = newFormData;
                currentReactiveFormData.tabIndex = tabIndex;
                currentReactiveFormData.creativeTexts = creativeTexts;
            }
            else {
                const {programTitles, programDescs} = newFormData;
                currentReactiveFormData.programTitles = programTitles;
                currentReactiveFormData.programDescs = programDescs;
            }
        },
        [customizeCreativeInstance?.formData, programmaticCreativeInstance?.formData]
    );
    const adgroupListForAichatCreative = useMemo(
        () => {
            if (isEmpty(adgroupList)) {
                return [];
            }
            return adgroupList.map(item => {
                if (item.adgroupId) {
                    return pick(item, ['adgroupId', 'campaignId']);
                }
                return {
                    landingPageUrl: item.mobileFinalUrl,
                    campaignId: item.campaignId
                };
            });
        },
        [adgroupList]
    );

    const onCreativeDataChange = useCallback(
        ({creativeType}) => setCreativeType(creativeType),
        [setCreativeType]
    );
    useInitAichatCreativeV2({
        creativeType, marketingTargetId,
        getDigitalHumanVideo, onAichatSubmit, onAddDigitalHumanVideo,
        adgroupInfo: adgroupListForAichatCreative?.[0],
        adgroupsList: adgroupListForAichatCreative,
        campaignList: adgroupList,
        operationId: operationId.current,
        pagePathType: PagePathType.createCreative
    });


    useEffect(() => {
        sendCreativeRecommendMonitor({
            target: CreativeRecommendTarget.enter_creative,
            id: operationId.current,
            source: PagePathType.createCreative
        });
    }, []);

    return (
        <Layout className="ad-main-separate-new-creative">
            <Sidebar sticky className="ad-main-separate-new-creative-sidebar">
                <ProcessSteps {...stepsProps} />
            </Sidebar>
            <Layout>
                <Header sticky>
                    <div className="separate-creative-header">
                        <span className="summary">{headerSummary}</span>
                        {!isAdgroupLevel && <span className="update-adgroup" onClick={prevStep}>修改</span>}
                    </div>
                </Header>
                <Content>
                    <CreativeSharedInfoProvider value={contextValue}>
                        <CreativesWithAichat
                            initialCreativeData={reactiveCreativeInfo}
                            programmaticCreativeInstance={programmaticCreativeInstance}
                            mountProgrammaticCreativeInstance={mountProgrammaticCreativeInstance}
                            mountCustomizeCreativeInstance={mountCustomizeCreativeInstance}
                            customizeCreativeInstance={customizeCreativeInstance}
                            slotProps={slotProps}
                            alertKey={alertKey}
                            displayUrl={displayUrl}
                            portraitPicUrl={portraitPicUrl}
                            isShowPreviewOnRightSection={isShowPreviewOnRightSection}
                            isShowPreviewOnMainSection={isPreview && !isShowPreviewOnRightSection}
                            onCreativeDataChange={onCreativeDataChange}
                        />
                    </CreativeSharedInfoProvider>
                </Content>
                <Footer sticky className="ad-main-separate-new-creative-footer">
                    <SaveFooter onSave={onSave} onCancel={onCancel} saveLabel="保存并关闭" />
                    <div>
                        <CreativeErrorsTip creativeFormInstance={creativeFormInstance} $trigger={$trigger} />
                        {
                            isShowPreviewOnRightSection ? null : (
                                <Button
                                    type="text-strong"
                                    className="extra-content-button"
                                    onClick={() => setIsPreview(p => !p)}
                                    icon={isPreview ? <IconEdit /> : <IconEye />}
                                >
                                    {isPreview ? '返回填写' : '查看预览'}
                                </Button>
                            )
                        }
                    </div>
                </Footer>
            </Layout>
        </Layout>
    );
};

function useCreativeInstances() {
    const [
        programmaticCreativeInstance,
        setProgrammaticCreativeInstance
    ] = useState({type: creativeTypeEnum.programmatic});
    const [customizeCreativeInstance, setCustomizeCreativeInstance] = useState({type: creativeTypeEnum.customize});
    const mountProgrammaticCreativeInstance = useCallback(
        values => setProgrammaticCreativeInstance(instance => ({...instance, ...values})),
        [setProgrammaticCreativeInstance]
    );
    const mountCustomizeCreativeInstance = useCallback(
        values => setCustomizeCreativeInstance(instance => ({...instance, ...values})),
        [setCustomizeCreativeInstance]
    );
    return [
        {programmaticCreativeInstance, customizeCreativeInstance},
        {mountProgrammaticCreativeInstance, mountCustomizeCreativeInstance}
    ];
}

// TODO 先放在这里，我还没想好是否要这么拆解方法，等串联+单独+编辑都写好了再调整
function formatVideoError(videoError) {
    const videoErrors = videoError?.errors?.[0]?.errorInfos || videoError?.errors || [];
    const videoErrorMessagesToAlert = videoErrors.map(err => ({
        code: err.code, message: `视频：${getSingleErrorMessage(err)}`
    }));
    const formattedVideoError = createError(videoError);
    if (formattedVideoError._normalized?.type === 'partial') {
        formattedVideoError.materialName = '视频';
        formattedVideoError.getErrorInfos = errors => errors.map(e => e.errorInfos[0]);
        formattedVideoError.optName = '添加';
        formattedVideoError.errMapFn = ({message, code}) => ({
            id: '-',
            message: getSingleErrorMessage({code, message}),
            position: '-'
        });
    }
    formattedVideoError.toAlert = videoErrorMessagesToAlert;
    return formattedVideoError;
}

// 程序化、自定义创意新建时类型不一致的错误、直播场景的url设置错误，详情看errorCode文件
const INCOMPATIBLE_ERROR_CODE = [
    80006399,
    80006401,
    80006405,
    80006406,
    80006407
];

export function formatCretiveTypeError({creativeErrors = [], status}, {creativeFormInstance}, {adgroupList = [{}]}) {
    const formattedCretiveTypeError = createError({errors: creativeErrors, status});
    const {methods: {setFieldsError}, formData} = creativeFormInstance;
    const errorFieldsMap = {};
    const creativeTypeMessagesToAlert = [];
    const creativeIncompatibleError = [];
    const incompatibleCodeMap = new Map();
    const adgroupCount = adgroupList.length;

    const isProgrammatic = creativeFormInstance.type === creativeTypeEnum.programmatic;

    // 这里id 就是创意X单元  的顺序， 假设有2个创意3个单元
    // 那么 0 代表 创意1单元1， 1 代表 创意1单元2， 2 代表 创意2单元1 以此类推
    Object.entries(groupBy(creativeErrors, 'id')).forEach(([index, errors]) => {
        errors.forEach(error => {
            const {code} = error;
            const message = getSingleErrorMessage(error);
            const adgroupIndex = index % adgroupCount;
            const {adgroupName, adgroupId} = adgroupList[adgroupIndex] || {
                adgroupName: '-',
                adgroupId: '-'
            }; // 兼容新建时，没有adgroupList的情况
            if (
                INCOMPATIBLE_ERROR_CODE.includes(code)
                && !(incompatibleCodeMap.get(adgroupId) || []).includes(code)
            ) {
                creativeIncompatibleError.push({
                    code,
                    message,
                    adgroupName,
                    adgroupId
                });
                incompatibleCodeMap.set(adgroupId, [
                    ...(incompatibleCodeMap.get(adgroupId) || []),
                    code
                ]);
            }
        });
    });

    if (isProgrammatic) {
        (groupBy(creativeErrors, 'id')[0] || [])?.forEach(error => {
            const {field, code, position} = error;
            if (INCOMPATIBLE_ERROR_CODE.includes(code)) {
                return;
            }
            const message = getSingleErrorMessage(error);
            if (mapProgramFieldToInnerField[field] && position != null) {
                errorFieldsMap[`${field}.${position}.${mapProgramFieldToInnerField[field]}`] = message;
            }
            else {
                errorFieldsMap[field] = message;
            }
            creativeTypeMessagesToAlert.push({code, message: `自适应创意文案: ${message}`});
        });
    }
    else {
        const creativeTexts = formData.$get('creativeTexts');
        creativeTexts.forEach((_, index) => {
            const creativeTypeError = groupBy(creativeErrors, 'id')[index * adgroupCount];
            if (creativeTypeError) {
                creativeTypeError.forEach(error => {
                    const {field, code} = error;
                    if (INCOMPATIBLE_ERROR_CODE.includes(code)) {
                        return;
                    }
                    const message = getSingleErrorMessage(error);
                    errorFieldsMap[`creativeTexts.${index}.${field}`] = message;
                    creativeTypeMessagesToAlert.push({code, message: `创意文案${index + 1}: ${message}`});
                });
            }
        });
    }
    formattedCretiveTypeError.errorFieldsMap = errorFieldsMap;
    formattedCretiveTypeError.fillFormErrors = () => setFieldsError(errorFieldsMap);
    formattedCretiveTypeError.toAlert = creativeTypeMessagesToAlert;
    formattedCretiveTypeError.creativeIncompatibleError = creativeIncompatibleError;
    formattedCretiveTypeError.isAllIncompatible = incompatibleCodeMap.size === adgroupCount;

    return formattedCretiveTypeError;
}

function getRecommendImageData(segments = []) {
    return segments.reduce((accu, currentImage) => {
        if (currentImage.recommendId) {
            accu[currentImage.imageId] = currentImage.recommendId;
        }
        return accu;
    }, {});
}

function getRecommendVideoData(videos = []) {
    const recommendVideos = videos.filter(video => video.isDigitalHumanVideo);
    return recommendVideos.map(({taskId, recommendId, addFrom}) => {
        return {
            taskId,
            recommendId,
            addFrom
        };
    });
}

function isValidRecommendId(id) {
    return !isNil(id);
}

async function generateParams(
    {isCheckAll, adgroupList, campaignIds},
    adgroupInfo,
    {creativeTypeValues, segments, videos = []}
) {
    const {marketingTargetId} = adgroupInfo;
    const {generateCreativeParams} = await remoteConfigLoaders[marketingTargetId]();
    const creativeTypes = creativeTypeValues.map(
        value => generateCreativeParams(adgroupInfo, value).creativeType
    );

    const urlSearch = new URLSearchParams(window.location.search);
    const from = urlSearch.get('from');
    if (from === 'newHelper') {
        creativeTypes?.forEach(item => {
            item.addFrom = newHelperAddFrom;
        });
    }
    let recommendTextIds = [];
    creativeTypeValues.forEach(creativeText => {
        recommendTextIds = [
            ...recommendTextIds,
            ...(creativeText.recommendId ? [creativeText.recommendId] : []),
            ...(creativeText.programTitles || []).map(data => data.recommendId),
            ...(creativeText.programDescs || []).map(data => data.recommendId)
        ].filter(isValidRecommendId);
    });

    const addAdgroupSegmentBinds = generateAdgroupSegmentBinds({bindInfo: {segments}});

    const params = {
        checkAll: !!isCheckAll,
        recommendTextIds,
        recommendImageMap: getRecommendImageData(segments),
        recommendVideos: getRecommendVideoData(videos),
        item: {
            creativeTypes,
            addAdgroupSegmentBinds
        }
    };
    if (isCheckAll) {
        params.checkAllCondition = {idType: idType.PLAN_LEVEL, ids: campaignIds || []};
    }
    else {
        params.mtlIds = adgroupList.map(adgroup => ({
            campaignId: adgroup.campaignId,
            adgroupId: adgroup.adgroupId
        }));
    }
    return params;
}
