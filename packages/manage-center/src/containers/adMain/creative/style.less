
.programmatic-segments-title {
    display: flex;
    align-items: center;
}

.programmatic-segments-counter {
    margin-left: @dls-padding-unit * 3;
}

.programmatic-segments-title-error-icon {
    color: @dls-color-error-7;
    margin-left: @dls-padding-unit * 3;
}

.ad-main-toggle-creative-group {
    position: absolute;
    top: 28px;
    right: 24px;
}

.ad-main-creatives-form-wrapper .creative-preview-block {
    display: flex;
    justify-content: center;
    .ad-main-creative-form {
        display: none;
    }
}
.ad-main-creatives-form-container {
    display: flex;
    justify-content: space-between;
    margin: 16px 20px;
    padding: 20px;
    background: #fff;

    .wood-spring-app-preview {
        margin-left: 0;
        margin-top: 0;

        .wood-spring-app-preview-container {
            padding: 0;
        }
    }
}
.rf-form-item-builtin-group .ad-main-creatives-form-container {
    margin: 0;
    padding: 0;
}

.ad-main-creative-side-container {
    width: @dls-padding-unit*75;
    padding: @dls-padding-unit*6 @dls-padding-unit*4;
    background-color: @dls-color-gray-1;
    border-radius: @dls-border-radius-2;
}

.ad-main-auto-label {
    display: flex;
    align-items: center;
}

.ad-main-inline-group {
    display: inline-flex;
    align-items: center;
}
.ad-main-space-between-group {
    display: flex;
    justify-content: space-between;
    align-items: center;

    --rf-form-item-input-min-height: 'auto';
}

.ad-main-form-creative-box {
    border-radius: @dls-border-radius-2;
    border: 1px solid @dls-color-gray-3;
}

.ad-main-form-creative-box-title-text {
    margin-right: @dls-padding-unit * 3;
}
.ad-main-form-creative-box-title-for-creatives {
    display: flex;
    justify-content: space-between;
    margin: @dls-padding-unit*4 @dls-padding-unit*4 @dls-padding-unit*2;
}
.ad-main-form-creative-box-title-for-block {
    display: flex;
    justify-content: flex-end;
    position: relative;
    top: 10px;
}
.ad-main-form-creative-box-title, .ad-main-form-creative-box-title-for-block {
    font-size: @dls-font-size-1;
    font-weight: @dls-font-weight-2;
    line-height: 20px;
    color: @dls-color-gray-9;
    margin-bottom: @dls-padding-unit * 5;
    button {
        margin-left: @dls-padding-unit * 2;
    }
}

.ad-main-form-creative-custom-segments {
    border-radius: @dls-border-radius-2;
    border: 1px solid @dls-color-gray-3;
    padding: @dls-padding-unit * 4;
}

.ad-main-customize-creative-group {
    --rf-form-item-builtin-expandable-group-control-padding-left: 0;
    .tab-title .error-tip {
        color: @dls-color-error-7;
        margin-left: @dls-padding-unit;
    }
}

.ad-main-creative-bottom-tip {
    margin: @dls-padding-unit*4 0;
    border-radius: @dls-border-radius-1;
    background: @dls-color-gray-1;
    padding: @dls-padding-unit*2 @dls-padding-unit*4;
    color: @dls-color-gray-7;
    font-size: @dls-font-size-0;
}

.ad-main-creative-bottom-tip-hide {
    display: none;
}

.ad-main-creative-form-tip {
    position: absolute;
    top: 28px;
    right: 24px;
    font-size: 12px;
    line-height: 22px;
    color: #545b66;
}
