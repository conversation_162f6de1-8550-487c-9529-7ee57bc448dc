import {Toast} from '@baidu/one-ui';
import {cloneDeep} from 'lodash-es';
import {getRecommendKey, RECOMMEND_KEY_CONFIG} from 'app/containers/fcNew/creative/recommend/config';
import {
    formatDigitalHumanVideoData, getImageSegment
} from 'app/containers/fcNew/creative/separateNewWithAiChat/utils/aichat';
import {creativeTypeEnum} from 'commonLibs/config/creativeText';
import {MAX_PROGRAM_COUNT} from '../programmaticCreative/config';

interface IProcessCreativeData {
    submitData: {
        image: string[]; // 图片
        video: unknown[]; // 视频
        // 自定义创意 文案
        text: Array<{
            recommendId: string | number;
            title: string;
            desc1: string;
            desc2: string;
            addFrom?: number;
        }>;
        // 自适应创意 标题 描述
        title: Array<{
            recommendId: string | number;
            title: string;
            addFrom?: number;
        }>;
        desc1: Array<{
            recommendId: string | number;
            desc1: string;
            addFrom?: number;
        }>;
        tempAdgroupId?: string;
        creativeType: number;
    };
    originData: Record<string, any>;
    currentCreative: {
        title: string;
        description1: string;
        tabIndex: number;
    };
    creativeType: number;
}

/**
 * 将aichat生成的创意物料应用到当前创意表单
 *
 * @param submitData aigc生成的创意物料，包含图片、视频、文案、标题、描述等
 * @param originData 创意表单数据
 * @param currentCreative 正在编辑的自定义创意；自适应创意可忽略，不是tab切换创意分别填写的交互
 * @param creativeType 创意类型，可选值：0（自定义创意）、1（自适应创意）
 * @param maxCreativeTextCount 自定义创意 文案数量上限
 * @param maxCreativeTitleCount 自适应创意 标题数量上限
 * @param maxCreativeDescCount 自适应创意 描述数量上限
 * @returns 更新后的创意表单数据
 */
export function getNewProcessCreativeData(
    {submitData, originData, currentCreative}: IProcessCreativeData,
    {
        maxCreativeTextCount = 20, maxCreativeTitleCount = MAX_PROGRAM_COUNT,
        maxCreativeDescCount = MAX_PROGRAM_COUNT
    } = {}
) {
    const {image, video, tempAdgroupId} = submitData;
    const newData = cloneDeep(originData);
    newData.tempAdgroupId = tempAdgroupId;
    newData.creativeType = submitData.creativeType;
    // 添加图片和视频
    newData.segmentBinds = {
        ...newData.segmentBinds,
        segments: (newData.segmentBinds?.segments || []).concat(image.map(getImageSegment)),
        videos: (newData.segmentBinds?.videos || []).concat(video.map(formatDigitalHumanVideoData))
    };
    // 自定义创意-填文本
    if (submitData.creativeType === creativeTypeEnum.customize) {
        const {text} = submitData;
        if (text.length) {
            const newCreatives = text.map(({recommendId, title, desc1, desc2, addFrom}) => {
                return {
                    addFrom,
                    recommendId,
                    key: getRecommendKey(
                        RECOMMEND_KEY_CONFIG.ZNZS,
                        recommendId ? `${RECOMMEND_KEY_CONFIG.ZNZS}${recommendId}` : ''
                    ),
                    title,
                    description1: desc1,
                    description2: desc2
                };
            });
            // 这块得外部再传一个currentCreative进来，是当前选中的文案数据，因为外面form change后并没有立即更新creatives的数据，
            // 所以用newData.creatives[currentIndex]获取到的是旧的数据，之前做的有点不好的
            const {title, description1, tabIndex} = currentCreative;
            const currentIndex = tabIndex || newData.creativeTexts.length - 1; // 兜底一下，如果找不到，则添加到最后一个
            // 如果当前文本没有填内容，则填入到当前文本，否则在后面增加
            newData.creativeTexts.splice(
                (title || description1) ? currentIndex + 1 : currentIndex,
                (title || description1) ? 0 : 1,
                ...newCreatives
            );
            if (newData.creativeTexts.length > maxCreativeTextCount) {
                Toast.info({content: `创意数量到达上限${maxCreativeTextCount}个，已丢弃超出限制的创意`});
                newData.creativeTexts = newData.creativeTexts.slice(0, maxCreativeTextCount); // 限制数量，超出的丢弃
            }
            newData.tabIndex = newData.creativeTexts.length - 1;
        }
    }
    // 自适应创意-填标题和描述
    if (submitData.creativeType === creativeTypeEnum.programmatic) {
        const {title = [], desc1 = []} = submitData;
        [
            [
                ['title', title, maxCreativeTitleCount],
                ['programTitles', 'title']
            ],
            [
                ['desc1', desc1, maxCreativeDescCount],
                ['programDescs', 'desc']
            ]
        ].forEach(config => {
            const [
                [key, values = [], maxCount],
                [filedKey, creativeKey]
            ] = config;
            if (values?.length) {
                const newCreatives = values.map(recommendItem => ({
                    addFrom: recommendItem.addFrom,
                    recommendId: recommendItem.recommendId,
                    [creativeKey]: recommendItem[key]
                }));
                const currentIndex = newData[filedKey].length - 1;
                const currentValue = newData[filedKey][currentIndex];
                // 如果当前文本没有填内容，则填入到当前文本，否则在后面增加
                newData[filedKey].splice(
                    currentValue[creativeKey] ? currentIndex + 1 : currentIndex,
                    currentValue[creativeKey] ? 0 : 1,
                    ...newCreatives
                );
                if (newData[filedKey].length > maxCount) {
                    Toast.info({content: `创意数量到达上限${maxCount}个，已丢弃超出限制的创意`});
                    newData[filedKey] = newData[filedKey].slice(0, maxCount);
                }
            }
        });
    }
    return newData;
}
