import {cloneDeep, isNil} from 'lodash-es';
import {isOcpcCampaign} from 'commonLibs/utils/campaign';
import {isLiveSubMarket} from 'commonLibs/config/marketTarget';
import {getRecommendConfig} from 'app/containers/fcNew/creative/recommend/config';

export function creativeParamsPipeline(generators, initial) {
    return function (info, values) {
        return generators.reduce(
            (result, generator) => generator(info, values, result),
            cloneDeep(initial)
        );
    };
}

function getProgramTextAddFrom({programTitles, programDescs}) {
    let max = 0;
    let maxAddFrom;
    [...programTitles, ...programDescs].reduce((counts, {addFrom, text}) => {
        if (addFrom) {
            counts[addFrom] = (counts[addFrom] || 0) + 1;
        }
        if (counts[addFrom] > max) {
            max = counts[addFrom];
            maxAddFrom = addFrom;
        }
        return counts;
    }, {});
    return maxAddFrom;
}

export function creativeTypeParams(
    {registerDomain, adType, campaignBidType, marketingTargetId, subMarketingTargetId},
    creativeInfo,
    result
) {
    const {
        programTitles, programDescs, title, description1, description2, pcFinalUrl, pcTrackParam, pcTrackTemplate,
        mobileFinalUrl, mobileTrackParam, mobileTrackTemplate, miniProgramUrl, source, addFrom
    } = creativeInfo;
    result.creativeType.creativePreference = 0;
    result.creativeType.smartCampaign = 0;
    if (programTitles) {
        // 获取出现次数最多的 addFrom
        const innerAddFrom = getProgramTextAddFrom({programTitles, programDescs});

        result.creativeType.programTitles = programTitles.map(({title}) => title);
        result.creativeType.programDescriptions = programDescs.map(({desc}) => desc);
        if (innerAddFrom) {
            result.creativeType.addFrom = innerAddFrom;
        }
        return result;
    }

    result.creativeType = {
        ...result.creativeType,
        title,
        description1,
        description2,
        ...(
            isLiveSubMarket(subMarketingTargetId) ? {} : {
                pcFinalUrl,
                pcTrackParam,
                pcTrackTemplate,
                mobileFinalUrl,
                mobileTrackParam,
                mobileTrackTemplate,
                miniProgramUrl,
                pcDisplayUrl: registerDomain,
                mobileDisplayUrl: registerDomain
            }
        )
    };
    if (addFrom) {
        result.creativeType.addFrom = addFrom;
    }
    if (source != null) {
        result.creativeType.mcId = source;
        const isConverionCampaign = isOcpcCampaign({adType, campaignBidType});
        const {addFrom} = getRecommendConfig({marketingTargetId, isConverionCampaign}) || {};
        result.creativeType.addFrom = addFrom;
    }
    return result;
}

export function creativeIdParams(_, {creativeId}, result) {
    if (creativeId) {
        result.creativeType.creativeId = creativeId;
    }
    return result;
}
