import {isObject, isFunction} from 'lodash-es';
import {
    campaignBidTypeConfig,
    campaignOcpcBidTypeConfig
} from 'commonLibs/config/ocpc';

export function convertFieldToConfig({
    field,
    baseFields,
    baseGroups,
    remoteConfig,
    props
}) {
    if (isObject(field)) {
        const {group, fields, arrayField} = field;

        if (arrayField) {
            const arrayFieldConfig = baseFields[arrayField];
            return {
                field: arrayField,
                ...(isFunction(arrayFieldConfig) ? arrayFieldConfig(props) : arrayFieldConfig),
                fieldArrayConfig: fields.map(field => convertFieldToConfig({
                    field,
                    baseFields,
                    baseGroups,
                    remoteConfig,
                    props
                }))
            };
        }
        const groupConfig = baseGroups[group];
        return {
            group,
            ...(isFunction(groupConfig) ? groupConfig(props) : groupConfig),
            fields: fields.map(field => convertFieldToConfig({
                field,
                baseFields,
                baseGroups,
                remoteConfig,
                props
            }))
        };
    }
    const fieldConfig = remoteConfig.fields?.[field] ?? baseFields[field];
    return isFunction(fieldConfig) ? fieldConfig(props) : fieldConfig;
}
// 筛选watch，如果fields中没有该field，就不需要watch
export function filterWatchByFields({
    watch,
    fields
}) {
    return fields.reduce((result, {fields: fieldList}) => {
        fieldList.map(fieldConfig => {
            if (fieldConfig?.group) {
                result = {
                    ...result,
                    ...filterWatchByFields({
                        watch,
                        fields: [fieldConfig]
                    })
                };
            }
            else if (watch[fieldConfig.field]) {
                result[fieldConfig.field] = watch[fieldConfig.field];
            }
        });
        return result;
    }, {});
}

export function getEfficiencyBidTypeByCampaignInfo({campaignOcpcBidType}) {
    const isConversionCampaign = [campaignOcpcBidTypeConfig.oCPC, campaignOcpcBidTypeConfig.cvMax]
        .includes(campaignOcpcBidType);
    return isConversionCampaign
        ? campaignBidTypeConfig.oCPC
        : campaignBidTypeConfig.cpc;
}
