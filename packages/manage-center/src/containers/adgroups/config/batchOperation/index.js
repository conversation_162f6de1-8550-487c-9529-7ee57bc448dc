/**
 * @file 批量编辑下拉框datasource配置文件
 * <AUTHOR>
 */
import Enum from 'enum';
import {IconPlayCircle, IconPauseCircle, IconTrash} from 'dls-icons-react';
import {concat, identity, flow, without} from 'lodash-es';
import MARKET_TARGET, {isLiveSubMarket} from 'commonLibs/config/marketTarget';
import {getBatchOptionsWithDivider} from 'commonLibs/utils/getBatchOptionsWithDivider';
import {isDirectStatusBatchEditUser, isUnitAgentUrlUser} from 'commonLibs/utils/getFlag';

export const MOD_NEGATIVE_KEY = 'modNegative';
// 否词批量编辑配置

// 商品计划 - 批量修改单元
export const COMMODITY_UNIT_ADD = 'commodityUnitAdd';

// 商品计划 - 单元点击出价
export const COMMODITY_PRICE_MOD = 'commodityPriceMod';

const BATCH_FIELDS_CONFIG = {
    [MARKET_TARGET.WEB]: [
        'modBid', 'start', 'pause', 'delete', MOD_NEGATIVE_KEY,
        'adgroupAutoTargetingStatus', 'segmentRecommendStatus',
        'adgroupUrlType', 'pcFinalUrl', 'mobileFinalUrl', 'adgroupAgentUrl',
        'adgroupAgentParam',
        'creativeTextOptimizationStatus', 'appShopDirectStatus'
    ],
    [MARKET_TARGET.APP]: [
        'modBid', 'start', 'pause', 'delete',
        'adgroupAutoTargetingStatus', 'segmentRecommendStatus',
        'mobileFinalUrl', 'creativeTextOptimizationStatus', 'appShopDirectStatus'
    ],
    [MARKET_TARGET.CPQL]: [
        'modBid', 'start', 'pause', 'delete', MOD_NEGATIVE_KEY,
        'adgroupAutoTargetingStatus', 'segmentRecommendStatus',
        'adgroupUrlType', 'pcFinalUrl', 'mobileFinalUrl',
        'adgroupAgentUrl', 'adgroupAgentParam',
        'creativeTextOptimizationStatus', 'structuredContentIds'
    ]
};

// 分隔配置
const batchFieldDividerConfig = new Enum({
    STATUS: '状态',
    SETTING: '设置',
    PRICE: '出价',
    WEBSITE: '访问网址'
});

const batchFieldMap = (selectedIds, dataMap, isCheckAll) => {
    const disabledUrlTypeByRotating = !isCheckAll && selectedIds.every(id => dataMap[id]?.rotatingCycStatus);
    const disabledBecauseLive = !isCheckAll && selectedIds.every(
        id => isLiveSubMarket(dataMap[id]?.subMarketingTargetId)
    );
    return {
        modBid: {
            value: 'modBid',
            label: '单元点击出价',
            disabled: false,
            divider: batchFieldDividerConfig.PRICE.key
        },
        start: {
            value: 'start',
            label: <><IconPlayCircle />&nbsp;启用</>,
            divider: batchFieldDividerConfig.STATUS.key,
            preset: true
        },
        pause: {
            value: 'pause',
            label: <><IconPauseCircle />&nbsp;暂停</>,
            divider: batchFieldDividerConfig.STATUS.key,
            preset: true
        },
        delete: {
            value: 'delete',
            label: <><IconTrash />&nbsp;删除</>,
            divider: batchFieldDividerConfig.STATUS.key,
            preset: true
        },
        appShopDirectStatus: {
            value: 'appShopDirectStatus',
            label: '应用商店直投',
            divider: batchFieldDividerConfig.SETTING.key,
            isHidden: !isDirectStatusBatchEditUser()
        },
        modPCBidFactor: {
            label: '计算机出价系数',
            value: 'modPCBidFactor',
            divider: batchFieldDividerConfig.PRICE.key
        },
        modMobileBidFactor: {
            label: '移动出价系数',
            value: 'modMobileBidFactor',
            divider: batchFieldDividerConfig.PRICE.key
        },
        [COMMODITY_UNIT_ADD]: {
            value: COMMODITY_UNIT_ADD,
            label: '复制单元',
            divider: batchFieldDividerConfig.SETTING.key
        },
        [COMMODITY_PRICE_MOD]: {
            value: COMMODITY_PRICE_MOD,
            label: '单元点击出价',
            divider: batchFieldDividerConfig.PRICE.key
        },
        [MOD_NEGATIVE_KEY]: {
            value: MOD_NEGATIVE_KEY,
            label: '否定关键词',
            divider: batchFieldDividerConfig.SETTING.key
        },
        adgroupAutoTargetingStatus: {
            value: 'adgroupAutoTargetingStatus',
            label: '自动定向',
            divider: batchFieldDividerConfig.SETTING.key
        },
        segmentRecommendStatus: {
            value: 'segmentRecommendStatus',
            label: '自动图片优化',
            divider: batchFieldDividerConfig.SETTING.key
        },
        creativeTextOptimizationStatus: {
            value: 'creativeTextOptimizationStatus',
            label: '自动文案优化',
            divider: batchFieldDividerConfig.SETTING.key
        },
        pcFinalUrl: {
            value: 'pcFinalUrl',
            label: '计算机访问网址',
            divider: batchFieldDividerConfig.WEBSITE.key,
            disabled: disabledBecauseLive,
            tip: disabledBecauseLive ? '直播场景不支持修改计算机访问网址' : ''
        },
        mobileFinalUrl: {
            value: 'mobileFinalUrl',
            label: '移动访问网址',
            divider: batchFieldDividerConfig.WEBSITE.key,
            disabled: disabledBecauseLive,
            tip: disabledBecauseLive ? '直播场景不支持修改移动访问网址' : ''
        },
        adgroupUrlType: {
            value: 'adgroupUrlType',
            label: '广告链接类型',
            isHidden: !isUnitAgentUrlUser(),
            divider: batchFieldDividerConfig.WEBSITE.key,
            disabled: disabledUrlTypeByRotating || disabledBecauseLive,
            tip: disabledUrlTypeByRotating
                ? '智能体轮值生效中的单元，不支持修改广告链接类型'
                : (disabledBecauseLive ? '直播场景不支持修改广告链接' : '')
        },
        adgroupAgentUrl: {
            value: 'adgroupAgentUrl',
            label: '商家智能体',
            isHidden: !isUnitAgentUrlUser(),
            divider: batchFieldDividerConfig.WEBSITE.key,
            disabled: disabledBecauseLive,
            tip: disabledBecauseLive ? '直播场景不支持关联商家智能体' : ''
        },
        adgroupAgentParam: {
            value: 'adgroupAgentParam',
            label: '智能体监控后缀',
            isHidden: !isUnitAgentUrlUser(),
            divider: batchFieldDividerConfig.WEBSITE.key,
            disabled: disabledBecauseLive,
            tip: disabledBecauseLive ? '直播场景不支持修改智能体监控后缀' : ''
        },
        structuredContentIds: {
            value: 'structuredContentIds',
            label: '关联产品',
            divider: batchFieldDividerConfig.SETTING.key
        }
    };
};

const commodityUnitAddField = value => concat(value, COMMODITY_UNIT_ADD);
const commodityPriceModField = flow(
    value => without(value, 'modBid'),
    value => concat(value, COMMODITY_PRICE_MOD)
);

export default function getBatchOptionFields(commonProps) {
    const {mtId = 0, bizId, campaignId, adgroupId, selectedIds,
        isCheckAll, dataMap, isPresetBatchOperation} = commonProps;
    const isCommodity = +mtId === MARKET_TARGET.COMMODITY;
    const optionsMap = batchFieldMap(selectedIds, dataMap, isCheckAll);
    let fields = BATCH_FIELDS_CONFIG[mtId] || BATCH_FIELDS_CONFIG[MARKET_TARGET.WEB];

    if (isCommodity) {
        // 只在这个页面生效 '/fc/manage/dashboard/user/:userId/mt/5/campaign/:campaignId/adgroups'
        const canAddUnit = !bizId && campaignId && !adgroupId && selectedIds.length === 1 && !isCheckAll;
        const canModBid = !bizId;
        fields = flow(
            canAddUnit ? commodityUnitAddField : identity,
            canModBid ? commodityPriceModField : identity,
            value => without(value, 'modPCBidFactor', 'modMobileBidFactor'),
            value => without(value, 'pcFinalUrl', 'mobileFinalUrl'),
            value => without(value, 'adgroupUrlType', 'adgroupAgentUrl', 'adgroupAgentParam')
        )(fields);
    }

    let filteredFields = fields.filter(field => !optionsMap[field].isHidden);
    // 如果批量操作预置，这里过滤掉预置的操作
    if (isPresetBatchOperation) {
        filteredFields = filteredFields.filter(field => !optionsMap[field].preset);
        return getBatchOptionsWithDivider(filteredFields.map(field => optionsMap[field]), batchFieldDividerConfig);
    }

    return getBatchOptionsWithDivider(filteredFields.map(field => optionsMap[field]), batchFieldDividerConfig);
}
