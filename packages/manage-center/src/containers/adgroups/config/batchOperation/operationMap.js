/**
 * @file operation map
 * <AUTHOR>
 * @date 2017/11/30 下午8:44
 */
import {COMMODITY_PRICE_MOD, COMMODITY_UNIT_ADD} from './index';
import {Toast, Dialog} from '@baidu/one-ui';
import sendMonitor from 'commonLibs/utils/sendHm';
import {getBatchModParams} from 'app/components/batchOperationFragment/utils';
import {getNegativeMap} from 'app/components/editorPanel/negativeWords/config';
import {ConfirmContent, getColumns} from 'commonLibs/editorPanel/batchErrorConfirm';
import {getBatchConfirmErrorSource, errorsMap} from 'app/config/errorMessage';
import {getDefaultProductCategoryType} from 'commonLibs/components/selectCard/product/config';
import {productCategoryTypeEnum} from 'commonLibs/config/enumEntry';

export default function operationMap(props, log) {
    const {
        requestHook,
        dispatch,
        tableRequestParams,
        onSuccessCallback,
        refreshAndExpire,
        search,
        setEditorColumn,
        openEditor
    } = props;

    return {
        structuredContentIds: () => {
            const productCategoryType = getDefaultProductCategoryType();
            const isLaw = productCategoryType === productCategoryTypeEnum.LAW;
            openEditor(
                'batch',
                isLaw ? 'structuredLawProductIds' : 'structuredContentIds',
                null,
                {
                    containerType: 'drawer',
                    width: 800,
                    ...(isLaw ? {} : {
                        hideDefaultFooter: true,
                        hideDefaultTitle: true
                    }),
                    type: 'basic'
                }
            );
            sendMonitor('click', {
                type: productCategoryType,
                level: 'adgroup_list',
                source: 'struct_product',
                item: 'batch_entry'
            });
        },
        adgroupAgentUrl: () => {
            openEditor('batch', 'adgroupAgentUrl', null, {
                containerType: 'dialog',
                width: 800
            });
        },
        adgroupAgentParam: () => {
            openEditor('batch', 'adgroupAgentParam', null, {
                containerType: 'dialog',
                width: 800
            });
        },
        adgroupUrlType: () => {
            openEditor('batch', 'adgroupUrlType', null, {
                containerType: 'dialog',
                width: 800
            });
        },
        modBid: () => {
            setEditorColumn('adgroupBid');
        },
        modBrandInfo: () => {
            setEditorColumn('adgroupBrand');
        },
        adgroupAutoTargetingStatus: () => {
            setEditorColumn('adgroupAutoTargetingStatus');
        },
        creativeTextOptimizationStatus: () => {
            setEditorColumn('creativeTextOptimizationStatus');
        },
        knowledgeTextStatus: () => {
            setEditorColumn('knowledgeTextStatus');
        },
        jimuyuContentStatus: () => {
            setEditorColumn('jimuyuContentStatus');
        },
        segmentRecommendStatus: () => {
            setEditorColumn('segmentRecommendStatus');
        },
        pcFinalUrl: () => {
            setEditorColumn('pcFinalUrl');
        },
        mobileFinalUrl: () => {
            setEditorColumn('mobileFinalUrl');
        },
        pcTrackParam: () => {
            setEditorColumn('pcTrackParam');
        },
        mobileTrackParam: () => {
            setEditorColumn('mobileTrackParam');
        },
        pcTrackTemplate: () => {
            setEditorColumn('pcTrackTemplate');
        },
        mobileTrackTemplate: () => {
            setEditorColumn('mobileTrackTemplate');
        },
        appShopDirectStatus: () => {
            setEditorColumn('appShopDirectStatus');
        },
        start: () => {
            sendMonitor('batch_edit', {level: 'adgroup', item: 'start', resp: 'all'});
            log('stage', {target: '^save_batch_start'});
            const updateParams = getBatchModParams(props, {pause: false}, tableRequestParams, search);
            requestHook({
                path: 'raining/MOD/AdgroupAsyncService/updateAdgroup',
                params: updateParams
            }).then(resp => {
                onSuccessCallback(resp, {needRefreshList: true, entityMode: false});
            }).catch(resp => {
                const errors = resp && resp.errors || [];
                if (resp.status === 1) {
                    refreshAndExpire();
                    if (errors && errors.length) {
                        const confirmProps = {
                            resp,
                            errorSource: getBatchConfirmErrorSource(errorsMap)(resp),
                            materialName: '单元',
                            optName: '启用',
                            getColumns
                        };
                        Dialog.confirm({
                            width: 500,
                            needCloseIcon: true,
                            title: '温馨提示',
                            content: (
                                <ConfirmContent {...confirmProps} />
                            )
                        });
                    }
                }
                else {
                    Toast.error({
                        content: `启用失败，请重试(${resp?.code})`,
                        showCloseIcon: false
                    });
                }
            }).finally(() => {
                log('stage', {target: '$save_batch_start'});
            });
        },
        pause: () => {
            log('stage', {target: '^save_batch_pause'});
            const updateParams = getBatchModParams(props, {pause: true}, tableRequestParams, search);
            requestHook({
                path: 'raining/MOD/AdgroupAsyncService/updateAdgroup',
                params: updateParams
            }).then(resp => {
                onSuccessCallback(resp, {needRefreshList: true, entityMode: false});
            }).catch(resp => {
                const errors = resp && resp.errors || [];
                if (resp.status === 1) {
                    refreshAndExpire();
                    if (errors && errors.length) {
                        const confirmProps = {
                            resp,
                            errorSource: getBatchConfirmErrorSource(errorsMap)(resp),
                            materialName: '单元',
                            optName: '暂停',
                            getColumns
                        };
                        Dialog.confirm({
                            width: 500,
                            needCloseIcon: true,
                            title: '温馨提示',
                            content: (
                                <ConfirmContent {...confirmProps} />
                            )
                        });
                    }
                }
                else {
                    Toast.error({
                        content: `暂停失败，请重试(${resp?.code})`,
                        showCloseIcon: false
                    });
                }
            }).finally(() => {
                log('stage', {target: '$save_batch_pause'});
            });
        },
        delete: () => {
            const {isCheckAll, selectedIds, excludeIds = [], pager} = props;
            const selectedNum = (excludeIds.length && isCheckAll)
                ? pager.totalCount - excludeIds.length
                : selectedIds.length;
            const confirmProps = {
                title: '确认删除',
                content: isCheckAll && !excludeIds.length ? '您确定要删除所选的单元吗？删除操作不可恢复'
                    : `您确定要删除所选的${selectedNum}个单元吗？
                    确定将同时删除这些单元下所有关键词、创意。删除操作不可恢复。`,
                onOk() {
                    log('stage', {target: '^save_batch_delete'});
                    const updateParams = getBatchModParams(props, null, tableRequestParams, search);
                    requestHook({
                        path: 'raining/DEL/AdgroupAsyncService/deleteAdgroup',
                        params: updateParams
                    }).then(resp => {
                        onSuccessCallback(resp, {needRefreshList: true, updateData: false});
                    }).catch(resp => {
                        const errors = resp && resp.errors || [];
                        if (resp.status === 1) {
                            refreshAndExpire();
                            if (errors && errors.length) {
                                const confirmProps = {
                                    resp,
                                    errorSource: getBatchConfirmErrorSource(errorsMap)(resp),
                                    materialName: '单元',
                                    optName: '删除',
                                    getColumns
                                };
                                Dialog.confirm({
                                    width: 500,
                                    needCloseIcon: true,
                                    title: '温馨提示',
                                    content: (
                                        <ConfirmContent {...confirmProps} />
                                    )
                                });
                            }
                        }
                        else {
                            Toast.error({
                                content: `删除失败，请重试(${resp?.code})`,
                                showCloseIcon: false
                            });
                        }
                    }).finally(() => {
                        log('stage', {target: '$save_batch_delete'});
                    });
                }
            };
            Dialog.confirm(confirmProps);
        },
        modMobileBidFactor: () => {
            setEditorColumn('mobilePriceRatio');
        },
        modPCBidFactor: () => {
            setEditorColumn('pcPriceRatio');
        },
        [COMMODITY_UNIT_ADD]() {
            setEditorColumn(COMMODITY_UNIT_ADD);
        },
        [COMMODITY_PRICE_MOD]() {
            setEditorColumn(COMMODITY_PRICE_MOD);
        },
        ...getNegativeMap(props, dispatch)
    };
}
