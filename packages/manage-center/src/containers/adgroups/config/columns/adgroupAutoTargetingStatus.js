/*
 * @file: adgroupAutoTargetingStatus
 * @author: l<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2021-11-12 16:11:56
 */

import {memo, useState} from 'react';
import {Switch, Tooltip} from '@baidu/one-ui';
import {getRecordRender} from 'commonLibs/tableList/utils';
import {createError, displayError} from 'commonLibs/utils/materialList/error';
import {updateAdgroupAutoTargetingStatus} from 'app/api/adgroup/update';
import {useSuccessCallback} from 'app/containers/adgroups/hooks/useSuccessCallback';
import {useLog} from 'commonLibs/logger';
import {cancelConfirm, isShowCancelConfirm} from 'app/containers/adgroups/utils/cancelConfirm';
import {getIsAdgroupDisabledEdit} from '../../utils';

export const ON = 1;
export const OFF = 0;
export const StatusOptions = [{
    label: '开启',
    value: ON
}, {
    label: '关闭',
    value: OFF
}];

const AdgroupAutoTargetingStatusEditor = memo(props => {
    const {record} = props;
    const {
        adgroupId, adgroupAutoTargetingStatus, structuredContentIdStrs,
        productCategoryType
    } = record;
    const [{onSuccessCallback}] = useSuccessCallback();
    const [loading, setLoading] = useState(false);
    const log = useLog();


    const onSave = async checked => {
        log('click', {target: 'mod_adgroupAutoTargeting'});
        const onOk = async () => {
            setLoading(true);
            try {
                const data = await updateAdgroupAutoTargetingStatus({
                    adgroupId, adgroupAutoTargetingStatus: checked
                });
                onSuccessCallback(data);
            }
            catch (err) {
                const formatError = createError(err);
                formatError.optName = '修改自动定向';
                displayError(formatError);
            }
            finally {
                setLoading(false);
            }
        };

        if (isShowCancelConfirm(productCategoryType, structuredContentIdStrs)
            && +checked === OFF
        ) {
            return cancelConfirm({
                contentLabel: '自动定向',
                onOk
            });
        }
        onOk();
    };
    const switchProps = {
        checked: adgroupAutoTargetingStatus,
        loading,
        onChange: onSave
    };
    const disabled = getIsAdgroupDisabledEdit('adgroupAutoTargetingStatus', record);

    return (
        <Tooltip title={disabled}>
            <Switch {...switchProps} disabled={!!disabled} />
        </Tooltip>
    );
});

export default getRecordRender(
    AdgroupAutoTargetingStatusEditor,
    {filters: {options: StatusOptions}, inView: true}
);
