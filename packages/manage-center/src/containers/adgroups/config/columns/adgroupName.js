import {Fragment} from 'react';
import {useSelector} from 'react-redux';
import {Link} from '@baidu/one-ui';
import {handleSummary} from 'commonLibs/tableList/utils';
import {getKeywordsLinkUrl, getQueryWordsLinkUrl} from 'commonLibs/utils/getKeywordsLinkUrl';
import {Name} from 'commonLibs/tableList/columns';
import {getInputConf} from 'commonLibs/validators';
import {useParams} from 'commonLibs/route';
import appendSearch from 'commonLibs/utils/appendSearch';
import ADTYPE from 'commonLibs/config/adType';
import {isD20KeywordUser} from 'commonLibs/utils/getFlag';
import {actionTypes as ACTIONS} from 'app/reducers/adgroups';
import getMtOrBiz from 'app/utils/getMtOrBiz';
import getKeywords from 'app/utils/getKeywords';
import {useSuccessCallback} from 'app/containers/adgroups/hooks/useSuccessCallback';
import {request, success, fail} from '../../utils/request';

const ADGROUP_MAX_LEN = 30;

const AdgroupName = props => {
    const {record, columnName, columnText} = props;
    const {userId} = useParams();
    const {campaignId, adgroupId, adType} = record;
    let toUrl = getKeywordsLinkUrl(`${getMtOrBiz(record)}/${getKeywords(adType)}`);
    if (adType === ADTYPE.URL) {
        toUrl = getQueryWordsLinkUrl(`${getMtOrBiz(record)}/${getKeywords(adType)}`);
    }
    const [{onSuccessCallback}] = useSuccessCallback();

    const nameProps = {
        name: record[columnName],
        record,
        field: columnName,
        fieldMap: getInputConf(columnText, 1, ADGROUP_MAX_LEN),
        ACTIONS,
        request: request(record),
        success: success(columnName),
        afterSave: onSuccessCallback,
        fail,
        toUrl: appendSearch(toUrl),
        isInternal: isD20KeywordUser() && /managecenter/.test(toUrl)
    };
    const accountRole = useSelector(state => state?.basicInfo?.accountInfo?.accountRole);
    const isAuditLink = accountRole === 'wordadmin';
    const linkProps = {
        type: 'strong',
        size: 'small',
        target: '_blank',
        toUrl: `http://wall.baidu.com/mtaudit/fcaudit/index?userid=${userId}&planid=${campaignId}&unitid=${adgroupId}`
    };
    return (
        <Fragment>
            <Name {...nameProps} />
            {isAuditLink && <Link {...linkProps}>审核该单元</Link>}
        </Fragment>
    );
};

export default params => {
    const {columnName, columnText} = params;
    return {
        render: (text, record, row) => {
            const nameProps = {
                record, columnName, columnText
            };
            return handleSummary(<AdgroupName {...nameProps} />, {needSum: true, field: columnName, record, row});
        },
        filters: {
            maxLen: ADGROUP_MAX_LEN
        }
    };
};
