import {useContext, useMemo} from 'react';
import {IconEdit} from 'dls-icons-react';
import {noop} from 'lodash-es';
import {Tooltip, Tag, Popover} from '@baidu/one-ui';
import {handleSummary} from 'commonLibs/tableList/utils';
import {isLiveSubMarket} from 'commonLibs/config/marketTarget';
import {
    adgroupUrlTypeText, AdgroupUrlTypeEnum,
    getAdgroupUrlTypeFilterOptions
} from 'commonLibs/components/commonEntry';
import {AdgroupEditorContext} from '../../materialQuery';
import {getIsAdgroupDisabledEdit} from '../../utils';
import {
    RotatingCycStatus,
    RotatingCycStatusList,
    getRotatingNoEffectDetail
} from '../../utils/rotatingCycStatus';

const AgentUrl = ({record, field}) => {
    const {adgroupId, adgroupUrlType = AdgroupUrlTypeEnum.normal, rotatingCycStatus,
        marketingTargetId, subMarketingTargetId, rotatingCycStatusDetail = []} = record || {};
    const {openEditor} = useContext(AdgroupEditorContext);

    const isLive = useMemo(() => isLiveSubMarket(subMarketingTargetId), [subMarketingTargetId]);

    const disabled = getIsAdgroupDisabledEdit('adgroupUrlType', record);

    const disabledRotatingNoEffect = rotatingCycStatus === RotatingCycStatus.NOEFFECT;
    const disabledRotatingNoEffectDetail = getRotatingNoEffectDetail(rotatingCycStatusDetail);

    return (
        <div className="column-cell-flex">
            {isLive ? (
                <Popover content="直播场景不支持设置广告链接类型">
                    <span className="column-cell-disabled-color">-</span>
                </Popover>
            ) : adgroupUrlTypeText[adgroupUrlType]}
            <span>
                {
                    RotatingCycStatusList.includes(rotatingCycStatus) && (
                        <Tooltip title={disabledRotatingNoEffect
                            ? disabledRotatingNoEffectDetail
                            : '当前单元正在轮值投放商家智能体，广告链接类型不支持修改'}
                        >
                            <Tag size="small" tipTag="info" disabled={disabledRotatingNoEffect}>轮值</Tag>
                        </Tooltip>
                    )
                }
            </span>
            <span>
                {
                    !rotatingCycStatus && (
                        <Tooltip title={disabled}>
                            <IconEdit
                                className={`inline-operation-icon ${
                                    disabled ? 'inline-operation-icon-disabled' : ''}`}
                                onClick={disabled ? noop : () => openEditor('inline', field, adgroupId)}
                            />
                        </Tooltip>
                    )
                }
            </span>
        </div>
    );
};

export default ({columnName}) => {
    return {
        render: (text, record, row) => {
            const urlProps = {
                record,
                field: columnName,
                text
            };
            return handleSummary(<AgentUrl {...urlProps} />, {field: columnName, record, row});
        },
        filters: {
            options: getAdgroupUrlTypeFilterOptions()
        }
    };
};
