
import {isAgentUrlType, AdgroupUrlTypeEnum} from 'commonLibs/components/commonEntry';
import {isLiveSubMarket} from 'commonLibs/config/marketTarget';

const materialNameMap = {
    adgroup: '单元',
    creative: '创意',
    keyword: '关键词'
} as const;

const materialUrlTypeMap = {
    adgroup: 'adgroupUrlType',
    creative: 'creativeUrlType',
    keyword: 'keyWordUrlType'
} as const;

export const getAdgroupUrlTypeProps = (
    record,
    level: 'adgroup' | 'creative' | 'keyword' = 'adgroup',
    field = 'adgroupAgentUrl'
) => {
    // 物料名称
    const materialName = materialNameMap[level];
    // 广告链接类型
    const materialUrlType = record[materialUrlTypeMap[level]];
    // 商家智能体广告链接类型
    const isAdgroupAgentUrlType = isAgentUrlType(materialUrlType);
    // 直播间广告链接类型
    // eslint-disable-next-line max-len
    const isLiveUrlType = isLiveSubMarket(record.subMarketingTargetId) || materialUrlType === AdgroupUrlTypeEnum.live;
    // 当前字段是否为商家智能体
    const isAgentUrlField = field.includes('AgentUrl');
    let disabledTip = '';

    if (isAdgroupAgentUrlType && !isAgentUrlField) {
        if (level === 'adgroup') {
            disabledTip = '当前广告链接类型为商家智能体，将会使用商家智能体进行投放，因此落地页链接不会投放生效';
        }
        else {
            disabledTip = `当前广告链接类型为商家智能体，${materialName}将会优先使用所属单元设置的商家智能体进行投放，${materialName}的落地页链接不会投放生效`;
        }
    }

    if (!isAdgroupAgentUrlType && isAgentUrlField) {
        disabledTip = '当前广告链接类型为落地页链接，将会使用落地页链接进行投放，因此商家智能体不会投放生效';
    }

    if (isLiveUrlType) {
        disabledTip = '直播场景不支持设置广告链接类型';
    }

    return {
        isAdgroupAgentUrlType,
        disabledTip,
        disabledEdit: isLiveUrlType,
        urlClassNameSuffix: disabledTip ? ' column-cell-disabled-color' : ''
    };
};
