import {useContext, useCallback, useMemo, Fragment} from 'react';
import {Popover, Tooltip} from '@baidu/one-ui';
import {noop} from 'lodash-es';
import {IconEdit, IconQrcode} from 'dls-icons-react';
import {handleSummary} from 'commonLibs/tableList/utils';
import {isUnitAgentUrlUser} from 'commonLibs/utils/getFlag';
import {DESTINATION_URL_MAX_LENGTH_IN_BYTES} from 'app/components/tableEditor/url/config';
import {AdgroupEditorContext} from '../../materialQuery';
import {CPQL, WEB, isLiveSubMarket} from 'commonLibs/config/marketTarget';
import {getAdgroupUrlTypeProps} from 'app/containers/adgroups/config/columns/agentUrl-util';
import {getIsAdgroupDisabledEdit} from '../../utils';

const AgentUrl = ({record, field, text}) => {
    const {adgroupId, marketingTargetId, adgroupAgentInfo = {}, subMarketingTargetId} = record || {};
    const {openEditor, openPreviewUrl} = useContext(AdgroupEditorContext);

    const {agentHeadImage, agentName} = adgroupAgentInfo;

    const isLive = useMemo(() => isLiveSubMarket(subMarketingTargetId), [subMarketingTargetId]);

    const isShowColumn = useMemo(() => {
        return [CPQL, WEB].includes(marketingTargetId) && isUnitAgentUrlUser() && !isLive;
    }, [marketingTargetId, isLive]);

    const onClickPreview = useCallback(() => {
        openPreviewUrl && openPreviewUrl(text);
    }, [openPreviewUrl, text]);

    const {disabledTip, urlClassNameSuffix} = getAdgroupUrlTypeProps(record, 'adgroup', field);
    const disabled = getIsAdgroupDisabledEdit('adgroupAgentInfo', record);

    return (
        <Fragment>
            {
                isShowColumn
                    ? (
                        <>
                            <div className="column-cell-flex adgroup-list-agent-url-column">
                                {
                                    text ? (
                                        <Popover content={disabledTip}>
                                            <div className={`adgroup-list-agent-url-column-text${urlClassNameSuffix}`}>
                                                <img src={agentHeadImage} alt={agentName} />
                                                <span className="page-name">{agentName}</span>
                                                <IconQrcode onClick={onClickPreview} className="qrcode" />
                                            </div>
                                        </Popover>
                                    ) : '未设置'
                                }
                                <span>
                                    <Tooltip title={disabled}>
                                        <IconEdit
                                            className={`inline-operation-icon ${
                                                disabled ? 'inline-operation-icon-disabled' : ''}`}
                                            onClick={disabled ? noop : () => openEditor('inline', field, adgroupId)}
                                        />
                                    </Tooltip>
                                </span>
                            </div>
                        </>
                    )
                    : (
                        <Popover content={disabledTip}>
                            <span className={urlClassNameSuffix}>-</span>
                        </Popover>
                    )
            }
        </Fragment>
    );
};

export default ({columnName}) => {
    return {
        render: (text, record, row) => {
            const urlProps = {
                record,
                field: columnName,
                text
            };
            return handleSummary(<AgentUrl {...urlProps} />, {field: columnName, record, row});
        },
        filters: {
            maxLen: DESTINATION_URL_MAX_LENGTH_IN_BYTES
        }
    };
};
