/*
 * @file: 应用商店直投开关
 * @author: ji<PERSON><PERSON>@baidu.com
 */

import {memo, useState} from 'react';
import {Switch} from '@baidu/one-ui';
import {getRecordRender} from 'commonLibs/tableList/utils';
import {createError, displayError} from 'commonLibs/utils/materialList/error';
import MARKET_TARGET from 'commonLibs/config/marketTarget';
import {updateListDataApi} from 'app/api/adgroup/update';
import {useSuccessCallback} from 'app/containers/adgroups/hooks/useSuccessCallback';

export const APP_SHOP_DIRECT_STATUS_FIELD = 'appShopDirectStatus';

const SWITCH_STATUS = {
    ON: 1,
    OFF: 0
};

const AppShopDirectStatusEditor = memo(props => {
    const {record} = props;
    const {adgroupId, appShopDirectStatus, marketingTargetId} = record;
    const [{onSuccessCallback}] = useSuccessCallback();
    const [loading, setLoading] = useState(false);

    if (marketingTargetId !== MARKET_TARGET.APP) {
        return '-';
    }

    const onSave = async checked => {
        setLoading(true);
        try {
            const data = await updateListDataApi(
                adgroupId,
                {appShopDirectStatus: checked ? SWITCH_STATUS.ON : SWITCH_STATUS.OFF}
            );
            onSuccessCallback(data);
        }
        catch (err) {
            const formatError = createError(err);
            formatError.optName = '修改应用商店直投';
            displayError(formatError);
        }
        finally {
            setLoading(false);
        }
    };

    const switchProps = {
        checked: appShopDirectStatus,
        loading,
        onChange: onSave
    };
    return <Switch {...switchProps} />;
});

const autoTargetStatusOptions = [{
    label: '开启',
    value: SWITCH_STATUS.ON
}, {
    label: '关闭',
    value: SWITCH_STATUS.OFF
}];

export default getRecordRender(
    AppShopDirectStatusEditor,
    // 筛选这里后端未放开，所以前端不展示，加的话后端放开就可以（需要考虑营销目标，直接筛选关闭可能会出现非应用推广的）
    {filters: {options: autoTargetStatusOptions}}
);
