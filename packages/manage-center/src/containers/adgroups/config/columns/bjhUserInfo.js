import {handleSummary} from 'commonLibs/tableList/utils';
import './style.less';

function BjhUserInfo({value = {}}) {
    const {bjhUserId, bjhUserName, bjhLogo} = value;
    if (!bjhUserId) {
        return '-';
    }
    return (
        <div className="adgroup-bjh-user-info">
            <img className="adgroup-bjh-user-info-img" src={bjhLogo} />
            <div className="adgroup-bjh-user-info-text">{bjhUserName}</div>
        </div>
    );
}

export default params => {
    const {columnName} = params;
    return {
        render: (text, record, row) => {
            const props = {
                record,
                field: columnName,
                value: text
            };
            return handleSummary(<BjhUserInfo {...props} />, {row, field: 'bjhUserInfo', record});
        }
    };
};