import {Tag} from '@baidu/one-ui';
import {handleSummary} from 'commonLibs/tableList/utils';

const ADGROUP_MAX_LEN = 30;

const BrandName = props => {
    const {record} = props;
    if (!record.brandInfo || record.brandInfo.length === 0) {
        return '-';
    }
    const {
        merchantName = '',
        isDefault = false,
        picUrl = '',
        isCompany = false
    } = record.brandInfo && (record.brandInfo[0].auditContent || record.brandInfo[0].content);
    return (
        <div className="adgroup-brand-column">
            {picUrl ? <img src={picUrl} alt="品牌头像" className="brand-icon" /> : null}
            <div className="brand-name">{merchantName}</div>
            {isCompany ? <Tag tipTag="warning" bordered={false} size="small">公司名</Tag> : null}
            {isDefault ? <Tag tipTag="info" size="small" bordered={false}>默认</Tag> : null}
        </div>
    );
};

export default params => {
    const {columnName, columnText} = params;
    return {
        render: (text, record, row) => {
            const nameProps = {
                record, columnName, columnText, text
            };
            return handleSummary(<BrandName {...nameProps} />, {field: columnName, record, row});
        },
        filters: {
            maxLen: ADGROUP_MAX_LEN
        }
    };
};
