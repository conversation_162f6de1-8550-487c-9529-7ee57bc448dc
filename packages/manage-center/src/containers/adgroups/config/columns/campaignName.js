import {isNil} from 'lodash-es';
import {Name} from 'commonLibs/tableList/columns';
import {handleSummary} from 'commonLibs/tableList/utils';
import {useParams, useRouterFormatter} from 'commonLibs/route';
import {FilterCampaignNamePriceStrategy} from 'app/containers/adgroups/filter/camapignNameAndPriceStategy';
import './style.less';

const CamName = props => {
    const {record, columnName} = props;
    const name = record[columnName];
    const {campaignId: routeCampaignId} = useParams();
    const {businessPointId, marketingTargetId, campaignId} = record;
    const formatUrl = useRouterFormatter('@dashboard/adgroups');
    const toUrl = formatUrl({
        campaignId,
        ...(isNil(marketingTargetId) ? {} : {mtId: marketingTargetId}),
        ...(isNil(businessPointId) ? {} : {bizId: businessPointId})
    });
    const nameProps = {
        name,
        canEdit: false,
        isInternal: true,
        toUrl: !routeCampaignId && toUrl
    };
    return <Name {...nameProps} />;
};


export default params => {
    const {columnName} = params;
    return {
        render: (text, record, row) => {
            const nameProps = {
                record, columnName
            };
            if (isNil(record.marketingTargetId)) {
                return <>-</>;
            }
            return handleSummary(<CamName {...nameProps} />, {record, row, field: columnName});
        },
        filters: {
            CustomFilter: FilterCampaignNamePriceStrategy,
            filterType: 'custom',
            detailFilterMap: {
                campaignName: {filterType: 'string'},
                priceStrategy: {filterType: 'custom'}
            }
        }
    };
};
