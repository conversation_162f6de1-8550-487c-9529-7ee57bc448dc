/*
 * @file: segmentRecommendStatus
 * @author: l<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2022-04-01 17:32:33
 */

import {memo} from 'react';
import {Switch, Tooltip} from '@baidu/one-ui';
import {useActionPending} from '@huse/action-pending';
import {getRecordRender} from 'commonLibs/tableList/utils';
import {createError, displayError} from 'commonLibs/utils/materialList/error';
import {updateAdgroupSwitchStatus} from 'app/api/adgroup/update';
import {useSuccessCallback} from 'app/containers/adgroups/hooks/useSuccessCallback';
import {useLog} from 'commonLibs/logger';
import {cancelConfirm, isShowCancelConfirm} from 'app/containers/adgroups/utils/cancelConfirm';
import {OFF} from './adgroupAutoTargetingStatus';
import {getIsAdgroupDisabledEdit} from '../../utils';

export const StatusOptions = [{
    label: '开启',
    value: 0
}, {
    label: '关闭',
    value: 1
}];

const SwitchStatus = memo(function (props) {
    const {
        record,
        field = 'creativeTextOptimizationStatus',
        fieldName = '自动文案优化'
    } = props;
    const {adgroupId, structuredContentIdStrs, productCategoryType} = record;
    const log = useLog();
    const [{onSuccessCallback}] = useSuccessCallback();
    const [updateAdgroup, pendingCount] = useActionPending(updateAdgroupSwitchStatus);


    const onSave = async checked => {
        log('click', {target: 'mod_creativeTextOptimizationStatus'});
        const onOk = async () => {
            try {
                const data = await updateAdgroup({adgroupId, status: checked, field});
                onSuccessCallback(data);
            }
            catch (err) {
                const formatError = createError(err);
                formatError.optName = `修改${fieldName}`;
                displayError(formatError);
            }
        };
        if (isShowCancelConfirm(productCategoryType, structuredContentIdStrs)
            && +checked === OFF
        ) {
            return cancelConfirm({
                contentLabel: fieldName,
                onOk
            });
        }
        onOk();
    };

    const switchProps = {
        checked: record[field],
        loading: !!pendingCount,
        onChange: onSave
    };
    const disabled = getIsAdgroupDisabledEdit('creativeTextOptimizationStatus', record);

    return (
        <Tooltip title={disabled}>
            <Switch {...switchProps} disabled={!!disabled} />
        </Tooltip>
    );
});

export default getRecordRender(
    SwitchStatus,
    {filters: {options: StatusOptions}}
);


export const getTextStatus = ({field, fieldName}) => {
    const SwitchStatusColumn = props => (
        <SwitchStatus
            {...props}
            field={field}
            fieldName={fieldName}
        />
    );
    return getRecordRender(
        SwitchStatusColumn,
        {filters: {options: StatusOptions}}
    );
};