import {useContext, useMemo, Fragment} from 'react';
import {IconEdit, IconCopy} from 'dls-icons-react';
import MARKET_TARGET, {isLiveSubMarket, isBjhSubMarket} from 'commonLibs/config/marketTarget';
import {Toast, Popover, Tooltip} from '@baidu/one-ui';
import {noop} from 'lodash-es';
import {handleSummary} from 'commonLibs/tableList/utils';
import ShadowCard from 'app/components/shadowCard';
import {copyText} from 'commonLibs/utils/copy';
import {getUrlDataAndShadowData} from 'app/utils/getUrl';
import {DESTINATION_URL_MAX_LENGTH_IN_BYTES} from 'app/components/tableEditor/url/config';
import {getShowFinalUrlDetailColumnFlag} from 'app/containers/adgroups/utils/finalUrlDetail';
import {AdgroupEditorContext} from '../../materialQuery';
import {getAdgroupUrlTypeProps} from 'app/containers/adgroups/config/columns/agentUrl-util';
import {getIsAdgroupDisabledEdit} from '../../utils';
import './style.less';

const FinalUrl = ({record, field, text}) => {
    const {adgroupId, subMarketingTargetId, marketingTargetId} = record || {};
    const [url, shadowUrl, urlText] = getUrlDataAndShadowData(record, field);
    const {openEditor} = useContext(AdgroupEditorContext);
    const {disabledTip, urlClassNameSuffix} = getAdgroupUrlTypeProps(record, 'adgroup', field);
    const shadowProps = {
        node: (<span className="url-text multiple-cut">{url}</span>),
        shadowNode: (<span className="url-text multiple-cut">{shadowUrl}</span>)
    };

    const isShowColumn = useMemo(() => {
        return getShowFinalUrlDetailColumnFlag({field, ...record});
    }, [record, field]);

    const canEdit = !isLiveSubMarket(subMarketingTargetId) && !isBjhSubMarket(subMarketingTargetId);

    const copyIconProps = {
        className: 'inline-operation-icon',
        title: '复制',
        style: {},
        onClick: () => {
            try {
                copyText(shadowUrl || text);
                Toast.success({content: '已复制到剪贴板', showCloseIcon: false});
            }
            catch (err) {
                Toast.error({content: err.message, showCloseIcon: false});
            }
        }
    };
    const isB2BMobileUrl = marketingTargetId === MARKET_TARGET.B2B && field === 'mobileFinalUrl';
    const disabled = getIsAdgroupDisabledEdit(field, record);

    return (
        <Fragment>
            {
                isShowColumn
                    ? (
                        <div className="column-cell-flex final-url-column">
                            <Popover content={disabledTip}>
                                <span className={disabledTip ? urlClassNameSuffix : ''}>
                                    {shadowUrl
                                        ? <ShadowCard {...shadowProps} />
                                        : (<span className="url-text multiple-cut">{urlText}</span>)}
                                </span>
                            </Popover>
                            <span>
                                {canEdit && (
                                    <>
                                        <Tooltip title={disabled}>
                                            <IconEdit
                                                className={`inline-operation-icon ${
                                                    disabled ? 'inline-operation-icon-disabled' : ''}`}
                                                onClick={
                                                    disabled
                                                        ? noop
                                                        : () => openEditor(
                                                            'inline',
                                                            field,
                                                            adgroupId,
                                                            {
                                                                containerType: isB2BMobileUrl ? 'drawer' : 'dialog',
                                                                width: isB2BMobileUrl ? 864 : undefined,
                                                                hideDefaultFooter: isB2BMobileUrl
                                                            }
                                                        )
                                                }
                                            />
                                        </Tooltip>
                                        {url || shadowUrl ? <IconCopy {...copyIconProps} /> : null}
                                    </>
                                )}
                            </span>
                        </div>
                    )
                    : '-'
            }
        </Fragment>
    );
};

export default ({columnName}) => {
    return {
        render: (text, record, row) => {
            const urlProps = {
                record,
                field: columnName,
                text
            };
            return handleSummary(<FinalUrl {...urlProps} />, {field: columnName, record, row});
        },
        filters: {
            maxLen: DESTINATION_URL_MAX_LENGTH_IN_BYTES
        }
    };
};
