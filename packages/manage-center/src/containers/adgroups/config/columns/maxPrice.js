import {NumberInput} from '@baidu/one-ui';
import {IconEdit} from 'dls-icons-react';
import {handleSummary} from 'commonLibs/tableList/utils';
import {PopoverEditor, getInlinePopupContainer} from 'commonLibs/tableList/columns';
import {useLog} from 'commonLibs/logger';
import {actionTypes as ACTIONS} from 'app/reducers/adgroups';
import {validatorRules} from 'app/components/editorPanel/batchBid/config';
import {getFilterSmart} from 'app/components/filterSmart';
import {useSuccessCallback} from 'app/containers/adgroups/hooks/useSuccessCallback';
import {request, success, fail} from '../../utils/request';
import './style.less';

export const priceUseCampaignBid = -1;

const MaxPrice = props => {
    const {text, record, field} = props;
    const numberInputProps = {
        width: 140,
        placeholder: record.maxPrice || '',
        min: 0.01,
        max: 999.99,
        errorLocation: 'right',
        location: 'right'
    };
    const log = useLog();
    // 如果出价未返回，则为无出价的单元，展示未设置。 后续pm将升级此单元出价编辑与展示
    const newRecord = {...record};
    if (record.maxPrice === priceUseCampaignBid) {
        numberInputProps.placeholder = '';
        newRecord.maxPrice = '';
    }
    const maxPriceText = text === priceUseCampaignBid ? '未设置' : text;
    const [{onSuccessCallback}] = useSuccessCallback();

    const editorProps = {
        field,
        record: newRecord,
        ACTIONS,
        overlayClassName: 'inline-bid-popover-layer price',
        getPopupContainer: getInlinePopupContainer,
        request: request({...record, field}),
        success: success(field),
        afterSave: onSuccessCallback,
        fail,
        fieldMap: {
            label: '单元点击出价',
            initialValue: text === priceUseCampaignBid ? '' : +text || 0,
            rules: [{
                validator: (rule, value, callback) => {
                    const errorMessage = validatorRules.validate(value, {canAdgroupPriceBeNone: true});
                    if (errorMessage) {
                        return callback(errorMessage);
                    }
                    return callback();
                }
            }],
            item: () => <NumberInput {...numberInputProps} />
        }
    };
    const onClickEdit = () => {
        log('click', {target: 'inline_price'});
    };

    return (
        <div className="inline-editor-cell">
            {maxPriceText}
            <PopoverEditor {...editorProps}>
                <IconEdit className="inline-operation-icon" onClick={onClickEdit} />
            </PopoverEditor>
        </div>
    );
};

// todo maxPrice 单元出价？
export default ({columnName: field} = {}) => {
    return {
        render: (text, record, row) => {
            const priceProps = {
                record,
                row,
                field,
                text
            };
            return getFilterSmart(record, handleSummary((
                <MaxPrice {...priceProps} />
            ), {record, row, field}));
        }
    };
};
