import {handleSummary} from 'commonLibs/tableList/utils';
import './style.less';

function NativeContentInfo({value = []}) {
    const {nativeId, text} = value[0] || {};
    if (!nativeId) {
        return '-';
    }
    return (
        <div className="adgroup-native-content-info">
            <div className="adgroup-native-content-info-text">{text}</div>
            <div className="adgroup-native-content-info-id">内容id：{nativeId}</div>
        </div>
    );
}

export default params => {
    const {columnName} = params;
    return {
        render: (text, record, row) => {
            const props = {
                record,
                field: columnName,
                value: text
            };
            return handleSummary(<NativeContentInfo {...props} />, {row, field: 'nativeContentInfos', record});
        }
    };
};