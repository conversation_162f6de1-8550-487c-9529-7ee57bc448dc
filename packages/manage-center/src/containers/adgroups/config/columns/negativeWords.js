/* eslint-disable max-len */
/*
 * @Author: <EMAIL>
 * @Date: 2022-12-29 14:33:43
 * @Description: 否定关键词
 */
import {memo, useContext} from 'react';
import {Button} from '@baidu/one-ui';
import {Negative} from 'commonLibs/tableList/columns';
import {useLog} from 'commonLibs/logger';
import {isNegativeWordUpgradeUser} from 'commonLibs/utils/getFlag';
import {getNegativeWordsLinkUrl} from 'commonLibs/utils/getKeywordsLinkUrl';
import NegatibeWordsOld, {NegativeWordCountTag} from 'app/components/negativeWords';
import getMtOrBiz from 'app/utils/getMtOrBiz';
import {getDefaultUrl} from 'app/utils/getKeywords';
import {AdgroupEditorContext} from '../../materialQuery';

const NegativeWords = props => {
    const {openEditor} = useContext(AdgroupEditorContext);
    const log = useLog();
    const {record} = props;
    const {negativeKeywordCountConfig, negativeWords = [], exactNegativeWords = [], adgroupId} = record || {};
    const negProps = {
        ...record,
        toUrl: getNegativeWordsLinkUrl(`${getMtOrBiz(record)}/${getDefaultUrl(record.adType, 'negative')}`),
        showEdit: false
    };
    if (!isNegativeWordUpgradeUser()) {
        return (
            <div className="column-cell-flex">
                <NegatibeWordsOld {...props} />
                <NegativeWordCountTag negativeKeywordCountConfig={negativeKeywordCountConfig} />
            </div>
        );
    }
    if (
        !negativeWords.length
        && !exactNegativeWords.length
    ) {
        const onClick = () => {
            openEditor('inline', 'negativeWords', adgroupId);
            log('click', {target: 'inline_negativeWords$start'});
        };
        const buttonProps = {
            type: 'text-strong',
            size: 'small',
            onClick
        };
        return (
            <div className="column-cell-flex">
                <Button {...buttonProps}>未设置</Button>
                <NegativeWordCountTag negativeKeywordCountConfig={negativeKeywordCountConfig} />
            </div>
        );
    }
    return (
        <div className="column-cell-flex">
            <Negative {...negProps} />
            <NegativeWordCountTag negativeKeywordCountConfig={negativeKeywordCountConfig} />
        </div>
    );
};

export default memo(NegativeWords);
