import {PC_PREFER, MOBILE_PREFER, bidPreferFactorLabel} from 'commonLibs/config/bidPrefer';
import {handleSummary} from 'commonLibs/tableList/utils';
import {DialogEditor} from 'commonLibs/tableList/columns';
import {IconEdit} from 'dls-icons-react';
import InlinePriceRatioEditor from 'app/components/inlineEditor/priceRatio/container';
import {ratioConfig} from 'app/config/priceRatio';
import PriceRatioCom from 'app/components/filterSmart';
import {useLog} from 'commonLibs/logger';

export const PRICE_RATIO_TYPE = {
    /**
     * 使用单元出价系数
     */
    PriceRatioTypeAdgroup: 0,
    /**
     * 使用计划出价系数,
     */
    PriceRatioTypeCampaign: -1
};

const camRatioConfig = {
    [PC_PREFER]: 'campaignPriceRatio',
    [MOBILE_PREFER]: 'campaignPcPriceRatio'
};

const PriceCom = props => {
    const {record, row, columnName} = props;
    const {priceRatio, pcPriceRatio, bidPrefer = PC_PREFER} = record;

    const ratio = record.bidPrefer === PC_PREFER ? priceRatio : pcPriceRatio;
    const isCam = ratio === PRICE_RATIO_TYPE.PriceRatioTypeCampaign;
    let name = bidPreferFactorLabel[bidPrefer] || bidPreferFactorLabel[PC_PREFER];
    let obj = isCam ? camRatioConfig : ratioConfig;
    const log = useLog();

    const tipNode = (
        <div className="title">
            设备出价系数
        </div>
    );
    const editorProps = {
        record,
        title: tipNode,
        Content: InlinePriceRatioEditor,
        customFooter: true
    };
    const onClickEdit = () => {
        log('click', {target: 'inline_price_ratio'});
    };
    return handleSummary(
        (
            <div>
                {`${(record[obj[bidPrefer]] || 0).toFixed(2)}(${name})`}
                <span onClick={onClickEdit}>
                    <DialogEditor {...editorProps}>
                        <IconEdit className="inline-operation-icon" />
                    </DialogEditor>
                </span>
            </div>
        ), {
            record,
            row,
            field: columnName
        });
};

export default ({columnName} = {}) => {
    return {
        render: (text, record = {}, row) => {
            const props = {
                Node: PriceCom,
                nodeProps: {record, row, columnName}
            };
            return <PriceRatioCom {...props} />;
        }
    };
};
