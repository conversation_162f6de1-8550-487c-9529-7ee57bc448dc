import {handleSummary} from 'commonLibs/tableList/utils';
import {IconEdit} from 'dls-icons-react';
import {useParams} from 'commonLibs/route';
import {useCallback, useContext} from 'react';
import ALL_ADTYPE from 'commonLibs/config/adType';
import {AdgroupEditorContext} from '../../materialQuery';

const ProductSetName = props => {
    const {record, text} = props;
    const {openEditor} = useContext(AdgroupEditorContext);
    const {campaignId: planId, adgroupId: unitId, adType} = record;
    const {userId} = useParams();
    const editProps = {
        onClick: useCallback(() => {
            if (adType === ALL_ADTYPE.DCA) {
                openEditor('inline', 'productSetName', unitId);
                return;
            }
            window.location.href = `/fc/manage/newCommodity/user/${userId}/plan/${planId}/unit/${unitId}/productSet`;
        }, [userId, planId, unitId])
    };
    return text ? (
        <div>{text}<IconEdit className="inline-operation-icon" {...editProps} /></div>
    ) : <>-</>;
};

export default ({columnName: field} = {}) => {
    return {
        render: (text, record, row) => {
            return handleSummary(
                <ProductSetName record={record} text={text} />,
                {record, row, field}
            );
        }
    };
};
