/*
 * @file: segmentRecommendStatus
 * @author: l<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2022-04-01 17:32:33
 */

import {memo} from 'react';
import {Switch, Tooltip} from '@baidu/one-ui';
import {useActionPending} from '@huse/action-pending';
import {getRecordRender} from 'commonLibs/tableList/utils';
import {useLog} from 'commonLibs/logger';
import {createError, displayError} from 'commonLibs/utils/materialList/error';
import {updateAdgroupSegmentRecommendStatus} from 'app/api/adgroup/update';
import {useSuccessCallback} from 'app/containers/adgroups/hooks/useSuccessCallback';
import {cancelConfirm, isShowCancelConfirm} from 'app/containers/adgroups/utils/cancelConfirm';
import {OFF} from './adgroupAutoTargetingStatus';
import {StatusOptions} from './creativeTextOptimizationStatus';
import {getIsAdgroupDisabledEdit} from '../../utils';

const SegmentRecommendStatusOptionsEditor = memo(function (props) {
    const {record} = props;
    const {adgroupId, segmentRecommendStatus, structuredContentIdStrs, productCategoryType} = record;
    const [{onSuccessCallback}] = useSuccessCallback();
    const log = useLog();
    const [updateAdgroup, pendingCount] = useActionPending(updateAdgroupSegmentRecommendStatus);


    const onSave = async checked => {
        log('click', {target: 'mod_creativeSegmentOptimizationStatus'});
        const onOk = async () => {
            try {
                const data = await updateAdgroup({adgroupId, segmentRecommendStatus: checked});
                onSuccessCallback(data);
            }
            catch (err) {
                const formatError = createError(err);
                formatError.optName = '修改自动图片优化';
                displayError(formatError);
            }
        };
        if (isShowCancelConfirm(productCategoryType, structuredContentIdStrs)
            && +checked === OFF
        ) {
            return cancelConfirm({
                contentLabel: '自动图片优化',
                onOk
            });
        }
        onOk();
    };

    const switchProps = {
        checked: segmentRecommendStatus,
        loading: !!pendingCount,
        onChange: onSave
    };
    const disabled = getIsAdgroupDisabledEdit('segmentRecommendStatus', record);

    return (
        <Tooltip title={disabled}>
            <Switch {...switchProps} disabled={!!disabled} />
        </Tooltip>
    );
});

export default getRecordRender(
    SegmentRecommendStatusOptionsEditor,
    {filters: {options: StatusOptions}}
);
