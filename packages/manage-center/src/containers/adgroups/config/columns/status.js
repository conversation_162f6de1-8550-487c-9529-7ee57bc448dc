import {useContext} from 'react';
import {handleSummary} from 'commonLibs/tableList/utils';
import idType from 'commonLibs/config/idType';
import Status from 'app/components/tableEditor/status/Index';
import {getIsShowAuditIcon} from 'app/containers/adgroups/utils/status';
import useReasons from 'app/hooks/useReasons';
import {getOfflineReasonApi} from 'app/api/adgroup/status';
import {
    options,
    statusKeyMap,
    statusTextMap,
    statusClassNames
} from 'app/containers/adgroups/config/status';
import {AdgroupEditorContext} from '../../materialQuery';


function AdgroupStatus({record}) {
    const {status, campaignId, adgroupId} = record;
    const {inlineSaveAdgroupStatus} = useContext(AdgroupEditorContext);
    const offlineReasonParams = useReasons('单元', campaignId); // 通用的获得原因的参数
    const isShowAuditIcon = getIsShowAuditIcon(status);
    const statusProps = {
        primaryId: adgroupId,
        record,
        isShowAuditIcon,
        onInlineMaterialSave: inlineSaveAdgroupStatus,
        getOfflineReasonApi,
        offlineReasonParams,
        statusConfig: {
            statusKeyMap,
            statusTextMap,
            statusClassNames
        },
        levelConfig: {
            idType: idType.UNIT_LEVEL,
            ids: [adgroupId]
        }
    };
    return (<Status {...statusProps} />);
}

export default () => {
    return {
        render: (text, record, row) => handleSummary(<AdgroupStatus record={record} />, {row, field: 'status', record}),
        filters: {
            options
        }
    };
};
