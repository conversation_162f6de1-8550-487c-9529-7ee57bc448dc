import {useContext} from 'react';
import {handleSummary} from 'commonLibs/tableList/utils';
import ALL_ADTYPE from 'commonLibs/config/adType';
import {IconEdit} from 'dls-icons-react';
import {AdgroupEditorContext} from '../../materialQuery';

function StoreName({record, field}) {
    const {adgroupId} = record || {};
    const {openEditor} = useContext(AdgroupEditorContext);

    return (
        <>
            {record.storeName || '-'}
            {
                record.adType === ALL_ADTYPE.NEW_STORE && (
                    <span>
                        <IconEdit
                            className="inline-operation-icon"
                            onClick={() => openEditor('inline', field, adgroupId)}
                        />
                    </span>
                )
            }
        </>
    );
}

export default params => {
    const {columnName} = params;
    return {
        render: (text, record, row) => {
            const props = {
                record,
                field: columnName,
                text
            };
            return handleSummary(<StoreName {...props} />, {row, field: 'storeName', record});
        }
    };
};