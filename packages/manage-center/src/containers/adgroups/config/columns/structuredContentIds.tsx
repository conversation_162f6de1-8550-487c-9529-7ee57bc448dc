import {useContext} from 'react';
import {IconEdit} from 'dls-icons-react';
import {CPQL} from 'commonLibs/config/marketTarget';
import {handleSummary} from 'commonLibs/tableList/utils';
import {productCategoryTypeEnum} from 'commonLibs/config/enumEntry';
import {getDefaultProductCategoryType} from 'commonLibs/components/selectCard/product/config';
import sendMonitor from 'commonLibs/utils/sendHm';
import {AdgroupEditorContext} from '../../materialQuery';


const Product = props => {
    const {record} = props;
    const {adgroupId, marketingTargetId} = record || {};
    const {
        productCategoryType: productCategoryType_,
        structuredContentIds = [],
        structuredContentIdStrs = []
    } = record;
    const productCategoryType = productCategoryType_ || getDefaultProductCategoryType();
    const {openEditor} = useContext(AdgroupEditorContext);

    const isLawIndustry = productCategoryType === productCategoryTypeEnum.LAW;
    const field = isLawIndustry ? 'structuredLawProductIds' : 'structuredContentIds';
    const finialContentIds = isLawIndustry ? structuredContentIdStrs : structuredContentIds;

    if (marketingTargetId === CPQL && !!productCategoryType) {
        const onIconClick = () => {
            openEditor('inline', field, adgroupId, {
                containerType: 'drawer',
                width: 844,
                hideDefaultFooter: !isLawIndustry,
                hideDefaultTitle: !isLawIndustry,
                type: 'basic'
            });
            sendMonitor('click', {
                type: productCategoryType,
                level: 'adgroup_list',
                source: 'struct_product',
                item: 'inline_entry'
            });
        };
        return (
            <>
                {finialContentIds.length ? '已选择' : '未选择'}
                <IconEdit
                    className="inline-operation-icon"
                    onClick={onIconClick}
                />
            </>
        );
    }

    return <>-</>;
};

const structuredContentIdsOptions = [{
    label: '已选择',
    value: 1
}, {
    label: '未选择',
    value: 0
}];

export default () => {
    return {
        render: (text, record, row) => {
            return handleSummary(<Product record={record} />);
        },
        filters: {
            options: structuredContentIdsOptions,
            filterType: 'enum'
        }
    };
};
