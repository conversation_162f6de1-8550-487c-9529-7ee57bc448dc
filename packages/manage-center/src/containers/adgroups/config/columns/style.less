.inline-bid-popover-layer {
    width: 300px;
    &.price {
        width: 320px;
    }
}

.inline-editor-cell {
    position: relative;
}

.adgroup-brand-column {
    display: flex;
    align-items: center;
    .brand-icon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border: 1px solid #e2e6f0;
    }
    .brand-name {
        padding-left: 4px;
        padding-right: 16px;
    }
    .inline-operation-icon {
        margin-left: 4px;
        visibility: hidden;
    }
    .inline-operation-icon:hover {
        visibility: visible;
    }
}
.adgroup-brand-inline-edit {
    .brave-the-winds-common-libs-save-footer {
        margin-top: 32px;
    }
}
.adgroup-list-agent-url-column {
    &-text {
        display: inline-flex;
        height: 26px;
        padding: 0 @dls-padding-unit;
        align-items: center;
        gap: @dls-padding-unit;
        border-radius: @dls-border-radius-3;
        background: rgba(109, 159, 247, 0.07);

        img {
            width: @dls-height-unit * 4;
            height: @dls-height-unit * 4;
            border-radius: @dls-height-unit * 2;
        }
        .page-name {
            display: inline-block;
            max-width: 114px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .dls-icon {
            cursor: pointer;
            color: @dls-color-brand-7;
        }
    }
    &-project-tip {
        color: @dls-color-gray-7;
        margin-top: @dls-padding-unit;
    }
}

.adgroup-native-content-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    &-text {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    &-id {
        color: @dls-color-gray-7;
    }
}

.adgroup-bjh-user-info {
    display: flex;
    align-items: center;
    gap: 4px;

    &-img {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border: 0.36px solid var(--cos-color-border-tiny, #dedfe0);
    }
}
