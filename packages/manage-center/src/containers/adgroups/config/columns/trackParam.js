import {useContext, Fragment, useMemo} from 'react';
import {partial} from 'lodash-es';
import {IconCopy} from 'dls-icons-react';
import {Toast, Popover} from '@baidu/one-ui';
import {isLiveSubMarket, isBjhSubMarket} from 'commonLibs/config/marketTarget';
import InlinePopoverEditor, {DefaultEditTrigger} from 'commonLibs/materialList/inlinePopoverEditor';
import inlineTrackParamEditor from 'app/components/tableEditor/url/inlineTrackParamEditor';
import {handleSummary} from 'commonLibs/tableList/utils';
import ShadowCard from 'app/components/shadowCard';
import {copyText} from 'commonLibs/utils/copy';
import {getUrlDataAndShadowData} from 'app/utils/getUrl';
import {getShowFinalUrlDetailColumnFlag} from 'app/containers/adgroups/utils/finalUrlDetail';
import {getAdgroupUrlTypeProps} from 'app/containers/adgroups/config/columns/agentUrl-util';
import {AdgroupEditorContext} from '../../materialQuery';
import {getIsAdgroupDisabledEdit} from '../../utils';

const TrackParam = ({record, field, text}) => {
    const {adgroupId, subMarketingTargetId} = record || {};
    const [trackParam, shadowTrackParam, trackParamText] = getUrlDataAndShadowData(record, field);
    const {inlineSaveUrlTrackParam} = useContext(AdgroupEditorContext);
    const shadowProps = {
        node: (<span className="url-text multiple-cut">{trackParam}</span>),
        shadowNode: (<span className="url-text multiple-cut">{shadowTrackParam}</span>)
    };

    const isShowColumn = useMemo(() => {
        return getShowFinalUrlDetailColumnFlag({field, ...record});
    }, [record, field]);

    const copyIconProps = {
        className: 'inline-operation-icon',
        title: '复制',
        style: {},
        onClick: () => {
            try {
                copyText(shadowTrackParam || text);
                Toast.success({content: '已复制到剪贴板', showCloseIcon: false});
            }
            catch (err) {
                Toast.error({content: err.message, showCloseIcon: false});
            }
        }
    };

    const popoverProps = {
        renderEditor: inlineTrackParamEditor,
        onSave: partial(inlineSaveUrlTrackParam, adgroupId, partial.placeholder),
        level: '单元',
        field,
        record
    };

    const {disabledTip, urlClassNameSuffix} = getAdgroupUrlTypeProps(record, 'adgroup', field);
    const canEdit = !isLiveSubMarket(subMarketingTargetId) && !isBjhSubMarket(subMarketingTargetId);

    return (
        <Fragment>
            {
                isShowColumn
                    ? (
                        <div className="column-cell-flex">
                            <Popover content={disabledTip}>
                                <span className={disabledTip ? urlClassNameSuffix : ''}>
                                    {
                                        shadowTrackParam
                                            ? <ShadowCard {...shadowProps} />
                                            : (<span className="url-text multiple-cut">{trackParamText}</span>)
                                    }
                                </span>
                            </Popover>
                            <span>
                                {
                                    canEdit && (
                                        <>
                                            <InlinePopoverEditor {...popoverProps}>
                                                <DefaultEditTrigger
                                                    disabled={getIsAdgroupDisabledEdit(field, record)}
                                                />
                                            </InlinePopoverEditor>
                                            {trackParam || shadowTrackParam ? <IconCopy {...copyIconProps} /> : null}
                                        </>
                                    )
                                }
                            </span>
                        </div>
                    )
                    : '-'
            }
        </Fragment>
    );
};

export default ({columnName}) => {
    return {
        render: (text, record, row) => {
            const trackParamProps = {
                record,
                field: columnName,
                text
            };
            return handleSummary(<TrackParam {...trackParamProps} />, {field: columnName, record, row});
        }
    };
};