import {useContext, Fragment, useMemo} from 'react';
import {partial} from 'lodash-es';
import {IconCopy} from 'dls-icons-react';
import {Toast, Popover} from '@baidu/one-ui';
import {isLiveSubMarket, isBjhSubMarket} from 'commonLibs/config/marketTarget';
import InlinePopoverEditor, {DefaultEditTrigger} from 'commonLibs/materialList/inlinePopoverEditor';
import InlineTrackTemplateEditor from 'app/components/tableEditor/url/inlineTrackTemplateEditor';
import {handleSummary} from 'commonLibs/tableList/utils';
import ShadowCard from 'app/components/shadowCard';
import {copyText} from 'commonLibs/utils/copy';
import {getUrlDataAndShadowData} from 'app/utils/getUrl';
import {getShowFinalUrlDetailColumnFlag} from 'app/containers/adgroups/utils/finalUrlDetail';
import {getAdgroupUrlTypeProps} from 'app/containers/adgroups/config/columns/agentUrl-util';
import LiveSenceTips from 'app/components/liveSenceTips';
import {AdgroupEditorContext} from '../../materialQuery';
import {getIsAdgroupDisabledEdit} from '../../utils';


const TrackTemplate = ({record, field, text}) => {
    const {adgroupId, subMarketingTargetId} = record || {};
    const [trackTemplate, shadowTrackTemplate] = getUrlDataAndShadowData(record, field);
    const {inlineSaveUrlTrackTemplate} = useContext(AdgroupEditorContext);
    const shadowProps = {
        node: (<span className="url-text multiple-cut">{trackTemplate}</span>),
        shadowNode: (<span className="url-text multiple-cut">{shadowTrackTemplate}</span>)
    };

    const isShowColumn = useMemo(() => {
        return getShowFinalUrlDetailColumnFlag({field, ...record});
    }, [record, field]);

    const copyIconProps = {
        className: 'inline-operation-icon',
        title: '复制',
        style: {},
        onClick: () => {
            try {
                copyText(shadowTrackTemplate || text);
                Toast.success({content: '已复制到剪贴板', showCloseIcon: false});
            }
            catch (err) {
                Toast.error({content: err.message, showCloseIcon: false});
            }
        }
    };

    const popoverProps = {
        renderEditor: InlineTrackTemplateEditor,
        onSave: partial(inlineSaveUrlTrackTemplate, adgroupId, partial.placeholder),
        level: '单元',
        field,
        record
    };

    const {disabledTip, urlClassNameSuffix} = getAdgroupUrlTypeProps(record, 'adgroup', field);
    const canEdit = !isLiveSubMarket(subMarketingTargetId) && !isBjhSubMarket(subMarketingTargetId);
    if (isLiveSubMarket(record.subMarketingTargetId)) {
        return <LiveSenceTips />;
    }

    return (
        <Fragment>
            {
                isShowColumn
                    ? (
                        <div className="column-cell-flex">
                            <Popover content={disabledTip}>
                                <span className={disabledTip ? urlClassNameSuffix : ''}>
                                    {
                                        shadowTrackTemplate
                                            ? <ShadowCard {...shadowProps} />
                                            : (<span className="url-text multiple-cut">{trackTemplate || '未设置'}</span>)
                                    }
                                </span>
                            </Popover>
                            <span>
                                {
                                    canEdit && (
                                        <InlinePopoverEditor {...popoverProps}>
                                            <DefaultEditTrigger disabled={getIsAdgroupDisabledEdit(field, record)} />
                                        </InlinePopoverEditor>
                                    )
                                }
                                {trackTemplate || shadowTrackTemplate ? <IconCopy {...copyIconProps} /> : null}
                            </span>
                        </div>
                    )
                    : '-'
            }
        </Fragment>
    );
};

export default ({columnName}) => {
    return {
        render: (text, record, row) => {
            const trackParamProps = {
                record,
                field: columnName,
                text
            };
            return handleSummary(<TrackTemplate {...trackParamProps} />, {field: columnName, record, row});
        }
    };
};
