import {request} from '@baidu/winds-ajax';

export const getCampaignInfo = async campaignId => {
    const data = await request({
        path: 'thunder/GET/CampaignService/getCampaign',
        params: {
            campaignFields: ['campaignName', 'campaignId', 'equipmentType', 'marketingTargetId'],
            campaignIds: [campaignId]
        }
    });
    return data && data.length > 0 ? data?.[0] : {};
};