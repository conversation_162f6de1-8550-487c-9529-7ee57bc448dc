/*
 * @file: 广告链接类型
 * @author: <EMAIL>
 * @Date: 2024-11-14
 */

import {forwardRef, useImperativeHandle, useMemo} from 'react';
import {Alert} from '@baidu/light-ai-react';
import {isEmpty, uniqBy, pick} from 'lodash-es';
import {createReactiveData} from '@baidu/react-formulator';
import {useResource} from 'commonLibs/magicSuspenseBoundary';
import {useUserInfo} from 'commonLibs/context/sharedInfo';
import {useRequest} from '@huse/request';
import {FLOW_SCOPE} from 'commonLibs/config/ocpc';
import {useRequestAgentAgentUrlList, AdgroupUrlTypeEnum, isAgentUrlType} from 'commonLibs/components/commonEntry';
import {isUnitAgentUrlUser, isCannotUseAgentUrlRegUser} from 'commonLibs/utils/getFlag';
import {useAdgroupForm} from 'app/containers/adMain/adgroup/hooks';
import {
    createError,
    displayError,
    displayErrorForFormkit,
    displayPartialError
} from 'commonLibs/utils/materialList/error';
import {CPQL, isLiveSubMarket} from 'commonLibs/config/marketTarget';
import {useControl} from 'commonLibs/hooks/externalControl';
import {adgroupUrlParams} from 'app/containers/adMain/adgroup/utils/generateParams';
import {CampaignInfoProvider} from 'app/containers/adMain/adgroup/context';
import {
    initialAdgroupForm,
    getAdgroupUrlFormConfig,
    getInitialData
} from './config';
import {getCampaignInfo} from './api';


interface IAdgroupRow {
    adgroupUrlType: AdgroupUrlTypeEnum;
}

const InlineForm = forwardRef((props, ref) => {
    const {
        adgroupInfo, wrappedComponentRef, isShowUseAgentUrlField,
        pending, pageList, isShowAdgroupUrlType, isDisabledAgentReg,
        isNormalUrlTypeDisabled, isAgentUrlTypeDisabled, normalUrlTypeTip, agentCreateUrl
    } = props;
    const initialData = getInitialData(adgroupInfo);
    const [
        {config}
    ] = useResource(initialAdgroupForm, {
        ...adgroupInfo,
        pending, pageList,
        getFormConfigFn: getAdgroupUrlFormConfig,
        isShowUseAgentUrlField,
        isShowAdgroupUrlType, isDisabledAgentReg,
        isNormalUrlTypeDisabled, isAgentUrlTypeDisabled, normalUrlTypeTip, agentCreateUrl,
        isNeedBlur: false
    });

    const defaultVerificationData = useMemo(() => createReactiveData({}), []);

    const [
        {Form, FormInstance},
        {validateFields, setFieldsError}
    ] = useAdgroupForm(config, initialData, defaultVerificationData);

    useImperativeHandle(ref, () => ({setFieldsError, validateFields}));
    useImperativeHandle(wrappedComponentRef, () => ({setFieldsError, validateFields}));

    return (
        <FormInstance>
            <Form className="use-rf-preset-form-ui adgroup-form-area" />
        </FormInstance>
    );
});

function useFormInitProps(props) {
    const {marketingTargetId, field} = props;
    const {userId} = useUserInfo();
    const {pending, pageList} = useRequestAgentAgentUrlList();
    const isDisabledAgentReg = useMemo(
        () => marketingTargetId === CPQL && isCannotUseAgentUrlRegUser(),
        [marketingTargetId]
    ); // 落地页链接是否禁选商家智能体类型Url

    const isAgentUrlTypeDisabled = useMemo(
        () => isEmpty(pageList) && !pending,
        [pending, pageList]
    );

    return {
        isDisabledAgentReg,
        pending,
        pageList,
        isShowAdgroupUrlType: isUnitAgentUrlUser() && field !== 'adgroupAgentUrl', // 修改商家智能体而不是广告链接类型时，需要隐藏广告链接类型表单项
        isShowUseAgentUrlField: field === 'adgroupAgentUrl', // 修改广告链接类型而不是商家智能体时，展示“广告链接类型设置为商家智能体”表单项
        isAgentUrlTypeDisabled,
        isNormalUrlTypeDisabled: field === 'adgroupAgentUrl',
        agentCreateUrl: `https://aiagent.baidu.com/mbot/user/${userId}/knowledge?ucUserId=${userId}`
    };
}

const defaultCampaignInfo = {
    equipmentType: FLOW_SCOPE.TOTAL
};

// 行内编辑广告链接类型
export const InlineAdgroupUrlTypeEditor = forwardRef(
    (props, ref) => {
        const {inlineSaveAdgroupUrlType, currentId, getMaterialById, field, wrappedComponentRef} = props;
        const initialMaterialInfo = getMaterialById(currentId) ?? {};
        const {marketingTargetId, campaignId} = initialMaterialInfo;
        const {data: campaignInfo = defaultCampaignInfo} = useRequest(getCampaignInfo, campaignId);
        const formProps_ = useFormInitProps({
            marketingTargetId, field, projectAgentUrl: initialMaterialInfo.projectAgentUrl
        });
        const formProps = {
            ...props,
            ...formProps_,
            adgroupInfo: {...initialMaterialInfo, ...campaignInfo}
        };

        const [Form, formMethods] = useControl(InlineForm);
        const onSave = async () => {
            const values = await formMethods.validateFields();
            const saveApi = inlineSaveAdgroupUrlType;
            let data;
            try {
                const payload = {
                    adgroupType: {}
                };
                adgroupUrlParams(
                    {marketingTargetId},
                    values,
                    payload
                );
                data = await saveApi(currentId, payload.adgroupType);
            }
            catch (error) {
                const err = createError(error);
                err.optName = '修改广告链接类型';
                displayErrorForFormkit(err, values, formMethods);
                throw error;
            }
            return data;
        };

        useImperativeHandle(wrappedComponentRef, () => ({onSave}));
        useImperativeHandle(ref, () => ({onSave}));

        return (
            <CampaignInfoProvider value={campaignInfo}>
                <Form {...formProps} ref={ref} />
            </CampaignInfoProvider>
        );
    }
);

// 批量修改广告链接类型
function BatchAdgroupUrlTypeEditor_(props, ref) {
    const {batchSaveAdgroupAgentUrl, field, wrappedComponentRef, getMaterialById, getSelectedInfo} = props;
    const marketingTargetId = CPQL;
    const formProps_ = useFormInitProps({marketingTargetId, field});
    const formProps = {
        ...props,
        ...formProps_,
        adgroupInfo: {
            marketingTargetId,
            adgroupUrlType: AdgroupUrlTypeEnum.normal,
            ...defaultCampaignInfo
        }
    };

    const [Form, formMethods] = useControl(InlineForm);
    const onSave = async () => {
        const values = await formMethods.validateFields();
        let data;
        try {
            const params = adgroupUrlParams({marketingTargetId}, values, {adgroupType: {}});
            data = await batchSaveAdgroupAgentUrl({items: params.adgroupType});
        }
        catch (error) {
            const err = createError(error);
            err.optName = '修改广告链接类型';
            displayPartialError(err);
            throw error;
        }
        return data;
    };

    useImperativeHandle(wrappedComponentRef, () => ({onSave}));
    useImperativeHandle(ref, () => ({onSave}));

    const {isCheckAll, selectedIds} = getSelectedInfo();
    const disabledUrlTypeByRotating = !isCheckAll && selectedIds.some(id => getMaterialById(id)?.rotatingCycStatus);
    const disabledUrlTypeByLive = selectedIds.some(
        id => isLiveSubMarket(getMaterialById(id)?.subMarketingTargetId)
    );
    const tip = disabledUrlTypeByRotating
        ? '智能体轮值生效中的单元，不支持修改广告链接类型'
        : (disabledUrlTypeByLive ? '直播场景不支持修改广告链接' : '');

    return (
        <div style={{display: 'flex', flexDirection: 'column', gap: 12}}>
            <Form {...formProps} />
            {tip && <Alert>{tip}</Alert>}
        </div>
    );
}

export const BatchAdgroupUrlTypeEditor = forwardRef(BatchAdgroupUrlTypeEditor_);

// 行内编辑商家智能体
export const InlineAdgroupAgentUrlEditor = forwardRef(
    (props, ref) => {
        const {inlineSaveAdgroupAgentUrl, currentId, getMaterialById, field, wrappedComponentRef} = props;
        const initialMaterialInfo = getMaterialById(currentId) ?? {};
        const {marketingTargetId, campaignId} = initialMaterialInfo;
        const {data: campaignInfo = defaultCampaignInfo} = useRequest(getCampaignInfo, campaignId);
        const formProps_ = useFormInitProps({
            marketingTargetId, field, projectAgentUrl: initialMaterialInfo.projectAgentUrl
        });
        const formProps = {
            ...props,
            ...formProps_,
            adgroupInfo: {
                ...initialMaterialInfo,
                ...campaignInfo,
                adgroupUrlType: AdgroupUrlTypeEnum.agent,
                useAgentUrl: isAgentUrlType(initialMaterialInfo.adgroupUrlType)
            }
        };

        if (initialMaterialInfo.adgroupAgentInfo) {
            const agentList = [
                {
                    ...initialMaterialInfo.adgroupAgentInfo,
                    agentUrl: initialMaterialInfo?.adgroupAgentUrl
                },
                ...formProps.pageList].filter(i => i?.agentId && i?.agentUrl);
            formProps.pageList = uniqBy(agentList, 'agentId');
        }

        const [Form, formMethods] = useControl(InlineForm);
        const onSave = async () => {
            const values = await formMethods.validateFields();
            const saveApi = inlineSaveAdgroupAgentUrl;
            let data;
            try {
                const payload = {
                    adgroupType: {}
                };
                adgroupUrlParams(
                    {marketingTargetId},
                    {...values, ...pick(formProps_, ['isShowUseAgentUrlField'])},
                    payload
                );
                data = await saveApi(
                    currentId,
                    payload.adgroupType
                );
            }
            catch (error) {
                const err = createError(error);
                err.optName = '商家智能体';
                displayError(err);
                throw error;
            }
            return data;
        };

        useImperativeHandle(wrappedComponentRef, () => ({onSave}));
        useImperativeHandle(ref, () => ({onSave}));

        return (
            <CampaignInfoProvider value={campaignInfo}>
                <Form {...formProps} ref={ref} />
            </CampaignInfoProvider>
        );
    }
);

const EMPTY_LIST: IAdgroupRow[] = [];
// 批量编辑商家智能体
export const BatchAdgroupAgentUrlEditor = forwardRef(
    (props, ref) => {
        const {batchSaveAdgroupAgentUrl, field, wrappedComponentRef,
            getMaterialById, getSelectedInfo, initialValue
        } = props;
        const marketingTargetId = CPQL;
        const initialMaterialInfo = {
            marketingTargetId,
            adgroupUrlType: AdgroupUrlTypeEnum.agent
        };
        const {selectedIds = EMPTY_LIST, isCheckAll} = getSelectedInfo();
        const selectedInfos = useMemo(() => selectedIds.map(id => getMaterialById(id)), [selectedIds, getMaterialById]);
        const formProps_ = useFormInitProps({marketingTargetId: CPQL, field});
        const formProps = {
            ...props,
            ...formProps_,
            adgroupInfo: {
                ...initialMaterialInfo,
                ...defaultCampaignInfo,
                useAgentUrl: isCheckAll ? false : !isEmpty(selectedInfos) && selectedInfos.every(
                    (info: IAdgroupRow) => isAgentUrlType(info.adgroupUrlType)
                ),
                adgroupAgentUrl: initialValue?.adgroupAgentUrl
            }
        };
        const disabledUrlTypeByLive = selectedIds.some(
            id => isLiveSubMarket(getMaterialById(id)?.subMarketingTargetId)
        );
        const tip = disabledUrlTypeByLive ? '直播场景不支持修改商家智能体' : '';

        const [Form, formMethods] = useControl(InlineForm);
        const onSave = async () => {
            const values = await formMethods.validateFields();
            let data;
            try {
                const payload = {
                    adgroupType: {}
                };
                adgroupUrlParams(
                    {marketingTargetId},
                    {...values, ...pick(formProps_, ['isShowUseAgentUrlField'])},
                    payload
                );
                data = await batchSaveAdgroupAgentUrl({
                    items: payload.adgroupType
                });
            }
            catch (error) {
                const err = createError(error);
                err.optName = '修改商家智能体';
                displayPartialError(err);
                throw error;
            }
            return data;
        };

        useImperativeHandle(wrappedComponentRef, () => ({onSave}));
        useImperativeHandle(ref, () => ({onSave}));

        return (
            <div>
                <Form {...formProps} ref={ref} />
                {tip && <Alert>{tip}</Alert>}
            </div>
        );
    }
);
