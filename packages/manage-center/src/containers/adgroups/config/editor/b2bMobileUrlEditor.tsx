/*
 * @file: 爱采购url编辑器
 * <AUTHOR>
 * @date Tue May 02 2023
 */
import {
    useQuickForm, QuickFormProps,
    useReactiveData, useFormContext,
    useFieldValue
} from '@baidu/react-formulator';
import {B2BPageInfoInput} from 'app/containers/adMain/adgroup/fields/B2BMobileUrl';
import SaveButton from 'commonLibs/components/saveButton';
import {ForwardRefRenderFunction, forwardRef, useCallback, useMemo, useState} from 'react';
import {Button, Table, TableProps} from '@baidu/one-ui';
import {pageTypeNameMap} from 'app/containers/adMain/adgroup/fields/B2BMobileUrl/config';
import {promisifyConfirm} from 'commonLibs/hooks/dialog';

interface B2BUrlInputProps {
    value: string;
    onChange: (value: string, pageInfo: PageInfo) => void;
    promotionScene: number;
    campaignTransTypes: number[];
};

type PageInfo = Record<string, any>;
function B2BUrlInput({value, onChange, ...props}: B2BUrlInputProps) {

    const [pageInfo, setPageInfo] = useState<PageInfo>(() => {
        return value ? {onlineUrl: value} : undefined;
    });

    const _onChange = (pageInfo: PageInfo) => {
        onChange(pageInfo?.onlineUrl || '', pageInfo);
        setPageInfo(pageInfo);
    };
    return <B2BPageInfoInput onChange={_onChange} value={pageInfo} {...props} />;
}


interface FormData {
    mobileFinalUrl: string;
};


interface B2bMobileUrlEditorProps {
    initialValue: string;
    promotionScene?: number;
    campaignTransTypes?: number[];
    adgroupList?: Array<{
        adgroupName: string;
        campaignTransTypes: number[];
    }>;
    onOk: () => void;
    onCancel: () => void;
    isSaving: boolean;
};



interface B2bMobileUrlEditorRef {
    validateFields: () => Promise<FormData>;
}

const B2BMobileUrlEditor_: ForwardRefRenderFunction<B2bMobileUrlEditorRef, B2bMobileUrlEditorProps> = ({
    initialValue,
    promotionScene,
    campaignTransTypes,
    adgroupList = [],
    onOk,
    onCancel,
    isSaving
}, ref) => {
    const [Form] = useQuickForm(ref);

    const [formData] = useReactiveData({
        mobileFinalUrl: initialValue
    });

    const [localPageInfo, setLocalPageInfo] = useState<PageInfo | null>(null);

    const onChange = useCallback((v: string, pageInfo: PageInfo) => {
        setLocalPageInfo(pageInfo);
    }, []);

    const formProps = useMemo(() => {
        const props: QuickFormProps<FormData> = {
            config: {
                fields: [
                    {
                        label: null,
                        field: 'mobileFinalUrl',
                        use: [B2BUrlInput, {promotionScene, campaignTransTypes, onChange}]
                    }
                ]
            },
            data: formData as unknown as FormData
        };
        return props;
    }, [formData, promotionScene, campaignTransTypes, onChange]);


    // 当已选择的落地页ctList与单元的转化类型没有交集时，需要列出所有单元的名称
    const notMatchAdgroupNames = useMemo(() => {
        const ctList = localPageInfo?.ctList || [];
        // 如果没有选择落地页（也可能是第一次进入带入的，带入时只有url没有转化类型），不需要校验
        if (!ctList.length) {
            return [];
        }

        const notMatchAdgroupNames = adgroupList.filter(item => {
            // 计划如果没有转化信息，则说明是支持所有转化类型的
            const isSupportAllTypes = !item.campaignTransTypes || !item.campaignTransTypes.length;
            return !isSupportAllTypes && !ctList.some((ct: number) => item.campaignTransTypes.includes(ct));
        }).map(item => ({adgroupName: item.adgroupName}));
        return notMatchAdgroupNames;
    }, [localPageInfo, adgroupList]);

    const onSaveClick = async () => {
        if (notMatchAdgroupNames.length) {

            const tableProps: TableProps = {
                columns: [
                    {
                        title: '单元名称',
                        dataIndex: 'adgroupName',
                        key: 'adgroupName',
                        width: 'auto' // 不加的话会有横向滚动条
                    }
                ],
                dataSource: notMatchAdgroupNames,
                pagination: false,
                updateWidthChange: true,
                scroll: {
                    y: 300
                },
                style: {
                    width: 'auto',
                    marginTop: 8
                }
            };
            const content = (
                <div>
                    <div>以下单元所属计划的优化目标与落地页组件不匹配，可能会影响投放效果</div>
                    <Table {...tableProps} />
                </div>
            );
            const isConfirm = await promisifyConfirm({
                title: '提示',
                content,
                okText: '确认提交',
                cancelText: '取消',
                width: 500
            });
            isConfirm && await onOk();
        }
        else {
            await onOk();
        }
    };

    return (
        <Form {...formProps}>
            {/* eslint-disable-next-line @typescript-eslint/no-use-before-define */}
            <Footer
                onSaveClick={onSaveClick}
                onCancel={onCancel}
                isSaving={isSaving}
                localPageInfo={localPageInfo}
            />
        </Form>
    );
};

interface FooterProps {
    onSaveClick: () => Promise<void> | void;
    onCancel: () => void;
    isSaving: boolean;
    localPageInfo: PageInfo | null;
};

function Footer({
    onSaveClick,
    onCancel,
    isSaving,
    localPageInfo
}: FooterProps) {

    return (
        <div className="b2b-pages-drawer-footer">
            <div className="b2b-pages-drawer-footer-inner">
                <SaveButton
                    className="b2b-pages-drawer-footer-btn"
                    onClick={onSaveClick}
                    disabled={!localPageInfo}
                    type="primary"
                    loading={isSaving} // 正常来说onOk是async就够了，但外面用了useRequestCallback导致onOK不是async  所以这里需要loading
                >
                    确定
                </SaveButton>
                <Button className="b2b-pages-drawer-footer-btn" onClick={onCancel}>取消</Button>
                {
                    localPageInfo && (
                        <div className="b2b-pages-drawer-select-msg">
                            已选择 {pageTypeNameMap[localPageInfo.pageType as keyof typeof pageTypeNameMap]} 1项
                        </div>
                    )
                }
            </div>
        </div>
    );
}

export const B2BMobileUrlEditor = forwardRef(B2BMobileUrlEditor_);
