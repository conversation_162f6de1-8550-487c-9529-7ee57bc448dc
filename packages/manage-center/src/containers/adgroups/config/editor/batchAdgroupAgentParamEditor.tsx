import {forwardRef, useImperativeHandle, useCallback} from 'react';
import {Form, Input} from '@baidu/one-ui';
import FormItem from 'commonLibs/materialList/FormItem';
import Tip from 'commonLibs/Tips';
import {
    createError,
    displayPartialError
} from 'commonLibs/utils/materialList/error';
import {AGENT_PARAM_LIMIT} from 'app/components/tableEditor/url/config';

function BatchAdgroupAgentParamEditor(props, ref) {
    const {batchSaveAdgroupAgentParam, form} = props;
    const {validateFields} = form;

    const onSave = useCallback(async () => {
        let data;
        try {
            const values = await validateFields();
            data = await batchSaveAdgroupAgentParam({
                items: {
                    adgroupAgentParam: values.adgroupAgentParam || ''
                }
            });
        }
        catch (error) {
            const err = createError(error);
            err.optName = '修改智能体监控后缀';
            displayPartialError(err);
            throw error;
        }
        return data;
    }, [validateFields, batchSaveAdgroupAgentParam]);

    useImperativeHandle(ref, () => ({
        onSave
    }), [onSave]);

    return (
        <Form className="use-rf-preset-form-ui use-label-top">
            <FormItem
                form={form}
                field="adgroupAgentParam"
                name={<span>智能体监控后缀 <Tip keyName="adgroupAgentParam" /></span>}
                rules={[
                    {
                        validator: (rule, value, callback) => {
                            if (value && value.trim() === '') {
                                callback('请设置监控后缀后再提交');
                            }
                            if (value && (value.length > AGENT_PARAM_LIMIT)) {
                                callback(`请输入0-${AGENT_PARAM_LIMIT}字符`);
                            }
                            callback();
                        }
                    }
                ]}
            >
                <Input placeholder="示例：?keyword={keyword}" maxLen={1024} />
            </FormItem>
        </Form>
    );
}

export default Form.create({name: 'batchAdgroupAgentParam'})(forwardRef(BatchAdgroupAgentParamEditor));