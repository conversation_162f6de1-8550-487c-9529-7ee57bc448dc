/*
 * @file: 应用商店直投批量编辑
 * @author: <EMAIL>
 * @Date: 2024/10/24
 */
import {useCallback, useContext, useState} from 'react';
import {status as StatusEnum} from '@baidu/winds-ajax';
import {Radio} from '@baidu/one-ui';
import SaveFooter from 'commonLibs/SaveFooter';
import {useEfficiencyPPMonitor} from 'commonLibs/hooks/monitor';
import {AdgroupEditorContext} from '../../materialQuery';
import './style.less';
import {displayError} from 'commonLibs/utils/materialList/error';
import {useSuccessCallback} from 'app/containers/adgroups/hooks/useSuccessCallback';

const {Button: RadioButton, Group: RadioGroup} = Radio;

enum AppShopDirectStatusEnum {
    CLOSE = '0',
    OPEN = '1'
}

const radioButtons = [
    <RadioButton value={AppShopDirectStatusEnum.OPEN} key={AppShopDirectStatusEnum.OPEN}>统一开启</RadioButton>,
    <RadioButton value={AppShopDirectStatusEnum.CLOSE} key={AppShopDirectStatusEnum.CLOSE}>统一关闭</RadioButton>
];

const BatchAppShopDirectStatusEditor = props => {
    const {
        onCancel,
        column,
        selectedIds = [],
        columnText
    } = props;
    const [value, setValue] = useState(AppShopDirectStatusEnum.OPEN);
    const {batchSaveFinalUrl} = useContext(AdgroupEditorContext);
    const reportMonitor = useEfficiencyPPMonitor(`mod_${column}`, 'adgroup_list_batch');
    const [{refreshAndExpire}] = useSuccessCallback();

    const onSave = useCallback(async () => {
        reportMonitor({count: selectedIds.length});
        const values = {
            [column]: Number(value)
        };
        let data;
        try {
            data = await batchSaveFinalUrl({
                items: values
            });
        }
        catch (err) {
            err.optName = `修改${columnText}`;
            displayError(err);
            if (err?._normalized?.status === StatusEnum.PARTFAIL) {
                refreshAndExpire(); // 部分失败刷新一下
            }
            throw err;
        }
        onCancel();
        return data;
    }, [reportMonitor, selectedIds.length, column, value, batchSaveFinalUrl, columnText, refreshAndExpire]);

    const saveFooterProps = {
        onSave,
        onCancel,
        className: 'adgroup-editor-footer'
    };

    return (
        <div className="todo-edit-panel adgroup-url-editor">
            <RadioGroup
                value={value}
                onChange={e => setValue(e.target.value)}
            >
                {radioButtons}
            </RadioGroup>
            <SaveFooter {...saveFooterProps} />
        </div>
    );
};

export default BatchAppShopDirectStatusEditor;
