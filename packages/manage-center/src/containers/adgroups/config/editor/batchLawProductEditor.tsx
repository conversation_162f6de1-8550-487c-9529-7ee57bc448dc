import {useCallback, forwardRef, useImperativeHandle, useRef} from 'react';
import {Form} from '@baidu/one-ui';
import {displayError, createError} from 'commonLibs/utils/materialList/error';
import LawProduct from 'app/containers/adMain/adgroup/fields/lawProduct';
import {productCategoryTypeEnum} from 'commonLibs/config/enumEntry';

export default forwardRef((props: any) => {
    const formRef = useRef(null);
    const {batchSaveLawProduct, wrappedComponentRef} = props;

    const onSave = useCallback(async () => {
        const values = await formRef?.current?.validateFieldsAndScroll();
        try {
            return await batchSaveLawProduct({
                items: {
                    productCategoryType: productCategoryTypeEnum.LAW,
                    structuredProductIdStrs: values.structuredProductIdStrs
                }
            });
        }
        catch (error) {
            const err = createError(error);
            err.optName = '修改单元产品';
            displayError(err);
        }
    }, [batchSaveLawProduct]);

    useImperativeHandle(wrappedComponentRef, () => ({onSave}));

    return (
        <Form ref={formRef}>
            <Form.Field
                label="选择产品"
                name="structuredProductIdStrs"
                initialValue={[]}
            >
                <LawProduct />
            </Form.Field>
        </Form>
    );
});