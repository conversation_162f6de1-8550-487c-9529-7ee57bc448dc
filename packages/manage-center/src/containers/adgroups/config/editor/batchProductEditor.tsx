import {useCallback, forwardRef, useImperativeHandle, useRef} from 'react';
import {displayError, createError} from 'commonLibs/utils/materialList/error';
import {TABLE_SELECTION_CONFIG, ProductSelect} from 'commonLibs/components/productSelect';
import {getCardConfig, getDefaultProductCategoryType} from 'commonLibs/components/selectCard/product/config';

export default forwardRef(props => {
    const {
        onCancel, onOk, batchSaveProduct, isSaving,
        wrappedComponentRef, getMaterialById,
        getSelectedInfo
    } = props;

    const {selectedIds} = getSelectedInfo();

    const productSelectRef = useRef();
    // 批量编辑商品必然都是同一个产品库的商品，取第一个就行
    const markAdgroupId = selectedIds.find(id => !!getMaterialById(+id).productCategoryType);
    const productCategoryType = getMaterialById(markAdgroupId)?.productCategoryType || getDefaultProductCategoryType();
    const onSave = useCallback(async () => {
        const {product} = productSelectRef.current;
        try {
            return await batchSaveProduct({
                items: {
                    // 医美库可以选择多个产品
                    structuredProductIds: product.map(item => item.structuredProductId),
                    catalogId: product?.[0]?.catalogId,
                    productCategoryType
                }
            });
        }
        catch (error) {
            const err = createError(error);
            err.optName = '修改单元产品';
            displayError(err);
        }
    }, [batchSaveProduct, onCancel]);

    useImperativeHandle(wrappedComponentRef, () => ({onSave}));

    const productSelectProps = {
        adgroupInfo: {
            productCategoryType
        },
        onOk,
        onCancel,
        isSaving,
        tableSelectionConfig: TABLE_SELECTION_CONFIG[productCategoryType]
    };

    return <ProductSelect {...productSelectProps} ref={productSelectRef} />;
});
