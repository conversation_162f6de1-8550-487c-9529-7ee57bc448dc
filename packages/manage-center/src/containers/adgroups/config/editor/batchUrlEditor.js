/*
 * @file: batchUrlEditor
 * @author: <EMAIL>
 * @Date: 2021/12/8
 */
import {useRef, useCallback, useContext, useMemo} from 'react';
import {Form} from '@baidu/one-ui';
import {Alert} from '@baidu/light-ai-react';
import {get, isObject, isEmpty} from 'lodash-es';
import SaveFooter from 'commonLibs/SaveFooter';
import {useEfficiencyPPMonitor} from 'commonLibs/hooks/monitor';
import BatchFinalUrlDetailEditor from 'app/components/tableEditor/url/batchFinalUrlDetailEditor';
import {useLog} from 'commonLibs/logger';
import {AdgroupEditorContext} from '../../materialQuery';
import './style.less';
import MARKET_TARGET, {isLiveSubMarket} from 'commonLibs/config/marketTarget';
import {B2BMobileUrlEditor} from './b2bMobileUrlEditor';
import {displayError} from 'commonLibs/utils/materialList/error';
import {
    urlTextMap
} from 'app/components/tableEditor/url/config';
import {useSuccessCallback} from 'app/containers/adgroups/hooks/useSuccessCallback';
import {status as StatusEnum} from '@baidu/winds-ajax';


const BatchAdgroupUrlEditorNormal = props => {
    const {
        form,
        onCancel,
        column,
        dataMap,
        selectedIds = [],
        initialValue
    } = props;
    const {batchSaveFinalUrl} = useContext(AdgroupEditorContext);
    const editor = useRef({});
    const log = useLog();
    const reportMonitor = useEfficiencyPPMonitor(`mod_${column}`, 'adgroup_list_batch');
    const [{refreshAndExpire}] = useSuccessCallback();

    const onSave = useCallback(() => {
        log('stage', {target: '^save_batch_finalUrl'});
        reportMonitor({count: selectedIds.length});
        return editor.current.onSave().catch(err => {
            if (err?._normalized?.status === StatusEnum.PARTFAIL) {
                refreshAndExpire(); // 部分失败刷新一下
            }
            throw err;
        });
    }, [editor]);

    const saveFooterProps = {
        onSave,
        onCancel,
        className: 'adgroup-editor-footer'
    };

    const urlProps = {
        form,
        field: column,
        finalUrlRequired: true,
        batchSaveFinalUrl,
        initialValue
    };
    const tip = '直播场景不支持修改计算机最终访问网址、移动最终访问网址';

    const disabledUrlTypeByLive = useMemo(
        () => {
            if (!isObject(dataMap) || isEmpty(selectedIds)) {
                return false;
            }
            return selectedIds.some(
                id => isLiveSubMarket(get(dataMap, [id, 'subMarketingTargetId']))
            );
        },
        [dataMap, selectedIds]
    );
    return (
        <div className="todo-edit-panel adgroup-url-editor">
            <Form className="new-form" labelCol={{className: 'label-container'}}>
                <BatchFinalUrlDetailEditor
                    {...urlProps}
                    wrappedComponentRef={editor}
                />
            </Form>
            {disabledUrlTypeByLive && <Alert style={{marginTop: 12}}>{tip}</Alert>}
            <SaveFooter {...saveFooterProps} />
        </div>
    );
};

/**
 * 注意这个组件在全选时不会被用到。 只有当选择的单元全是b2b时才会用到
 */
const BatchMobileUrlEditorForB2B = props => {
    const {
        onCancel,
        column,
        selectedIds = [],
        dataMap
    } = props;
    const {batchSaveFinalUrl} = useContext(AdgroupEditorContext);
    const editor = useRef({});
    const log = useLog();
    const reportMonitor = useEfficiencyPPMonitor(`mod_${column}`, 'adgroup_list_batch');
    const [{refreshAndExpire}] = useSuccessCallback();

    const {
        promotionScene,
        mobileFinalUrl: initialValue = ''
    } = selectedIds.length === 1 ? dataMap[selectedIds[0]] : {};

    // 用于展示哪些单元的转化类型与落地页不一致
    const adgroupList = [];
    // 如果有某个单元的转化类型为空，则拉取所有所有转化类型的落地页
    let isSupportAllTransTypes = false;

    // 所有单元的转化类型取并集并用Set去重
    const transTypesForAllAdgroups = [...new Set(selectedIds.map(id => {
        const {campaignTransTypes, adgroupName} = dataMap[id];
        adgroupList.push({adgroupName, campaignTransTypes});
        if (!campaignTransTypes || campaignTransTypes.length === 0) {
            isSupportAllTransTypes = true;
        }
        return campaignTransTypes;
    }).flat())];

    const onSave = async () => {
        log('stage', {target: '^save_batch_finalUrl'});
        reportMonitor({count: selectedIds.length});
        let values;
        try {
            values = await editor.current.validateFields();
        }
        catch (err) {
            return;
        }
        let data;
        try {
            data = await batchSaveFinalUrl({
                items: values
            });
        }
        catch (err) {
            err.optName = `修改${urlTextMap[column]}`;
            displayError(err);
            if (err?._normalized?.status === StatusEnum.PARTFAIL) {
                refreshAndExpire(); // 部分失败刷新一下
            }
            throw err;
        }
        return data;
    };

    return (
        <B2BMobileUrlEditor
            initialValue={initialValue}
            onCancel={onCancel}
            onOk={onSave}
            promotionScene={promotionScene}
            {
                ...(
                    isSupportAllTransTypes // 如果有某个
                        ? {}
                        : {campaignTransTypes: transTypesForAllAdgroups}
                )
            }
            adgroupList={adgroupList}
            ref={editor}
        />
    );
};



const BatchAdgroupUrlEditor = props => {
    const {selectedIds = [], dataMap, column, isCheckAll} = props;
    const isAllB2B = getIsAllB2B({selectedIds, dataMap, column, isCheckAll});

    if (isAllB2B) {
        return <BatchMobileUrlEditorForB2B {...props} />;
    }

    return <BatchAdgroupUrlEditorNormal {...props} />;
};

export default BatchAdgroupUrlEditor;


export function getIsAllB2B({selectedIds, dataMap, column, isCheckAll}) {
    return !isCheckAll && selectedIds.every(id => {
        const data = dataMap[id];
        return data && data.marketingTargetId === MARKET_TARGET.B2B && column === 'mobileFinalUrl';
    });
}
