/*
 * @file: segmentRecommendStatusEditor
 * @author: lian<PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2022-04-01 18:14:42
 */
import {useMemo} from 'react';
import {Form, Switch} from '@baidu/one-ui';
import Tip from 'commonLibs/Tips';
import FormItem from 'commonLibs/materialList/FormItem';
import SaveFooter from 'commonLibs/SaveFooter';
import {batchSaveAdgroupAutoTargetingStatus} from 'app/api/adgroup/batchUpdate';
import {useAdgroupBatchModParams} from 'app/containers/adgroups/hooks/useBatchProps';
import BatchSwitchRadio, {
    SelectStatusTextCom,
    getMatrialInfoAllValid,
    genNumFunc
} from 'app/components/tableEditor/batchSwitchRadio';
import {useSuccessCallback} from 'app/containers/adgroups/hooks/useSuccessCallback';
import {PATH_ARRAY} from 'app/containers/adgroups/config/reducer';
import {createError, getErrorDisplayForForm} from 'commonLibs/utils/materialList/error';
import {useLog} from 'commonLibs/logger';
import './style.less';

const AdgroupStatusEditor = props => {
    const {
        form,
        onCancel,
        selectedIds,
        isCheckAll,
        dataMap,
        field = 'creativeTextOptimizationStatus',
        fieldName = '自动文案优化',
        keyName = 'creativeTextOptimizationStatus'
    } = props;
    const [showMaterialStatusTipFlag, openLength] = useMemo(() => {
        const showMaterialStatusTipFlag = getMatrialInfoAllValid({
            selectedIds,
            isCheckAll,
            getInfoById: id => dataMap[id]
        });
        const openMaterialLength = genNumFunc({
            condition: record => !!record[field],
            getInfoById: id => dataMap[id],
            selectedIds
        });
        return [showMaterialStatusTipFlag, openMaterialLength];
    }, [selectedIds, dataMap, isCheckAll, field]);
    const makeBatchModParams = useAdgroupBatchModParams(PATH_ARRAY, 'adgroupMap');
    const [{onSuccessCallback}] = useSuccessCallback();
    const displayError = useMemo(() => getErrorDisplayForForm(form), [form]);
    const log = useLog();

    const onSave = async () => {
        log('stage', {target: '^save_batch_creativeTextOptimizationStatus'});
        log('stage', {target: '~save_batch_creativeTextOptimizationStatus'});
        const values = await form.validateFields();
        try {
            const params = makeBatchModParams({items: {
                [field]: values[field]
            }});
            const data = await batchSaveAdgroupAutoTargetingStatus(params);
            onSuccessCallback(data);
        }
        catch (err) {
            const formatError = createError(err);
            formatError.materialName = '单元';
            formatError.optName = `修改${fieldName}`;
            displayError(formatError);
        }
        finally {
            log('stage', {target: '$save_batch_creativeTextOptimizationStatus'});
        }
    };
    const selectStatusTextComProps = {
        levelName: '单元',
        optionName: fieldName,
        openLength,
        selectedLength: selectedIds.length,
        showMaterialStatusTipFlag
    };
    const saveFooterProps = {
        onSave,
        onCancel,
        className: 'adgroup-editor-footer'
    };
    return (
        <div className="todo-edit-panel adgroup-autotarget-status-editor">
            <Form className="new-form" labelCol={{className: 'label-container'}}>
                <SelectStatusTextCom {...selectStatusTextComProps} />
                <FormItem
                    form={form}
                    name={
                        <span>
                            {fieldName}
                            {keyName && <Tip keyName={keyName} />}
                        </span>
                    }
                    field={field}
                    initialValue
                    colon={false}
                >
                    <BatchSwitchRadio />
                </FormItem>
            </Form>
            <SaveFooter {...saveFooterProps} />
        </div>
    );
};

function SwitchOneUI(props) {
    const {value, onChange} = props;
    const switchProps = {
        onChange,
        checked: value
    };
    return (
        <Switch {...switchProps} />
    );
}

export default AdgroupStatusEditor;

export function getStatusEditor({field, fieldName, keyName}) {
    return props => (
        <AdgroupStatusEditor
            {...props}
            field={field}
            fieldName={fieldName}
            keyName={keyName}
        />
    );
}
