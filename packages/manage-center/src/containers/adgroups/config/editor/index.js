/**
 * @file 单元列表弹窗编辑配置
 * <AUTHOR>
 * @date 2021/12/15
 */
import InlineAdgroupFinalUrlDetailEditor from './inlineAdgroupFinalUrlDetailEditor';
import InlineNegativeWordsEditor from './inlineNegativeWordsEditor';
import InlineProductSetEditor from './inlineProductSetEditor';
import StoreMobileUrlEditor from './inlineStoreMobileUrlEditor';
import InlineProductEditor from './inlineProductEditor';
import InlineLawProductEditor from './inlineLawProductEditor';
import BatchProductEditor from './batchProductEditor';
import BatchAppShopDirectStatus from './batchAppShopDirectStatus';
import BatchNegativeWordsEditor from 'app/containers/campaigns/list/editor/negativeWords/batchNegativeWordsEditor';
import BatchLawProductEditor from './batchLawProductEditor';
import {
    InlineAdgroupUrlTypeEditor,
    BatchAdgroupAgentUrlEditor,
    InlineAdgroupAgentUrlEditor,
    BatchAdgroupUrlTypeEditor
} from './adgroupUrlType';
import BatchAdgroupAgentParamEditor from './batchAdgroupAgentParamEditor';

export const editorConfig = {
    appShopDirectStatus: {
        title: '修改应用商店直投',
        width: 800,
        batch: BatchAppShopDirectStatus
    },
    pcFinalUrl: {
        title: '修改计算机最终访问网址',
        width: 800,
        inline: InlineAdgroupFinalUrlDetailEditor
    },
    adgroupUrlType: {
        title: '修改广告链接类型',
        width: 800,
        inline: InlineAdgroupUrlTypeEditor,
        batch: BatchAdgroupUrlTypeEditor
    },
    adgroupAgentUrl: {
        title: '修改商家智能体',
        width: 800,
        inline: InlineAdgroupAgentUrlEditor,
        batch: BatchAdgroupAgentUrlEditor,
        transformInitialValue: data => ({
            adgroupAgentUrl: data?.adgroupAgentUrl
        })
    },
    adgroupAgentParam: {
        title: '修改智能体监控后缀',
        width: 800,
        batch: BatchAdgroupAgentParamEditor,
        transformInitialValue: data => ({
            adgroupAgentParam: data?.adgroupAgentParam
        })
    },
    mobileFinalUrl: {
        title: '修改移动最终访问网址',
        width: 800,
        inline: InlineAdgroupFinalUrlDetailEditor
    },
    negativeWords: {
        title: '添加否定关键词',
        width: 800,
        inline: InlineNegativeWordsEditor,
        batch: BatchNegativeWordsEditor
    },
    productSetName: {
        isDefineTitle: true,
        title: '编辑商品组',
        width: 800,
        inline: InlineProductSetEditor
    },
    storeName: {
        title: '修改店铺信息',
        width: 800,
        inline: StoreMobileUrlEditor
    },
    structuredContentIds: {
        inline: InlineProductEditor,
        batch: BatchProductEditor
    },
    structuredLawProductIds: {
        title: '选择产品',
        inline: InlineLawProductEditor,
        batch: BatchLawProductEditor
    }
};

export const dialogConfig = {
    targetNameField: 'adgroupName', // 用来在dataMap中获取物料名称
    targetLevelName: '单元' // 当前的物料列表类型，中文
};
