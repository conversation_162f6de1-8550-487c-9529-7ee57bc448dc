/*
 * @file: inlineAdgroupFinalUrlDetailEditor
 * @author: <EMAIL>
 * @Date: 2022/2/22
 */
import {forwardRef, useCallback, useState, useImperativeHandle, useMemo, useRef} from 'react';
import {isNil} from 'lodash-es';
import {Form, Button} from '@baidu/one-ui';
import {useEfficiencyPPMonitor} from 'commonLibs/hooks/monitor';
import {isMedicalForceUser, isAllowAgentUrlRegUser, isUnitAgentUrlUser} from 'commonLibs/utils/getFlag';
import {displayError, getErrorDisplayForForm} from 'commonLibs/utils/materialList/error';
import {showTypeKeyMap} from 'commonLibs/components/jimuyuSelect/config';
import {shopTypeConfig} from 'commonLibs/config/shop';
import {useLog} from 'commonLibs/logger';
import {
    getIsOnlyMobile,
    getIsOnlyPc
} from 'app/containers/creatives/utils/segment';
import {
    urlTextMap,
    getInlineFinalUrlValidator,
    getInlineRequiredFinalUrlValidator,
    getInlineTrackParamsValidator,
    getInlineTrackTemplateValidator,
    getUrlIsOnlyShowMobile,
    getValidatorJimuyuSelectFlag,
    urlDataToShadowField,
    AGENT_URL_REG
} from 'app/components/tableEditor/url/config';
import MARKET_TARGET, {CPQL} from 'commonLibs/config/marketTarget';
import {getCampaignAndAdgroupPriceFactor} from 'app/config/priceFactor';
import {MATERIAL_CONFIG} from 'app/config/material';
import FinalUrlDetailEditor from 'app/components/tableEditor/url/finalUrlDetailEditor';
import {B2BMobileUrlEditor} from './b2bMobileUrlEditor';

function FinalUrlGuide(props) {
    const {changedFieldInfo, getMaterialById, name, field, currentId, openEditor, closeEditor} = props;

    const log = useLog();
    const materiaData = getMaterialById(currentId);
    const {marketingTargetId} = materiaData || {};
    const showAgentUrlEditorGuide = useMemo(() => {
        if (isNil(changedFieldInfo)) {
            return false;
        }
        const {value} = changedFieldInfo;
        return typeof value === 'string' && AGENT_URL_REG.test(value);
    }, [changedFieldInfo]);

    const openAgentUrlEditor = useCallback(
        () => {
            log('click', {target: 'open_adgroup_urlType_editor'});
            closeEditor();
            openEditor('inline', 'adgroupUrlType', currentId);
        },
        [openEditor, closeEditor]
    );

    if (
        !showAgentUrlEditorGuide
        || !isUnitAgentUrlUser()
        || isAllowAgentUrlRegUser()
        || marketingTargetId !== CPQL
        || name !== 'InlineAdgroupFinalUrlDetailEditor'
        || !field.includes('FinalUrl')
    ) {
        return null;
    }

    return (
        <div style={{marginLeft: 124, marginTop: '-32px'}}>
            落地页链接暂不支持填写商家智能体链接，您可为当前单元<Button type="text-strong" onClick={openAgentUrlEditor}>关联商家智能体</Button>
        </div>
    );
}

const InlineAdgroupFinalUrlDetailEditor = (props, ref) => {
    const {
        form, field, currentId, inlineSaveFinalUrl, getMaterialById
    } = props;

    const [finalUrlFieldChangedInfo, onFinalUrlFieldChange] = useState();

    const [trackParamsField, trackTemplateField] = useMemo(() => {
        if (field === 'pcFinalUrl') {
            return ['pcTrackParam', 'pcTrackTemplate'];
        }
        return ['mobileTrackParam', 'mobileTrackTemplate'];
    }, [field]);

    const finalUrlDetailData = useMemo(() => {
        const materiaData = getMaterialById(currentId);
        const {marketingTargetId, adgroupId, shopType, adType} = materiaData || {};
        return {
            materiaData,
            marketingTargetId,
            adType,
            adgroupIds: [adgroupId],
            showType: field.includes('mobile') ? showTypeKeyMap.mobile : showTypeKeyMap.pc,
            shopType: shopType || shopTypeConfig.THIRD_PARTY[0]
        };
    }, [currentId, getMaterialById, field]);

    const hasJimuyuSelect = useMemo(() => getValidatorJimuyuSelectFlag({
        marketingTargetId: finalUrlDetailData.marketingTargetId,
        field
    }), [finalUrlDetailData.marketingTargetId, field]);

    const {validateFields} = form;

    const displayError = useMemo(() => getErrorDisplayForForm(form), [form]);
    const reportMonitor = useEfficiencyPPMonitor(`mod_${field}`, 'adgroup_list_inline');

    const onSave = async () => {
        let data;
        const values = await validateFields();
        delete values.urlPerview;
        try {
            data = await inlineSaveFinalUrl(currentId, values, {field});
            reportMonitor({count: 1});
        }
        catch (err) {
            err.optName = `修改${urlTextMap[field]}`;
            displayError(err);
            throw err;
        }
        return data;
    };

    useImperativeHandle(ref, () => ({onSave}));

    const validators = useMemo(() => {
        const {
            campaign: [campaignPcPriceFactor, campaignMobilePriceFactor],
            adgroup: [adgroupPcPriceFactor, adgroupMobilePriceFactor]
        } = getCampaignAndAdgroupPriceFactor(finalUrlDetailData.materiaData, MATERIAL_CONFIG.ADGROUP) || {};

        let finalUrlRequired;

        if (field.includes('mobile')) {
            if (finalUrlDetailData.materiaData?.marketingTargetId === MARKET_TARGET.APP) {
                finalUrlRequired = false;
            }
            else if (isMedicalForceUser()) {
                finalUrlRequired = true;
            }
            else {
                finalUrlRequired = !getIsOnlyPc({
                    bidPrefer: finalUrlDetailData.materiaData?.bidPrefer,
                    priceRatio: adgroupMobilePriceFactor,
                    campaignPriceRatio: campaignMobilePriceFactor,
                    equipmentType: finalUrlDetailData.materiaData?.equipmentType
                });
            }
        }
        else {
            finalUrlRequired = !getIsOnlyMobile({
                isOnlyShowMobile: getUrlIsOnlyShowMobile(finalUrlDetailData.materiaData),
                bidPrefer: finalUrlDetailData.materiaData?.bidPrefer,
                pcPriceRatio: adgroupPcPriceFactor,
                campaignPcPriceRatio: campaignPcPriceFactor,
                equipmentType: finalUrlDetailData.materiaData?.equipmentType
            });
        }

        const getFinalUrlValidatorMethod = finalUrlRequired
            ? getInlineRequiredFinalUrlValidator
            : getInlineFinalUrlValidator;

        return {
            [field]: getFinalUrlValidatorMethod(form, field, finalUrlDetailData.materiaData, onFinalUrlFieldChange),
            [trackParamsField]: getInlineTrackParamsValidator(form, trackParamsField, finalUrlDetailData.materiaData),
            [trackTemplateField]: getInlineTrackTemplateValidator(
                form,
                trackTemplateField,
                finalUrlDetailData.materiaData
            )
        };
    }, [field, finalUrlDetailData, trackParamsField, trackTemplateField]);

    const editorProps = {
        hasSelect: hasJimuyuSelect,
        form,
        field,
        validators,
        disabled: finalUrlDetailData.marketingTargetId === MARKET_TARGET.SHOP
            && finalUrlDetailData.materiaData?.shopType === shopTypeConfig.HEALTH,
        ...finalUrlDetailData,
        slotProps: {
            slots: {
                finalUrlGuide: <FinalUrlGuide {...props} changedFieldInfo={finalUrlFieldChangedInfo} />
            }
        }
    };
    return (
        <FinalUrlDetailEditor {...editorProps} />
    );
};

const InlineAdgroupFinalUrlDetailEditorForm
    = Form.create({name: 'InlineAdgroupFinalUrlDetailEditor'})(forwardRef(InlineAdgroupFinalUrlDetailEditor));



const InlineB2BMobileUrl = props => {

    const {
        field, currentId, inlineSaveFinalUrl, getMaterialById, onCancel, onOk, isSaving, wrappedComponentRef
    } = props;

    const ref = useRef();

    const materiaData = getMaterialById(currentId);
    const {promotionScene, campaignTransTypes} = materiaData || {};

    const initialValue = materiaData[urlDataToShadowField[field]] || materiaData[field];
    const reportMonitor = useEfficiencyPPMonitor(`mod_${field}`, 'adgroup_list_inline');

    const onSave = async () => {

        const values = await ref.current.validateFields();

        let data;
        try {
            data = await inlineSaveFinalUrl(currentId, values, {field});
            reportMonitor({count: 1});
        }
        catch (err) {
            err.optName = `修改${urlTextMap[field]}`;
            displayError(err);
            throw err;
        }
        return data;
    };
    useImperativeHandle(wrappedComponentRef, () => ({onSave}));

    return (
        <B2BMobileUrlEditor
            initialValue={initialValue}
            onCancel={onCancel}
            onOk={onOk}
            isSaving={isSaving}
            promotionScene={promotionScene}
            campaignTransTypes={campaignTransTypes}
            ref={ref}
        />
    );

};

export default forwardRef(function UrlForm(props, ref) {
    const {
        field, currentId, getMaterialById
    } = props;
    const materiaData = getMaterialById(currentId);
    const {marketingTargetId} = materiaData || {};

    const isB2BMobileUrl = marketingTargetId === MARKET_TARGET.B2B && field === 'mobileFinalUrl';

    if (isB2BMobileUrl) {
        return <InlineB2BMobileUrl {...props} ref={ref} />;
    }
    return <InlineAdgroupFinalUrlDetailEditorForm {...props} ref={ref} />;

});

