/**
 * @file 法律行业关联产品行内编辑
 * <AUTHOR>
 * @date 2025/01/12
*/

import {useCallback, forwardRef, useImperativeHandle, useRef} from 'react';
import {Form} from '@baidu/one-ui';
import {displayError, createError} from 'commonLibs/utils/materialList/error';
import LawProduct from 'app/containers/adMain/adgroup/fields/lawProduct';

export default forwardRef(props => {
    const formRef = useRef(null);
    const {
        currentId, getMaterialById, inlineSaveLawProduct,
        wrappedComponentRef
    } = props;
    const materiaData = getMaterialById(currentId);

    const onSave = useCallback(async () => {
        const values = await formRef?.current?.validateFieldsAndScroll();
        try {
            return await inlineSaveLawProduct(currentId, materiaData, values.structuredProductIdStrs);
        }
        catch (error) {
            const err = createError(error);
            err.optName = '修改单元产品';
            displayError(err);
        }
    }, [inlineSaveLawProduct, materiaData]);

    useImperativeHandle(wrappedComponentRef, () => ({onSave}));

    return (
        <Form ref={formRef}>
            <Form.Field
                label="选择产品"
                name="structuredProductIdStrs"
                initialValue={materiaData.structuredContentIdStrs || []}
            >
                <LawProduct />
            </Form.Field>
        </Form>
    );
});