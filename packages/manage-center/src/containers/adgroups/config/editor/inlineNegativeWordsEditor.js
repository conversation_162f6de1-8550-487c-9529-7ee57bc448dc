/*
 * @Author: <EMAIL>
 * @Date: 2022-12-29 14:08:02
 * @Description: 行内编辑否定关键词（在该单元没有否定关键词的时候）
 */
import {Form} from '@baidu/one-ui';
import {useEfficiencyPPMonitor} from 'commonLibs/hooks/monitor';
import {useRef, useState, useEffect, useMemo, useCallback, useImperativeHandle} from 'react';
import {getErrorDisplayForForm, createError} from 'commonLibs/utils/materialList/error';
import {getNegativeWordValidator} from 'app/containers/negativeWords/negativeUpgrade/addNegativeWord';
import {NEGATIVE_ADD_TYPE, BIND_SCOPE_TYPE} from 'app/containers/negativeWords/negativeUpgrade/addNegativeWord/config';
import {NEGATIVE_TYPE} from 'app/containers/negativeWords/negativeUpgrade/config';
import AddWord from 'app/containers/negativeWords/negativeUpgrade/addNegativeWord/addWord';


const InlineNegativeWords = props => {
    const formRef = useRef(null);
    const {
        wrappedComponentRef, currentId, inlineSaveNegativeWords,
        getMaterialById
    } = props;
    const adgroupInfo = getMaterialById(currentId);
    const [form, setForm] = useState(formRef?.current);

    useEffect(() => {
        setForm(formRef?.current);
    }, []);

    const displayError = useMemo(() => getErrorDisplayForForm(form), [form]);
    const reportMonitor = useEfficiencyPPMonitor('mod_negative', 'adgroup_list_inline');

    const onSave = useCallback(async () => {
        const {validateFieldsAndScroll} = form;
        const values = await validateFieldsAndScroll();
        const errorWords = values.negativeWords.words.filter(item => item.error);
        if (errorWords.length) {
            throw '添加单元否定关键词失败';
        }
        let data;
        try {
            data = await inlineSaveNegativeWords(adgroupInfo, values);
            reportMonitor({count: 1});
        }
        catch (error) {
            const err = createError(error);
            err.optName = '添加否定关键词';
            displayError(err);
            throw err;
        }
        return data;
    }, [form]);

    const onCancel = () => {
        reportMonitor({completed: 0, count: 1});
    };

    useImperativeHandle(wrappedComponentRef, () => ({onSave, onCancel}));

    const negativeWordValidator = getNegativeWordValidator();
    return (
        <Form ref={formRef} style={{'--dls-form-label-width': '90px'}}>
            <Form.Field
                label="添加至"
                required
            >
                单元 - {adgroupInfo.adgroupName}
            </Form.Field>
            <Form.Field
                label="否定关键词"
                name="negativeWords"
                rules={negativeWordValidator.toOneUIFormRules()}
                initialValue={{
                    type: [NEGATIVE_ADD_TYPE.add],
                    packets: [],
                    words: [{
                        keyValue: '',
                        matchType: NEGATIVE_TYPE.phrase,
                        error: ''
                    }]
                }}
                required
            >
                {
                    form && (
                        <AddWord
                            form={form}
                            values={{
                                bindScope: {
                                    type: BIND_SCOPE_TYPE.adgroup
                                }
                            }}
                            negativeKeywordCountConfig={adgroupInfo.negativeKeywordCountConfig}
                            campaignId={adgroupInfo.campaignId}
                            addWordType="adgroup"
                        />
                    )
                }
            </Form.Field>
        </Form>
    );
};

export default InlineNegativeWords;