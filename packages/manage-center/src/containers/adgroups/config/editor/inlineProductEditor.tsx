import {useCallback, forwardRef, useImperativeHandle, useRef, useEffect, useState} from 'react';
import {isEmpty} from 'lodash-es';
import sendMonitor from 'commonLibs/utils/sendHm';
import {displayError, createError} from 'commonLibs/utils/materialList/error';
import {
    TABLE_SELECTION_CONFIG,
    ProductSelect,
    AutoProductSceneEnum,
    AutoProductFromEnum
} from 'commonLibs/components/productSelect';
import {getDefaultProductCategoryType} from 'commonLibs/components/selectCard/product/config';
import {productCategoryTypeEnum} from 'commonLibs/config/enumEntry';
import {isUrlAutoParseProductUser} from 'commonLibs/utils/getFlag';
import {getUrlStructResult} from 'app/api/adgroup/product';

function sendLog({item, value}: {item: string, value?: number}) {
    sendMonitor('action', {
        level: 'adgroup_list',
        source: 'struct_product',
        item,
        ...(value ? {value} : {})
    });
}

export default forwardRef(props => {
    const {
        currentId, getMaterialById, onCancel, onOk, inlineSaveProduct, isSaving,
        wrappedComponentRef
    } = props;
    const materiaData = getMaterialById(currentId);
    const {mobileFinalUrl, structuredContentIds} = materiaData;
    const productCategoryType = materiaData.productCategoryType || getDefaultProductCategoryType();
    const isLXTIndustry = productCategoryType === productCategoryTypeEnum.LXT;

    const [autoProductContent, setAutoProductContent] = useState({});

    const productSelectRef = useRef();

    useEffect(() => {
        (async () => {
            if (isLXTIndustry && isUrlAutoParseProductUser() && mobileFinalUrl && !structuredContentIds?.length) {
                const res = await getUrlStructResult(mobileFinalUrl);
                if (res?.[0]) {
                    const {productContent, productId} = res[0];
                    const triggerScene = productId ? AutoProductSceneEnum.matched : AutoProductSceneEnum.autoCreate;
                    if (!isEmpty(productContent) || productId) {
                        sendLog({item: 'url_preparse_success', value: triggerScene}); // 有预解析结果上报
                        setAutoProductContent({...res[0], triggerScene, from: AutoProductFromEnum.adgroupList});
                    }
                    else {
                        sendLog({item: 'url_preparse_fail'}); // 无预解析结果上报
                    }
                }
            }
        })();
    }, [mobileFinalUrl, structuredContentIds, isLXTIndustry]);

    const onSave = useCallback(async () => {
        const {product} = productSelectRef.current;
        try {
            const res = await inlineSaveProduct(currentId, {...materiaData, productCategoryType}, product);
            if (isLXTIndustry && isUrlAutoParseProductUser()) {
                const {productId: submitProductId} = product?.[0] || {};
                const {productId, triggerScene} = autoProductContent;
                if (!!submitProductId && submitProductId === productId) {
                    sendLog({item: 'autoProduct_save_success', value: triggerScene});
                }
            }
            return res;
        }
        catch (error) {
            const err = createError(error);
            err.optName = '修改单元产品';
            displayError(err);
        }
    }, [inlineSaveProduct, materiaData, onCancel, productCategoryType, isLXTIndustry, autoProductContent]);

    const onJmyContentSave = () => {
        const {product} = productSelectRef.current;
        // 更新下 productId, 保存时用来判断是否是使用了新生成的产品
        setAutoProductContent({...autoProductContent, productId: product?.[0].productId});
        onOk();
    };

    useImperativeHandle(wrappedComponentRef, () => ({onSave}));

    const productSelectProps = {
        adgroupInfo: {
            ...materiaData,
            productCategoryType,
            ...(
                autoProductContent.structuredProductId
                    ? {
                        structuredProductIds: [autoProductContent.structuredProductId],
                        // 传这个为了触发查询，然后filter字段为structuredProductId
                        structuredContentIds: [autoProductContent.structuredProductId]
                    }
                    : {}
            )
        },
        autoProductContent,
        onJmyContentSave,
        onOk,
        onCancel,
        isSaving,
        tableSelectionConfig: TABLE_SELECTION_CONFIG[productCategoryType]
    };

    return <ProductSelect {...productSelectProps} ref={productSelectRef} />;
});
