/*
 * @Author: zhang<PERSON><EMAIL>
 * @Date: 2023-4-14
 * @Description: 行内编辑投放商品组
 */
import {Form} from '@baidu/one-ui';
import {forwardRef, useCallback, useImperativeHandle} from 'react';
import {displayErrorAsToast, createError} from 'commonLibs/utils/materialList/error';
import ProductSetEditor from 'app/components/productSet';

const editorConfig = {
    field: 'productSetting',
    name: '关联商品',
    validator: (rule, value, callback) => {
        if (!value) {
            return callback('请选择关联商品');
        }
        return callback();
    }
};

const InlineProductSet = (props, ref) => {
    const {
        currentId, inlineSaveProductSet,
        getMaterialById, form: {validateFieldsAndScroll, getFieldDecorator}
    } = props;

    const adgroupInfo = getMaterialById(currentId);
    const {productSetId, catalogId} = adgroupInfo;
    const editorProps = {
        campaignInfo: {catalogId},
        initialProductSetId: productSetId
    };

    const onSave = useCallback(async () => {
        const values = await validateFieldsAndScroll();
        let data;
        try {
            data = await inlineSaveProductSet(currentId, {
                catalogId,
                productSetId: values[editorConfig.field]
            });
        }
        catch (error) {
            const err = createError(error);
            err.optName = '编辑商品组';
            displayErrorAsToast(err);
            throw err;
        }
        return data;
    }, [validateFieldsAndScroll, catalogId, currentId]);

    useImperativeHandle(ref, () => ({onSave}));

    return (
        <Form>
            <Form.Item
                label={editorConfig.name}
                required
            >
                {getFieldDecorator(editorConfig.field, {
                    rules: [{validator: editorConfig.validator}],
                    initialValue: productSetId
                })(<ProductSetEditor {...editorProps} />)}
            </Form.Item>
        </Form>
    );
};

export default Form.create()(forwardRef(InlineProductSet));
