/*
 * @file: 单元店铺落地页编辑器
 * <AUTHOR>
 * @date May 15 2023
 */
import {
    useQuickForm,
    useReactiveData
} from '@baidu/react-formulator';
import StoreMobileUrlList from 'app/containers/adMain/adgroup/fields/storeMobileUrl';
import StoreInfo from 'app/containers/adMain/adgroup/fields/storeInfo';
import {storeMobileUrlOptions, storeMobileUrlRadioMap} from 'app/containers/adMain/adgroup/config/STORE';
import {displayError} from 'commonLibs/utils/materialList/error';
import {forwardRef, useMemo, useRef, useImperativeHandle} from 'react';
import {fetchStoreMobileUrlInfo, fetchStoreListAndPageList} from 'commonLibs/apis/store';
import SuspenseBoundary, {useResource, CacheProvider} from 'commonLibs/suspenseBoundary';

const initialData = {totalRowCount: 0, storeInfoList: []};

const StoreMobileUrlEditor_ = ({
    storeMobileUrlInfo,
    scopeType = 1
}, ref) => {
    const [Form] = useQuickForm(ref);
    const initialData = {
        storeInfo: [storeMobileUrlInfo],
        storeMobileUrl: scopeType,
        storeMobileUrlList: storeMobileUrlInfo.storePageDataList || []
    };
    const [formData] = useReactiveData(initialData);

    const formProps = useMemo(() => {
        const props = {
            config: {
                fields: [
                    {
                        label: '推广店铺',
                        field: 'storeInfo',
                        rules: [['required']],
                        use: [StoreInfo],
                        componentProps: () => {
                            return {
                                storeDistance: storeMobileUrlInfo.storeDistance,
                                regionType: storeMobileUrlInfo.regionType
                            };
                        },
                        validators: function (value = []) {
                            if (!value.length || !value[0]?.storeId) {
                                return '请选择推广店铺';
                            }
                        }
                    },
                    {
                        label: '落地页',
                        field: 'storeMobileUrl',
                        showRequiredMark: true,
                        use: ['RadioGroup', {options: storeMobileUrlOptions}]
                    },
                    {
                        label: '',
                        field: 'storeMobileUrlList',
                        use: [StoreMobileUrlList],
                        componentProps: ['storeInfo'],
                        visible: formData => {
                            return formData.storeMobileUrl === storeMobileUrlRadioMap.CUSTOM;
                        },
                        validators: function (value = []) {
                            if (!value.length) {
                                return '请填写落地页';
                            }
                        }
                    }
                ],
                watch: {
                    storeInfo(value, formData) {
                        formData.storeMobileUrl = storeMobileUrlRadioMap.ALL;
                        formData.storeMobileUrlList = [];
                    }
                }
            },
            data: formData,
            className: 'use-rf-preset-form-ui use-label-top'
        };
        return props;
    }, [scopeType, storeMobileUrlInfo]);
    return (
        <Form {...formProps} />
    );
};

const StoreMobileUrlEditor = forwardRef(StoreMobileUrlEditor_);

const InlineStoreMobileUrl = props => {

    const {
        field, currentId, inlineSaveStoreName, wrappedComponentRef
    } = props;

    const [{
        selectedStoreId,
        selectedPageIds,
        isCheckAllPages,
        storeDistance,
        regionType
    }] = useResource(
        fetchStoreMobileUrlInfo, {adgroupId: currentId}
    );


    const [[storeData = initialData, pageData = initialData]] = useResource(fetchStoreListAndPageList, {
        storeId: selectedStoreId
    });

    const selectedStoreMobileUrlInfo = useMemo(() => {
        const storeMobileUrlInfo = storeData.storeInfoList?.[0] || {};
        const storePageInfo = pageData.storeInfoList?.[0] || {};
        return {
            ...storeMobileUrlInfo,
            storePageDataList: storePageInfo.storePageDataList?.filter(item => selectedPageIds.includes(item.pageId)),
            storeDistance,
            regionType
        };
    }, [selectedPageIds, storeData, pageData, storeDistance, regionType]);

    const ref = useRef();

    const onSave = async () => {

        let values = await ref.current.validateFields();

        const {storeInfo, storeMobileUrl, storeMobileUrlList} = values;
        const values_ = storeMobileUrl === storeMobileUrlRadioMap.ALL
            ? {
                promotionTypes: storeInfo.map(item => ({
                    storeId: item.storeId, checkAllPages: true
                }))
            }
            : {
                promotionTypes: storeMobileUrlList.map(item => {
                    return {
                        storeId: storeInfo?.[0].storeId,
                        checkAllPages: false,
                        pageId: item.pageId,
                        url: item.pageUrl
                    };
                })
            };

        let data;
        try {
            data = await inlineSaveStoreName(currentId, values_, {storeName: storeInfo[0].storeName});
        }
        catch (err) {
            err.optName = '修改门店';
            displayError(err);
            throw err;
        }
        return data;
    };

    useImperativeHandle(wrappedComponentRef, () => ({onSave}));

    return (
        <StoreMobileUrlEditor
            storeMobileUrlInfo={selectedStoreMobileUrlInfo}
            scopeType={isCheckAllPages ? storeMobileUrlRadioMap.ALL : storeMobileUrlRadioMap.CUSTOM}
            ref={ref}
        />
    );

};

export default forwardRef(props => {
    return (
        <CacheProvider>
            <SuspenseBoundary>
                <InlineStoreMobileUrl {...props} />
            </SuspenseBoundary>
        </CacheProvider>
    );
});
