/*
 * @file: segmentRecommendStatusEditor
 * @author: lian<PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2022-04-01 18:14:42
 */
import {useMemo} from 'react';
import {Form, Switch} from '@baidu/one-ui';
import FormItem from 'commonLibs/materialList/FormItem';
import Tip from 'commonLibs/Tips';
import SaveFooter from 'commonLibs/SaveFooter';
import {batchSaveAdgroupAutoTargetingStatus} from 'app/api/adgroup/batchUpdate';
import BatchSwitchRadio, {
    SelectStatusTextCom,
    getMatrialInfoAllValid,
    genNumFunc
} from 'app/components/tableEditor/batchSwitchRadio';
import {useAdgroupBatchModParams} from 'app/containers/adgroups/hooks/useBatchProps';
import {useSuccessCallback} from 'app/containers/adgroups/hooks/useSuccessCallback';
import {PATH_ARRAY} from 'app/containers/adgroups/config/reducer';
import {createError, getErrorDisplayForForm} from 'commonLibs/utils/materialList/error';
import {useLog} from 'commonLibs/logger';
import './style.less';

const AdgroupAutoTargetingStatusEditor = props => {
    const {
        form,
        onCancel,
        selectedIds,
        dataMap,
        isCheckAll
    } = props;
    const [showMaterialStatusTipFlag, openLength] = useMemo(() => {
        const showMaterialStatusTipFlag = getMatrialInfoAllValid({
            selectedIds,
            isCheckAll,
            getInfoById: id => dataMap[id]
        });
        const openMaterialLength = genNumFunc({
            condition: ({segmentRecommendStatus}) => !!segmentRecommendStatus,
            getInfoById: id => dataMap[id],
            selectedIds
        });
        return [showMaterialStatusTipFlag, openMaterialLength];
    }, [selectedIds, dataMap, isCheckAll]);
    const makeBatchModParams = useAdgroupBatchModParams(PATH_ARRAY, 'adgroupMap');
    const [{onSuccessCallback}] = useSuccessCallback();
    const displayError = useMemo(() => getErrorDisplayForForm(form), [form]);
    const log = useLog();

    const onSave = async () => {
        log('stage', {target: '^save_batch_segmentRecommendStatus'});
        log('stage', {target: '~save_batch_segmentRecommendStatus'});
        const values = await form.validateFields();
        try {
            const params = makeBatchModParams({items: {segmentRecommendStatus: values.segmentRecommendStatus}});
            const data = await batchSaveAdgroupAutoTargetingStatus(params);
            onSuccessCallback(data);
        }
        catch (err) {
            const formatError = createError(err);
            formatError.materialName = '单元';
            formatError.optName = '修改自动图片优化';
            displayError(formatError);
        }
        finally {
            log('stage', {target: '$save_batch_segmentRecommendStatus'});
        }
    };
    const selectStatusTextComProps = {
        levelName: '单元',
        optionName: '自动图片优化',
        openLength,
        selectedLength: selectedIds.length,
        showMaterialStatusTipFlag
    };
    const saveFooterProps = {
        onSave,
        onCancel,
        className: 'adgroup-editor-footer'
    };
    return (
        <div className="todo-edit-panel adgroup-autotarget-status-editor">
            <Form className="new-form" labelCol={{className: 'label-container'}}>
                <SelectStatusTextCom {...selectStatusTextComProps} />
                <FormItem
                    form={form}
                    name={
                        <span>自动图片优化<Tip keyName="segmentRecommendStatus" /></span>
                    }
                    field='segmentRecommendStatus'
                    initialValue={true}
                    colon={false}
                >
                    <BatchSwitchRadio />
                </FormItem>
            </Form>
            <SaveFooter {...saveFooterProps} />
        </div>
    );
};

function SwitchOneUI(props) {
    const {value, onChange} = props;
    const switchProps = {
        onChange,
        checked: value
    };
    return (
        <Switch {...switchProps} />
    );
}

export default AdgroupAutoTargetingStatusEditor;
