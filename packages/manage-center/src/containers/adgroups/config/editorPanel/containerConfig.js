/**
 * @file 面板编辑器映射关系
 * <AUTHOR>
 * @date 2017/11/15
 */

import {getBatchPanelContainer} from 'app/components/editorPanel/negativeWords/config';
import {useBatchSaveHook} from 'commonLibs/tableList/editors/common';
import batchBrand from 'app/components/editorPanel/batchBrand/container';
import adgroupAutoTargetingStatusEditor from 'app/components/tableEditor/adgroupAutoTargetingStatus';
import {COMMODITY_PRICE_MOD, COMMODITY_UNIT_ADD} from '../batchOperation';
import batchCommodityUnitAdd from 'app/components/editorPanel/batchCommodityUnitAdd';
import batchBid from 'app/components/editorPanel/batchBid/container';
import {formatEditorTitle} from 'commonLibs/materialList/dialogEditor';
import batchFinalUrlEditor, {getIsAllB2B} from '../editor/batchUrlEditor';
import batchAppShopDirectStatusEditor from '../editor/batchAppShopDirectStatus';
import segmentRecommendStatusEditor from '../editor/segmentRecommendStatusEditor';
import creativeTextOptimizationStatusEditor, {getStatusEditor} from '../editor/creativeTextOptimizationStatusEditor';
import BatchAdgroupAgentParamEditor from '../editor/batchAdgroupAgentParamEditor';

const GROUP_CONFIG = getBatchPanelContainer('adgroups');

// column：列；from:'inline'来自行内的事件，'batch'来自批量的事件
export default (column, from = 'batch') => {
    const map = {
        adgroupBid: {
            batch: {
                title: ({dataMap, selectedIds, isCheckAll, excludeIds = [], pager = {}}) => {
                    const data = dataMap[selectedIds[0]] || {};
                    if (isCheckAll && !excludeIds.length) {
                        return '为全部单元编辑出价';
                    }
                    else if (isCheckAll && excludeIds.length) {
                        return `为所选${pager.totalCount - excludeIds.length}个单元编辑出价`;
                    }
                    else if (selectedIds.length > 1) {
                        return `为"${data.adgroupName}"...等${selectedIds.length}个单元编辑出价`;
                    }
                    return `为"${data.adgroupName}"单元编辑出价`;
                },
                container: batchBid,
                cancelFuncName: 'onCancelAdgroupBid',
                saveHook: useBatchSaveHook,
                needFooter: false,
                dialogWrap: false
            }
        },
        [COMMODITY_UNIT_ADD]: {
            batch: {
                title: '批量复制单元',
                container: batchCommodityUnitAdd,
                cancelFuncName: 'onClose',
                saveHook: useBatchSaveHook,
                needFooter: false
            }
        },
        [COMMODITY_PRICE_MOD]: {
            batch: {
                title: ({dataMap, selectedIds, isCheckAll, excludeIds = [], pager = {}}) => {
                    const data = dataMap[selectedIds[0]] || {};
                    if (isCheckAll && !excludeIds.length) {
                        return '为全部单元编辑出价';
                    }
                    else if (isCheckAll && excludeIds.length) {
                        return `为所选${pager.totalCount - excludeIds.length}个单元编辑出价`;
                    }
                    else if (selectedIds.length > 1) {
                        return `为"${data.adgroupName}"...等${selectedIds.length}个单元编辑出价`;
                    }
                    return `为"${data.adgroupName}"单元编辑出价`;
                },
                container: batchBid,
                cancelFuncName: 'onCancelAdgroupBid',
                saveHook: useBatchSaveHook,
                needFooter: false
            }
        },
        ...GROUP_CONFIG,
        adgroupBrand: {
            batch: {
                title: ({selectedIds, isCheckAll}) => {
                    const adgroupNum = isCheckAll ? '全部' : `${selectedIds.length}个`;
                    return `为${adgroupNum}单元修改品牌信息`;
                },
                container: batchBrand,
                cancelFuncName: 'onCancel',
                saveHook: useBatchSaveHook,
                needFooter: false,
                dialogWrap: false
            }
        }
    };

    map.appShopDirectStatus = {
        batch: {
            title: ({dataMap, selectedIds, isCheckAll, pager, excludeIds}) => {
                const selectedCount = isCheckAll
                    ? (excludeIds.length ? pager.totalCount - excludeIds.length : pager.totalCount)
                    : selectedIds.length;
                return formatEditorTitle({
                    selectedCount,
                    totalCount: pager.totalCount,
                    targetLevelName: '单元',
                    actionName: '修改应用商店直投',
                    editorType: 'batch',
                    targetName: dataMap[selectedIds[0]].adgroupName
                });
            },
            container: batchAppShopDirectStatusEditor,
            cancelFuncName: 'onCancel',
            saveHook: useBatchSaveHook,
            columnText: '应用商店直投',
            modalWidth: 500,
            needFooter: false
        }
    };

    map.adgroupAutoTargetingStatus = {
        batch: {
            title: ({dataMap, selectedIds, isCheckAll, pager, excludeIds}) => {
                const selectedCount = isCheckAll
                    ? (excludeIds.length ? pager.totalCount - excludeIds.length : pager.totalCount)
                    : selectedIds.length;
                return formatEditorTitle({
                    selectedCount,
                    totalCount: pager.totalCount,
                    targetLevelName: '单元',
                    actionName: '修改自动定向',
                    editorType: 'batch',
                    targetName: dataMap[selectedIds[0]].adgroupName
                });
            },
            container: adgroupAutoTargetingStatusEditor,
            cancelFuncName: 'onCancel',
            saveHook: useBatchSaveHook,
            modalWidth: 500,
            needFooter: false
        }
    };
    map.segmentRecommendStatus = {
        batch: {
            title: ({dataMap, selectedIds, isCheckAll, pager, excludeIds}) => {
                const selectedCount = isCheckAll
                    ? (excludeIds.length ? pager.totalCount - excludeIds.length : pager.totalCount)
                    : selectedIds.length;
                return formatEditorTitle({
                    selectedCount,
                    totalCount: pager.totalCount,
                    targetLevelName: '单元',
                    actionName: '修改自动图片优化',
                    editorType: 'batch',
                    targetName: dataMap[selectedIds[0]].adgroupName
                });
            },
            container: segmentRecommendStatusEditor,
            cancelFuncName: 'onCancel',
            saveHook: useBatchSaveHook,
            modalWidth: 500,
            needFooter: false
        }
    };

    map.creativeTextOptimizationStatus = {
        batch: {
            title: ({dataMap, selectedIds, isCheckAll, pager, excludeIds}) => {
                const selectedCount = isCheckAll
                    ? (excludeIds.length ? pager.totalCount - excludeIds.length : pager.totalCount)
                    : selectedIds.length;
                return formatEditorTitle({
                    selectedCount,
                    totalCount: pager.totalCount,
                    targetLevelName: '单元',
                    actionName: '修改自动文案优化',
                    editorType: 'batch',
                    targetName: dataMap[selectedIds[0]].adgroupName
                });
            },
            container: creativeTextOptimizationStatusEditor,
            cancelFuncName: 'onCancel',
            saveHook: useBatchSaveHook,
            modalWidth: 500,
            needFooter: false
        }
    };


    map.pcFinalUrl = {
        batch: {
            title: ({dataMap, selectedIds, isCheckAll, pager, excludeIds}) => {
                const selectedCount = isCheckAll
                    ? (excludeIds.length ? pager.totalCount - excludeIds.length : pager.totalCount)
                    : selectedIds.length;
                return formatEditorTitle({
                    selectedCount,
                    totalCount: pager.totalCount,
                    targetLevelName: '单元',
                    actionName: '修改计算机最终访问网址',
                    editorType: 'batch',
                    targetName: dataMap[selectedIds[0]].adgroupName
                });
            },
            container: batchFinalUrlEditor,
            cancelFuncName: 'onCancel',
            saveHook: useBatchSaveHook,
            modalWidth: 800,
            needFooter: false,
            defaultInitialValue: '',
            transformInitialValue: data => ({
                pcFinalUrl: data.pcFinalUrl || '',
                pcTrackParam: data.pcTrackParam || '',
                pcTrackTemplate: data.pcTrackTemplate || ''
            })
        }
    };
    map.mobileFinalUrl = {
        batch: {
            title: ({dataMap, selectedIds, isCheckAll, pager, excludeIds}) => {
                const selectedCount = isCheckAll
                    ? (excludeIds.length ? pager.totalCount - excludeIds.length : pager.totalCount)
                    : selectedIds.length;
                return formatEditorTitle({
                    selectedCount,
                    totalCount: pager.totalCount,
                    targetLevelName: '单元',
                    actionName: '修改移动最终访问网址',
                    editorType: 'batch',
                    targetName: dataMap[selectedIds[0]].adgroupName
                });
            },
            drawerWrap: ({dataMap, selectedIds, isCheckAll, column}) => {
                return getIsAllB2B({dataMap, selectedIds, isCheckAll, column});
            },
            container: batchFinalUrlEditor,
            cancelFuncName: 'onCancel',
            saveHook: useBatchSaveHook,
            modalWidth: 800,
            drawerWidth: 864,
            needFooter: false,
            defaultInitialValue: '',
            transformInitialValue: data => ({
                mobileFinalUrl: data.mobileFinalUrl || '',
                mobileTrackParam: data.mobileTrackParam || '',
                mobileTrackTemplate: data.mobileTrackTemplate || ''
            })
        }
    };
    map.knowledgeTextStatus = {
        batch: {
            title: ({dataMap, selectedIds, isCheckAll, pager, excludeIds}) => {
                const selectedCount = isCheckAll
                    ? (excludeIds.length ? pager.totalCount - excludeIds.length : pager.totalCount)
                    : selectedIds.length;
                return formatEditorTitle({
                    selectedCount,
                    totalCount: pager.totalCount,
                    targetLevelName: '单元',
                    actionName: '修改全网知识问答',
                    editorType: 'batch',
                    targetName: dataMap[selectedIds[0]].adgroupName
                });
            },
            container: getStatusEditor({field: 'knowledgeTextStatus', fieldName: '全网知识问答'}),
            cancelFuncName: 'onCancel',
            saveHook: useBatchSaveHook,
            modalWidth: 500,
            needFooter: false
        }
    };
    map.jimuyuContentStatus = {
        batch: {
            title: ({dataMap, selectedIds, isCheckAll, pager, excludeIds}) => {
                const selectedCount = isCheckAll
                    ? (excludeIds.length ? pager.totalCount - excludeIds.length : pager.totalCount)
                    : selectedIds.length;
                return formatEditorTitle({
                    selectedCount,
                    totalCount: pager.totalCount,
                    targetLevelName: '单元',
                    actionName: '修改基木鱼内容问答',
                    editorType: 'batch',
                    targetName: dataMap[selectedIds[0]].adgroupName
                });
            },
            container: getStatusEditor({field: 'jimuyuContentStatus', fieldName: '基木鱼内容问答'}),
            cancelFuncName: 'onCancel',
            saveHook: useBatchSaveHook,
            modalWidth: 500,
            needFooter: false
        }
    };
    map.userCommentStatus = {
        batch: {
            title: ({dataMap, selectedIds, isCheckAll, pager, excludeIds}) => {
                const selectedCount = isCheckAll
                    ? (excludeIds.length ? pager.totalCount - excludeIds.length : pager.totalCount)
                    : selectedIds.length;
                return formatEditorTitle({
                    selectedCount,
                    totalCount: pager.totalCount,
                    targetLevelName: '单元',
                    actionName: '修改用户评论',
                    editorType: 'batch',
                    targetName: dataMap[selectedIds[0]].adgroupName
                });
            },
            container: getStatusEditor({field: 'userCommentStatus', fieldName: '用户评论'}),
            cancelFuncName: 'onCancel',
            saveHook: useBatchSaveHook,
            modalWidth: 500,
            needFooter: false
        }
    };
    map.adgroupAgentParam = {
        batch: {
            title: ({dataMap, selectedIds, isCheckAll, pager, excludeIds}) => {
                const selectedCount = isCheckAll
                    ? (excludeIds.length ? pager.totalCount - excludeIds.length : pager.totalCount)
                    : selectedIds.length;
                return formatEditorTitle({
                    selectedCount,
                    totalCount: pager.totalCount,
                    targetLevelName: '单元',
                    actionName: '修改智能体监控后缀',
                    editorType: 'batch',
                    targetName: dataMap[selectedIds[0]].adgroupName
                });
            },
            container: BatchAdgroupAgentParamEditor,
            cancelFuncName: 'onCancel',
            saveHook: useBatchSaveHook,
            modalWidth: 500,
            needFooter: false
        }
    };
    return (map[column] && map[column][from]) || {};
};
