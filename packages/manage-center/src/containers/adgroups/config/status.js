/**
 * @file 单元状态相关配置
 * <AUTHOR>
 * @date 2022/01/13
 */
import {isNumber} from 'lodash-es';
import {classNameTypes} from 'commonLibs/config/materialList/status';
import handleLabels from 'commonLibs/utils/handleLabels';

export const UNLIMITED = 0;
const UNAUDITED = 43;

// 自动定向开关遵循单元设置
export const campaignAutoTargetingStatusByAdgroup = 2;
export const adgroupAutoTargetingStatusByCampaign = 2;

const disabledTip = '当前为“按方案设置”，需要前往轻舸修改方案层级设置。';

// campaignStatus 0 计划上设置且关闭，1 计划上设置且开启 2 在单元上设置，其他值可能代表不是轻舸关键词计划
// adgroupStatus 0 单元上设置且关闭，1 单元上设置且开启 2 在计划上设置
export function isOptimizationStatusSettingOnCampaign(campaignStatus, adgroupStatus) {
    return isNumber(campaignStatus) && campaignStatus !== campaignAutoTargetingStatusByAdgroup
        || isNumber(adgroupStatus) && adgroupStatus === adgroupAutoTargetingStatusByCampaign;
}
// 轻舸关键词计划下的单元的自动定向开关，仅在设置为遵循单元设置时，才能够在搜索平台修改
export function disabledModAdgroupAutoTargetingStatus({
    campaignAutoTargetingStatus,
    newAdgroupAutoTargetingStatus
}) {
    // eslint-disable-next-line max-len
    const disabled = isOptimizationStatusSettingOnCampaign(campaignAutoTargetingStatus, newAdgroupAutoTargetingStatus);
    return {
        disabled,
        disabledTip: disabled ? disabledTip : ''
    };
}

// 轻舸关键词计划下的单元、创意的自动文案优化开关，仅在设置为遵循单元设置时，才能够在搜索平台修改
export function disabledCreativeTextOptimizationStatus({
    campaignCreativeTextOptimizationStatus,
    newAdgroupCreativeTextOptimizationStatus
}) {
    // eslint-disable-next-line max-len
    const disabled = isOptimizationStatusSettingOnCampaign(campaignCreativeTextOptimizationStatus, newAdgroupCreativeTextOptimizationStatus);
    return {
        disabled,
        disabledTip: disabled ? disabledTip : ''
    };
}

// 轻舸关键词计划下的单元、创意的自动图片优化开关，仅在设置为遵循单元设置时，才能够在搜索平台修改
export function disabledModAdgroupSegmentRecommendStatus({
    campaignSegmentRecommendStatus,
    newAdgroupSegmentRecommendStatus
}) {
    // eslint-disable-next-line max-len
    const disabled = isOptimizationStatusSettingOnCampaign(campaignSegmentRecommendStatus, newAdgroupSegmentRecommendStatus);
    return {
        disabled,
        disabledTip: disabled ? disabledTip : ''
    };
}

export const statusKeyMap = {
    START: 31,
    PAUSE: 32,
    CAMPAIGN_PAUSE: 33,
    APP_INVALID: 34,
    APP_PARTIAL_INVALID: 35,
    APP_AUDITING: 36,
    PC_URL_INVALID: 37,
    MOBILE_URL_INVALID: 38,
    PC_URL_AUDITING: 39,
    MOBILE_URL_AUDITING: 40,
    URL_INVALID: 41,
    URL_AUDITING: 42,
    NATIVE_AUDITING: 44,
    NATIVE_INVALID: 45,
    NATIVE_DELETED: 46
};

export const statusTextMap = {
    [UNLIMITED]: '不限',
    [statusKeyMap.START]: '有效',
    [statusKeyMap.PAUSE]: '暂停',
    [statusKeyMap.CAMPAIGN_PAUSE]: '计划暂停',
    [statusKeyMap.APP_INVALID]: '应用审核不通过',
    [statusKeyMap.APP_PARTIAL_INVALID]: '部分应用审核不通过',
    [statusKeyMap.APP_AUDITING]: '应用审核中',
    [statusKeyMap.URL_AUDITING]: '单元网址审核中',
    [statusKeyMap.PC_URL_INVALID]: '计算机网址审核不通过',
    [statusKeyMap.MOBILE_URL_INVALID]: '移动网址审核不通过',
    [statusKeyMap.PC_URL_AUDITING]: '有效-计算机网址审核中',
    [statusKeyMap.MOBILE_URL_AUDITING]: '有效-移动网址审核中',
    [statusKeyMap.URL_INVALID]: '网址审核不通过',
    [statusKeyMap.URL_AUDITING]: '网址审核中',
    [statusKeyMap.NATIVE_AUDITING]: '物料审核中',
    [statusKeyMap.NATIVE_INVALID]: '物料审核不通过',
    [statusKeyMap.NATIVE_DELETED]: '物料已删除',
    [UNAUDITED]: '未审核'
};

export const statusClassNames = {
    [statusKeyMap.START]: classNameTypes.normal,
    [statusKeyMap.PAUSE]: classNameTypes.warning,
    [statusKeyMap.CAMPAIGN_PAUSE]: classNameTypes.warning,
    [statusKeyMap.APP_INVALID]: classNameTypes.error,
    [statusKeyMap.APP_PARTIAL_INVALID]: classNameTypes.error,
    [statusKeyMap.APP_AUDITING]: classNameTypes.warning,
    [statusKeyMap.URL_AUDITING]: classNameTypes.warning,
    [statusKeyMap.PC_URL_INVALID]: classNameTypes.error,
    [statusKeyMap.MOBILE_URL_INVALID]: classNameTypes.error,
    [statusKeyMap.PC_URL_AUDITING]: classNameTypes.warning,
    [statusKeyMap.MOBILE_URL_AUDITING]: classNameTypes.warning,
    [statusKeyMap.URL_INVALID]: classNameTypes.error,
    [statusKeyMap.URL_AUDITING]: classNameTypes.warning,
    [statusKeyMap.NATIVE_AUDITING]: classNameTypes.warning,
    [statusKeyMap.NATIVE_INVALID]: classNameTypes.error,
    [statusKeyMap.NATIVE_DELETED]: classNameTypes.error,
    [UNAUDITED]: classNameTypes.warning
};

export const auditStatusMap = [
    statusKeyMap.APP_PARTIAL_INVALID,
    statusKeyMap.PC_URL_INVALID,
    statusKeyMap.MOBILE_URL_INVALID,
    statusKeyMap.URL_INVALID,
    statusKeyMap.NATIVE_INVALID
];

export const withUnLtd = [UNLIMITED].concat(Object.values(statusKeyMap));
export const withUnLtdOption = handleLabels(withUnLtd, statusTextMap);

const newStatusTextMap = {
    ...statusTextMap,
    [statusKeyMap.URL_AUDITING]: `${statusTextMap[statusKeyMap.URL_AUDITING]}/${statusTextMap[UNAUDITED]}`
};
export const options = handleLabels(Object.values(statusKeyMap), newStatusTextMap);
