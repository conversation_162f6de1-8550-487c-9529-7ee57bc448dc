/*
 * @Author: <EMAIL>
 * @Date: 2022-12-07 14:21:08
 * @Description: 单元列表的列配置
 */
import bidPrefer from 'app/components/bidPrefer';
import adgroupName from './columns/adgroupName';
import campaignName from './columns/campaignName';
import status from './columns/status';
import priceRatio from './columns/priceRatio';
import NegativeWords from './columns/negativeWords';
import {getRecordRender} from 'commonLibs/tableList/utils';
import productSetName from './columns/productSetName';
import maxPrice from './columns/maxPrice';
import brandInfo from './columns/brandName';
import adgroupAutoTargetingStatus from './columns/adgroupAutoTargetingStatus';
import appShopDirectStatus from './columns/appShopDirectStatus';
import finalUrl from './columns/finalUrl';
import trackParam from './columns/trackParam';
import agentUrl from './columns/agentUrl';
import trackTemplate from './columns/trackTemplate';
import segmentRecommendStatus from './columns/segmentRecommendStatus';
import structuredContentIds from './columns/structuredContentIds';
import creativeTextOptimizationStatus, {getTextStatus} from './columns/creativeTextOptimizationStatus';
import materialIdFilter from 'app/components/tableFilters/materialIdFilter';
import storeName from './columns/storeName';
import adgroupUrlType from './columns/adgroupUrlType';
import nativeContentInfos from './columns/nativeContentInfos';
import bjhUserInfo from './columns/bjhUserInfo';

const knowledgeTextStatus = getTextStatus({field: 'knowledgeTextStatus', fieldName: '全网知识问答'});
const jimuyuContentStatus = getTextStatus({field: 'jimuyuContentStatus', fieldName: '基木鱼内容问答'});
const userCommentStatus = getTextStatus({field: 'userCommentStatus', fieldName: '用户评论'});

export default {
    adgroupName,
    status,
    bidPrefer,
    priceRatio,
    negativeWords: getRecordRender(NegativeWords, {inView: true}),
    productSetName,
    campaignName,
    maxPrice,
    brandInfo,
    adgroupAutoTargetingStatus,
    pcFinalUrl: finalUrl,
    mobileFinalUrl: finalUrl,
    pcTrackParam: trackParam,
    adgroupUrlType,
    adgroupAgentUrl: agentUrl,
    adgroupAgentParam: trackParam,
    mobileTrackParam: trackParam,
    pcTrackTemplate: trackTemplate,
    mobileTrackTemplate: trackTemplate,
    appShopDirectStatus,
    segmentRecommendStatus,
    creativeTextOptimizationStatus,
    knowledgeTextStatus,
    jimuyuContentStatus,
    userCommentStatus,
    adgroupId: () => ({filters: materialIdFilter}),
    campaignId: () => ({filters: materialIdFilter}),
    storeName,
    structuredContentIds,
    structuredContentIdStrs: structuredContentIds,
    nativeContentInfos,
    bjhUserInfo
};
