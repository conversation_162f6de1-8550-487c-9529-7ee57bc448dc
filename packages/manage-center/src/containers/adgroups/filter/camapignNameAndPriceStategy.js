/*
 * @file: campaignNameAndStrategy
 * @author: l<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2021-11-12 11:59:27
 */

import {useMemo, useEffect, useCallback} from 'react';
import {TextLine} from '@baidu/one-ui-pro';
import {Select} from '@baidu/one-ui';
import {actionTypes as ACTIONS} from 'app/reducers/adgroups';
import {editFilterProps} from 'commonLibs/actions/tableList/filters';
import {useDispatch, useSelector} from 'react-redux';
import {FilterTree} from 'commonLibs/tableList/operationBar';
import {useCustomFilterResource, CustomFilterBoundary} from 'commonLibs/hooks/materialList/resource';
import {convertStrategyListToCascasderOptions} from 'app/containers/adgroups/utils/priceStrategy';
import {stringMap, stringList} from 'commonLibs/tableList/config/filter';
import {fetchPriceStrategy} from 'app/api/adgroup/priceStrategy';
import classNames from 'classnames';
import {WithTitleFilter} from './withTitleFilter';
import './style.less';

function PriceStrategyFilter({value, onChange, className}) {
    const [data] = useCustomFilterResource(fetchPriceStrategy, null);

    const {dataSource, labelMap} = useMemo(() => convertStrategyListToCascasderOptions(data), [data]);
    const dispatch = useDispatch();
    // 老的写法 为了再FilterList中使用数据先存在reducer中
    useEffect(() => {
        const fieldMap = {
            label: '出价策略',
            value: 'priceStrategy'
        };
        dispatch(editFilterProps(ACTIONS)({field: 'priceStrategy', propsMap: {fieldMap, labelMap}}));
    }, [labelMap]);

    const filterProps = {
        dataSource,
        checkedKeys: value,
        onCheck: onChange,
        className: classNames('mc-strategy-filter-tree', className)
    };

    return <FilterTree {...filterProps} />;
}

export function FilterPriceStrategy(props) {
    return (
        <CustomFilterBoundary>
            <PriceStrategyFilter {...props} />
        </CustomFilterBoundary>
    );
}

const Option = Select.Option;
export const TextLineFilters = ({value = {}, onChange, textLineProps = {}}) => {
    const onChangeText = e => {
        onChange({
            ...value,
            value: e.value
        });
    };

    const onChangeOperator = e => {
        onChange({
            ...value,
            operatorValue: e
        });
    };
    const textProps = {
        title: '计划',
        maxLine: 500,
        maxLen: 30,
        minLine: 0,
        isRequired: false,
        defaultValue: [],
        delLabel: '清空',
        width: 304,
        onChange: onChangeText,
        ...textLineProps
    };
    return (
        <div className="custom-textline-filter">
            <Select
                width={120}
                value={value.operatorValue}
                onChange={onChangeOperator}
                className='filter-operator-select'
            >
                {stringList.map(key => (
                    <Option value={key} key={key}>{stringMap[key]}</Option>
                ))}
            </Select>
            <TextLine {...textProps} value={value.value} />
        </div>
    );
};

export const FilterCampaignNamePriceStrategy = props => {
    const {onChange, value} = props;
    const {filters} = useSelector(state => state.adgroups);
    useEffect(() => {
        setTimeout(() => {
            onChange({
                campaignName: {value: [], operatorValue: 'like'},
                priceStrategy: {value: filters.map.priceStrategy?.value || []}
            });
        }, 10);
    }, [filters, onChange]);

    const onChangePriceStrategy = useCallback(e => {
        onChange(value => {
            return {
                ...value,
                priceStrategy: {value: e}
            };
        });
    }, [onChange]);

    const onChangeTextlineFilter = useCallback(e => {
        onChange(value => {
            return {
                ...value,
                campaignName: {
                    operatorValue: e.operatorValue || 'like',
                    value: e.value || []
                }
            };
        });
    }, [onChange]);

    const priceStrategyProps = {
        value: value?.priceStrategy?.value || [],
        onChange: onChangePriceStrategy
    };
    const operatorValue = value?.campaignName?.operatorValue || 'like';
    const textValue = value?.campaignName?.value || [];
    const textProps = {
        value: {
            value: textValue,
            operatorValue
        },
        onChange: onChangeTextlineFilter
    };
    return (
        <div className="campaign-and-pricestrategy-filter">
            <WithTitleFilter title="计划名称" className="campaign-name-filter">
                <TextLineFilters {...textProps} />
            </WithTitleFilter>
            <WithTitleFilter title="出价策略" className="price-strategy-filter">
                <FilterPriceStrategy {...priceStrategyProps} />
            </WithTitleFilter>
        </div>
    );
};
