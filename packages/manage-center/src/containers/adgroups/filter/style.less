.custom-textline-filter {
    display: flex;
    flex-direction: column;
    .filter-operator-select {
        margin-bottom: @dls-padding-unit*3;
    }
}
.mc-strategy-filter-tree {
    overflow-y: auto;
    height: 130px;
}
.campaign-and-pricestrategy-filter {
    display: flex;
    border-top: 1px solid #e3e6ef;
    border-bottom: 1px solid #e3e6ef;
    .campaign-name-filter {
        border-right: 1px solid #e3e6ef;
        padding: 16px 12px 16px 0;
    }
    .price-strategy-filter {
        padding: 16px 0 16px 12px;
        min-width: 300px;
        .mc-strategy-filter-tree {
            height: 282px;
        }
    }
}

.custom-filter {
    .filter-title {
        font-size: @dls-font-size-1;
        color: #545b66;
        margin-bottom: @dls-padding-unit*3;
    }
}