/*
 * @file: withTitleFIlter
 * @author: l<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2021-11-12 14:32:52
 */
import classNames from 'classnames';
import './style.less';

export const WithTitleFilter = props => {
    const {title, children, className} = props;
    const filterCls = classNames(className, 'custom-filter');
    return (
        <div className={filterCls}>
            <div className="filter-title">{title}</div>
            {children}
        </div>
    );
};
