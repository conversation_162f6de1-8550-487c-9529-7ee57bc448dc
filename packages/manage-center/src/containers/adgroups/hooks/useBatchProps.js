/**
 * @file 批量编辑常用props hooks
 * <AUTHOR>
 */

import useRequest from 'commonLibs/hooks/useRequest';
import {useDispatch} from 'react-redux';
import useTableParams from 'app/containers/adgroups/hooks/useTableParams';
import useFetchProps from 'app/containers/adgroups/hooks/useFetchProps';
import useFetchTableList from 'commonLibs/hooks/useFetchList';
import {useCallback} from 'react';
import {pick} from 'lodash-es';
import useTableListState from 'commonLibs/hooks/useTableListState';
import {useURLQuery} from 'commonLibs/route';

export default function useBatchProps() {
    const {requestHook} = useRequest();
    const dispatch = useDispatch();
    const tableRequestParams = useTableParams().params;
    const [query] = useURLQuery();
    const fetchProps = useFetchProps(); // 进度条刷新列表请求第一页数据
    const fetchList = useFetchTableList(fetchProps);
    return {
        requestHook,
        dispatch,
        tableRequestParams,
        search: query,
        fetchProps,
        fetchList
    };
}

// 构造修改参数
export const useAdgroupBatchModParams = (PATH_ARRAY, entitiesKey) => {
    const {selectedIds, isCheckAll, filters = {}, excludeIds} = useTableListState({PATH_ARRAY, entitiesKey});
    const tableRequestParams = useTableParams().params;
    return useCallback(({items, itemsHelper} = {}) => {
        const params = {
            mtlIds: selectedIds,
            checkAll: isCheckAll,
            items,
            itemsHelper
        };
        if (excludeIds.length) {
            params.excludeIds = excludeIds;
            params.mtlIds = [];
        }
        if (filters.ids?.length > 0 || isCheckAll) {
            params.checkAllCondition = pick(
                tableRequestParams,
                ['fieldFilters', 'startTime', 'endTime', 'idType', 'ids']
            );
        }
        return params;
    }, [selectedIds, isCheckAll, filters, excludeIds, tableRequestParams]);
};