import {useState, useEffect} from 'react';
import queryString from 'query-string';
import {isB2BnewProcessUser, isStoreNewProcessUser} from 'commonLibs/utils/getFlag';
import {POI_CONFIG} from 'commonLibs/config/store';

export const useCampaignDialogVisible = () => {
    // 场景：爱采购b端带着商品/店铺的pageId进入凤巢单元页，自动选择爱采购计划新建流程
    const {b2bPageId, storePageId, poi} = queryString.parse(location.search);
    const [visible, setVisible] = useState(false);
    const isB2BPageUrl = isB2BnewProcessUser() && b2bPageId;
    const isStoreInfoUrl = isStoreNewProcessUser() && storePageId;
    const isKaidianToStore = (+poi === POI_CONFIG.STORE || +poi === POI_CONFIG.NATION)
    && storePageId; // 开店后台点击跳转到搜索场景下的本地店铺推广营销目标
    useEffect(() => {
        if (isB2BPageUrl || isStoreInfoUrl || isKaidianToStore) {
            setVisible(true);
        }
    }, [setVisible, isB2BPageUrl, isStoreInfoUrl, isKaidianToStore]);
    return [visible, setVisible];
};