import {SUMKEY} from 'commonLibs/utils/handleTable';
import {normalizeMaterialListData} from 'app/api/utils/materialList';
import {actionTypes as ACTIONS} from 'app/reducers/adgroups';
import {PATH_ARRAY} from 'app/containers/adgroups/config/reducer';
import useTableParams from './useTableParams';

const common = {
    ACTIONS,
    PATH_ARRAY,
    id: 'adgroupId',
    // responseKey: 'rows',
    hasPageCache: true, // 分页缓存,翻页选择，反选功能支持
    totalCountKey: 'totalRowCount',
    entitiesKey: 'adgroupMap'
};

const NormalizeMaterialFieldsConfig = [
    ['pcFinalUrl', ''],
    ['pcTrackparam', ''],
    ['pcTrackTemplate', ''],
    ['mobileFinalUrl', ''],
    ['mobileTrackParam', ''],
    ['mobileTrackTemplate', ''],
    ['shadowPcFinalUrl', ''],
    ['shadowPcTrackParam', ''],
    ['shadowPcTrackTemplate', ''],
    ['shadowMobileFinalUrl', ''],
    ['shadowMobileTrackParam', ''],
    ['shadowMobileTrackTemplate', '']
];

function handleResp(data) {
    const {rows, summary, totalRowCount} = data || {};
    const ids = [];
    let defaultObj = {};
    if (summary && rows.length) {
        ids.push(SUMKEY);
        defaultObj[SUMKEY] = {totalCount: totalRowCount, ...summary};
    }
    const normalizedRows = normalizeMaterialListData(rows, NormalizeMaterialFieldsConfig);
    const dataMap = normalizedRows.reduce((memo, item) => {
        let id = item[common.id];
        ids.push(`${id}`); // 有些地方id是字符串有些是数字，统一为字符串方便使用indexOf或者includes
        memo[id] = item;
        return memo;
    }, defaultObj);

    return {
        ids,
        dataMap: {},
        __entities__: {[common.entitiesKey]: dataMap},
        totalCount: totalRowCount,
        hasPageCache: true
    };
}


// responseKey和handleResp不能共存
export default () => {
    const params = useTableParams();
    return {
        params,
        handleResp,
        ...common
    };
};
