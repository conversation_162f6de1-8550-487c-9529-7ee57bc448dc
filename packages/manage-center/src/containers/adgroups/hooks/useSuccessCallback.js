/**
 * @file adgroup 的操作成功回调
 * <AUTHOR>
 */

import {useCallback, useContext} from 'react';
import {useDispatch} from 'react-redux';
import {AdgroupEditorContext, updateMutateStrategy} from '../materialQuery';
import useFetchAsyncOverview from 'commonLibs/hooks/useFetchAsyncOverview';
import {actionTypes as ACTIONS} from 'app/reducers/adgroups';
import {batchUpdate} from 'commonLibs/actions/tableList/columns';
import {resetSelection} from 'commonLibs/actions/tableList/selection';

const getDefaultAdgroupMap = dataList => dataList.reduce((memo, item) => {
    memo[item.adgroupId] = item;
    return memo;
}, {});

export const useSuccessCallback = (getAdgroupMap = getDefaultAdgroupMap) => {
    const dispatch = useDispatch();
    const {matchMutate, refreshAndExpire} = useContext(AdgroupEditorContext);
    const fetchAsyncOverview = useFetchAsyncOverview({ACTIONS});

    const onSuccessCallback = useCallback((
        data, {needRefreshList, updateData = true, entityMode = true} = {}
    ) => {
        const dataList = data.dataList || data; // 批量保存使用dataList，行内保存使用data
        if (data.isAsyncTask) {
            fetchAsyncOverview && fetchAsyncOverview();
        }
        else {
            needRefreshList && refreshAndExpire();
            if (updateData) {
                if (!needRefreshList) {
                    matchMutate(updateMutateStrategy(dataList));
                }
                const dataMap = getAdgroupMap(dataList);
                const payload = entityMode ? {__entities__: {adgroupMap: dataMap}} : dataMap;
                dispatch(batchUpdate(ACTIONS)(payload));
            }
        }
        dispatch(resetSelection(ACTIONS)());
    }, [getAdgroupMap, fetchAsyncOverview, matchMutate, refreshAndExpire]);

    return [{onSuccessCallback, refreshAndExpire}];
};
