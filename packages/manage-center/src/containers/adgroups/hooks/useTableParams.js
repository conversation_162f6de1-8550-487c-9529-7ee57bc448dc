import {useSelector} from 'react-redux';
import getLevelParamsFromUrl from 'commonLibs/utils/getLevelParamsFromUrl';
import useParams from 'app/hooks/useParams';
import {toBackend} from 'commonLibs/utils/convertFilter';
import {getDepsFields} from 'commonLibs/tableList/utils';
import useAdgroupList, {depColumnsObj} from 'app/hooks/useAdgroupList';

export const useReportType = () => {
    const {isDPA} = useParams();
    return isDPA ? 1620201 : 1620200;
};

// 列表的请求
export default () => {
    const urlParams = useParams();
    const {pageNo, pageSize} = useSelector(state => state.adgroups.pager);
    const {sortField, sortType} = useSelector(state => state.adgroups.sorter);
    const {filters, customizedCols} = useSelector(state => state.adgroups);
    const {levelType: idType, levelId} = getLevelParamsFromUrl(urlParams);
    const {ids: filterIds = [], map: filterMap = {}} = filters;
    const {selectedFields = [], originFields = [], fieldMap} = customizedCols;
    const fields = selectedFields.length ? selectedFields : originFields;
    return useAdgroupList({
        idType,
        ids: [levelId],
        sortField,
        isDesc: sortType === 'descend',
        limit: [(pageNo - 1) * pageSize, +pageSize],
        fieldFilters: filterIds.map(key => toBackend(key, filterMap, fieldMap)),
        fields: getDepsFields(fields, depColumnsObj)
    });
};
