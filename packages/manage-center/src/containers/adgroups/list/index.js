import {useCallback, Fragment, useState, useMemo, memo} from 'react';
import {useBoolean} from '@huse/boolean';
import PagePreviewModal from '@baidu/m-vibranium-react/lib/components/pagePreview/pagePreviewModal';
import {UNIT_LEVEL, DPA_ADGROUP} from 'commonLibs/config/idType';
import useListModel from 'commonLibs/hooks/useListModel';
import {isEqual} from 'lodash-es';
import TableList from 'commonLibs/CustomTableList';
import BatchOperationFragment from 'app/components/batchOperationFragment';
import useFetchProps from '../hooks/useFetchProps';
import {useReportType, depColumnsObj} from 'app/hooks/useAdgroupList';
import fieldsMap from '../config/tableFieldsMap';
import getAdgroups from 'commonLibs/utils/getAdgroups';
import useParams, {transfromFiledsConfig, getDownloadColumns} from 'app/hooks/useParams';
import {useControl} from 'commonLibs/hooks/externalControl';
import {CampaignDialog} from 'commonLibs/levelSelector';
import DialogEditor from 'commonLibs/materialList/dialogEditor';
import AddAble from 'app/components/addAble';
import {useRecordByComponent} from 'commonLibs/hooks/record';
import {RECORD_MATERIAL_KEY} from 'commonLibs/config/record';
import {useSelector} from 'react-redux';
import {Button} from '@baidu/one-ui';
import AdType from 'commonLibs/config/adType';
import {FilterPriceStrategy} from 'app/containers/adgroups/filter/camapignNameAndPriceStategy';
import {stringMap, stringList} from 'commonLibs/tableList/config/filter';
import {MATERIAL_CONFIG} from 'app/config/material';
import {getColumns} from 'commonLibs/tableList/utils';
import {useRouterRedirect} from 'commonLibs/route';
import {adgroupsPageType} from 'app/routes/config';
import {isLocalCachePreloadUser, isBatchOperationPresetUser} from 'commonLibs/utils/getFlag';
import {LogEmphasize} from 'commonLibs/logger';
import {COMMODITY, isMtSupportProgramCreative,
    isSupportNewAdgroupProcess, isDQA, isBjhSubMarket} from 'commonLibs/config/marketTarget';
import {DataPrefetchByHotCondition} from 'app/containers/overview/materialDataPrefetch';
import {useAdgroupEditor, AdgroupEditorContext} from '../materialQuery';
import {editorConfig, dialogConfig} from '../config/editor';
import {APP_SHOP_DIRECT_STATUS_FIELD} from '../config/columns/appShopDirectStatus';
import {useCampaignDialogVisible} from '../hooks/useCampaignDialogVisible';
import {BIND_SCOPE_TYPE} from 'app/containers/negativeWords/negativeUpgrade/addNegativeWord/config';
import '@baidu/m-vibranium-react/lib/components/pagePreview/index.css';


const commonProps = {material: MATERIAL_CONFIG.ADGROUP};
const DisableButton = () => <Button disabled>新建单元</Button>;

// 小流量
function formatReportConfig({customColumns, defaultVisibleColumns, columnCategories, columnConfigs}) {
    // 因为单元列表要在计划名称处筛选出价策略 但出价策略并不展示在列表中 所以没有相关配置 前端补齐 后续最好rd支持。
    columnConfigs.priceStrategy = {
        category: '属性',
        columnName: 'priceStrategy',
        columnText: '出价策略',
        columnType: 'ENUM',
        comment: '在puppet请求thunder的时候这个字段会换成4个字段',
        commentKey: 'campaignPriceStrategy',
        conflictColumns: [],
        feConfig: {requestTo: 'thunder', tipKeyName: 'campaignPriceStrategy', filterType: 'enum'},
        filterable: true,
        filterableOperators: [],
        optional: true,
        sortable: false,
        visible: true
    };
}

function transformFieldFiltersSpecialParams(fieldFilters = []) {
    const transformedFilters = fieldFilters.map(item => {
        // ---- 特殊处理 单元层级 关联产品列 的 筛选功能 -----
        if (item.field === 'structuredContentIds' || item.field === 'structuredContentIdStrs') {
            // structuredContentIds关联产品筛选功能，与后端协商，给后端backEndValue总是传['0']，根据不同情况传不同op。
            if (isEqual(item.values, [0])) {
                // value是[0]，用户筛选：未选择
                item.op = 'in';
            }
            else if (isEqual(item.values, [1])) {
                // value是[1]，用户筛选：已选择
                item.op = 'gt';
            }
            else if (isEqual(item.values, [0, 1]) || isEqual(item.values, [1, 0])) {
                // value是[0,1]，用户筛选：未选择、已选择
                item.op = 'gte';
            }
            item.values = [0]; // 给后端backEndValue总是传[0]
        }
        return item;
    });

    if (transformedFilters.length > 0) {
        return transformedFilters.map(({field, ...otherProps}) => {
            return {
                field: transfromFiledsConfig[field] || field,
                ...otherProps
            };
        });
    }
    return transformedFilters;
}

const List = memo(() => {
    const {campaignId, mtId, bizId, userId, isDPA, isDCA} = useParams();
    const [visible, setVisible] = useCampaignDialogVisible();
    useRecordByComponent({recordKey: RECORD_MATERIAL_KEY.adgroupList});
    const clickToNewAdgroup = useRouterRedirect('@newAdgroup', {inheritParams: true, appendBackUrl: true});
    const clickToAdMainAdgroup = useRouterRedirect('@newProcessAdgroupV1', {inheritParams: true, appendBackUrl: true});
    const clickToNewAdgroupProcess = useRouterRedirect(
        '@newProcessAdgroupV2', {inheritParams: true, appendBackUrl: true});
    const clickToNewDcaAdgroup = useRouterRedirect('@newSeparateAdgroup', {inheritParams: true, appendBackUrl: true});

    const campaignMap = useSelector(state => state.__entities__.campaignMap) || {};
    const adType = campaignMap[campaignId]?.adType;
    const subMarketingTargetId = campaignMap[campaignId]?.subMarketingTargetId;

    const addProps = useMemo(() => ({
        material: MATERIAL_CONFIG.ADGROUP,
        name: '单元',
        onClick: () => {
            if (campaignId) {
                if (isMtSupportProgramCreative({marketingTargetId: mtId, adType})) {
                    clickToNewAdgroupProcess({campaignId});
                    return;
                }
                if (isSupportNewAdgroupProcess({marketingTargetId: mtId, adType})) {
                    clickToAdMainAdgroup({campaignId});
                    return;
                }
                if (isDCA) {
                    clickToNewDcaAdgroup({campaignId});
                    return;
                }
                if (+mtId !== COMMODITY) {
                    clickToNewAdgroup({campaignId});
                    return;
                }
                if (+mtId === COMMODITY) {
                    getAdgroups({marketingTargetId: mtId, userId, campaignId});
                    return;
                }
                clickToNewAdgroup({campaignId});
                return;
            }
            setVisible(true);
        },
        ...(isDQA(subMarketingTargetId) ? {disabled: true, disabledTips: '原生互动-摘要不支持自行添加单元'} : {}),
        ...(isBjhSubMarket(subMarketingTargetId) ? {disabled: true, disabledTips: '原生互动-笔记不支持自行添加单元'} : {})
    }), [mtId, userId, campaignId, isDCA, clickToNewAdgroup, clickToNewDcaAdgroup, subMarketingTargetId]);

    // 临时加个过滤，报告后端暂不支持下载应用直投开关，后续pm提需求，支持后直接去掉这个参数
    // 长期未下，联系PM xiangheng
    // 额外加过滤，暂不支持下载投放商品组，后续让rd支持下载
    const downloadColumns = getColumns(MATERIAL_CONFIG.ADGROUP);
    const downloadCustomColumns = useMemo(() => {
        return getDownloadColumns(downloadColumns,
            [APP_SHOP_DIRECT_STATUS_FIELD, 'productSetName',
                'adgroupAgentParam', 'adgroupAgentInfo']
        );
    }, [downloadColumns]);

    const listProps = {
        pageType: adgroupsPageType,
        reportType: useReportType(),
        fieldsMap,
        depColumnsObj,
        useFetchProps,
        showRowSelection: true,
        newButtonProps: addProps,
        operationProps: {
            material: MATERIAL_CONFIG.ADGROUP,
            materialLevel: isDPA ? DPA_ADGROUP : UNIT_LEVEL
        },
        mtId,
        changeIds: [campaignId, mtId, bizId],
        customFields: [
            {field: 'priceStrategy', Node: FilterPriceStrategy},
            {field: 'campaignName',
                customPropsMap: {
                    filterType: 'string',
                    defaultOperatorValue: 'like',
                    defaultValue: [],
                    operatorOptions: stringList.map(value => ({value, label: stringMap[value]}))
                }
            }
        ],
        transformParams: ({fieldFilters = []}) => {
            // 自定义修改请求参数
            return transformFieldFiltersSpecialParams(fieldFilters);
        },
        formatReportConfig // todo 推全后删掉
    };

    const [{matchMutate, refreshAndExpire}, listModel] = useListModel(listProps);
    const [{
        inlineMethods,
        batchMethods,
        getSelectedInfo,
        getAdgroupById,
        resetRowSelection
    }] = useAdgroupEditor({matchMutate, refreshAndExpire});

    const [ControllableDialog, {openEditor, closeEditor}] = useControl(DialogEditor);
    const dialogEditorSaveMethods = useMemo(
        () => ({...batchMethods, ...inlineMethods}),
        [batchMethods, inlineMethods]
    );
    const dialogProps = {
        visible,
        mtId,
        onCancel: useCallback(() => {
            setVisible(false);
        }, [])
    };
    const dialogEditorProps = {
        options: {
            width: 880
        },
        getSelectedInfo,
        getMaterialById: getAdgroupById,
        refreshList: refreshAndExpire,
        saveMethods: dialogEditorSaveMethods,
        resetRowSelection,
        editorConfig,
        ...dialogConfig,
        openEditor,
        closeEditor,
        bindScopeType: BIND_SCOPE_TYPE.adgroup,
        addWordType: BIND_SCOPE_TYPE.adgroup
    };
    const batchOperationNode = (
        <BatchOperationFragment
            {...commonProps}
            batchUpdateBrand={batchMethods.batchUpdateBrand}
            openEditor={openEditor}
            isPresetBatchOperation={isBatchOperationPresetUser()}
        />
    );
    const tableProps = {
        ...listModel, batchOperationNode,
        NewButtonNode: (Number(adType) === AdType.URL && campaignId) ? DisableButton : AddAble,
        downloadCustomColumns, // todo 后端支持应用商店直投后去掉
        showQuestionnaireWhenClose: true
    };
    tableProps.operationBarProps.useNewStyle = isBatchOperationPresetUser();

    const {selectedFields} = listModel;
    const hotConditionProps = {
        idType: isDPA ? DPA_ADGROUP : UNIT_LEVEL,
        reportType: useReportType(),
        customColumns: selectedFields
    };

    const [previewUrl, setPreviewUrl] = useState('');
    const [previewVisible, {on: showPreview, off: hidePreview}] = useBoolean();
    const previewProps = {
        visible: previewVisible,
        onModalClose: hidePreview,
        isShowPcLink: false, // 是否展示Pc
        isShowCopyLink: false,
        isShowPageSelect: false,
        pageList: [{
            id: 0,
            previewUrl
        }]
    };

    const openPreviewUrl = useCallback(
        (onlineUrl, evt) => {
            setPreviewUrl(onlineUrl);
            showPreview();
            evt && evt.stopPropagation();
        },
        [showPreview]
    );

    return (
        <Fragment>
            <CampaignDialog {...dialogProps} />
            <AdgroupEditorContext.Provider
                value={{
                    ...inlineMethods,
                    ...batchMethods,
                    openEditor,
                    openPreviewUrl,
                    matchMutate,
                    refreshAndExpire
                }}
            >
                <TableList {...tableProps} />
                <ControllableDialog {...dialogEditorProps} />
                <PagePreviewModal {...previewProps} />
                {isLocalCachePreloadUser() && <DataPrefetchByHotCondition {...hotConditionProps} />}
            </AdgroupEditorContext.Provider>
        </Fragment>
    );
});

// LogEmphasize 必须包外面，因为 useListModel 里面有 stage 埋点
export default () => (
    <LogEmphasize source="adgroup_material_list">
        <List />
    </LogEmphasize>
);
