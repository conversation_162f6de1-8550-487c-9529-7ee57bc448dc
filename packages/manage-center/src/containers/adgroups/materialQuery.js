/**
 * @file 单元列表中行内编辑和批量封装
 * <AUTHOR>
 * @date 2021/12/10
 */
import {useMemo, createContext, useCallback} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {createError} from 'commonLibs/utils/materialList/error';
import {actionTypes as ACTIONS} from 'app/reducers/adgroups';
import {PATH_ARRAY} from 'app/containers/adgroups/config/reducer';
import useFetchAsyncOverview from 'commonLibs/hooks/useFetchAsyncOverview';
import {batchUpdate} from 'commonLibs/actions/tableList/columns';
import {convertArrayToMap} from 'commonLibs/utils/array';
import {resetSelection} from 'commonLibs/actions/tableList/selection';
import {useAdgroupBatchModParams} from 'app/containers/adgroups/hooks/useBatchProps';
import {
    updateListDataApi, updateAdgroupFinalUrlDetail,
    updateAdgroupNegativeWords, updateAdgroupProductSetId,
    updateAdgroupProduct,
    updateAdgroupLawProduct,
    updateAdgroupStoreName
} from 'app/api/adgroup/update';
import {
    asyncUpdateAdgroupFinalUrlDetail,
    asyncUpdateBrandInfo,
    asyncUpdateAdgroupProduct,
    asyncUpdateNegativeWords,
    asyncUpdateAdgroup
} from 'app/api/adgroup/batchUpdate';
import {useLog} from 'commonLibs/logger';

export const AdgroupEditorContext = createContext({});
export const updateMutateStrategy = data => ({action: 'update', updateValue: {data, getKey: row => row.adgroupId}});

export function useAdgroupEditor({matchMutate, refreshAndExpire}) {
    const {
        isCheckAll,
        selectedIds,
        excludeIds,
        pager: {totalCount}
    } = useSelector(state => state.adgroups);
    const adgroupMap = useSelector(state => state.__entities__.adgroupMap);
    const getSelectedInfo = useCallback(() => {
        const selectedCount = isCheckAll
            ? (excludeIds.length ? totalCount - excludeIds.length : totalCount)
            : selectedIds.length;
        return {
            isCheckAll,
            selectedCount,
            selectedIds,
            excludeIds,
            totalCount
        };
    }, [isCheckAll, selectedIds, excludeIds, totalCount]);
    const dispatch = useDispatch();
    const fetchAsyncOverview = useFetchAsyncOverview({ACTIONS});
    const makeBatchModParams = useAdgroupBatchModParams(PATH_ARRAY, 'adgroupMap');
    const log = useLog();

    const getAdgroupById = useCallback(id => {
        return adgroupMap && adgroupMap[id];
    }, [adgroupMap]);
    const updateAdgroupById = useCallback((id, data) => {
        matchMutate(updateMutateStrategy(data));
        const dataMap = {[id]: data};
        dispatch(batchUpdate(ACTIONS)({__entities__: {adgroupMap: dataMap}}));
    }, [dispatch, matchMutate]);

    const resetRowSelection = useCallback(() => {
        dispatch(resetSelection(ACTIONS)());
    }, [dispatch]);

    const inlineSaveFactory = useCallback(
        (inlineUpdateApi, field) => async function (materialId, values, params) {
            let data;
            try {
                [data] = await inlineUpdateApi(materialId, values, params);
            }
            catch (error) {
                throw createError(error);
            }
            updateAdgroupById(materialId, data);
            log('click', {target: `inline_${field}`});
        },
        [updateAdgroupById]
    );

    // updateType 为更新数据方式，可选，刷新列表(refresh)或者更新dataSource(replace)；默认为更新dataSource
    const batchSaveFactory = useCallback((batchUpdateApi, field, {updateType = 'replace'} = {}) => async values => {
        let data;
        try {
            const params = makeBatchModParams(values);
            data = await batchUpdateApi(params, values);
        }
        catch (err) {
            const batchSaveError = createError(err);
            batchSaveError.materialName = '单元';
            throw batchSaveError;
        }
        finally {
            log('stage', {target: `$save_batch_${field}`});
        }

        if (data.isAsyncTask) {
            fetchAsyncOverview();
            return;
        }
        if (updateType === 'refresh') {
            refreshAndExpire();
        }
        matchMutate(updateMutateStrategy(data.dataList));
        const adgroupMap = convertArrayToMap(data.dataList, 'adgroupId');
        dispatch(batchUpdate(ACTIONS)({__entities__: {adgroupMap}}));
        dispatch(resetSelection(ACTIONS)());
        return data;
    }, [makeBatchModParams, fetchAsyncOverview, matchMutate, refreshAndExpire, dispatch, log]);

    const inlineMethods = useMemo(() => ({
        inlineSaveFinalUrl: inlineSaveFactory(updateAdgroupFinalUrlDetail),
        inlineSaveUrlTrackParam: inlineSaveFactory(updateAdgroupFinalUrlDetail),
        inlineSaveUrlTrackTemplate: inlineSaveFactory(updateAdgroupFinalUrlDetail),
        inlineSaveAdgroupStatus: inlineSaveFactory(updateListDataApi, 'status'),
        inlineSaveAdgroupUrlType: inlineSaveFactory(updateListDataApi, 'adgroupUrlType'),
        inlineSaveAdgroupAgentUrl: inlineSaveFactory(updateListDataApi, 'adgroupAgentUrl'),
        inlineSaveNegativeWords: inlineSaveFactory(updateAdgroupNegativeWords),
        inlineSaveStoreName: inlineSaveFactory(updateAdgroupStoreName),
        inlineSaveProductSet: inlineSaveFactory(updateAdgroupProductSetId),
        inlineSaveProduct: inlineSaveFactory(updateAdgroupProduct),
        inlineSaveLawProduct: inlineSaveFactory(updateAdgroupLawProduct)
    }), [inlineSaveFactory]);

    const batchMethods = useMemo(() => ({
        batchSaveFinalUrl: batchSaveFactory(asyncUpdateAdgroupFinalUrlDetail, 'finalUrl'),
        batchSaveUrlTrackParam: batchSaveFactory(asyncUpdateAdgroupFinalUrlDetail, 'urlTrackParam'),
        batchSaveUrlTrackTemplate: batchSaveFactory(asyncUpdateAdgroupFinalUrlDetail, 'urlTrackTemplate'),
        batchUpdateBrand: batchSaveFactory(asyncUpdateBrandInfo, 'brandInfo'),
        batchSaveProduct: batchSaveFactory(asyncUpdateAdgroupProduct, 'structuredContentIds'),
        batchSaveAdgroupAgentUrl: batchSaveFactory(asyncUpdateAdgroupProduct, 'adgroupAgentUrl'),
        batchSaveAdgroupAgentParam: batchSaveFactory(asyncUpdateAdgroupProduct, 'adgroupAgentParam'),
        batchSaveNegativeWords: batchSaveFactory(asyncUpdateNegativeWords, 'negativewords', {updateType: 'refresh'}),
        batchSaveLawProduct: batchSaveFactory(asyncUpdateAdgroup, 'lawProduct')
    }), [batchSaveFactory]);

    return [{
        inlineMethods,
        batchMethods,
        getSelectedInfo,
        getAdgroupById,
        resetRowSelection
    }];
}
