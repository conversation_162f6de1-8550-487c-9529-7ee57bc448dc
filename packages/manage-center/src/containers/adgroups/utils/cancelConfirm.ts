/**
 * 取消确认弹窗(用于法律类目产品下关闭自动定向等操作时弹出二次确认弹窗)
*/

import {Dialog} from '@baidu/one-ui';
import {productCategoryTypeEnum} from 'commonLibs/config/enumEntry';
export const cancelConfirm = props => {
    const {contentLabel, onOk} = props;
    Dialog.confirm({
        title: '提示',
        content: `关闭${contentLabel}会导致关联的法律业务类目不生效，影响投放效果，请确认是否关闭自动定向。`,
        onOk() {
            onOk && onOk();
        }
    });
};

// 是否需要开启二次确认的弹窗：法律业务类目条件下需要二次确认
export const isShowCancelConfirm = (
    productCategoryType: number,
    structuredContentIdStrs: string[],
) => {
    return productCategoryType === productCategoryTypeEnum.LAW
        && structuredContentIdStrs?.length > 0;
};
