import MARKET_TARGET from 'commonLibs/config/marketTarget';
import {shopTypeConfig} from 'commonLibs/config/shop';
import AdType from 'commonLibs/config/adType';
import {isAppPcUser} from 'commonLibs/utils/getFlag';

export function getShowFinalUrlDetailColumnFlag({
    marketingTargetId, field, shopType, adType
}) {
    // pcFinalUrl、pcTrackParam、pcTrackTemplate
    // mobileFinalUrl、mobileTrackParam、mobileTrackTemplate
    // 各列在在不同营销下的的展示处理逻辑
    // 计算机：应用推广、商品推广、本地推广、电商店铺（度小店、健康商城）不展示
    // 移动：商品推广、本地推广不展示
    // 爱采购：仅展示最终访问网址
    const invisibleMarketingTarget = [MARKET_TARGET.COMMODITY, MARKET_TARGET.STORE];
    if (invisibleMarketingTarget.includes(marketingTargetId)) {
        return false;
    }
    if (marketingTargetId === MARKET_TARGET.B2B) {
        if (field === 'pcFinalUrl' || field === 'mobileFinalUrl') {
            return true;
        }
        return false;
    }
    if (field.includes('pc')
        && (
            (
                marketingTargetId === MARKET_TARGET.APP
                && (
                    adType === AdType.DCA || !isAppPcUser()
                )
            )
            || (marketingTargetId === MARKET_TARGET.SHOP
                && (
                    shopType === shopTypeConfig.DU_XIAO_DIAN
                    || shopType === shopTypeConfig.HEALTH
                )
            )
        )
    ) {
        return false;
    } else if (
        field.includes('mobile') && (adType === AdType.DCA)
    ) {
        return false;
    }
    return true;
}
