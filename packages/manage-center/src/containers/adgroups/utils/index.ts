import {isBjhSubMarket, isDQA} from 'commonLibs/config/marketTarget';
import {ProductDepositoryType} from 'app/containers/fcNew/cpl/core-type';

const TravelAgencyEditableFields = [
    'creativeTextOptimizationStatus',
    'adgroupAutoTargetingStatus',
    'segmentRecommendStatus'
];
export function getIsAdgroupDisabledEdit(field: string, adgroup: Record<string, any>) {
    if (isDQA(adgroup?.subMarketingTargetId)) {
        return '原生互动-摘要项目的单元暂不支持修改';
    }
    if (isBjhSubMarket(adgroup?.subMarketingTargetId)) {
        return '原生互动-笔记的单元暂不支持修改';
    }
    if (
        TravelAgencyEditableFields.includes(field)
        && adgroup.structuredContentIds?.length > 0
        && adgroup.productCategoryType === ProductDepositoryType.tourism
        && !!adgroup[field]
    ) {
        return '该单元已关联旅行社产品，为了更好理解您的业务信息，帮助您全面覆盖目标人群，不支持关闭';
    }
}
