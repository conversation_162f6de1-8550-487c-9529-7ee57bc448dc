/*
 * @file: priceStrategy
 * @author: l<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2021-11-15 14:23:30
 */
import {map} from 'lodash-es';

const priceStrategyMap = {
    tendency: {label: '排名倾向', keyName: 'CPC'},
    clickMax: {label: '点击最大化', keyName: 'CPC'},
    ocpc: {label: 'oCPC'},
    project: {label: '项目'}
};

const strategyNoneValue = 'NONE';
const strategyNoneLabel = '未设置';

export function convertStrategyListToCascasderOptions(data) {
    const labelMap = {
        [strategyNoneValue]: strategyNoneLabel
    };
    const strategyGroupList = ['tendency', 'clickMax', 'ocpc', 'project'].filter(
        type => data[type] && data[type].length > 0);
    const dataSource = map(strategyGroupList, type => {
        const {label, keyName} = priceStrategyMap[type];
        return {
            label,
            value: type,
            children: data[type].map(({strategyName, strategyId}) => {
                const value = `${keyName ? `${keyName}_` : ''}${strategyId}`;
                labelMap[value] = strategyName;
                return {
                    label: strategyName,
                    value
                };
            })
        };
    });
    dataSource.unshift({
        value: strategyNoneValue,
        label: strategyNoneLabel
    });
    return {dataSource, labelMap};
}