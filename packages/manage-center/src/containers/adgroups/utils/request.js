import {priceUseCampaignBid} from 'app/containers/adgroups/config/columns/maxPrice';
import {getErrorTextByCode} from 'commonLibs/utils/getErrorTextByCode';

export const success = () => resp => {
    const {adgroupId} = resp[0];
    return {__entities__: {adgroupMap: {[adgroupId]: resp[0]}}};
};

// dpa处理单元出价
export const dpaSuccess = field => resp => {
    const {unitId, price} = resp[0];

    return {__entities__: {adgroupMap: {[unitId]: {
        adgroupId: unitId,
        maxPrice: price
    }}}};
};

export const fail = error => {
    const {code} = error.errors?.[0] || {};
    return getErrorTextByCode(code);
};

// record, field必传，如果params传了，values就可以不传
export const request = (record, params) => values => {
    const {field} = record;
    const finalParms = params || values;
    let requestParams = {...finalParms};
    // 计划自动出价特殊逻辑，未升级单元出价修改交互  前端判空则实际含义为不使用单元出价
    if (field === 'maxPrice' && (requestParams?.maxPrice === '' || requestParams?.maxPrice === undefined)) {
        // 处理单元出价为空的情况（为空代表使用计划出价，传参传-1）
        requestParams.maxPrice = priceUseCampaignBid;
    }
    return {
        path: 'thunder/MOD/AdgroupService/updateAdgroup',
        params: {
            adgroupTypes: [{adgroupId: record.adgroupId, ...requestParams}]
        }
    };
};

export const dpaRequest = (record, params) => (values = {}) => {
    const {field} = record;
    const finalParms = params || values;
    let maxPrice = finalParms.maxPrice;
    if (field === 'maxPrice' && (maxPrice === '' || maxPrice === undefined)) {
        // 处理单元出价为空的情况（为空代表使用计划出价，传参传-1）
        maxPrice = priceUseCampaignBid;
    }
    return {
        path: 'sdpa-api/MOD/DpaWebUnitService/updateUnit',
        params: {
            unitTypes: [{unitId: record.adgroupId, planId: record.campaignId, price: maxPrice}]
        }
    };
};
