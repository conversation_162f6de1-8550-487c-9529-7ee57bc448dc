/**
 * 轮值状态、未生效原因config
*/

export enum RotatingCycStatus {
    NOEFFECT = 0, // 未生效
    EFFECT = 1, // 生效
}

export const RotatingCycStatusList = [
    RotatingCycStatus.NOEFFECT,
    RotatingCycStatus.EFFECT
];

// 轮值失效原因
export const RotatingCycStatusDetailMap = {
    1: '未关联商家智能体',
    2: '当前不在方案投放时段内',
    3: '当前不在设置的轮值时段内'
};

export function getRotatingNoEffectDetail(list: Array<keyof typeof RotatingCycStatusDetailMap>) {
    let detail = '';
    if (list.length === 1) {
        detail = RotatingCycStatusDetailMap[list[0]];
    }
    else {
        detail = list.map((item, index) => `${index + 1}、${RotatingCycStatusDetailMap[item]}`).join('；');
    }
    return `轮值未生效原因：${detail}`;
}
