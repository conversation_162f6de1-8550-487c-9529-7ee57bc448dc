/**
 * @file 创意组件-app-绑定管理-接口
 * <AUTHOR>
 * @date 2021/08/23
 */

import {request} from '@baidu/winds-ajax';
import {OPT_MAP} from './formItems/config';
import {channelStatusMap} from 'commonLibs/AppSelect';
import getLevelParamsFromUrl from 'commonLibs/utils/getLevelParamsFromUrl';
import flags from 'commonLibs/utils/flags';
// eslint-disable-next-line import/no-cycle
import {BID_RATIO_CONFIG} from './config';

const APP_FIELDS = [
    'appName', // app名称
    'appId', // appId
    'platform', // 1 安卓  3 ios
    'versionId', // 版本id
    'versionCode', // 版本号
    'sid', // 凤巢唯一id， feed用的是channelId
    'channelId', // 暂时没有用,后续可能把sid统一成channelId
    'channelPackage', // 渠道包名称
    'icon', // 渠道包icon url
    'packageName', // 包名称  package.cxxxx.com
    'status', // 渠道包状态 0 通过  1拒绝 2审核中
    'downloadUrl', // 下载url， ios为iTunes链接
    'source',
    'packageId'
];

export const getAppList = params => {
    return request({
        path: 'silver/GET/AppProcessService/getApp',
        params
    }).then(res => {
        return res.appInfoList;
    });
};

export const getFlattenAppList = params => {
    return request({
        path: 'silver/GET/AppProcessService/getAppInfo',
        params
    }).then(res => {
        return res.fields;
    });
};

// 获取可用于绑定的应用： 获取审核通过的包（下个迭代加上审核中）， isBind为false， 不包含不能使用的包
export const getChangeAppList = ({platform}) => {

    return getFlattenAppList({
        fields: APP_FIELDS,
        fieldFilters: [
            {
                field: 'platform',
                op: 'in',
                values: [platform]
            },
            {
                field: 'status',
                op: 'in',
                values: [
                    channelStatusMap.effective,
                    channelStatusMap.auditing
                ]
            },
            {
                field: 'isBind',
                op: 'eq',
                values: [false]
            }
        ]
    });
};

// 获取当前有单元绑定的包，可拉取到审核拒绝的包，可拉取有绑定关系的所有包
export const getBindedAppList = ({platform}) => {
    return getFlattenAppList({
        fields: APP_FIELDS,
        fieldFilters: [
            {
                field: 'platform',
                op: 'in',
                values: [platform]
            },
            {
                field: 'status',
                op: 'in',
                values: [channelStatusMap.effective, channelStatusMap.offEffective, channelStatusMap.auditing]
            },
            {
                field: 'isBind',
                op: 'eq',
                values: [true]
            }
        ]
    });
};

export const getAppInfoByUrl = iosDownloadUrl => {
    return request({
        params: {
            iosDownloadUrl
        },
        path: 'silver/ADD/AppServingService/addIosApp'
    })
        .then(res => {
            return (res.appInfoList?.[0] || []);
        });
};


export const getSaveParams = (formValues, urlParams) => {
    const {
        optType,
        currentApp,
        adgroupTransfer,
        unbindAdgroupTransfer,
        changeApp,
        batchEditBidRatio
    } = formValues;

    const {levelId, levelType} = getLevelParamsFromUrl(urlParams);

    const isUnbind = optType === OPT_MAP.UN_BIND.value;

    let params = {};

    if (isUnbind) {
        const {
            selectedList,
            isCheckAll,
            checkAllFilters,
            filters
        } = unbindAdgroupTransfer;
        params = {
            oldsid: currentApp?.sid,
            mtlIds: isCheckAll ? [] : selectedList,
            checkAll: isCheckAll,
            checkAllCondition: {
                ids: [levelId],
                idType: levelType,
                fieldFilters: [...checkAllFilters, ...filters]
            }
        };
    }
    else {
        const {
            selectedList,
            isCheckAll,
            checkAllFilters,
            filters
        } = adgroupTransfer;

        params = {
            oldsid: currentApp?.sid,
            mtlIds: isCheckAll ? [] : selectedList,
            checkAll: isCheckAll,
            checkAllCondition: {
                ids: [levelId],
                idType: levelType,
                fieldFilters: [...checkAllFilters, ...filters]
            },
            sid: changeApp?.sid,
            bidRatio: batchEditBidRatio || BID_RATIO_CONFIG.min
        };
    }

    return {
        params,
        path: 'raining/MOD/AppBindAsyncService/modBind'
    };

};

const bindCommonFields = [
    'sid', 'adgroupId', 'campaignId', 'campaignName', 'marketingTargetId'
];
export const getBindCampaignList = (sid, filters = []) => {
    return request({
        params: {
            fields: bindCommonFields,
            fieldFilters: [
                ...filters,
                {
                    op: 'in',
                    field: 'sid',
                    values: [sid]
                }
            ]
        },
        path: 'silver/GET/AppBindService/getBindMtlList'
    }).then(res => {

        const campaignMap = res.fields
            .reduce((acc, item) => {
                if (acc[item.campaignId]?.fetchAdgroupIds) {
                    acc[item.campaignId].fetchAdgroupIds.push(item.adgroupId);
                    acc[item.campaignId].adgroupCount++;
                }
                else {
                    acc[item.campaignId] = {
                        ...item,
                        fetchAdgroupIds: [item.adgroupId],
                        adgroupCount: 1
                    };
                }
                return acc;
            }, {});
        return Object.values(campaignMap);
    });
};

export const getBindAdgroupListByName = (sid, filters, searchValue) => {

    return request({
        params: {
            fields: [...bindCommonFields, 'adgroupName'], // 请求单元名称，如果单元多后端比较耗时，这里按需请求名称
            fieldFilters: [
                ...filters,
                {
                    op: 'like',
                    field: 'adgroupName',
                    values: [searchValue]
                },
                {
                    op: 'in',
                    field: 'sid',
                    values: [sid]
                }
            ]
        },
        path: 'silver/GET/AppBindService/getBindMtlList'
    }).then(res => {
        return res.fields;
    });
};
