/**
 * @file 创意组件-app-绑定管理-表单config
 * <AUTHOR>
 * @date 2021/08/23
 */


import {targetTypeEnum} from 'commonLibs/config/targetType';
import AppSelect, {AppType, channelStatusMap} from 'commonLibs/AppSelect';
import {NumberInput} from '@baidu/one-ui';
import {partial} from 'lodash-es';
import MARKET_TARGET from 'commonLibs/config/marketTarget';
import flags from 'commonLibs/utils/flags';
import OptType from './formItems/optType';
import {OPT_MAP, notAppFilters} from './formItems/config';
// eslint-disable-next-line import/no-cycle
import AdgroupBindTransfer from './formItems/adgroupBindTransfer';
import ChangeBindTransfer from './formItems/changeBindTransfer';

import emi from 'commonLibs/singleton/emi';
import {
    getChangeAppList, getBindedAppList, getAppInfoByUrl
} from './api';
import {isIosAppStructuredUser} from 'commonLibs/utils/getFlag';
const {ADGROUP} = targetTypeEnum;

const NEW_GAME_STATUS = 2;

export const BID_RATIO_CONFIG = {
    max: 9.99,
    min: 1.01,
    step: 0.01,
    fixed: 2
};

export const FORM_FIELDS = {
    OPT_TYPE: 'optType',
    CURRENT_APP: 'currentApp',
    AD_GROUP: 'adgroupTransfer',
    UNBIND_AD_GROUP: 'unbindAdgroupTransfer',
    CHANGE_APP: 'changeApp',
    BATCH_EDIT_BID_RATIO: 'batchEditBidRatio',
    ONLY_APP_CHECK: 'onlyAppChecked'
};

const checkIsAllApp = ({selectedList, allDataMap = {}}) => {
    return selectedList.every(id => {
        return allDataMap[id].marketingTargetId === MARKET_TARGET.APP;
    });
};

// e秘敏捷调研的keys，理论上只要一个就够了，但O端不支持修改， 留几个备用的key吧
const APP_FLATTEN_SURVEY_KEYS = [
    'fc_app_flatten_select',
    'fc_app_flatten_select_1',
    'fc_app_flatten_select_2',
    'fc_app_flatten_select_3'
];
const showEmiSurvey = app => {
    if (app.platform === AppType.ANDROID.value && app.sid) {
        emi.instance.showEmiReport?.(APP_FLATTEN_SURVEY_KEYS);
    }
};

const appIdValidator = (rule, value, callback) => {
    if (!value?.appId || !value?.sid) {
        return callback('请选择应用');
    }
    return callback();
};

const changeAppIdValidator = (form, rule, value, callback) => {

    if (!value?.appId || !value?.sid) {
        return callback('请选择应用');
    }

    // 审核中的app只能选择营销目标为应用推广的app
    const adgroupSelect = form.getFieldValue(FORM_FIELDS.AD_GROUP); // adgroupSelect在隐藏时可能没有值
    if (value.status === channelStatusMap.auditing && adgroupSelect && !checkIsAllApp(adgroupSelect)) {
        return callback('已选计划单元包含非应用推广营销目标计划单元，不可换绑审核中渠道包');
    }

    return callback();
};


const adgroupValidator = (message, rule, value, callback) => {

    if (value?.isCandidateBlank) {
        return callback('您选中的应用没有可操作单元');
    }
    if (!value?.selectedList?.length) {
        return callback(message);
    }
    return callback();
};

export const formFieldsFunc = ({form}, currentAppFromUrl) => {

    return {
        [FORM_FIELDS.OPT_TYPE]: {
            field: FORM_FIELDS.OPT_TYPE,
            name: '操作类型',
            isRequired: true,
            item: props => (
                <OptType
                    form={form}
                />
            ),
            initialValue: OPT_MAP.CHANGE_BIND.value
        },
        [FORM_FIELDS.AD_GROUP]: {
            field: FORM_FIELDS.AD_GROUP,
            name: '选择换绑单元',
            isRequired: true,
            desc: '换绑时，仅展示选中应用已绑定的计划单元',
            item: props => {
                const {sid} = form.getFieldsValue()[FORM_FIELDS.CURRENT_APP];
                return (
                    <ChangeBindTransfer
                        form={form}
                        field={FORM_FIELDS.AD_GROUP}
                        onlyAppFiled={FORM_FIELDS.ONLY_APP_CHECK}
                        sid={sid}
                        additionalValidateField={FORM_FIELDS.CHANGE_APP}
                        refreshAdgroupId={props.refreshAdgroupId}
                    />
                );

            },
            initialValue: {
                targetType: ADGROUP,
                adgroup: { // 单元层级下如果全选,需要提交搜索词和全选状态,所以这里数据是对象
                    selectAdgroupIds: [],
                    checkAll: false,
                    checkAllSearchValue: '' // 在全选状态下的搜索词
                }
            },
            validator: partial(adgroupValidator, '请选择目标单元进行换绑操作')
        },
        [FORM_FIELDS.UNBIND_AD_GROUP]: {
            field: FORM_FIELDS.UNBIND_AD_GROUP,
            name: '选择解绑单元',
            isRequired: true,
            desc: '解绑时，仅展示选中应用已绑定的非应用推广营销目标对应计划单元',
            item: props => {
                const {sid} = form.getFieldsValue()[FORM_FIELDS.CURRENT_APP];

                return (
                    <AdgroupBindTransfer
                        form={form}
                        field={FORM_FIELDS.UNBIND_AD_GROUP}
                        sid={sid}
                        filters={notAppFilters}
                        refreshAdgroupId={props.refreshAdgroupId}
                    />
                );
            },
            initialValue: {
                targetType: ADGROUP,
                adgroup: { // 单元层级下如果全选,需要提交搜索词和全选状态,所以这里数据是对象
                    selectAdgroupIds: [],
                    checkAll: false,
                    checkAllSearchValue: '' // 在全选状态下的搜索词
                }
            },
            validator: partial(adgroupValidator, '请选择目标单元进行解绑操作')
        },
        [FORM_FIELDS.CURRENT_APP]: {
            field: FORM_FIELDS.CURRENT_APP,
            name: '选择应用',
            isRequired: true,
            item: props => {
                return (
                    <AppSelect
                        androidInputPlaceholder="请选择或搜索有绑定投放的渠道包"
                        iosInputPlaceholder="请选择有绑定投放的应用"
                        initialValue={currentAppFromUrl}
                        getAppList={getBindedAppList}
                        getAppInfoByUrl={getAppInfoByUrl}
                        onInitFail={() => {
                            form.setFields({[FORM_FIELDS.CURRENT_APP]: {errors: [{
                                message: '您选中的应用不存在或没有绑定单元，请选择其他应用进行操作'
                            }]}});
                        }}
                        // 当前应用仅可以选择已有应用，不支持输入ios应用
                        iosInputReadOnly
                        onChange={
                            app => {
                                // 重置选中仅应用推广checkbox
                                form.setFields({
                                    [FORM_FIELDS.ONLY_APP_CHECK]: {errors: null, value: false}
                                });
                                // 解绑时触发问卷
                                const optType = form.getFieldsValue()[FORM_FIELDS.OPT_TYPE];
                                if (optType === OPT_MAP.UN_BIND.value) {
                                    showEmiSurvey(app);
                                }
                            }
                        }
                        onPlatformChange={
                            value => {
                                // 重置一下错误状态。。。不然切换安卓ios时，触发了onChange,会触发校验
                                // 组件内部其实已经重置过一次了。。。
                                // one-ui没有只重置error状态的方法，只能把value一起设置了
                                form.setFields({
                                    [FORM_FIELDS.CURRENT_APP]: {errors: null, value: {platform: value}},
                                    [FORM_FIELDS.CHANGE_APP]: {
                                        errors: null, value: form.getFieldsValue()[FORM_FIELDS.CHANGE_APP]
                                    }
                                });
                            }
                        }
                    />
                );
            },
            initialValue: {
                platform: currentAppFromUrl.platform || AppType.ANDROID.value
            },
            validator: appIdValidator
        },
        [FORM_FIELDS.CHANGE_APP]: {
            field: FORM_FIELDS.CHANGE_APP,
            name: '选择应用',
            isRequired: true,
            item: props => {
                return (
                    <AppSelect
                        getAppList={getChangeAppList}
                        getAppInfoByUrl={getAppInfoByUrl}
                        onChange={showEmiSurvey}
                        onPlatformChange={
                            value => {
                                // 重置一下错误状态。。。不然切换安卓ios时，触发了onChange,会触发校验
                                // 组件内部其实已经重置过一次了。。。
                                // one-ui没有只重置error状态的方法，只能把value一起设置了
                                form.setFields({
                                    [FORM_FIELDS.CHANGE_APP]: {errors: null, value: {platform: value}}
                                });
                            }
                        }
                        iosInputReadOnly={isIosAppStructuredUser()}
                    />
                );
            },
            initialValue: {
                platform: AppType.ANDROID.value
            },
            validator: partial(changeAppIdValidator, form)
        },
        [FORM_FIELDS.BATCH_EDIT_BID_RATIO]: {
            field: FORM_FIELDS.BATCH_EDIT_BID_RATIO,
            name: '批量设置出价系数',
            isRequired: true,
            requiredErrorMessage: '批量设置出价系数不能为空',
            inlineDesc: `范围：${BID_RATIO_CONFIG.min}~${BID_RATIO_CONFIG.max}`,
            item: props => {
                return (
                    <NumberInput {...BID_RATIO_CONFIG} onChange={props.onChange} showErrorMessage={false} />
                );
            },
            initialValue: ''
        }
    };
};


export const optFields = {
    title: '绑定方式',
    fields: [
        FORM_FIELDS.OPT_TYPE
    ]
};

export const ENTRY_SOURCE = {
    CRATIVE_COMPONENT: 1,
    APP_CENTER: 2,
    NEW_APP_CENTER: 3
};

export const getCurrentAppFields = formValue => {
    const hasCurrentApp = formValue[FORM_FIELDS.CURRENT_APP]?.sid;
    const isChangeBind = formValue[FORM_FIELDS.OPT_TYPE] === OPT_MAP.CHANGE_BIND.value;

    return {
        title: '当前应用',
        fields: [
            FORM_FIELDS.CURRENT_APP,
            ...(hasCurrentApp ? [isChangeBind ? FORM_FIELDS.AD_GROUP : FORM_FIELDS.UNBIND_AD_GROUP] : [])
        ]
    };
};

export const getChangeAppFields = formValue => {
    // 渲染组件前，form里还没有值，初始化时换绑也展示
    const isChangeBind = formValue[FORM_FIELDS.OPT_TYPE] === undefined
        || formValue[FORM_FIELDS.OPT_TYPE] === OPT_MAP.CHANGE_BIND.value;
    return {
        title: '换绑应用',
        fields: isChangeBind
            ? [
                FORM_FIELDS.CHANGE_APP
            ]
            : []
    };
};

// 需要刷新单元选择
export const REFRESH_ADGROUP_CODE = [100022];
