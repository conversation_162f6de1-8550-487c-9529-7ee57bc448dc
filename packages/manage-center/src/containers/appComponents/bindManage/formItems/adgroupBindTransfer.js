/**
 @file 创意组件-应用类-绑定管理-单元选择
 <AUTHOR>
*/

import AdgroupTransfer from 'commonLibs/materialTransfer/adgroup';
import {getBindCampaignList, getBindAdgroupListByName} from '../api';
import {useParams} from 'commonLibs/route';
import {useCallback} from 'react';

// 如果有值就去联动校验换绑app
// issue https://console.cloud.baidu-int.com/devops/icafe/issue/baidu-fc-fe-one-ui-208/show
const validateChangeApp = (form, field) => {
    if (field && form.getFieldValue(field)?.sid) {
        // force 有错误时也强制校验
        form.validateFields([field], {force: true});
    }
};

const AdgroupBindTransfer = ({
    value, onChange, form, field,
    sid, filters = [], additionalValidateField, refreshAdgroupId
}) => {

    // one-ui  getFieldDecorator 触发时机无法选择，只能暂时先内部处理下
    // issue: https://console.cloud.baidu-int.com/devops/icafe/issue/baidu-fc-fe-one-ui-203/show
    const setValueWithoutValidate = useCallback(v => {
        form.setFieldsValue({[field]: v});
        validateChangeApp(form, additionalValidateField);
    }, [field, additionalValidateField]);

    const urlParams = useParams();

    const getCampaignList = useCallback(() => {
        return getBindCampaignList(sid, filters);
    }, [filters, sid, refreshAdgroupId]); // refreshAdgroupId 用于刷新


    const getAdgroupListByName = useCallback(searchValue => {
        return getBindAdgroupListByName(sid, filters, searchValue);
    }, [filters, sid]);

    const bindProps = {
        value,
        onChange: useCallback((...args) => {
            onChange(...args);
            validateChangeApp(form, additionalValidateField);
        }, [additionalValidateField]),
        getCampaignList,
        getAdgroupListByName,
        setValueWithoutValidate,
        filters,
        urlParams
    };
    return (<AdgroupTransfer {...bindProps} />);
};

export default AdgroupBindTransfer;
