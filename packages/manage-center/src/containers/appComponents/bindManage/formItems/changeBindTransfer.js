/**
 @file 创意组件-应用类-绑定管理-换绑-单元选择
 <AUTHOR>
*/

import AdgroupTransfer from './adgroupBindTransfer';
import {Checkbox, Form} from '@baidu/one-ui';
import {appFilters} from './config';
import {useMemo} from 'react';
import flags from 'commonLibs/utils/flags';


const ChangeBindTransfer = props => {

    const {getFieldDecorator, getFieldValue} = props.form;

    const onlyAppFiled = props.onlyAppFiled;
    const isOnlyAppChecked = getFieldValue(onlyAppFiled);

    const filters = useMemo(() => {
        return isOnlyAppChecked ? appFilters : [];
    }, [isOnlyAppChecked]);


    return (

        <div>
            <Form.Item
                key={onlyAppFiled}
                label={null}
                colon={false}
                className="app-bind-form-item-checkbox"
            >
                {
                    getFieldDecorator(onlyAppFiled, {
                        valuePropName: 'checked'
                    })(
                        <Checkbox>
                            仅展示营销目标为“应用推广”的计划单元
                        </Checkbox>
                    )
                }
            </Form.Item>
            <AdgroupTransfer {...props} filters={filters} />
        </div>
    );
};

export default ChangeBindTransfer;
