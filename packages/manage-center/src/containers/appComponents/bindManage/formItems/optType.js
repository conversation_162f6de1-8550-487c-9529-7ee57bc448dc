/**
 @file 创意组件-应用类-绑定管理-操作类型
 <AUTHOR>
*/

import {Radio} from '@baidu/one-ui';
import {useCallback} from 'react';
import {OPT_OPTIONS} from './config';

const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;



const OptType = ({value, onChange}) => {

    const onRadioChange = useCallback(e => {
        onChange(e.target.value);
    }, [onChange]);

    return (
        <RadioGroup value={value} onChange={onRadioChange}>
            {
                OPT_OPTIONS.map(({value, name}) => (<RadioButton value={value} key={value}>{name}</RadioButton>))
            }
        </RadioGroup>
    );
};

export default OptType;
