/**
 @file 创意组件-应用类-绑定管理
 <AUTHOR>
*/

import {useMemo, useState, useCallback} from 'react';
import {clone} from 'lodash-es';
import {Form, Dialog, Toast} from '@baidu/one-ui';
import {isInQinggeIframe} from 'commonLibs/utils/isInIframe';
import {useParams, useHistory, useRouterFormatter, appendQuery} from 'commonLibs/route';
import SaveFooter from 'commonLibs/SaveFooter';
import useRequest from 'commonLibs/hooks/useRequest';
import {RECORD_MATERIAL_KEY, RECORD_INITIAL_DELAY} from 'commonLibs/config/record';
import {useRecordByComponent} from 'commonLibs/hooks/record';
import {appCenterListUrl} from 'commonLibs/config/route';
import {getRouteLink} from 'commonLibs/RouteLink';
import {getSingleErrorMessage} from 'commonLibs/utils/getErrorMessage';
import queryString from 'query-string';
import {
    optFields, getCurrentAppFields, getChangeAppFields,
    formFieldsFunc, ENTRY_SOURCE, REFRESH_ADGROUP_CODE
} from './config';
import {getSaveParams} from './api';

import './style.less';

// 表单宽度设置
const formItemLayout = {
    labelCol: {
        style: {width: 144}
    },
    labelAlign: 'right',
    colon: true
};

const confirmOnCancel = ({title, content}) => {
    return new Promise(resolve => {
        const {destroy} = Dialog.confirm({
            title,
            content,
            onOk: () => {
                resolve();
                destroy && destroy();
            }
        });
    });
};

const renderFields = (fieldsBlock, formFieldsMap, props) => {
    const {getFieldDecorator} = props.form;
    const {fields, title} = fieldsBlock;
    return (
        fields.length
            ? (
                <div className="bind-app-form-box">
                    <div className="bind-app-form-box-title">{title}</div>
                    {
                        fields.map(field => {
                            const {
                                name,
                                isRequired,
                                requiredErrorMessage,
                                validator,
                                item,
                                validateTrigger,
                                colon,
                                initialValue,
                                desc,
                                inlineDesc
                            } = formFieldsMap[field];

                            const rules = [];
                            if (isRequired) {
                                rules.push({
                                    required: true,
                                    message: requiredErrorMessage
                                });
                            }
                            if (validator) {
                                rules.push({validator});
                            }
                            const options = {
                                rules,
                                initialValue: clone(initialValue)
                            };
                            if (validateTrigger) {
                                options.validateTrigger = validateTrigger;
                            }

                            return (
                                <Form.Item
                                    key={field}
                                    label={name}
                                    colon={colon}
                                >
                                    <div>
                                        {
                                            desc && (<div className="app-bind-form-item-desc">{desc}</div>)
                                        }
                                        {getFieldDecorator(field, options)(item(props))}
                                        {
                                            inlineDesc && (
                                                <span className="app-bind-form-item-desc batch-item-desc">
                                                    {inlineDesc}
                                                </span>
                                            )
                                        }
                                    </div>
                                </Form.Item>
                            );
                        })
                    }
                </div>
            )
            : null
    );
};

const backToList = (entrySource, userId, history, appListUrl) => {
    switch (+entrySource) {
        // 返回创意组件-应用类-自定义创意
        case ENTRY_SOURCE.CRATIVE_COMPONENT:
        default: {
            if (isInQinggeIframe) {
                history.push(appendQuery(
                    appListUrl.replace('dashboard', 'qingge'), {in: 'iframe', 'host_app': 'qingge'}
                ));
            }
            else {
                history.push(appListUrl);
            }
            break;
        }
        // 返回资产-应用中心
        case ENTRY_SOURCE.APP_CENTER: {
            window.open(`/fc/assets/material/user/${userId}/app`, '_self');
            break;
        }
        // 返回新版本应用中心
        case ENTRY_SOURCE.NEW_APP_CENTER: {
            if (isInQinggeIframe) {
                history.replace(getRouteLink({
                    route: appCenterListUrl,
                    params: {userId},
                    query: {in: 'iframe', 'host_app': 'qingge'}
                }));
            }
            else {
                history.replace(getRouteLink({route: appCenterListUrl, params: {userId}}));
            }
            break;
        }
    }
};

const BindManage = props => {
    const {form} = props;
    const {getFieldsValue} = form;

    const urlParams = useParams();
    const userId = urlParams.userId;
    const history = useHistory();

    const {platform, appId, versionId, sid, entrySource} = queryString.parse(location.search);
    const {loading: isSaving, requestHook: onSaveRequest} = useRequest();

    const [refreshAdgroupId, setRefreshAdgroupId] = useState(1);

    useRecordByComponent({recordKey: RECORD_MATERIAL_KEY.creativeAppBind, delay: RECORD_INITIAL_DELAY});

    // url 中传了渠道包信息（platform 必选, appId,versionId 和 sid非必选），说明是从资产中心列表点击渠道包进入的，需要带入渠道包
    const currentAppFromUrl = useMemo(() => {

        if (!platform) {
            return {};
        }
        return Object
            .entries({platform, appId, versionId, sid})
            .reduce((acc, [key, val]) => {
                if (val) {
                    acc[key] = +val;
                }
                return acc;
            }, {});

    }, []);

    const formValue = getFieldsValue();
    const formFieldsMap = formFieldsFunc({form}, currentAppFromUrl);

    const onRefreshAdgroup = useCallback(() => {
        setRefreshAdgroupId(i => i + 1);
    }, []);

    const formatAppListUrl = useRouterFormatter('@dashboard/app');
    const appListUrl = formatAppListUrl();
    const saveFooterProps = {
        onSave: async () => {
            const formValues = await form.validateFields();
            let res;
            try {
                res = await onSaveRequest(getSaveParams(formValues, urlParams));
                Toast.success({content: '保存成功'});
                backToList(entrySource, userId, history, appListUrl);
            }
            catch (err) {
                const code = err?.errors?.[0]?.code;
                const errorMessage = getSingleErrorMessage(err?.errors?.[0]);
                if (REFRESH_ADGROUP_CODE.includes(+code)) {
                    Dialog.confirm({
                        content: (
                            <>
                                <div>{errorMessage}</div>
                                <div>点击“确定”后，将刷新当前应用对应可选计划单元</div>
                            </>
                        ),
                        width: 600,
                        okText: '确定',
                        okCancel: false,
                        onOk: onRefreshAdgroup
                    });
                }
                else {
                    Toast.error({content: errorMessage || '提交失败，请稍后重试'});
                }
            }
            return res;
        },
        saveLabel: '保存',
        onCancel: async () => {
            await confirmOnCancel({title: '温馨提示', content: '退出后您已经填写的内容将不会保存，是否退出?'});
            backToList(entrySource, userId, history, appListUrl);
        },
        isSaving
    };
    const formItemProps = {
        ...props,
        refreshAdgroupId
    };
    return (
        <div className="creative-app-bind-manage">
            <Form {...formItemLayout}>
                <div className="creative-app-bind-manage-title">应用绑定管理</div>
                <div className="">
                    {
                        renderFields(optFields, formFieldsMap, formItemProps)
                    }
                    {
                        renderFields(getCurrentAppFields(formValue), formFieldsMap, formItemProps)
                    }
                    {
                        renderFields(getChangeAppFields(formValue), formFieldsMap, formItemProps)
                    }
                </div>
                <div>

                </div>
                <div className="creative-app-bind-manage-footer">
                    <SaveFooter {...saveFooterProps} />
                </div>
            </Form>
        </div>
    );
};

export default Form.create({})(BindManage);
