
.creative-app-bind-manage {
    // transfer 里的组件有问题需要hack一下
    // one-ui issue https://console.cloud.baidu-int.com/devops/icafe/issue/baidu-fc-fe-one-ui-201/show?source=drawer-header

    .one-form-item .one-form-has-error {
        .one-transfer {
            .one-select {
                border-color: #d3d9e6;
            }
            .one-button:focus {
                box-shadow: none;
            }
        }
    }

    .app-bind-form-item-checkbox {
        line-height: @dls-height-m;
        margin-bottom: @dls-padding-unit * 2;
    }
}
.creative-app-bind-manage-title {
    font-size: @dls-font-size-3;
    color: @dls-color-gray-9;
    line-height: @dls-font-size-1 * 2;
    margin: @dls-padding-unit*6 0;
}
.bind-app-form-box {
    background: @dls-background-color-base-1;
    border-radius: @dls-border-radius-2;
    padding: @dls-padding-unit * 6;
    margin-bottom: @dls-padding-unit * 6;
    .bind-app-form-box-title {
        font-size: @dls-font-size-3;
        color: @dls-color-gray-9;
        line-height: @dls-font-size-1 * 2;
        margin-bottom: @dls-height-xs;
    }
}

.app-bind-form-item-desc {
    font-size: 14px;
    color: @dls-color-gray-7;
    line-height: @dls-height-m;
    &.batch-item-desc {
        margin-left: @dls-padding-unit * 4;
    }
}

.creative-app-bind-manage-footer {
    background: @dls-background-color-base-1;
    border-radius: @dls-border-radius-2;
    padding: @dls-padding-unit * 6;
}
