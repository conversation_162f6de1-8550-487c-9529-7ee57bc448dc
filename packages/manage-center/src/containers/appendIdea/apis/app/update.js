import {request} from '@baidu/winds-ajax';
import IdType from 'commonLibs/config/idType';
import {reduce} from 'lodash-es';
import {appDynamicStatusMap} from 'app/containers/appendIdea/app/filterConfig';

export async function inlineSaveStatusApi(bindId, {pause}, {
    userId
}) {
    const data = await request({
        path: 'cartier/MOD/AdvancedSegmentService/updateSegmentBind',
        params: {
            items: [
                {
                    bindId,
                    pause
                }
            ]
        }
    });
    return data || [];
}


export async function batchSaveStatusApi(baseParams, {pause}) {
    const {
        selectionInfo: {selectedRecords}
    } = baseParams;

    const items = reduce(selectedRecords, (memo, record) => {
        const {bindId, status} = record;
        if (status !== appDynamicStatusMap.invalid) {
            memo.push({
                bindId: bindId,
                pause
            });
        }
        return memo;
    }, []);
    const data = await request({
        path: 'cartier/MOD/AdvancedSegmentService/updateSegmentBind',
        params: {
            items
        }
    });
    return {dataList: data, isAsyncTask: false};
}