import {SUMKEY} from 'commonLibs/utils/handleTable';
import {getFetchParams} from 'commonLibs/utils/getLevelParamsFromUrl';
import {getReportConfig} from 'commonLibs/apis/customizedCols';
import {FIX_TYPE} from 'commonLibs/tableList/config/fixType';
import {request} from '@baidu/winds-ajax';

const defautSortDesc = 'descend';

/**
 * 与表格数据无关请求参数
 * @param {Object} urlParams
 * @returns
 */
const getSegmentListCommonParams = urlParams => {
    const {userId} = urlParams;
    const {idType, ids}  = getFetchParams(urlParams);
    return {
        idType,
        ids,
        userId: +userId
    };
};

/**
 * 根据数据实时获取请求参数
 * @param {Object} urlParams url参数
 * @param {Object} state 当前table的所有数据
 */
export const getSegmentListParams = (segmentFilters, {
    urlParams,
    startTime,
    endTime,
    pageNo,
    pageSize,
    sortType,
    sortField,
    filterList,
    baseColumns
}) => {
    let fieldFilters = filterList.map(filter => {
        const {field, value, operatorValue = 'in'} = filter; // 当时状态等枚举类型时，默认是in
        return {field, values: Array.isArray(value) ? value : [value], op: operatorValue};
    });
    fieldFilters = fieldFilters.concat(segmentFilters);
    const params = {
        ...getSegmentListCommonParams(urlParams),
        startTime,
        endTime,
        pageNo,
        limit: [(pageNo - 1) * pageSize, pageSize || 20],
        sortField,
        isDesc: sortType === defautSortDesc || sortType === '',
        fields: baseColumns.map(column => column.columnName),
        fieldFilters
    };
    return {
        path: 'puppet/GET/MaterialFunction/getMtlQuerySegmentComponentList',
        params
    };
};

/**
 * 获取table源数据dataSource
 * @param {Object} params
 * @returns
 */
export const getSegmentList = async (segmentFilters, params) => {
    const {baseColumns} = params;
    if (!baseColumns || baseColumns.length === 0) {
        return;
    }
    const requestParams = getSegmentListParams(segmentFilters, {...params});
    const result = await request(requestParams);
    return result;
};


/**
 * 获取自定义列column信息
 * @param {string} reportType
 * @returns
 */
export const getSegmentColumn = async reportType => {
    const params = getReportConfig(reportType);
    const result = await request(params);
    // 根据report返回数据初始化column配置
    const {defaultVisibleColumns, customColumns, columnCategories = [], columnConfigs = {}} = result;
    // 映射成自定义列组件需要的字段
    const groups = columnCategories.map((category, index) => {
        const {name, columns} = category;
        return {
            id: index,
            label: name, // 中文描述
            fields: columns // columns配置数组
        };
    });
    // column配置后端字段映射到前端
    const columnMap = Object.entries(columnConfigs).reduce((memo, [key, config]) => {
        const {columnText, optional, feConfig = {}} = config;
        const {fixType} = feConfig;
        memo[key] = {
            ...config,
            label: columnText, // 中文描述
            removable: optional // 是否可选
        };
        if (FIX_TYPE.left === fixType) {
            memo[key].draggable = false;
            memo[key].fixLeft = true;
        }
        return memo;
    }, {});
    return {
        customColumns,
        columnMap,
        defaultVisibleColumns,
        columnCategories: groups
    };
};
