import {request} from '@baidu/winds-ajax';
import IdType from 'commonLibs/config/idType';

export async function inlineSaveStatusApi(segmentId, {pause}, {
    userId
}) {
    const data = await request({
        path: 'raining/MOD/AdvancedSegmentAsyncService/updateSegmentAndBindStatus',
        params: {
            checkAll: false,
            idType: IdType.USER_LEVEL,
            ids: [userId],
            mtlIds: [segmentId],
            segmentIds: [segmentId],
            pause
        }
    });
    const dataList = (data.dataMap || {}).segments || [];
    return dataList;
}


export async function batchSaveStatusApi(baseParams, {pause}) {
    const {
        pageLevel: {idType, levelId},
        selectionInfo: {selectedIds},
        userId
    } = baseParams;
    const updateParams = {
        checkAll: false,
        idType,
        ids: [levelId],
        mtlIds: selectedIds,
        checkAllCondition: {
            idType: IdType.USER_LEVEL,
            ids: [userId],
            fields: ['segmentId'],
            fieldFilters: null
        },
        bindIds: [],
        segmentIds: selectedIds,
        pause
    };
    const data = await request({
        path: 'raining/MOD/AdvancedSegmentAsyncService/updateSegmentAndBindStatus',
        params: updateParams
    });
    const dataList = (data.dataMap || {}).segments || [];
    return {dataList, isAsyncTask: false};
}