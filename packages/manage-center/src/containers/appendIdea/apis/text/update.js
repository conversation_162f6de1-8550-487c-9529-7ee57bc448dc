/*
 * @file: index
 * @Author: lian<PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2022-09-20 20:53:05
 */
import {difference} from 'lodash-es';
import {request} from '@baidu/winds-ajax';
import {handleBatchUpdateParams} from 'app/api/utils/materialList';
import {isSublinkType} from 'app/containers/appendIdea/text/config';
import {material_token as token} from 'commonLibs/tableList/config/token';
import IdType from 'commonLibs/config/idType';
import MARKET_TARGET from 'commonLibs/config/marketTarget';
import {CAMPAIGNLEVEL} from 'commonLibs/levelSelector';
import {subscriptConfig} from '../../text/subscript/config';
import {SUBSCRIPT} from 'commonLibs/config/segment';
import CreativeSource from 'commonLibs/config/creativeSource';
import {AutoStatusKeyMap} from 'app/containers/appendIdea/text/config';
import dayjs from 'dayjs';

const subscriptFields = [
    'segmentId',
    'segmentType',
    'content',
    'auditContent',
    'source',
    'bindInfo'
];


const {
    // '网站链接',
    WEB,
    // '应用推广',
    APP,
    // '线下门店',
    STORE,
    SHOP
} = MARKET_TARGET;

export async function inlineSaveStatusApi(segmentId, {status}) {
    const data = await request({
        path: 'thunder/MOD/SegmentService/updateSegment',
        params: {
            segmentTypes: [{
                segmentId,
                status
            }]
        }
    });
    return data || [];
}


export async function batchSaveStatusApi(baseParams, {status}) {
    const {
        selectionInfo: {selectedIds}
    } = baseParams;
    const data = await request({
        path: 'thunder/MOD/SegmentService/updateSegment',
        params: {
            segmentTypes: selectedIds.map(id => {
                return {
                    segmentId: id,
                    status
                };
            })
        }
    });
    const dataList = data || [];
    return {dataList, isAsyncTask: false};
}


export async function batchDeleteApi(commonParams) {
    const realCommonParams = handleBatchUpdateParams(commonParams);
    const data = await request({
        path: 'raining/DEL/SegmentAsyncService/deleteSegmentAndBind',
        params: {
            ...realCommonParams,
            segmentListType: 'sublinkSelf'
        }
    });
    const isAsyncTask = data.isAsyncTask;
    const dataList = data.dataList || data; // 批量保存使用dataList，行内保存使用data
    return {dataList, isAsyncTask};
};


export async function batchSaveTextLinkApi(commonParams, values) {
    const {selectedRecords} = commonParams.selectionInfo;
    const realCommonParams = handleBatchUpdateParams({
        ...commonParams,
        selectionInfo: {
            ...commonParams.selectionInfo,
            selectedIds: selectedRecords.filter(item => isSublinkType(item.segmentType)).map(item => item.segmentId)
        }
    });
    const {rangeType, replaceText, searchText} = values;
    const data = await request({
        path: 'raining/MOD/SegmentAsyncService/updateSegment',
        params: {
            ...realCommonParams,
            items: {
                [rangeType]: replaceText
            },
            itemsHelper: {
                modType: 1,
                searchContent: searchText,
                targetContent: replaceText
            }
        }
    });
    const isAsyncTask = data.isAsyncTask;
    const dataList = data.dataList || [];
    return {dataList, isAsyncTask};
};

export async function batchSaveTextUrlApi(commonParams, values) {
    const {selectedRecords} = commonParams.selectionInfo;
    const realCommonParams = handleBatchUpdateParams({
        ...commonParams,
        selectionInfo: {
            ...commonParams.selectionInfo,
            selectedIds: selectedRecords.filter(item => isSublinkType(item.segmentType)).map(item => item.segmentId)
        }
    });
    const {rangeType, replaceText} = values;
    const data = await request({
        path: 'raining/MOD/SegmentAsyncService/updateSegment',
        params: {
            ...realCommonParams,
            items: {
                [rangeType]: replaceText
            }
        }
    });
    const isAsyncTask = data.isAsyncTask;
    const dataList = data.dataList || [];
    return {dataList, isAsyncTask};
};

export async function inlineSaveAutoStatusApi(segmentId, {adgroupId, segmentType, pause}, options) {

    const {
        pageLevel: {idType, levelId}
    } = options;
    const data = await request({
        path: 'raining/MOD/ExcavateTextAsyncService/updateExcavateTextStatus',
        params: {
            mtlIds: [],
            checkAll: true,
            componentLevel: true,
            items: {
                [AutoStatusKeyMap[segmentType]]: !pause
            },
            checkAllCondition: {
                fieldFilters: [],
                idType,
                ids: [levelId]
            }
        }
    });
    const isAsyncTask = data.isAsyncTask;
    const dataList = data.dataList || [];
    return [{dataList, isAsyncTask}];
}

export async function batchSaveAutoStatusApi(baseParams, {pause}) {
    const {selectionInfo: {selectedIds}} = baseParams;
    const {
        pageLevel: {idType, levelId}
    } = baseParams;
    const items = {};
    selectedIds.forEach(segmentType => {
        items[AutoStatusKeyMap[segmentType]] = !pause;
    });
    const data = await request({
        path: 'raining/MOD/ExcavateTextAsyncService/updateExcavateTextStatus',
        params: {
            mtlIds: [],
            checkAll: true,
            componentLevel: true,
            items,
            checkAllCondition: {
                fieldFilters: [],
                idType,
                ids: [levelId]
            }
        }
    });
    const isAsyncTask = data.isAsyncTask;
    const dataList = data.dataList || [];
    return {dataList, isAsyncTask};
}

const fields = [
    'userCommentStatus',
    'knowledgeTextStatus',
    'jimuyuContentStatus',
    'adgroupName',
    'campaignId',
    'adgroupId',
    'marketingTargetId',
    'businessPointId',
    'impression'
];
export function fetchBindRelationUserAdgroupParams(params) {
    const {userId, field = 'knowledgeTextStatus'} = params;
    return {
        path: 'puppet/GET/MaterialQueryFunction/getMaterialAdgroupList',
        params: {
            token,
            reportType: 1620200,
            idType: IdType.USER_LEVEL,
            ids: [userId],
            sortField: '',
            isDesc: false,
            limit: [0, 2000], // 固定2000
            fieldFilters: [
                {
                    field, // 必须传
                    op: 'in',
                    // values: [0]
                    values: [field === 'userCommentStatus' ? 1 : 0] // userCommentStatus是反的
                },
                ...(
                    field !== 'userCommentStatus'
                        ? [{
                            field: 'marketingTargetId', // 必须传
                            op: 'in',
                            values: [WEB, APP, STORE, SHOP]
                        }] : []
                )
            ],
            fields
        }
    };
};

export async function fetchBindRelationUserAdgroup(params) {
    const adgroupParams = fetchBindRelationUserAdgroupParams(params);
    const data = await request(adgroupParams);
    return data;
};

export async function inlineSaveNotAllBindRelationApi(segmentId, values, options) {
    const {
        pageLevel: {idType, levelId},
        initialBindData,
        bindLevel
    } = options;
    const {selectAdgroupIds} = values.imageBindRelation.adgroup;
    const initialBindIds = initialBindData.map(i => i.adgroupId);
    const deletedIds = difference(initialBindIds, selectAdgroupIds) || [];
    const mtlIds = deletedIds.map(adgroupId => ({
        adgroupId,
        operationType: 1 // 0-勾选；1-去掉勾选
    }));
    const addIds = difference(selectAdgroupIds, initialBindIds) || [];
    mtlIds.push(...addIds.map(adgroupId => ({
        adgroupId,
        operationType: 0 // 0-勾选；1-去掉勾选
    })));
    const params = {
        mtlIds,
        checkAll: false,
        items: {
            [AutoStatusKeyMap[segmentId]]: true
        },
        checkAllCondition: {
            fieldFilters: [],
            idType,
            ids: [levelId]
        },
        bindLevel
    };
    const data = await request({
        path: 'raining/MOD/ExcavateTextAsyncService/updateExcavateTextScopeStatus',
        params
    });
    const isAsyncTask = data.isAsyncTask;
    const dataList = data.dataList || [];
    return [{dataList, isAsyncTask}];
}

export async function inlineSaveAllBindRelationApi(segmentId, values, options) {
    const {
        pageLevel: {idType, levelId}
    } = options;
    const {checkAll, checkAllLevel, checkAllSearchValue} = values.imageBindRelation.adgroup;
    let fieldFilters = [];
    // 非营销目标层级 过滤掉商品目录的计划。营销目标层级已经筛选该mt 不需要再过滤
    // 尴尬 后端不支持notin 先用in吧
    if (levelId !== IdType.MARKETINGTARGET_LEVEL) {
        fieldFilters = fieldFilters.concat([{
            field: 'marketingTargetId',
            op: 'in',
            values: [
                MARKET_TARGET.WEB, MARKET_TARGET.APP, MARKET_TARGET.STORE, MARKET_TARGET.SHOP,
                MARKET_TARGET.CPQL, MARKET_TARGET.B2B
            ]
        }]);
    }
    if (checkAllSearchValue) {
        const searchValueFilters = checkAllLevel !== CAMPAIGNLEVEL ? [{
            field: 'adgroupName', op: 'like', values: [checkAllSearchValue]
        }] : [{
            field: 'campaignName', op: 'like', values: [checkAllSearchValue]
        }];
        fieldFilters = fieldFilters.concat(searchValueFilters);
    }
    const params = {
        mtlIds: [],
        checkAll,
        items: {
            [AutoStatusKeyMap[segmentId]]: true
        },
        checkAllCondition: {
            fieldFilters,
            idType,
            ids: [levelId]
        }
    };
    const data = await request({
        path: 'raining/MOD/ExcavateTextAsyncService/updateExcavateTextStatus',
        params
    });
    const isAsyncTask = data.isAsyncTask;
    const dataList = data.dataList || [];
    return [{dataList, isAsyncTask}];
}

export async function inlineSaveBindRelationApi(segmentId, values, options) {
    if (isNaN(+segmentId)) {
        segmentId = segmentId.match(/(\d+)/)[1];
    }
    const {checkAll} = values.imageBindRelation.adgroup;
    if (checkAll) {
        return await inlineSaveAllBindRelationApi(segmentId, values, options);
    }
    return await inlineSaveNotAllBindRelationApi(segmentId, values, options);
}

export const fetchSubscriptList = ({
    userId
}) => {
    const params = {
        idType: IdType.USER_LEVEL,
        ids: [userId],
        sortField: '',
        isDesc: false,
        startTime: dayjs().format('YYYY-MM-DD'),
        endTime: dayjs().format('YYYY-MM-DD'),
        limit: [0, 20],
        fieldFilters: [
            {
                'field': 'source',
                'op': 'in',
                'values': [
                    CreativeSource.USER_CAMPAIGN
                ]
            },
            {
                'field': 'segmentType',
                'op': 'in',
                'values': [
                    SUBSCRIPT
                ]
            }
        ],
        fields: subscriptFields
    };
    return request({
        path: 'puppet/GET/MaterialFunction/getMtlQuerySegmentComponentList',
        params
    });
};

export const inlineSaveSubscriptApi = async ({userId, campaignIds, currentId}) => {
    const params = {
        checkAll: false,
        idType: IdType.USER_LEVEL,
        ids: [userId],
        bindLevel: 2,
        items: [
            {
                segment: {
                    segmentId: currentId,
                    source: CreativeSource.USER_CAMPAIGN
                },
                segmentBinds: campaignIds.map(item => ({
                    bindLevel: IdType.USER_LEVEL,
                    campaignId: item,
                    bindSource: 0
                }))
            }
        ],
        mtlIds: campaignIds,
        checkAllCondition: {
            idType: IdType.USER_LEVEL,
            ids: [userId],
            fieldFilters: []
        },
        deleteBindIds: []
    };
    const data = await request({
        path: 'raining/MOD/AdvancedSegmentAsyncService/updateSegmentAndBind',
        params
    });
    return data;
};

export const inlineAddSubscriptApi = ({userId, campaignIds, currentId}) => {
    const params = {
        checkAll: false,
        bindLevel: 2,
        items: [
            {
                segment: {
                    source: CreativeSource.USER_CAMPAIGN,
                    segmentType: SUBSCRIPT, // 角标组件
                    auditContent: {
                        label: subscriptConfig[currentId] // 角标组件内容
                    }
                },
                segmentBinds: campaignIds.map(item => ({
                    bindLevel: IdType.USER_LEVEL,
                    campaignId: item,
                    bindSource: 0
                }))
            }
        ],
        mtlIds: campaignIds
    };
    return request({
        path: 'raining/ADD/AdvancedSegmentAsyncService/addSegmentAndBind',
        params
    });
};

export const inlineSubscriptStatus = async (baseParams, {pause}) => {
    const {
        selectedIds,
        userId
    } = baseParams;
    const params = {
        checkAll: false,
        idType: IdType.USER_LEVEL,
        ids: [userId],
        mtlIds: selectedIds,
        segmentIds: selectedIds,
        pause
    };
    return request({
        path: 'raining/MOD/AdvancedSegmentAsyncService/updateSegmentAndBindStatus',
        params
    });
};

export const batchSaveSubscriptApi = async (baseParams, {pause}) => {
    const {
        selectedIds,
        userId
    } = baseParams;
    const params = {
        checkAll: false,
        idType: IdType.USER_LEVEL,
        ids: [userId],
        mtlIds: selectedIds,
        segmentIds: selectedIds,
        pause
    };
    const data = await request({
        path: 'raining/MOD/AdvancedSegmentAsyncService/updateSegmentAndBindStatus',
        params
    });
    const dataList = (data.dataMap || {}).segments || [];
    return {dataList, isAsyncTask: false};
};


export const getCampaignList = ({userId}) => {
    const params = {
        queryFields: [
            'campaignId',
            'campaignName',
            'adgroupCount',
            'priceStrategyId',
            'priceStrategyName',
            'carLabelContent'
        ],
        ids: [userId],
        idType: IdType.USER_LEVEL,
        filterSmartCampaign: 1,
        excludeDpaCampaign: 1
    };
    return request({
        path: 'thunder/GET/AccountTreeService/getAccountTreeCampaignQuery',
        params
    });
};

