/*
 * @file: index
 * @Author: <EMAIL>
 * @Date: 2022-11-14 17:23:30
 */
import {request} from '@baidu/winds-ajax';
import {targetTypeEnum} from 'commonLibs/config/targetType';
import CreativeSource from 'commonLibs/config/creativeSource';
import {transMtorBizToUserLevel, makeSelection, transIdFilterTofieldFilter} from 'app/api/utils/materialList';
import {convertFieldFilterForMaterialList} from 'commonLibs/api/materialQuery/utils';
import {formatVideoSegments} from 'app/containers/fcNew/creative/utils/image';

export async function inlineSaveBindRelationApi(segmentId, values, {record, initialBindData, pageLevel, userId}) {
    const {idType, levelId} = transMtorBizToUserLevel({...pageLevel, userId});
    const {targetType} = values.imageBindRelation;
    const {checkAll, bindIds: selectAdgroupIds} = values.imageBindRelation;
    const params = {
        checkAll,
        idType,
        ids: [levelId],
        bindLevel: targetType,
        items: [{
            segment: {
                segmentId,
                source: targetType === targetTypeEnum.ACCOUNT
                    ? CreativeSource.USER_ACCOUNT
                    : CreativeSource.USER_ADGROUP
            },
            segmentBinds: targetType === targetTypeEnum.ACCOUNT ? null : selectAdgroupIds.map(adgroupId => ({
                bindLevel: targetType,
                adgroupId,
                bindSource: 0

            }))
        }],
        mtlIds: selectAdgroupIds,
        checkAllCondition: {
            idType,
            ids: [levelId],
            fieldFilters: []
        },
        deleteBindIds: initialBindData.reduce((memo, item) => {
            if (!selectAdgroupIds.includes(item.adgroupId)) {
                memo.push(item.bindId);
            }
            return memo;
        }, [])
    };
    const data = await request({
        path: 'raining/MOD/AdvancedSegmentAsyncService/updateSegmentAndBind',
        params
    });
    return data?.dataMap?.segments || [];
}


export async function inlineSaveStatusApi(segmentId, {status, pause}, {pageLevel, userId}) {
    const {idType, levelId} = transMtorBizToUserLevel({...pageLevel, userId});

    const data = await request({
        path: 'raining/MOD/AdvancedSegmentAsyncService/updateSegmentAndBindStatus',
        params: {
            checkAll: false,
            idType,
            ids: [levelId],
            mtlIds: [segmentId],
            segmentIds: [segmentId],
            pause
        }
    });
    return data?.dataMap?.segments || [];
}

export async function inlineSaveVideoApi(segmentId, {record, splendidVideoInfo, imagesArray}) {
    const {source, segmentType, auditContent, content} = record;
    const {videoUrl, videoId, videoWidth, videoHeight} = (auditContent || content).videos[0];

    const videoSegment = formatVideoSegments({
        videoId,
        videoUrl,
        splendidVideoInfo,
        images: imagesArray,
        width: videoWidth,
        height: videoHeight,
        segmentType,
        isNeedJson: false
    });
    const data = await request({
        path: 'cartier/MOD/AdvancedSegmentService/updateSegment',
        params: {
            items: [{
                source,
                segmentId,
                segmentType,
                auditContent: videoSegment.segment.auditContent
            }]
        }
    });
    return data || [];
}

export async function batchSaveStatusApi(baseParams, {pause}) {
    const {
        selectionInfo,
        pageLevel: {idType, levelId},
        userId,
        filters,
        defaultFieldFilters = []
    } = baseParams;
    const {mtlIds, checkAll} = makeSelection(selectionInfo);
    const fieldFilters = filters.map(convertFieldFilterForMaterialList).concat(defaultFieldFilters);
    const {
        idType: idType_,
        ids,
        formatedFilters
    } = transIdFilterTofieldFilter(fieldFilters, {idType, levelId, userId}, true);

    const data = await request({
        path: 'raining/MOD/AdvancedSegmentAsyncService/updateSegmentAndBindStatus',
        params: {
            checkAll,
            idType: idType_,
            ids,
            mtlIds: checkAll ? [] : mtlIds,
            segmentIds: checkAll ? [] : mtlIds,
            bindIds: [],
            pause,
            checkAllCondition: {
                idType: idType_,
                ids,
                fields: ['segmentId'],
                fieldFilters: checkAll ? formatedFilters : null
            }
        }
    });
    const {dataMap, isAsyncTask} = data || {};
    return {dataList: dataMap?.segments || [], isAsyncTask};
}

export async function batchDeleteApi(commonParams) {
    const {
        selectionInfo,
        pageLevel: {idType, levelId},
        filters,
        userId,
        defaultFieldFilters = []
    } = commonParams;
    const fieldFilters = filters.map(convertFieldFilterForMaterialList).concat(defaultFieldFilters);
    const {mtlIds, checkAll} = makeSelection(selectionInfo);
    const {
        idType: idType_,
        ids,
        formatedFilters
    } = transIdFilterTofieldFilter(fieldFilters, {idType, levelId, userId}, true);

    const data = await request({
        path: 'raining/DEL/AdvancedSegmentAsyncService/deleteSegmentAndBind',
        params: {
            checkAll,
            ids,
            idType: idType_,
            mtlIds,
            checkAllCondition: {
                idType: idType_,
                ids,
                fields: ['segmentId'],
                fieldFilters: checkAll ? formatedFilters : null
            },
            segmentIds: mtlIds,
            bindIds: []
        }
    });
    const isAsyncTask = data.isAsyncTask;
    const dataList = data.dataList || data; // 批量保存使用dataList，行内保存使用data
    return {dataList, isAsyncTask};
};