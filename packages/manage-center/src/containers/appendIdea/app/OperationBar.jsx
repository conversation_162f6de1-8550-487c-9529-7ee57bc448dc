/*
 * @file: 顶部操作区
 * @author: lian<PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2022-04-18 15:54:08
 */
import {memo, useCallback} from 'react';
import {Button} from '@baidu/one-ui';
import RouteCalendar from 'commonLibs/RouteCalendar';
import {MODULE_MC} from 'commonLibs/config/route';
import SearchBox from 'commonLibs/materialList/SearchBox';
import {defaultOperatorConfig} from 'commonLibs/config/materialList/defaultOperatorConfig';
import {useRouterRedirect} from 'commonLibs/route';


function OperationBar({
    resetColumnsWidth,
    changeFilterByField
}) {
    const routeCalendarProps = {
        module: MODULE_MC,
        material: 'app'
    };
    const clickToAppBind = useRouterRedirect('@appBindManage');

    const newBtnProps = {
        type: 'primary',
        onClick: () => {
            clickToAppBind({}, {entrySource: 1});
        }
    };
    const onSearch = useCallback(
        (value, {operatorValue} = {}) => {
            if (!value) {
                return;
            }
            changeFilterByField('appName', {
                value: [value],
                operatorValue: operatorValue || defaultOperatorConfig.string[0].value
            });
        },
        [changeFilterByField]
    );
    return (
        <div className="operation-container">
            <div className="area">
                <Button {...newBtnProps}>+应用绑定管理</Button>
                <SearchBox fieldName="应用名称" onSearch={onSearch} />
            </div>
            <div className="area">
                <RouteCalendar {...routeCalendarProps} />
                <Button onClick={resetColumnsWidth}>重置列宽</Button>
            </div>
        </div>
    );
}

export default memo(OperationBar);
