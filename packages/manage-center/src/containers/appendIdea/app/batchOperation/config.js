/*
 * @file: 创意列表批量编辑项配置
 * @author: l<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2022-04-25 17:18:53
 */
import Enum from 'enum';
import {IconPlayCircle, IconPauseCircle} from 'dls-icons-react';
import {getBatchOptionsWithDivider} from 'commonLibs/utils/getBatchOptionsWithDivider';


// 分隔配置
const batchFieldDividerConfig = new Enum({
    STATUS: '状态'
});

const BatchFieldMap = {
    start: {
        value: 'start',
        label: <><IconPlayCircle />&nbsp;启用</>,
        divider: batchFieldDividerConfig.STATUS.key
    },
    pause: {
        value: 'pause',
        label: <><IconPauseCircle />&nbsp;暂停</>,
        divider: batchFieldDividerConfig.STATUS.key
    }
};

export default function getBatchOptionFields() {
    const fields = ['start', 'pause'];
    return getBatchOptionsWithDivider(fields.map(field =>
        BatchFieldMap[field]), batchFieldDividerConfig);
}
