/*
 * @file: AppName
 * @author: lianxiaof<PERSON>@baidu.com
 */
import {withSumCell} from 'commonLibs/tableList/utils';
import {createCustomRender} from 'commonLibs/utils/render';

function AppName({record, trigger}) {
    return (
        <div className='column-cell-flex'>
            <div className='multiple-cut'>{record.appinfo?.name || '-'}</div>
        </div>
    );
}
export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => <AppName record={record} trigger={trigger} />
    ))
};
