/*
 * @file: Desc
 * @author: <EMAIL>
 * @Date: 2022-04-21 20:23:31
 */
import {withSumCell} from 'commonLibs/tableList/utils';
import {createCustomRender} from 'commonLibs/utils/render';
import {Fragment} from 'react';
import {appSegmentEnum} from 'commonLibs/config/osType';
import {IconImage} from 'dls-icons-react';
import Stars from './stars';
import {appDynamicStatusMap} from '../../filterConfig';
import qrcode from 'app/resource/svg/qrCode.svg';
import './style.less';

function PcPreview({record}) {
    const {
        name,
        iconurl,
        version,
        developername
    } = record.appinfo;
    return (
        <div className="creative-component-app-preview pc-preview">
            <div className="preview-left-container">
                <div className="base-info-container">
                    <img className="preview-icon" src={iconurl} alt="" />
                    <div className="preview-name-container"><span className="preview-name">{name}</span></div>
                    <div className="preview-stars"><Stars starScore={4.5} />4.5</div>
                </div>
                {developername && <div className="app-preview-developer-name">{developername}</div>}
                {
                    version && (
                        <div className="app-preview-developer-name">
                            <span className="preview-developer-name-item">版本{version}</span>
                            <span className="preview-developer-name-item">功能</span>
                            <span className="preview-developer-name-item">隐私</span>
                            <span className="preview-developer-name-item">权限</span>
                        </div>
                    )
                }
            </div>
            <div className="preview-right-container">
                <img src={qrcode} className="qrcode-icon" />
                扫码下载
            </div>
        </div>
    );
}

const AppPreview = ({
    record
}) => {
    if (!record.appinfo) {
        return (
            <div className="creative-component-app-preview creative-component-app-preview-skeleton">
                <div className="app-preview-badge">已解绑</div>
                <div className="app-preview-content">
                    <div className="app-preview-icon-skeleton">
                        <IconImage className="app-preview-icon-skeleton-img" />
                    </div>
                    <div className="app-preview-message">
                        <div className="app-preview-skeleton-block size-md" />
                        <div className="app-preview-rate-info">
                            <div className="app-preview-skeleton-block size-sm" />
                            <div className="app-preview-skeleton-block size-md" />
                        </div>
                        <div className="app-preview-skeleton-block size-lg" />

                    </div>
                </div>

                <div className="app-preview-download-button disabled">
                    下载
                </div>
            </div>
        );
    }

    const {
        name,
        iconurl,
        version,
        summary,
        developername
    } = record.appinfo;

    const segmentType = record.segmentType;
    const isInvalid = record.status === appDynamicStatusMap.invalid;
    const isAndroid = segmentType === appSegmentEnum.ANDROID_DOWNLOAD.value;
    const isIOS = segmentType === appSegmentEnum.IOS_DOWNLOAD.value;
    const isAndroidGame = segmentType === appSegmentEnum.ANDROID_GAME.value;
    const isGame = isAndroidGame; // 后续加ios的话加个 ||
    if (segmentType === appSegmentEnum.PC_DOWNLOAD.value) {
        return <PcPreview record={record} />;
    }
    return (
        <div className={`creative-component-app-preview ${isInvalid ? 'creative-component-app-preview-invalid' : ''}`}>
            <div className="app-preview-content">
                <img className="app-preview-icon" src={iconurl} alt="" />
                <div className="app-preview-message">
                    <div className="app-preview-app-title">
                        <span className={`app-preview-app-name ${isAndroid ? 'app-name-android' : ''}`}>{name}</span>
                        {isIOS && (<span className="app-preview-app-summary">-{summary}</span>)}
                        {isAndroid && (<span className="app-preview-version">版本{version}</span>)}
                    </div>
                    <div className="app-preview-rate-info">
                        {
                            isGame
                                ? (
                                    <div className="app-preview-rate-points">xx分</div>
                                )
                                : (
                                    <Fragment>
                                        <div className="app-preview-rate-stars">
                                            {/* 星级默认显示4 */}
                                            <Stars starScore={4} />
                                        </div>
                                        <div className="app-preview-rate-points">xx分</div>
                                    </Fragment>
                                )
                        }

                        {
                            isIOS && (
                                <div className="app-preview-feedback">xx份评价</div>
                            )
                        }
                        {
                            isGame && (
                                <div className="app-preview-feedback">xx下载</div>
                            )
                        }
                        {
                            isAndroid && (
                                <div>
                                    <span className="app-preview-privacy">隐私</span>
                                    <span>权限</span>
                                </div>
                            )
                        }


                    </div>
                    {
                        isAndroid && (
                            <div className="app-preview-developer-name">{developername}</div>
                        )
                    }
                    {
                        isGame && (
                            <div className="app-preview-developer-name">{summary}</div>
                        )
                    }

                </div>
            </div>

            <div className={`app-preview-download-button ${isGame ? 'game' : ''}`}>
                下载
            </div>
        </div>
    );
};

export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => <AppPreview record={record} trigger={trigger} />,
        {needSum: true}
    ))
};
