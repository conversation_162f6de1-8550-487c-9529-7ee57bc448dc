
/**
* @file 创意组件预览 - 星级
* <AUTHOR> (jilian<PERSON>@baidu.com)
* @date 2021/7/7
*/

import {
    IconStarHalf,
    IconStar,
    IconStarSolid
} from 'dls-icons-react';

const Stars = ({
    starScore
}) => {

    const iconProps = {
        className: 'app-star-icon'
    };
    return (
        <span className="app-star-container">
            {
                Array(5).fill(0).map((i, idx) => {
                    if (starScore >= idx + 1) {
                        return (<IconStarSolid key={idx} {...iconProps} />);
                    }
                    else if (starScore - idx > 0) {
                        return (<IconStarHalf key={idx} {...iconProps} />);

                    }
                    return (<IconStar key={idx} {...iconProps} />);
                })
            }
        </span>
    );
};

export default Stars;
