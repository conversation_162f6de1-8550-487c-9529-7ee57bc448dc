.creative-component-app-preview {
    font-size: @dls-font-size-0;
    display: flex;
    align-items: center;
    color: #858585;
    width: 304px;
    border: 1px solid @dls-color-gray-4;
    padding: @dls-padding-unit * 3;
    border-radius: @dls-border-radius-1;
    background-color: @dls-background-color-base-1;
    position: relative;

    &.creative-component-app-preview-invalid::after {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background-color: @dls-color-gray-2;
        opacity: .3;
        content: '';
        pointer-events: none;
    }

    &.creative-component-app-preview-skeleton {
        .app-preview-badge {
            position: absolute;
            right: 0;
            top: 0;
            width: 52px;
            height: @dls-height-unit * 6;
            line-height: @dls-height-unit * 6;
            background: #f1f6ff;
            border-radius: 4px;
            font-size: 12px;
            color: @dls-color-brand-7;
            text-align: center;
        }
        .app-preview-icon-skeleton-img {
            width: @dls-height-unit * 6;
            height: @dls-height-unit * 6;
            color: @dls-color-gray-2;
        }
        .app-preview-skeleton-block {
            height: 18px;
            background-color: @dls-color-gray-1;
            border-radius: @dls-border-radius-0;

            &.size-sm {
                width: 20px;
            }
            &.size-md {
                width: 80px;
            }
            &.size-lg {
                width: 160px;
            }
        }
        .app-preview-rate-info {
            margin-top: @dls-padding-unit * 2;
            margin-bottom: @dls-padding-unit;

            .app-preview-skeleton-block + .app-preview-skeleton-block {
                margin-left: @dls-padding-unit;
            }
        }
    }

    .app-preview-download-button {
        width: @dls-height-xl;
        height: @dls-height-unit * 5;
        line-height: @dls-height-unit * 5;
        background-color: #4e6ef2;
        color: #fff;
        font-weight: 400;
        border-radius: @dls-border-radius-3;
        text-align: center;
        border: 1px solid transparent;
        margin-left: @dls-padding-unit;
        &.game {
            background-color: #fff;
            color: @dls-color-gray-9;
            border-color: @dls-color-gray-6;
        }
        &.disabled {
            background-color: @dls-background-color-base-2-disabled;
            color: @dls-color-gray-5;
            border-color: @dls-color-gray-2;
        }
    }
    .app-preview-content {
        display: flex;
        align-items: center;
        flex: 1;
        width: 0;
    }

    .app-preview-message {
        flex: 1;
        width: 0;
    }
    .app-preview-icon,
    .app-preview-icon-skeleton {
        width: @dls-height-xl;
        height: @dls-height-xl;
        border-radius: @dls-border-radius-2;
        margin-right: @dls-padding-unit * 2;
    }
    .app-preview-icon-skeleton {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: @dls-color-gray-1;
    }

    .app-preview-app-title {
        font-size: @dls-font-size-0;
        color: @dls-color-gray-9;
        margin-bottom: @dls-padding-unit;

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: flex;
        .app-preview-app-name {
            color: @dls-color-error-6;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            &.app-name-android {
                max-width: @dls-padding-unit * 25;
            }
        }
        .app-preview-version {
            margin-left: @dls-padding-unit * 2;
        }
    }
    .app-preview-app-summary {
        color: @dls-color-gray-9;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        width: 0;
    }

    .app-preview-rate-info {
        display: flex;
    }

    .app-preview-rate-stars {
        .app-star-icon {
            color: #f60;
        }
    }

    .app-preview-rate-points {
        margin: 0 @dls-padding-unit;
    }

    .app-preview-privacy {
        margin-right: @dls-padding-unit;
    }
    .app-preview-developer-name {
        margin-top: @dls-padding-unit;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .app-type-game {
        .app-preview-rate-points {
            margin-left: 0;
            color: #f60;
        }
    }

    .preview-left-container {
        .base-info-container {
            display: flex;
            .preview-icon {
                width: 20px;
                height: 20px;
                margin-right: @dls-padding-unit;
            }
            .preview-name-container {
                max-width: 100px;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
            }
            .preview-name {
                font-size: @dls-font-size-0;
                color: #333;
                font-weight: @dls-font-weight-2;
                margin-right: @dls-padding-unit;
            }
            .preview-stars {
                color: #f60;
                display: flex;
            }
        }
        .preview-developer-name-item {
            margin-right: @dls-padding-unit;
        }
    }
    .preview-right-container {
        display: flex;
        align-items: center;
        border-radius: @dls-border-radius-3;
        gap: 2px;
        color: #4e6ef2;
        font-weight: @dls-font-weight-2;
        background-color: #E8F7FF;
        padding: 3px 8px;
    }
}
.pc-preview {
    justify-content: space-between;
}
