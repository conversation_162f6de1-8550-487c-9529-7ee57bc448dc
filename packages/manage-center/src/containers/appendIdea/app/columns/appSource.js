/*
 * @file: appSource
 * @author: lian<PERSON><PERSON><PERSON>@baidu.com
 */
import {withSumCell} from 'commonLibs/tableList/utils';
import {createCustomRender} from 'commonLibs/utils/render';
import {appSourceTextMap, appSourceMap} from '../filterConfig';

function AppSource({record, trigger}) {
    const source = record.appinfo?.source;
    return (
        <div className='column-cell-flex'>
            <div className='multiple-cut'>{appSourceTextMap[source] || '-'}</div>
        </div>
    );
}

const appSourceOptions = [{
    label: appSourceTextMap[appSourceMap.fastDownload],
    value: appSourceMap.fastDownload
}, {
    label: appSourceTextMap[appSourceMap.putOn],
    value: appSourceMap.putOn
}, {
    label: appSourceTextMap[appSourceMap.xingju],
    value: [appSourceMap.appDev, appSourceMap.xingju]
}];

export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => <AppSource record={record} trigger={trigger} />
    )),
    filters: {
        options: appSourceOptions
    }
};




