/*
 * @file: AppVersion
 * @author: <EMAIL>
 */
import {withSumCell} from 'commonLibs/tableList/utils';
import {createCustomRender} from 'commonLibs/utils/render';

function AppVersion({record, trigger}) {
    return (
        <div className='column-cell-flex'>
            <div className='multiple-cut'>{record.appinfo?.version || '-'}</div>
        </div>
    );
}
export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => <AppVersion record={record} trigger={trigger} />
    ))
};
