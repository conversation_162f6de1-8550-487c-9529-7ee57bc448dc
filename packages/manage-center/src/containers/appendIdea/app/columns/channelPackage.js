/*
 * @file: ChannelPackage
 * @author: <EMAIL>
 */
import {withSumCell} from 'commonLibs/tableList/utils';
import {createCustomRender} from 'commonLibs/utils/render';

function ChannelPackage({record, trigger}) {
    return (
        <div className='column-cell-flex'>
            <div className='multiple-cut'>{record.appinfo?.channelpackage || '-'}</div>
        </div>
    );
}
export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => <ChannelPackage record={record} trigger={trigger} />
    ))
};
