/*
 * @file: platform
 * @author: lian<PERSON>of<PERSON>@baidu.com
 */
import {withSumCell} from 'commonLibs/tableList/utils';
import {createCustomRender} from 'commonLibs/utils/render';
import {getAppSystemConfig, getAppOSNameByKey} from 'commonLibs/AppSelect/config';

function Platform({record, trigger}) {
    const platform = record.appinfo?.platform;
    return (
        <div className='column-cell-flex'>
            <div className='multiple-cut'>{getAppOSNameByKey(platform) || '-'}</div>
        </div>
    );
}

export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => <Platform record={record} trigger={trigger} />
    )),
    filters: {
        options: getAppSystemConfig()
    }
};