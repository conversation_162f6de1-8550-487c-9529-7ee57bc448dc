/*
 * @file: SegmentType
 * @author: lian<PERSON><PERSON><PERSON>@baidu.com
 */
import {withSumCell} from 'commonLibs/tableList/utils';
import {createCustomRender} from 'commonLibs/utils/render';
import {appSegmentText, appSegmentEnum} from 'commonLibs/config/osType';

function SegmentType({record, trigger}) {
    return (
        <div className='column-cell-flex'>
            <div className='multiple-cut'>{appSegmentText[record.segmentType] || '-'}</div>
        </div>
    );
}

const segmentTypeOptions = [{
    label: '下载按钮',
    value: [appSegmentEnum.ANDROID_DOWNLOAD.value, appSegmentEnum.IOS_DOWNLOAD.value]
}, {
    label: '百度游戏',
    value: appSegmentEnum.ANDROID_GAME.value
}];

export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => <SegmentType record={record} trigger={trigger} />
    )),
    filters: {
        options: segmentTypeOptions
    }
};

