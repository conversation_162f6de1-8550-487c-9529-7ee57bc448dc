/*
 * @file: status
 * @author: lian<PERSON><PERSON><EMAIL>
 */
import {withSumCell} from 'commonLibs/tableList/utils';
import {createCustomRender} from 'commonLibs/utils/render';
import {classNameTypes} from 'commonLibs/config/materialList/status';
import {useCallback} from 'react';
import {StatusPure} from 'commonLibs/tableList/columns/status';
import {IconPlayCircle, IconPauseCircle} from 'dls-icons-react';
import {displayError} from 'commonLibs/utils/materialList/error';
import InvalidReason from './invalidReason';

export const statusKeyMap = {
    START: 0,
    PAUSE: 1,
    INVALID: 2
};

export const statusTextMap = {
    [statusKeyMap.START]: '开启',
    [statusKeyMap.PAUSE]: '暂停',
    [statusKeyMap.INVALID]: '无效'
};

export const statusClassNames = {
    [statusKeyMap.START]: classNameTypes.normal,
    [statusKeyMap.PAUSE]: classNameTypes.warning,
    [statusKeyMap.INVALID]: classNameTypes.error
};


function Status({record, trigger}) {
    const {bindId, status} = record;
    const statusPureProps = {
        color: statusClassNames[status],
        label: statusTextMap[status],
        hasUnderscore: false
    };

    const Icon = status === statusKeyMap.PAUSE ? IconPlayCircle : IconPauseCircle;
    const inlineSaveStatus =  trigger('getInlineSaveByMethodName', 'inlineSaveStatus');
    const updateStatus = useCallback(async e => {
        e.stopPropagation();
        try {
            await inlineSaveStatus(
                bindId,
                {
                    ...record,
                    pause: !record.pause
                }
            );
        }
        catch (err) {
            displayError(err);
        }

    }, [inlineSaveStatus, bindId, record]);
    const isShowEditIcon = status !== statusKeyMap.INVALID;
    return (
        <StatusPure {...statusPureProps}>
            <div className="brand-status">
                {
                    isShowEditIcon && (
                        <Icon className="inline-operation-icon" onClick={updateStatus} />
                    )
                }
                {
                    status === statusKeyMap.INVALID
                        ? (<InvalidReason bindId={record.bindId} />)
                        : null
                }
            </div>
        </StatusPure>
    );
}

export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => <Status record={record} trigger={trigger} />
    ))
};
