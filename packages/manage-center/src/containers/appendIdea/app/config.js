/*
 * @file: config
 * @author: l<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import {formatColumnConfiguration} from 'commonLibs/utils/materialList/columns';
import appPreview from './columns/appPreview';
import status from './columns/status';
import segmentType from './columns/segmentType';
import appName from './columns/appName';
import appVersion from './columns/appVersion';
import channelPackage from './columns/channelPackage';
import appSource from './columns/appSource';
import platform from './columns/platform';

const customFields = [
    'appPreview', 'segmentType', 'status', 'appName', 'appVersion', 'channelPackage',
    'appSource', 'platform', 'campaignName',
    'adgroupName', 'cost', 'impression', 'click', 'cpc', 'ctr'
];

const columnConfigs = {
    appPreview: {
        category: '属性',
        columnName: 'appPreview',
        columnText: '应用类组件',
        columnType: 'STRING',
        filterable: false,
        optional: false,
        sortable: false,
        draggable: true,
        feConfig: {
            columnMinWidth: 356,
            columnWidth: 356,
            fixType: 'left'
        }
    },
    appName: {
        category: '属性',
        columnName: 'appName',
        columnText: '应用名称',
        columnType: 'STRING',
        filterable: true,
        optional: false,
        sortable: false,
        draggable: true
    },
    segmentType: {
        category: '属性',
        columnName: 'segmentType',
        columnText: '组件类型',
        columnType: 'ENUM',
        filterable: true,
        optional: false,
        sortable: false,
        draggable: true
    },
    appVersion: {
        category: '属性',
        columnName: 'appVersion',
        columnText: '版本',
        columnType: 'STRING',
        filterable: false,
        optional: false,
        sortable: false,
        draggable: true
    },
    appSource: {
        category: '属性',
        columnName: 'appVersion',
        commentKey: 'appSource',
        columnText: '来源',
        columnType: 'ENUM',
        filterable: true,
        optional: false,
        sortable: false,
        draggable: true
    },
    platform: {
        category: '属性',
        columnName: 'platform',
        columnText: '类型',
        columnType: 'ENUM',
        filterable: true,
        optional: false,
        sortable: false,
        draggable: true
    },
    status: {
        category: '属性',
        columnName: 'status',
        columnText: '状态',
        columnType: 'STRING',
        filterable: false,
        optional: false,
        sortable: false,
        draggable: true,
        feConfig: {
            columnWidth: '120'
        }
    },
    campaignName: {
        category: '属性',
        columnName: 'campaignName',
        columnText: '推广计划',
        columnType: 'STRING',
        filterable: true,
        optional: false,
        sortable: false,
        draggable: true
    },
    adgroupName: {
        category: '属性',
        columnName: 'adgroupName',
        columnText: '推广单元',
        columnType: 'STRING',
        filterable: true,
        optional: false,
        sortable: false,
        draggable: true
    },
    channelPackage: {
        category: '属性',
        columnName: 'channelPackage',
        columnText: '渠道包名称',
        columnType: 'STRING',
        filterable: true,
        optional: false,
        sortable: false,
        draggable: true
    },
    cost: {
        category: '属性',
        columnName: 'cost',
        columnText: '消费',
        columnType: 'DOUBLE',
        precision: 2,
        filterable: true,
        optional: false,
        sortable: true,
        draggable: true,
        feConfig: {}
    },
    click: {
        category: '效果指标',
        columnName: 'click',
        columnText: '点击',
        columnType: 'DOUBLE',
        filterable: true,
        optional: false,
        sortable: true,
        draggable: true,
        feConfig: {}
    },
    impression: {
        category: '效果指标',
        columnName: 'impression',
        columnText: '展现',
        columnType: 'DOUBLE',
        filterable: true,
        optional: false,
        sortable: true,
        draggable: true,
        feConfig: {}
    },
    cpc: {
        category: '效果指标',
        columnName: 'cpc',
        precision: 2,
        columnText: '平均点击价格',
        columnType: 'DOUBLE',
        filterable: true,
        optional: false,
        sortable: true,
        draggable: true,
        feConfig: {}
    },
    ctr: {
        category: '效果指标',
        columnName: 'ctr',
        columnText: '点击率',
        columnType: 'DOUBLE',
        filterable: true,
        optional: false,
        sortable: true,
        draggable: true,
        feConfig: {
            filter: JSON.stringify({
                tailLabel: '%'
            })
        },
        isPercentage: true,
        precision: 2
    }
};

export function getColumConfig() {
    return {
        tableFieldsMap: {
            appPreview,
            segmentType,
            appName,
            appVersion,
            channelPackage,
            appSource,
            platform,
            status
        },
        columnConfiguration: formatColumnConfiguration({customFields, columnConfigs})
    };
}
