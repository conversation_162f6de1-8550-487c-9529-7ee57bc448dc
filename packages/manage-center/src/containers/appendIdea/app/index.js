/*
 * @file: 创意组件-门店
 * @author: <EMAIL>
 */

import {useMemo, useEffect} from 'react';
import EmptyInfo from 'commonLibs/tableList/EmptyText';
import {Table, Pagination, Button} from '@baidu/one-ui';
import {getColumConfig} from './config';
import {useCreativeStoreList, getCreativeAppListId} from './materialQuery';
import {useColumns, useDraggableColumns, useSortableColumns} from 'commonLibs/hooks/materialList/columns';
import {SEGMENT_IMAGE as materialLevel} from 'commonLibs/config/idType';
import {toOneUIRowSelectionProps} from 'commonLibs/utils/materialList/rowSelection';
import {toOneUIPaginationProps} from 'commonLibs/hooks/pagination';
import {useCalendarWithSessionStorage} from 'commonLibs/utils/getCalendar';
import {usePageLevel} from 'app/hooks/pageLevel';
import FilterList from 'commonLibs/materialList/FilterList';
import {getColumnWidthStorageKeyPath} from 'app/utils/storage';
import {useLocalStorage} from 'commonLibs/hooks/storage';
import {unshift} from 'commonLibs/utils/array';
import {MODULE_MC} from 'commonLibs/config/route';
import {isInQinggeIframe} from 'commonLibs/utils/isInIframe';
import OperationBar from './OperationBar';
import {useParams} from 'commonLibs/route';
import {keys} from 'lodash-es';
import BatchOperationBar from '../common/batchOperation';
import {statusOperationMap} from '../common/batchOperation/operationMap';
import getBatchOptionFields from './batchOperation/config';
import StyleAnalysisBtn from 'app/components/styleAnalysisBtn';
import {defaultTableProps} from 'app/containers/appendIdea/common/config';

export default function ImageListUser() {
    const [timeRange] = useCalendarWithSessionStorage({material: 'app', module: MODULE_MC});
    const pageLevel = usePageLevel();
    const params = useParams();
    const pageType = params.pageType;
    const isAdgroupLevel = !!params.adgroupId;
    const {
        tableFieldsMap,
        columnConfiguration
    } = useMemo(() => getColumConfig(), []);
    const {columnConfigs} = columnConfiguration;
    const allFields = useMemo(() => keys(columnConfigs), [columnConfigs]);
    const {
        filters,
        columns: columns_,
        handleColumnAction,
        filterMethods,
        getFilterContentByField
    } = useColumns({
        columnConfiguration, tableFieldsMap, extraConfig: {enableParamsToQuery: true}
    });
    const [{
        pending,
        error,
        data: {summary, rows, totalCount},
        selection,
        pagination,
        sorter
    },
    {
        onSelectChange,
        getSelectedInfo,
        resetRowSelection,
        setPageNo,
        setPageSize,
        refreshCreativeAppList,
        onSort,
        inlineMethods,
        batchMethods,
        deleteMethods
    }] = useCreativeStoreList({
        filters,
        materialLevel,
        timeRange,
        pageLevel,
        allFields
    });

    useEffect(
        () => handleColumnAction('getInlineSaveByMethodName', methodName => inlineMethods[methodName]),
        [handleColumnAction, inlineMethods]
    );
    useEffect(
        () => handleColumnAction('refreshCreativeAppList', refreshCreativeAppList),
        [handleColumnAction, refreshCreativeAppList]
    );

    const dataSource = useMemo(
        () => (rows.length ? unshift(rows, {...summary, totalCount}) : []),
        [rows, summary, totalCount]
    );
    const columnWidthStorageKeyPath = useMemo(() => getColumnWidthStorageKeyPath(pageType), [pageType]);
    const columnWidthStorage = useLocalStorage(columnWidthStorageKeyPath, {});
    const columns = useSortableColumns(columns_, sorter);
    const [
        columnsWithCustomWidth,
        {onDragEnd, resetColumnsWidth}
    ] = useDraggableColumns(columns, {storage: columnWidthStorage});

    const paginationProps = toOneUIPaginationProps({...pagination, setPageNo, setPageSize}, totalCount);
    const headBordered = useMemo(() => columns.some(col => col.children), [columns]);
    const rowSelectionProps = useMemo(
        () => toOneUIRowSelectionProps(
            {selection, onSelectChange},
            rows,
            {getId: getCreativeAppListId, multiPageSelection: true, maxNumber: 100}
        ), [selection, onSelectChange, rows]
    );
    const tableProps = {
        ...defaultTableProps,
        locale: {
            emptyText: <EmptyInfo hasFilter={filters.length} fetchError={error} text="暂无数据" />
        },
        rowKey: getCreativeAppListId,
        loading: pending,
        onSortClick: onSort,
        columns: columnsWithCustomWidth,
        dataSource,
        onDragEnd,
        rowSelection: rowSelectionProps,
        headBordered
    };
    const {changeFilterByField, deleteFilterByIndex, changeFilterByIndex, resetFilters} = filterMethods;
    const filterListProps = {
        filters,
        deleteFilterByIndex,
        changeFilterByIndex,
        getFilterContentByField
    };
    const operationBarsProps = {
        resetColumnsWidth,
        changeFilterByField
    };
    const options = useMemo(() => getBatchOptionFields(), []);
    const batchOperationProps = {
        getSelectedInfo,
        resetRowSelection,
        isAdgroupLevel,
        operationMap: statusOperationMap,
        options,
        ...batchMethods,
        ...deleteMethods
    };
    return (
        <div className='manage-center-dashboard-material-list-container creative-store-list-container'>
            {!isInQinggeIframe && <StyleAnalysisBtn />}
            <div
                className="operation-bar-and-operator-container"
                style={{...(isInQinggeIframe ? {paddingTop: 0} : {})}}
            >
                <BatchOperationBar {...batchOperationProps} />
                <OperationBar {...operationBarsProps} />
            </div>
            <FilterList {...filterListProps}>
                <div className="filter-list-operations">
                    <Button type="text-strong" onClick={resetFilters}>清空</Button>
                </div>
            </FilterList>
            <Table {...tableProps} />
            <Pagination className="table-pagination" {...paginationProps} />
        </div>
    );
}