/*
 * @file: useCreativeAppsList
 * @author: lian<PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2022-04-15 17:54:40
 */
import {useEffect, useMemo, useCallback, useState} from 'react';
import {useHybridRequest} from 'commonLibs/hooks/request';
import useUserInfo from 'commonLibs/hooks/useUserInfo';
import {fetchMaterialCreativeAppList} from 'app/api/materialQuery/creativeApp';
import {useKeyOrientedArray} from 'commonLibs/hooks/collection/array';
import {useQueryTableSort} from 'commonLibs/hooks/sorter';
import {useQueryTablePagination} from 'commonLibs/hooks/pagination';
import {useRowSelection} from 'commonLibs/hooks/materialList/rowSelection';
import {createError} from 'commonLibs/utils/materialList/error';
import {appSegmentEnum} from 'commonLibs/config/osType';
import {
    batchSaveStatusApi,
    inlineSaveStatusApi
} from 'app/containers/appendIdea/apis/app/update';
import {useLog, wrapLog} from 'commonLibs/logger';
import {isAppPcUser} from 'commonLibs/utils/getFlag';

const initialCreativeAppsListData = {fields: [], sum: {}};
export const getCreativeAppListId = record => record.bindId;

export function useCreativeStoreList({allFields, filters, materialLevel, ...options}) {
    const {userId} = useUserInfo();
    const log = useLog();
    const [sorter, onSort_] = useQueryTableSort({validFields: allFields});
    const [onSort] = wrapLog(log, [onSort_], {target: '^save_sort'});
    const [
        pagination,
        {setPageNo: setPageNo_, setPageSize: setPageSize_}
    ] = useQueryTablePagination();
    const [setPageNo, setPageSize] = wrapLog(log, [setPageNo_, setPageSize_], {target: '^save_pager'});
    const defaultFieldFilters = useMemo(() => getDefaultFilters(), []);
    const [refreshCreativeAppList, {
        pending,
        error,
        data: {fields: rawRows, totalCount, sum: summary} = initialCreativeAppsListData
    }] = useHybridRequest(fetchMaterialCreativeAppList, {
        ...options,
        sorter,
        pagination,
        filters,
        defaultFieldFilters,
        userId
    });

    const [rows, {
        updateByKey: updateCreativeAppById,
        getItemByKey: getCreativeAppById,
        updateItems: updateCreativeApps,
        set: setCreativeApps
    }] = useKeyOrientedArray(rawRows, {getKey: getCreativeAppListId});
    const [accumulateList, setAccumulateList] = useState(rows);
    useEffect(() => {
        setCreativeApps(rawRows);
        setAccumulateList(list => [...list, ...rawRows]);
    }, [rawRows]);

    const getCreativeAppsByKeys = useCallback(
        keys => keys.map(key => accumulateList.find(item => getCreativeAppListId(item) === key)),
        [accumulateList]
    );

    const listIds = useMemo(() => rows.map(getCreativeAppListId), [rows]);
    const [selection, selectionOperations] = useRowSelection({ids: listIds, totalCount});
    const {getSelectedInfo, resetRowSelection} = selectionOperations;

    useEffect(
        () => {
            resetRowSelection();
        },
        [options.pageLevel, filters, resetRowSelection]
    );

    const inlineSaveFactory = useCallback(
        (inlineUpdateApi, {updateType = 'replace'} = {}) => async function (materialId, values, params) {
            let data;
            try {
                [data] = await inlineUpdateApi(materialId, values, {...options, userId, ...params});
            }
            catch (error) {
                throw createError(error);
            }
            if (updateType === 'replace') {
                updateCreativeAppById(materialId, data);
            }
            else if (updateType === 'refresh') {
                refreshCreativeAppList();
            }
        },
        [updateCreativeAppById, refreshCreativeAppList, options, userId]
    );

    // updateType 为更新数据方式，可选，刷新列表(refresh)或者更新dataSource(replace)；默认为更新dataSource
    const batchSaveFactory = useCallback((batchUpdateApi, {updateType = 'replace'} = {}) => async values => {
        let data;
        try {
            const selectedInfo = getSelectedInfo();
            const {selectedIds} = selectedInfo;
            data = await batchUpdateApi({
                ...options,
                selectionInfo: {
                    ...selectedInfo,
                    selectedRecords: getCreativeAppsByKeys(selectedIds)
                },
                filters,
                userId
            }, values);
        }
        catch (err) {
            const batchSaveError = createError(err);
            batchSaveError.materialName = '应用组件';
            if (batchSaveError._normalized?.type === 'partial') {
                refreshCreativeAppList();
            }
            throw batchSaveError;
        }
        if (updateType === 'replace') {
            updateCreativeApps(data.dataList);
        }
        else if (updateType === 'refresh') {
            refreshCreativeAppList();
        }
        return data;
    }, [
        updateCreativeApps, filters, getSelectedInfo, options, userId,
        refreshCreativeAppList, getCreativeAppsByKeys
    ]);

    const inlineMethods = useMemo(() => ({
        inlineSaveStatus: inlineSaveFactory(inlineSaveStatusApi)
    }), [inlineSaveFactory]);

    const {batchMethods, deleteMethods} = useMemo(() => {
        return {
            batchMethods: {
                batchSaveStatus: batchSaveFactory(batchSaveStatusApi)
            }
        };
    }, [batchSaveFactory]);

    return [
        {
            pending,
            error,
            data: {summary, rows, totalCount},
            selection,
            sorter,
            pagination
        },
        {
            ...selectionOperations,
            setPageNo,
            setPageSize,
            refreshCreativeAppList,
            onSort,
            inlineMethods,
            batchMethods,
            deleteMethods
        }
    ];
}


function getDefaultFilters() {
    const defaultFilters = [
        {
            field: 'segmentType',
            op: 'in',
            values: [
                appSegmentEnum.ANDROID_DOWNLOAD.value,
                appSegmentEnum.IOS_DOWNLOAD.value,
                appSegmentEnum.ANDROID_GAME.value,
                ...(isAppPcUser() ? [appSegmentEnum.PC_DOWNLOAD.value] : [])
            ]
        }
    ];
    return defaultFilters;
}