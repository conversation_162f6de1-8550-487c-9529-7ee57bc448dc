/*
 * @file: 顶部操作区
 * @Author: sun<PERSON><PERSON><PERSON>
 * @Date: 2023-07-17 15:51:06
 */
import {memo} from 'react';
import {Button} from '@baidu/one-ui';
import RouteCalendar from 'commonLibs/RouteCalendar';
import {MODULE_MC} from 'commonLibs/config/route';


function OperationBar({
    resetColumnsWidth
}) {
    const routeCalendarProps = {
        module: MODULE_MC,
        material: 'app'
    };

    return (
        <div className="operation-container">
            <div className="area" />
            <div className="area">
                <RouteCalendar {...routeCalendarProps} />
                <Button onClick={resetColumnsWidth}>重置列宽</Button>
            </div>
        </div>
    );
}

export default memo(OperationBar);
