import Enum from 'enum';
import {IconPlayCircle, IconPauseCircle} from 'dls-icons-react';
import {getBatchOptionsWithDivider} from 'commonLibs/utils/getBatchOptionsWithDivider';


// 分隔配置
const batchFieldDividerConfig = new Enum({
    STATUS: '状态'
});

const BatchFieldMap = {
    start: {
        value: 'start',
        label: <><IconPlayCircle />&nbsp;启用</>,
        divider: batchFieldDividerConfig.STATUS.key
    },
    pause: {
        value: 'pause',
        label: <><IconPauseCircle />&nbsp;暂停</>,
        divider: batchFieldDividerConfig.STATUS.key
    }
};

const fields = ['start', 'pause'];

export default function getBatchOptionFields() {
    return getBatchOptionsWithDivider(fields.map(field =>
        BatchFieldMap[field]), batchFieldDividerConfig);
}
