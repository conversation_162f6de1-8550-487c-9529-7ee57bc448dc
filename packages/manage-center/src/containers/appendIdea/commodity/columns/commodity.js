/*
 * @Author: sun<PERSON><PERSON><PERSON>
 * @Date: 2023-07-17 16:17:22
 */
import {withSumCell} from 'commonLibs/tableList/utils/handleSummary';
import {Popover} from '@baidu/one-ui';
import {createCustomRender} from 'commonLibs/utils/render';
import mt259 from 'app/resource/img/mt259.png';
import mt260 from 'app/resource/img/mt260.png';
import mt261 from 'app/resource/img/mt261.png';
import mtPreview259 from 'app/resource/img/mtPreview259.png';
import mtPreview260 from 'app/resource/img/mtPreview260.png';
import mtPreview261 from 'app/resource/img/mtPreview261.png';
import {MT_CONFIG} from './config';
import './style.less';


const renderImg = {
    [MT_CONFIG.multiProjectList]: mt259,
    [MT_CONFIG.multiProject]: mt260,
    [MT_CONFIG.singleProject]: mt261
};

const previewImg = {
    [MT_CONFIG.multiProjectList]: mtPreview259,
    [MT_CONFIG.multiProject]: mtPreview260,
    [MT_CONFIG.singleProject]: mtPreview261
};

const Preview = ({mtId}) => {
    return (
        <div className="commodity-component-preview">
            <img src={previewImg[mtId]} />
        </div>
    );
};

function Commodity({record, trigger}) {
    const {mtId} =  record;
    return (
        <Popover
            placement="right"
            content={<Preview mtId={mtId} />}
        >
            <div className="commodity-component">
                <img src={renderImg[mtId]} />
            </div>
        </Popover>
    );
}

export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => <Commodity record={record} trigger={trigger} />
    ))
};

