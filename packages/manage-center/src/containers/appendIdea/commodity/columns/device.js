/*
 * @Author: sunmeijie
 * @Date: 2023-07-17 16:17:22
 */
import {withSumCell} from 'commonLibs/tableList/utils/handleSummary';
import {createCustomRender} from 'commonLibs/utils/render';
import {getConfigFromDataSource} from 'commonLibs/types/base/config';

export const deviceDataSource = [
    [0, 'all', '全部'],
    [1, 'mobile', '移动'],
    [2, 'pc', '计算机']
];

const {
    valueMapByKey: deviceType,
    nameMapByValue: deviceTypeDisplay
} = getConfigFromDataSource(deviceDataSource);

function DeviceType({record, trigger}) {
    const {devicetype} = record;
    return (
        <div className='column-cell-flex'>
            <div className='multiple-cut'>
                {deviceTypeDisplay[+devicetype]}
            </div>
        </div>
    );
}

export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => <DeviceType record={record} trigger={trigger} />
    ))
};

