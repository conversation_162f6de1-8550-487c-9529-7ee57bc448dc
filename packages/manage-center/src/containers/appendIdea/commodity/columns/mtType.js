/*
 * @Author: sun<PERSON><PERSON><PERSON>
 * @Date: 2023-07-17 16:17:22
 */
import {withSumCell} from 'commonLibs/tableList/utils/handleSummary';
import {createCustomRender} from 'commonLibs/utils/render';
import {MT_WORD_CONFIG} from './config';


function DeviceType({record, trigger}) {
    const {mtId} = record;
    return (
        <div className='column-cell-flex'>
            <div className='multiple-cut'>
                {MT_WORD_CONFIG[+mtId]}
            </div>
        </div>
    );
}

export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => <DeviceType record={record} trigger={trigger} />
    ))
};

