/*
 * @Author: sun<PERSON><PERSON>e
 * @Date: 2023-07-17 16:17:22
 */
import {withSumCell} from 'commonLibs/tableList/utils/handleSummary';
import {usePageLevel} from 'app/hooks/pageLevel';
import {createCustomRender} from 'commonLibs/utils/render';
import IdType from 'commonLibs/config/idType';

const bindScopeConfig = {
    [IdType.USER_LEVEL]: '账户'
};

function BindScope({record, trigger}) {
    const {idType} = usePageLevel();
    return (
        <div className='column-cell-flex'>
            <div className='multiple-cut'>
                {bindScopeConfig[+idType]}
            </div>
        </div>
    );
}

export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => <BindScope record={record} trigger={trigger} />
    ))
};

