/*
 * @file: status
 * @author: sunmeijie
 */
import {withSumCell} from 'commonLibs/tableList/utils/handleSummary';
import {createCustomRender} from 'commonLibs/utils/render';
import {classNameTypes} from 'commonLibs/config/materialList/status';
import {useCallback} from 'react';
import {StatusPure} from 'commonLibs/tableList/columns/status';
import {IconPlayCircle, IconPauseCircle} from 'dls-icons-react';
import {displayError} from 'commonLibs/utils/materialList/error';

export const statusKeyMap = {
    START: 0,
    PAUSE: 1
};

export const statusTextMap = {
    [statusKeyMap.START]: '开启',
    [statusKeyMap.PAUSE]: '暂停'
};

export const statusClassNames = {
    [statusKeyMap.START]: classNameTypes.normal,
    [statusKeyMap.PAUSE]: classNameTypes.warning
};


function Status({record, trigger}) {
    const {id, status} = record;
    const statusPureProps = {
        color: statusClassNames[status],
        label: statusTextMap[status],
        hasUnderscore: false
    };

    const Icon = status === statusKeyMap.PAUSE ? IconPlayCircle : IconPauseCircle;
    const inlineSaveStatus =  trigger('getInlineSaveByMethodName', 'inlineSaveStatus');
    const updateStatus = useCallback(async e => {
        e.stopPropagation();
        try {
            await inlineSaveStatus(
                id,
                {
                    ...record,
                    pause: !record.pause
                }
            );
        }
        catch (err) {
            displayError(err);
        }

    }, [inlineSaveStatus, id, record]);
    return (
        <StatusPure {...statusPureProps}>
            <div className="brand-status">
                <Icon className="inline-operation-icon" onClick={updateStatus} />
            </div>
        </StatusPure>
    );
}

export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => <Status record={record} trigger={trigger} />
    ))
};
