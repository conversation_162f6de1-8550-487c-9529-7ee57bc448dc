/*
 * @file: config
 * @author: sunmeijie
 */
import {formatColumnConfiguration} from 'commonLibs/utils/materialList/columns';
import commodity from './columns/commodity';
import status from './columns/status';
import bindScope from './columns/scope';
import devicetype from './columns/device';
import mtType from './columns/mtType';

const customFields = [
    'mtId', 'mtType', 'status', 'bindScope', 'devicetype',
    'cost', 'impression', 'click', 'ctr', 'cpc'
];

const columnConfigs = {
    mtId: {
        category: '属性',
        columnName: 'mtId',
        columnText: '商品类组件',
        columnType: 'STRING',
        filterable: false,
        optional: false,
        sortable: false,
        draggable: true,
        feConfig: {
            columnMinWidth: 328,
            columnWidth: 328,
            fixType: 'left'
        }
    },
    mtType: {
        category: '属性',
        columnName: 'mtType',
        columnText: '组件类型',
        columnType: 'STRING',
        filterable: false,
        optional: false,
        sortable: false,
        draggable: true,
        feConfig: {
            columnMinWidth: 140,
            columnWidth: 140
        }
    },
    status: {
        category: '属性',
        columnName: 'status',
        columnText: '状态',
        columnType: 'STRING',
        filterable: false,
        optional: false,
        sortable: false,
        draggable: true,
        feConfig: {
            columnWidth: '120'
        }
    },
    bindScope: {
        category: '属性',
        columnName: 'bindScope',
        columnText: '投放范围',
        columnType: 'STRING',
        filterable: false,
        optional: false,
        sortable: false,
        draggable: true
    },
    devicetype: {
        category: '属性',
        columnName: 'devicetype',
        columnText: '推广设备',
        columnType: 'STRING',
        filterable: false,
        optional: false,
        sortable: false,
        draggable: true
    },
    //
    cost: {
        category: '属性',
        columnName: 'cost',
        columnText: '消费',
        columnType: 'DOUBLE',
        precision: 2,
        filterable: false,
        optional: false,
        sortable: false,
        draggable: true,
        feConfig: {}
    },
    click: {
        category: '效果指标',
        columnName: 'click',
        columnText: '点击',
        columnType: 'DOUBLE',
        filterable: false,
        optional: false,
        sortable: false,
        draggable: true,
        feConfig: {}
    },
    impression: {
        category: '效果指标',
        columnName: 'impression',
        columnText: '展现',
        columnType: 'DOUBLE',
        filterable: false,
        optional: false,
        sortable: false,
        draggable: true,
        feConfig: {}
    },
    cpc: {
        category: '效果指标',
        columnName: 'cpc',
        precision: 2,
        columnText: '平均点击价格',
        columnType: 'DOUBLE',
        filterable: false,
        optional: false,
        sortable: false,
        draggable: true,
        feConfig: {}
    },
    ctr: {
        category: '效果指标',
        columnName: 'ctr',
        columnText: '点击率',
        columnType: 'DOUBLE',
        filterable: false,
        optional: false,
        sortable: false,
        draggable: true,
        feConfig: {
            filter: JSON.stringify({
                tailLabel: '%'
            })
        },
        isPercentage: true,
        precision: 2
    }
};

export function getColumConfig() {
    return {
        tableFieldsMap: {
            mtId: commodity,
            status,
            mtType,
            bindScope,
            devicetype
        },
        columnConfiguration: formatColumnConfiguration({customFields, columnConfigs})
    };
}
