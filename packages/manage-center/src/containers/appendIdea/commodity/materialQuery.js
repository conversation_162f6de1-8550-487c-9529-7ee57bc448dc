/*
 * @Author: sunmeijie
 * @Date: 2023-07-17 15:52:30
 */
import {useEffect, useMemo, useCallback, useState} from 'react';
import {useHybridRequest} from 'commonLibs/hooks/request';
import useUserInfo from 'commonLibs/hooks/useUserInfo';
import {fetchMaterialCreativeCommodityList,
    inlineSaveStatusApi, batchSaveStatusApi} from 'app/api/materialQuery/creativeCommodity';
import {useKeyOrientedArray} from 'commonLibs/hooks/collection/array';
import {useQueryTableSort} from 'commonLibs/hooks/sorter';
import {useQueryTablePagination} from 'commonLibs/hooks/pagination';
import {useRowSelection} from 'commonLibs/hooks/materialList/rowSelection';
import {createError} from 'commonLibs/utils/materialList/error';
import {useLog, wrapLog} from 'commonLibs/logger';

const initialCreativeCommodityListData = {fields: [], sum: {}};
export const getCreativeCommodityListId = record => record.structuredMtid;

export function useCreativeCommodityList({allFields, filters, materialLevel, ...options}) {
    const {userId} = useUserInfo();
    const log = useLog();
    const [sorter, onSort_] = useQueryTableSort({validFields: allFields});
    const [onSort] = wrapLog(log, [onSort_], {target: '^save_sort'});
    const [
        pagination,
        {setPageNo: setPageNo_, setPageSize: setPageSize_}
    ] = useQueryTablePagination();
    const [setPageNo, setPageSize] = wrapLog(log, [setPageNo_, setPageSize_], {target: '^save_pager'});
    const [refresh, {
        pending,
        error,
        data: {fields: rawRows, totalCount, sum: summary} = initialCreativeCommodityListData
    }] = useHybridRequest(fetchMaterialCreativeCommodityList, {
        ...options,
        sorter,
        pagination,
        filters,
        userId
    });

    const [rows, {
        updateByKey: updateCreativeCommodityById,
        getItemByKey: getCreativeCommodityById,
        updateItems: updateCreativeCommoditys,
        set: setCreativeCommoditys
    }] = useKeyOrientedArray(rawRows, {getKey: getCreativeCommodityListId});
    const [accumulateList, setAccumulateList] = useState(rows);
    useEffect(() => {
        setCreativeCommoditys(rawRows);
        setAccumulateList(list => [...list, ...rawRows]);
    }, [rawRows]);

    const getCreativeCommodityByKeys = useCallback(
        keys => keys.map(key => accumulateList.find(item => getCreativeCommodityListId(item) === key)),
        [accumulateList]
    );

    const listIds = useMemo(() => rows.map(getCreativeCommodityListId), [rows]);
    const [selection, selectionOperations] = useRowSelection({ids: listIds, totalCount});
    const {getSelectedInfo, resetRowSelection} = selectionOperations;

    useEffect(
        () => {
            resetRowSelection();
        },
        [options.pageLevel, filters, resetRowSelection]
    );

    const inlineSaveFactory = useCallback(
        (inlineUpdateApi, {updateType = 'replace'} = {}) => async function (materialId, values, params) {
            let data;
            try {
                [data] = await inlineUpdateApi(materialId, values, {...options, userId, ...params});
            }
            catch (error) {
                throw createError(error);
            }
            if (updateType === 'replace') {
                updateCreativeCommodityById(materialId, data);
            }
            else if (updateType === 'refresh') {
                refresh();
            }
        },
        [updateCreativeCommodityById, refresh, options, userId]
    );

    // updateType 为更新数据方式，可选，刷新列表(refresh)或者更新dataSource(replace)；默认为更新dataSource
    const batchSaveFactory = useCallback((batchUpdateApi, {updateType = 'replace'} = {}) => async values => {
        let data;
        try {
            const selectedInfo = getSelectedInfo();
            data = await batchUpdateApi({
                ...options,
                selectionInfo: {
                    ...selectedInfo
                },
                filters,
                userId
            }, values);
        }
        catch (err) {
            const batchSaveError = createError(err);
            batchSaveError.materialName = '应用组件';
            if (batchSaveError._normalized?.type === 'partial') {
                refresh();
            }
            throw batchSaveError;
        }
        if (updateType === 'replace') {
            updateCreativeCommoditys(data.dataList);
        }
        else if (updateType === 'refresh') {
            refresh();
        }
        return data;
    }, [
        updateCreativeCommoditys, filters, getSelectedInfo, options, userId,
        refresh, getCreativeCommodityByKeys
    ]);

    const inlineMethods = useMemo(() => ({
        inlineSaveStatus: inlineSaveFactory(inlineSaveStatusApi, {updateType: 'refresh'})
    }), [inlineSaveFactory]);

    const {batchMethods, deleteMethods} = useMemo(() => {
        return {
            batchMethods: {
                batchSaveStatus: batchSaveFactory(batchSaveStatusApi)
            }
        };
    }, [batchSaveFactory]);

    return [
        {
            pending,
            error,
            data: {summary, rows, totalCount},
            selection,
            sorter,
            pagination
        },
        {
            ...selectionOperations,
            setPageNo,
            setPageSize,
            refresh,
            onSort,
            inlineMethods,
            batchMethods,
            deleteMethods
        }
    ];
}
