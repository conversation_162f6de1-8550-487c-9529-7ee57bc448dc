/*
 * @file: 创意列表批量操作bar
 * @author: l<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2022-04-25 17:18:53
 */
import {Dropdown} from '@baidu/one-ui';
import BatchOperationBar from 'commonLibs/batchOperationBar';

const BatchOperationFragment = props => {
    const {
        openEditor,
        resetRowSelection,
        getSelectedInfo,
        operationMap,
        options
    } = props;
    const {totalCount, selectedCount} = getSelectedInfo();

    const handleMenuClick = ({key: field}) => {
        if (operationMap[field]) {
            operationMap[field]({
                ...props,
                selectedCount,
                totalCount,
                resetRowSelection
            });
        }
        else {
            openEditor('batch', field);
        }
    };

    const dropDownProps = {
        options: options,
        title: '批量编辑',
        handleMenuClick,
        trigger: ['click'],
        size: 'medium'
    };

    const batchOperationProps = {
        selectedCount,
        totalCount,
        showCheckAllTip: false,
        showTotalCount: false,
        checkAll: false,
        onClose: resetRowSelection,
        minOverlayWidthMatchTrigger: true
    };

    return (
        <BatchOperationBar {...batchOperationProps}>
            <Dropdown.Button {...dropDownProps} />
        </BatchOperationBar>
    );
};

export default BatchOperationFragment;
