/*
 * @file: 创意列表批量操作bar
 * @author: l<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2022-04-25 17:18:53
 */
import {displayError} from 'commonLibs/utils/materialList/error';
import {Dialog} from '@baidu/one-ui';

const statusKeyMap = {
    ENABLE: 0, // 启动
    PAUSE: 1 // 暂停
};

// 绑定关系的启停状态
const bindStatusEnum = {
    start: false,
    pause: true
};

export const statusOperationMap = {
    async start({batchSaveStatus, resetRowSelection}) {
        let data;
        try {
            data = await batchSaveStatus({
                pause: bindStatusEnum.start,
                status: statusKeyMap.ENABLE
            });
            resetRowSelection();
        }
        catch (err) {
            err.optName = '启用';
            displayError(err);
        }
        return data;
    },
    async pause({batchSaveStatus, resetRowSelection}) {
        let data;
        try {
            data = await batchSaveStatus({
                pause: bindStatusEnum.pause,
                status: statusKeyMap.PAUSE
            });
            resetRowSelection();
        }
        catch (err) {
            err.optName = '暂停';
            displayError(err);
        }
        return data;
    },
    async delete({batchDeleteMaterial, resetRowSelection, targetLevelName}) {
        const confirmProps = {
            title: '确认删除',
            content: `确定删除所选${targetLevelName || '内容'}吗？`,
            onOk: async () => {
                let data;
                try {
                    data = await batchDeleteMaterial();
                    resetRowSelection();
                }
                catch (err) {
                    err.optName = '删除';
                    displayError(err);
                }
                return data;
            }
        };
        Dialog.confirm(confirmProps);
    }
};
