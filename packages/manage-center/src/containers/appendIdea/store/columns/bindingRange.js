/*
 * @file: BindRelation
 * @author: lian<PERSON>of<PERSON>@baidu.com
 */
import {withSumCell} from 'commonLibs/tableList/utils';
import {createCustomRender} from 'commonLibs/utils/render';

const DeepLinkSegmentType = 11086;

function BindRelation({record, trigger}) {
    const {segmentType} = record;
    return (
        <div className='column-cell-flex'>
            <div className='multiple-cut'>
                {segmentType ===  DeepLinkSegmentType ? '所有计划' : '本地店铺推广计划'}
            </div>
        </div>
    );
}
export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => <BindRelation record={record} trigger={trigger} />
    ))
};
