/*
 * @file: status
 * @author: lian<PERSON><PERSON><PERSON>@baidu.com
 */
import {withSumCell} from 'commonLibs/tableList/utils';
import {parseJsonWithFallback} from 'commonLibs/utils/json';
import {useRequestCallback} from '@huse/request';
import useUserInfo from 'commonLibs/hooks/useUserInfo';
import {createCustomRender} from 'commonLibs/utils/render';
import {classNameTypes} from 'commonLibs/config/materialList/status';
import {fetchAuditTime as fetchAuditTime_} from 'app/api/creativeImages/status';
import {useCallback, useMemo} from 'react';
import {Toast} from '@baidu/one-ui';
import {useBoolean} from '@huse/boolean';
import {formatWholeReason} from 'commonLibs/tableList/columns/status/Panel/util';
import {StatusPure, StatusPanel as Panel} from 'commonLibs/tableList/columns/status';
import {IconEllipsisCircle, IconPlayCircle, IconPauseCircle} from 'dls-icons-react';

export const statusKeyMap = {
    PASSED: 110,
    REJECTED: 111,
    AUDITING: 112,
    PAUSE: 113,
    INVALID: 114,
    UNAUDITED: 115
};

export const statusTextMap = {
    [statusKeyMap.PASSED]: '有效',
    [statusKeyMap.REJECTED]: '审核不通过',
    [statusKeyMap.AUDITING]: '审核中',
    [statusKeyMap.PAUSE]: '暂停推广',
    [statusKeyMap.INVALID]: '无效',
    [statusKeyMap.UNAUDITED]: '未审核'
};

export const statusClassNames = {
    [statusKeyMap.PASSED]: classNameTypes.normal,
    [statusKeyMap.REJECTED]: classNameTypes.error,
    [statusKeyMap.AUDITING]: classNameTypes.warning,
    [statusKeyMap.PAUSE]: classNameTypes.warning,
    [statusKeyMap.INVALID]: classNameTypes.warning,
    [statusKeyMap.UNAUDITED]: classNameTypes.warning
};


function Status({record, trigger, isShowEditIcon = true}) {
    const {status, segmentId} = record;

    const statusPureProps = {
        color: statusClassNames[status],
        label: statusTextMap[status],
        hasUnderscore: false
    };

    const Icon = status === statusKeyMap.PAUSE ? IconPlayCircle : IconPauseCircle;
    const inlineSaveStatus =  trigger('getInlineSaveByMethodName', 'inlineSaveStatus');
    const updateStatus = useCallback(async e => {
        e.stopPropagation();
        await inlineSaveStatus(
            segmentId,
            {
                ...record,
                pause: status !== statusKeyMap.PAUSE
            }
        );
    }, [status, inlineSaveStatus, segmentId, record]);
    return (
        <StatusPure {...statusPureProps}>
            <div className="brand-status">
                {
                    isShowEditIcon && (
                        <Icon className="inline-operation-icon" onClick={updateStatus} />
                    )
                }
            </div>
        </StatusPure>
    );
}

export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => <Status record={record} trigger={trigger} />
    ))
};
