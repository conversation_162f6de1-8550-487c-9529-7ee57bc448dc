/*
 * @file: Desc
 * @author: l<PERSON><PERSON>of<PERSON>@baidu.com
 * @Date: 2022-04-21 20:23:31
 */
import {Tooltip} from '@baidu/one-ui';
import {withSumCell} from 'commonLibs/tableList/utils';
import {createCustomRender} from 'commonLibs/utils/render';
import './style.less';

function StoreCreative({record, trigger}) {
    const {content = {}} = record;
    const {thumbnail, preview} = content;
    return (
        <Tooltip
            overlayClassName="dashboard-store-creative-column-store-creative"
            placement="right"
            title={<img width="300" alt="预览图" src={preview} />}
        >
            <img width="300" alt="缩略图" src={thumbnail} />
        </Tooltip>
    );
}
export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => <StoreCreative record={record} trigger={trigger} />,
        {needSum: true}
    ))
};
