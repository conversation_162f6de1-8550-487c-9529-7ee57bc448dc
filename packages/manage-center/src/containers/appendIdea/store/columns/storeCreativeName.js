/*
 * @file: StoreCreativeName
 * @author: lian<PERSON><PERSON><PERSON>@baidu.com
 */
import {withSumCell} from 'commonLibs/tableList/utils';
import {createCustomRender} from 'commonLibs/utils/render';
import {storeSegmentTypeText} from 'commonLibs/config/store';

function StoreCreativeName({record, trigger}) {
    const {segmentType} = record;
    return (
        <div className='column-cell-flex'>
            <div className='multiple-cut'>{storeSegmentTypeText[segmentType] || ''}</div>
        </div>
    );
}
export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => <StoreCreativeName record={record} trigger={trigger} />
    ))
};
