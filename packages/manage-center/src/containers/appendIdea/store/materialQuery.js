/*
 * @file: useCreativeStoresList
 * @author: l<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2022-04-15 17:54:40
 */
import {useEffect, useMemo, useCallback, useState} from 'react';
import {useHybridRequest} from 'commonLibs/hooks/request';
import useUserInfo from 'commonLibs/hooks/useUserInfo';
import {fetchMaterialCreativeStoreList} from 'app/api/materialQuery/creativeStore';
import {useKeyOrientedArray} from 'commonLibs/hooks/collection/array';
import {useQueryTableSort} from 'commonLibs/hooks/sorter';
import {useQueryTablePagination} from 'commonLibs/hooks/pagination';
import {useRowSelection} from 'commonLibs/hooks/materialList/rowSelection';
import {createError} from 'commonLibs/utils/materialList/error';
import {storeSegmentType} from 'commonLibs/config/store';
import {
    batchSaveStatusApi,
    inlineSaveStatusApi
} from 'app/containers/appendIdea/apis/store/update';
import {useLog, wrapLog} from 'commonLibs/logger';

const initialCreativeStoresListData = {fields: [], sum: {}};
export const getCreativeStoreListId = record => record.segmentId;

const defaultFieldFilters = [
    {
        field: 'segmentType',
        op: 'in',
        values: [
            storeSegmentType.STORE_NAVIGATION_CLUE.value,
            storeSegmentType.STORE_NAVIGATION.value,
            storeSegmentType.STORE_TRIPLE_STORE.value,
            storeSegmentType.STORE_DOUBLE_STORE.value,
            storeSegmentType.STORE_MULTI_STORE.value,
            storeSegmentType.STORE_ZHI_LIAO.value,
            storeSegmentType.STORE_MALL.value
        ]
    }
];

export function useCreativeStoreList({allFields, filters, materialLevel, ...options}) {
    const {userId} = useUserInfo();
    const log = useLog();
    const [sorter, onSort_] = useQueryTableSort({validFields: allFields});
    const [onSort] = wrapLog(log, [onSort_], {target: '^save_sort'});
    const [
        pagination,
        {setPageNo: setPageNo_, setPageSize: setPageSize_}
    ] = useQueryTablePagination();
    const [setPageNo, setPageSize] = wrapLog(log, [setPageNo_, setPageSize_], {target: '^save_pager'});
    const [refreshCreativeStoreList, {
        pending,
        error,
        data: {fields: rawRows, totalCount, sum: summary} = initialCreativeStoresListData
    }] = useHybridRequest(fetchMaterialCreativeStoreList, {
        ...options,
        sorter,
        pagination,
        filters,
        defaultFieldFilters,
        userId
    });

    const [rows, {
        updateByKey: updateCreativeStoreById,
        getItemByKey: getCreativeStoreById,
        updateItems: updateCreativeStores,
        set: setCreativeStores
    }] = useKeyOrientedArray(rawRows, {getKey: getCreativeStoreListId});
    const [accumulateList, setAccumulateList] = useState(rows);
    useEffect(() => {
        setCreativeStores(rawRows);
        setAccumulateList(list => [...list, ...rawRows]);
    }, [rawRows]);

    const getCreativeStoresByKeys = useCallback(
        keys => keys.map(key => accumulateList.find(item => getCreativeStoreListId(item) === key)),
        [accumulateList]
    );

    const listIds = useMemo(() => rows.map(getCreativeStoreListId), [rows]);
    const [selection, selectionOperations] = useRowSelection({ids: listIds, totalCount});
    const {getSelectedInfo, resetRowSelection} = selectionOperations;

    useEffect(
        () => {
            resetRowSelection();
        },
        [options.pageLevel, filters, resetRowSelection]
    );

    const inlineSaveFactory = useCallback(
        (inlineUpdateApi, {updateType = 'replace'} = {}) => async function (materialId, values, params) {
            let data;
            try {
                [data] = await inlineUpdateApi(materialId, values, {...options, userId, ...params});
            }
            catch (error) {
                throw createError(error);
            }
            if (updateType === 'replace') {
                updateCreativeStoreById(materialId, data);
            }
            else if (updateType === 'refresh') {
                refreshCreativeStoreList();
            }
        },
        [updateCreativeStoreById, refreshCreativeStoreList, options, userId]
    );

    // updateType 为更新数据方式，可选，刷新列表(refresh)或者更新dataSource(replace)；默认为更新dataSource
    const batchSaveFactory = useCallback((batchUpdateApi, {updateType = 'replace'} = {}) => async values => {
        let data;
        try {
            const selectedInfo = getSelectedInfo();
            const {selectedIds} = selectedInfo;
            data = await batchUpdateApi({
                ...options,
                selectionInfo: {
                    ...selectedInfo,
                    selectedRecords: getCreativeStoresByKeys(selectedIds)
                },
                filters,
                userId
            }, values);
        }
        catch (err) {
            const batchSaveError = createError(err);
            batchSaveError.materialName = '本地组件';
            if (batchSaveError._normalized?.type === 'partial') {
                refreshCreativeStoreList();
            }
            throw batchSaveError;
        }
        if (updateType === 'replace') {
            updateCreativeStores(data.dataList);
        }
        else if (updateType === 'refresh') {
            refreshCreativeStoreList();
        }
        return data;
    }, [
        updateCreativeStores, filters, getSelectedInfo, options, userId,
        refreshCreativeStoreList, getCreativeStoresByKeys
    ]);

    const inlineMethods = useMemo(() => ({
        inlineSaveStatus: inlineSaveFactory(inlineSaveStatusApi)
    }), [inlineSaveFactory]);

    const {batchMethods, deleteMethods} = useMemo(() => {
        return {
            batchMethods: {
                batchSaveStatus: batchSaveFactory(batchSaveStatusApi)
            }
        };
    }, [batchSaveFactory]);

    return [
        {
            pending,
            error,
            data: {summary, rows, totalCount},
            selection,
            sorter,
            pagination
        },
        {
            ...selectionOperations,
            setPageNo,
            setPageSize,
            refreshCreativeStoreList,
            onSort,
            inlineMethods,
            batchMethods,
            deleteMethods
        }
    ];
}


