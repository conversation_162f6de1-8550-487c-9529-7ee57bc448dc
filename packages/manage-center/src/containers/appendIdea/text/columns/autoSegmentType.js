/*
 * @file: 自动生成类型SegmentType
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2022-09-20 14:54:13
 */
import {withSumCell} from 'commonLibs/tableList/utils';
import {createCustomRender} from 'commonLibs/utils/render';
import {segmentTypeEnum} from 'commonLibs/config/segmentType';

const SegmentTextMap = {
    [segmentTypeEnum.JIMIUYU_QA]: '基木鱼内容问答',
    [segmentTypeEnum.ALLNET_CONTENT]: '全网知识内容',
    [segmentTypeEnum.COMMENT]: '用户评论'
};

const segmentOptions = [{
    label: SegmentTextMap[segmentTypeEnum.JIMIUYU_QA],
    value: segmentTypeEnum.JIMIUYU_QA
}, {
    label: SegmentTextMap[segmentTypeEnum.ALLNET_CONTENT],
    value: segmentTypeEnum.ALLNET_CONTENT
}, {
    label: SegmentTextMap[segmentTypeEnum.COMMENT],
    value: segmentTypeEnum.COMMENT
}];

function SegmentType({record, trigger}) {
    return (
        <div className='column-cell-flex'>
            <div className='multiple-cut'>{SegmentTextMap[record.segmentType]}</div>
        </div>
    );
}
export default {
    render: createCustomRender((configs, {trigger}) =>
        (text, record) => <SegmentType record={record} trigger={trigger} />
    ),
    filters: {
        options: segmentOptions
    }
};
