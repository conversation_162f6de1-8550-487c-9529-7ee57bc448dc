.subscript-module-text {
    width: @dls-padding-unit * 14;
    padding: @dls-padding-unit @dls-padding-unit * 2;
    border-radius: @dls-padding-unit;
    border: 1px solid #ffe4e3;
    background: @dls-color-brand-0;
    color: #d9150b;
    font-size: 14px;
    line-height: @dls-padding-unit * 5;
}
.subscript-preview-container {
    display: flex;
    width: @dls-padding-unit * 90;
    padding: @dls-padding-unit * 3;
    flex-direction: column;
    align-items: flex-start;
    gap: @dls-padding-unit * 2;
    border-radius: @dls-border-radius-2;
    border: 1px solid @dls-color-gray-3;
    background: @dls-color-brand-0;
    .subscript-preview-title {
        color: #2440b3;
        font-size: 14px;
        line-height: @dls-padding-unit * 5;
        text-decoration-line: underline;
        &-color {
            color: #f73131;
        }
    }
    .subscript-preview-content {
        display: flex;
        align-items: flex-start;
        gap: @dls-padding-unit * 2;
        color: @dls-color-gray-9;
        text-align: justify;
        font-size: 12px;
        line-height: 22px;
        &-img {
            border-radius: @dls-border-radius-2;
            width: @dls-padding-unit * 24;
        }
        &-color {
            color: #f73131;
        }
    }
    .subscript-preview-footer {
        display: flex;
        align-items: center;
        font-size: 10px;
        line-height: 14px;
        gap: @dls-padding-unit;
        &-subscript {
            padding: @dls-padding-unit * 0.5;
            border-radius: 4px;
            border: 1px solid #fd503e;
            color: #fd503e;
            text-align: center;
            font-size: 10px;
            font-weight: @dls-font-weight-2;
            line-height: @dls-padding-unit * 2.5;
        }
        &-brand {
            display: flex;
            align-items: center;
            color: @dls-color-gray-9;
        }
        &-ad {
            color: @dls-color-gray-6;
        }
    }
}