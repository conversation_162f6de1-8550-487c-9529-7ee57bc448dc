/*
 * @file: 角标组件
 * @Author: <EMAIL>
 * @Date: 2023-12-14
 */
import {Popover} from '@baidu/one-ui';
import {subscriptConfig} from '../config';
import subscriptPic from 'app/resource/img/subscript-preview.png';
import defaultBrand from 'app/resource/svg/default-brand.svg';
import {ensure} from 'commonLibs/resource';
import './style.less';


const SubscriptPreview = ({subscript}) => {
    return (
        <div className='subscript-preview-container'>
            <div className='subscript-preview-title'>
                这里是<span className='subscript-preview-title-color'>创意标题</span>内容内容内容内容内容内容内容
            </div>
            <div className='subscript-preview-content'>
                <img className='subscript-preview-content-img' src={subscriptPic} />
                <div>
                    这里<span className='subscript-preview-content-color'>创意标题</span>
                    这里是文案描述这里是文案描述这里是文案描文案描述这里是文案描述这里是文案描述这里是文案描述里是文案描述里是文案描述里是文案描述里是文案描…
                </div>
            </div>
            <div className='subscript-preview-footer'>
                <div className='subscript-preview-footer-subscript'>{subscript}</div>
                <div className='subscript-preview-footer-brand'><img src={defaultBrand} />品牌名称 2022-10</div>
                <div className='subscript-preview-footer-ad'>广告</div>
                <img className='subscript-preview-footer-guarantee' src={ensure} />
            </div>
        </div>
    );
};

export const SubscriptModule = ({record}) => {
    const {subscriptModule} = record;

    return (
        <Popover
            content={<SubscriptPreview subscript={subscriptConfig[subscriptModule]} />}
            placement="right"
        >
            <div className="subscript-module-text">
                {subscriptConfig[subscriptModule]}
            </div>
        </Popover>
    );
};