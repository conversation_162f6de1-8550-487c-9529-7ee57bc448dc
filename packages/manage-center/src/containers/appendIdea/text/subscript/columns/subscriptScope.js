/*
 * @file: 生效范围
 * @Author: <EMAIL>
 * @Date: 2023-12-14
 */
import {IconEdit} from 'dls-icons-react';

export function SubscriptScope({record, trigger, openEditor}) {
    const {segmentId, subscriptScope = []} = record;
    const openBindRelationDialog = () => {
        openEditor('inline', 'modSubscriptScope', segmentId);
    };
    const unEffectCampaignLength = subscriptScope.filter(item => !item.carLabelStatus).length;
    return (
        <div>
            <span className="column-cell-flex">
                {subscriptScope.length}个计划
                {unEffectCampaignLength ? `（${unEffectCampaignLength}个计划暂不生效）` : ''}
                <IconEdit className="inline-operation-icon" onClick={openBindRelationDialog} />
            </span>
        </div>
    );
}

