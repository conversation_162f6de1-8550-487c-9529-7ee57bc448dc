import {useCallback} from 'react';
import {Badge} from '@baidu/one-ui';
import {IconPlayCircle, IconPauseCircle} from 'dls-icons-react';
import {subscriptValueConfig} from '../config';
import {auditStatusTextMap, auditStatusKeyMap} from 'app/containers/appendIdea/videos/columns/status';
import {classNameTypes} from 'commonLibs/config/materialList/status';

const auditStatusClassNames = {
    [auditStatusKeyMap.PASSED]: classNameTypes.success,
    [auditStatusKeyMap.REJECTED]: classNameTypes.error,
    [auditStatusKeyMap.AUDITING]: classNameTypes.warning,
    [auditStatusKeyMap.UNAUDITED]: classNameTypes.warning,
    [auditStatusKeyMap.PAUSE]: classNameTypes.warning,
    [auditStatusKeyMap.INVALID]: classNameTypes.error
};

export function getStatusColorAndLabel({status}) {
    return {
        color: auditStatusClassNames[status],
        label: auditStatusTextMap[status]
    };
}

export const SubscriptStatus = ({record, trigger}) => {
    const {subscriptStatus, segmentId} = record;
    // segmentId是初始值，是未生效的状态
    const unEffective = Object.values(subscriptValueConfig).includes(segmentId);
    const isPause = subscriptStatus === auditStatusKeyMap.PAUSE;
    const Icon = isPause ? IconPlayCircle : IconPauseCircle;

    const {color, label} = getStatusColorAndLabel({status: subscriptStatus}) || {};

    const inlineSaveSubscriptStatus = useCallback(
        (...args) => trigger('inlineSaveSubscriptStatus', ...args), [trigger]
    );
    const updateStatus = useCallback(async e => {
        e.stopPropagation();
        inlineSaveSubscriptStatus({
            selectedIds: [segmentId],
            pause: !isPause});
    }, [inlineSaveSubscriptStatus, isPause, segmentId]);

    return (
        <div>
            {
                unEffective
                    ? <Badge type="default" text="未生效" />
                    : <Badge type={color} text={label} />
            }
            {
                !unEffective && (
                    <Icon className="inline-operation-icon" onClick={updateStatus} />
                )
            }
        </div>
    );
};
