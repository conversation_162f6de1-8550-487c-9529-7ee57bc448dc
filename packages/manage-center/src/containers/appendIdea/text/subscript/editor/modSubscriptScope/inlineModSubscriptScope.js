/*
 * @file: InlineModSubscript
 * @author: <EMAIL>
 * @Date: 2023-12-14
 */

import {forwardRef, useState, useMemo, useImperativeHandle, useEffect} from 'react';
import {getErrorDisplayForForm} from 'commonLibs/utils/materialList/error';
import {Transfer, Popover, Alert} from '@baidu/one-ui';
import {IconInfoCircle} from 'dls-icons-react';
import FormItem, {Form} from 'commonLibs/materialList/FormItem';
import {subscriptValueConfig} from '../../config';
import {getCampaignList} from 'app/containers/appendIdea/apis/text/update';
import {useRequest} from '@huse/request';
import useUserInfo from 'commonLibs/hooks/useUserInfo';
import EmptyInfo from 'commonLibs/tableList/EmptyText';
import {parseJsonWithFallback} from 'commonLibs/utils/json';

const CAMPAIGN_LEVEL = 'campaign';
const PRICE_STRATEGY_LEVEL = 'priceStrategy';

const levelOptions = [
    {
        value: CAMPAIGN_LEVEL,
        label: '全部计划'
    },
    {
        value: PRICE_STRATEGY_LEVEL,
        label: '排名倾向出价策略的计划'
    }
];

const CandidateTitle = props => {
    const candidateNum = props.unSelectedNum;
    return (
        <div className="crowd-setting-candidate-list-title">
            <span style={{display: 'inline-block', width: 200}}>可选计划({candidateNum})</span>
            <span style={{display: 'inline-block', width: 100}}>出价策略类型</span>
        </div>
    );
};

const CandidateItem = props => {
    const {title, priceStrategyId, disabled, subscript} = props;
    return (
        <div>
            <span style={{display: 'inline-block', width: 250}}>
                {title}
                {
                    disabled
                        ? (
                            <Popover
                                content={`当前计划已绑定”${subscript}”，不可重复绑定`}
                                placement='top'
                            >
                                <IconInfoCircle style={{marginLeft: '4px'}} />
                            </Popover>
                        )
                        : null
                }
            </span>
            <span style={{display: 'inline-block', width: 100}}>{priceStrategyId ? '排名倾向' : ''}</span>
        </div>
    );
};

const SelectedItem = props => {
    const {title, priceStrategyId} = props;
    return (
        <div>
            <span>
                {title}
                {
                    !priceStrategyId
                        ? (
                            <Popover
                                content='非排名倾向绑定计划暂不生效'
                                placement='top'
                            >
                                <IconInfoCircle style={{marginLeft: '4px', color: '#F27318'}} />
                            </Popover>
                        )
                        : null
                }
            </span>
        </div>
    );
};

export const Trans = forwardRef((props, ref) => {
    const {
        onChange,
        value,
        initialValue
    } = props;
    const {userId} = useUserInfo();


    const {data = [], pending, error} = useRequest(getCampaignList, {userId});


    const campaignDataSource = data.map(item => ({
        ...item,
        title: item.campaignName,
        key: item.campaignId,
        subscript: parseJsonWithFallback(item?.carLabelContent, {label: ''}).label,
        // 本角标绑定的不需要置灰
        disabled: !!item?.carLabelContent && !initialValue.includes(item.campaignId)
    }));


    const [candidateList, setCandidateList] = useState(data.map(item => item.campaignId));
    const [level, setLevel] = useState(CAMPAIGN_LEVEL);

    useEffect(() => {
        if (data.length) {
            setCandidateList(data.map(item => item.campaignId));
        }
    }, [data]);

    const handleLevelChange = level => {
        setLevel(level);
        if (level === PRICE_STRATEGY_LEVEL) {
            setCandidateList(data.filter(item => !!item.priceStrategyId).map(item => item.campaignId));
        }
        else {
            setCandidateList(data.map(item => item.campaignId));
        }
    };
    const onSearchChange = e => {
        const searchValue = data
            .filter(
                item => (level === PRICE_STRATEGY_LEVEL ? item.priceStrategyId : true)
            )
            .filter(item => item.campaignName.includes(e.target.value || '')).map(item => item.campaignId);
        setCandidateList(searchValue);
    };

    const transferProps = {
        className: 'subscript-transfer',
        showCandidateNum: true,
        showSelectedNum: true,
        dataSource: campaignDataSource,
        loading: pending,
        candidateList,
        value,
        onChange,
        treeName: '计划',
        showSearchBox: true,
        SelectedItem,
        CandidateItem,
        CandidateTitleRender: CandidateTitle,
        isShowLevel: true,
        isShowLevelSelect: true,
        levelKey: level,
        handleLevelChange,
        levelOptions,
        onSearchChange,
        candidateEmpty: (
            <EmptyInfo fetchError={error} text="暂无数据" />
        )
    };

    return (
        <div className='manage-new-campaign-crowd-binding-main-container'>
            <Transfer {...transferProps} />
        </div>
    );
});


function InlineModSubscriptScope(props, ref) {
    const {form, inlineSaveSubscript, currentId, getMaterialById} = props;
    const {bindInfo = []} = getMaterialById(currentId) || {};
    const inital = bindInfo.map(item => item.campaignId);

    // 未绑定过计划（未生效）的角标组件 segmentId初始值是角标类型，此时绑定计划应该调用新增接口
    const isAdd = Object.values(subscriptValueConfig).includes(currentId);
    const {validateFields} = form;
    const displayError = useMemo(() => getErrorDisplayForForm(form), [form]);

    const onSave = async () => {
        let data;
        const values = await validateFields();
        try {
            data = await inlineSaveSubscript(
                {campaignIds: values.subscriptScope, currentId, isAdd}
            );
        }
        catch (err) {
            err.optName = '修改绑定范围';
            displayError(err);
            throw err;
        }
        return data;
    };

    useImperativeHandle(ref, () => ({onSave}));
    return (
        <Form>
            <Alert
                type='warning'
                content='角标组件仅在计划绑定了排名倾向出价策略时生效; 绑定计划后将为您开启投放'
                showIcon
                style={{marginBottom: 8}}
            />
            <FormItem
                form={form}
                field='subscriptScope'
                colon={false}
                required
                initialValue={inital}
                rules={[{required: true, message: '请选择绑定的计划'}]}
            >
                <Trans {...props} initialValue={inital} />
            </FormItem>
        </Form>
    );
}

export default Form.create({name: 'InlineModSubscriptScope'})(forwardRef(InlineModSubscriptScope));
