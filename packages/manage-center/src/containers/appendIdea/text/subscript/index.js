/*
 * @file: 角标组件
 * @Author: <EMAIL>
 * @Date: 2023-12-14
 */

import {useMemo, useRef, useCallback, useEffect} from 'react';
import {formatColumnConfiguration} from 'commonLibs/utils/materialList/columns';
import {useColumns} from 'commonLibs/hooks/materialList/columns';
import {Table} from '@baidu/one-ui';
import {useRegister} from 'commonLibs/hooks/materialList/register';
import DialogEditor from 'commonLibs/materialList/dialogEditor';
import {getAllColumns, customFields, initalSubscriptData, subscriptConfig, subscriptValueConfig} from './config';
import {tableFieldsMap} from './tableFields';
import {editorConfig} from './editor/editorConfig';
import BatchOperationBar from '../../common/batchOperation';
import {useSubscriptList, getSubscriptListId} from './materialQuery';
import {toOneUIRowSelectionProps} from 'commonLibs/utils/materialList/rowSelection';
import {statusOperationMap, getBatchOptionFields} from './batchOperation/config';
import './style.less';

const getCheckBoxEnabled = record => !Object.values(subscriptValueConfig).includes(record.segmentId);

export default function TextSubscript() {

    const editor = useRef();
    const columnConfiguration = useMemo(() => formatColumnConfiguration({
        customFields, columnConfigs: getAllColumns()}), []);


    const {
        columns,
        handleColumnAction
    } = useColumns({columnConfiguration, tableFieldsMap, extraConfig: {enableParamsToQuery: true}});

    const openEditor = (...args) => editor.current?.openEditor(...args);
    const registerOpenEditor = useCallback(() => {
        return handleColumnAction('openEditor', openEditor);
    }, [handleColumnAction, openEditor]);
    useRegister(registerOpenEditor);

    const [{
        pending,
        error,
        data: {rows = [], totalCount} = {},
        selection
    },
    {
        onSelectChange,
        getSelectedInfo,
        resetRowSelection,
        getMaterialById,
        refreshList,
        inlineMethods,
        batchMethods
    }] = useSubscriptList();
    useEffect(
        () => handleColumnAction('inlineSaveSubscriptStatus', inlineMethods.inlineSaveSubscriptStatus),
        [handleColumnAction, inlineMethods]
    );

    const dataSource = useMemo(() => {
        return initalSubscriptData.map(item => {
            // 根据接口返回的数据匹配对应的角标，将状态，绑定信息以及id填充到默认数据中
            const subscriptInfo = rows.find(
                field => field?.content?.label === subscriptConfig[item.subscriptModule]
            ) || {};
            return {
                ...item,
                subscriptScope: subscriptInfo.bindInfo || [],
                subscriptStatus: subscriptInfo.status,
                ...(
                    subscriptInfo.segmentId
                        ? {
                            segmentId: subscriptInfo.segmentId
                        }
                        : {}
                )
            };
        });
    }, [rows]);

    const rowSelectionProps = useMemo(
        () => toOneUIRowSelectionProps(
            {selection, onSelectChange},
            rows,
            {
                getId: getSubscriptListId,
                multiPageSelection: true,
                maxNumber: 100,
                getCheckBoxEnabled: getCheckBoxEnabled
            }
        ), [selection, onSelectChange, rows]
    );
    const tableProps = {
        size: 'small',
        headerFixTop: 140,
        bottomScroll: {
            bottom: 40
        },
        loading: pending,
        rowKey: getSubscriptListId,
        columns,
        dataSource,
        pagination: false,
        rowSelection: rowSelectionProps
    };

    const dialogEditorProps = {
        getSelectedInfo,
        getMaterialById,
        refreshList,
        saveMethods: {
            ...inlineMethods
        },
        resetRowSelection,
        editorConfig
    };
    const options = getBatchOptionFields();
    const batchOperationProps = {
        getSelectedInfo,
        resetRowSelection,
        operationMap: statusOperationMap,
        options,
        ...batchMethods
    };

    return (
        <div className='manage-center-dashboard-material-list-container text-subscript-container'>
            <div className="operation-bar-and-operator-container">
                <BatchOperationBar {...batchOperationProps} />
            </div>
            <Table {...tableProps} />
            <DialogEditor ref={editor} {...dialogEditorProps} />
        </div>
    );
}